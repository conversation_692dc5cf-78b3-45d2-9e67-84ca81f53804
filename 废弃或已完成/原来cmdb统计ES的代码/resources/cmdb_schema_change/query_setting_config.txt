Schema : 服务器
	serverId 	t_device.id
	CICODE 	t_device.cicode
	固资编号 	t_device.assetid
	设备编号 	t_device.deviceid
	主机名称 	t_device.name
	负责人 	t_device.operator
	备份负责人 	t_device.bakoperator
	备份 	t_device.backup
	服务器SN 	t_device.sn
	是否固资 	t_device.isspecial
	备注 	t_device.memo
	SCM设备类型 	t_device.deviceclass
	标准设备类型(腾讯机型代号) 	ifnull(server_typeclass.DeviceClassName,t_device.deviceclass)
	硬件备注 	t_device.hardmemo
	入库时间 	t_device.firstUseTime
	启用时间 	t_device.firstUseTime
	合同号 	t_device.CompactNum
	服务类型 	t_device.ServiceType
	服务到期时间 	t_device.pacttime
	借用终止时间 	t_device.borrowendtime
	是否安装agent 	t_device.InstallAgent
	服务器类型ID 	t_device.ServerTypeId
	服务器安全标识 	t_device.Int02
	服务器类型 	SvrTypeId
	分级级别ID 	t_device.ClassifyLevel
	分级级别名称 	SvrClassifyLevel
	运维部门ID 	t_device.dep_id
	运维部门 	t_dep.name
	运维小组ID 	t_device.group_id
	运维小组 	t_groups.name
	服务器型号ID 	t_device.DeviceType_Id
	服务器型号 	t_devicetype.name
	带外管理卡类型 	dic_outofband.OutOfBandModel
	服务器高度 	t_devicetype1.height
	型号平均功耗 	t_devicetype.electricity
	状态ID 	t_device.Status_Id
	状态 	equipment_status.name
	RAIDID 	t_device.raidid
	RAID 	Raid.name
	响应级别ID 	t_device.AlarmLevel_Id
	响应级别 	alarmlevel.name
	响应级别及说明 	group_concat(distinct concat(alarmlevel.name, '(', alarmlevel.memo, ')'))
	重要级别ID 	t_device.ImportantLevelId
	服务器维保方式 	t_device.SvrMaintMode
	客户属性 	t_device.ClientAttr
	客户名称 	t_device.ClientName
	重要级别 	importantlevel.name
	IP地址 	ip_equipment.ipaddress
	allIP地址_string 	group_concat(ip_equipment.ipaddress)
	IP地址状态 	ip_equipment.enabled
	服务器操作系统ID 	software_library.id
	服务器操作系统及版本 	group_concat(distinct concat(software_library.name, ' - ', software_library.version))
	服务器进程ID 	group_concat(server_process.id)
	服务器进程 	group_concat(server_process.Name)
	域名名称 	ip_equipment.domain
	所有IP 	SvrId
	所有IP 	SvrId
	所有IP_cache 	SvrId
	IPv6 	SvrAssetId
	未启用IP 	SvrId
	内网IP 	SvrId
	外网IP 	SvrId
	区域ID 	idc_parent.areaid
	区域 	area.name
	城市ID 	idc_parent.cityid
	城市 	idc_parent.cityname
	机房ID 	idc_parent.id
	机房 	idc_parent.name
	功能类型ID 	idc_parent.FunctionId
	功能类型 	idc_parent.FunctionName
	机房运营商ID 	idc_parent.OperationId
	机房运营商名称 	idc_parent.OperationName
	机房管理单元 	t_idc.name
	机房管理单元ID 	equipment_position.idcid
	单元负责人 	t_idc.operator
	存放机架 	t_equipment.name
	机架规划类型 	t_equipment.PlanDeviceType
	存放机架ID 	equipment_position.shelfid
	存放机架名称 	t_equipment.name
	ModuleId 	idc_safedomain.ModuleId
	Module名称 	t_module.name
	OS扁平化 0老机房支持windows 1新机房 	t_module.OsNew
	Campus名称 	t_subzone.name
	CampusID 	t_module.SubZoneId
	Zone名称 	t_zone.name
	ZoneID 	t_subzone.ZoneId
	RegionId 	t_zone.AreaId
	Region名称 	t_zone_area.name
	机架SP编号 	t_equipment.sn
	存放机位 	idc_position.code
	存放机位ID 	equipment_position.PositionId
	带外管理方式 	idc_position.manageType
	机位高度 	idc_position.hight
	IDC全名 	SvrId
	IDCsID 	SvrId
	逻辑区域ID 	idc_safedomain.id
	逻辑区域 	idc_safedomain.name
	内网络结构ID 	idc_safedomain.InnerNetworkStructureId
	内网络结构 	idc_safedomain.InnerNetworkStructureName
	外网络结构ID 	idc_safedomain.outerNetworkStructureId
	外网络结构 	idc_safedomain.outerNetworkStructureName
	网络结构版本ID 	idc_safedomain.StructureVersion
	网络结构版本 	dic_net_version.name
	内网机房网络模块ID 	SvrId
	内网机房网络模块 	SvrId
	外网机房网络模块ID 	SvrId
	外网机房网络模块 	SvrId
	所有内网机房网络模块 	SvrId
	所有外网机房网络模块 	SvrId
	产品初始运维部门 	group_concat(distinct t_dep_busi.name)
	业务部门 	group_concat(distinct bsi_dep.DeptName)
	业务部门ID 	bsi_dep.DeptId
	运营产品编号 	group_concat(distinct product.productname)
	运营产品 	group_concat(distinct product.productname)
	产品ID 	group_concat(distinct product.productid)
	业务产品集 	group_concat(distinct product_set.productsetname)
	业务产品集ID 	group_concat(distinct product_set.productsetid)
	业务集合 	group_concat(distinct bs1.chinesename)
	业务集合ID 	group_concat(distinct bs1.id SEPARATOR ';')
	业务 	group_concat(distinct bs2.chinesename)
	业务ID 	group_concat(distinct bs2.id SEPARATOR ';')
	业务模块Uid 	group_concat(distinct bs3.Uid SEPARATOR ';')
	业务模块id 	group_concat(distinct bs3.id SEPARATOR ';')
	业务路径 	group_concat(distinct bs3.Path SEPARATOR ';')
	业务模块 	group_concat(distinct bs3.chinesename)
	业务模块汇总 	SvrId
	业务部门+业务模块汇总 	SvrId
	业务模块汇总cache 	SvrId
	子机固资号 	SvrId
	服务器端口 	SvrId
	内网交换机端口 	SvrId
	外网交换机端口 	SvrId
	管理网交换机端口 	SvrId
	内网交换机端口(DB) 	SvrId
	外网交换机端口(DB) 	SvrId
	管理网交换机端口(DB) 	SvrId
	内网交换机IP 	SvrId
	外网交换机IP 	SvrId
	管理网交换机IP 	SvrId
	内网交换机IP(DB) 	SvrId
	外网交换机IP(DB) 	SvrId
	管理网交换机IP(DB) 	SvrId
	网络设备ID 	SvrId
	最后更新 	t_device.lastupdate
	虚拟子机ID 	vchild.childServerId
	虚拟子机固资号 	vchild_server.assetid
	虚拟母机ID 	vparent.parentServerId
	虚拟母机固资号 	vparent_server.assetid
	服务器操作系统名称 	software_library.name
	服务器操作系统内核版本 	software_library.version
	连接的存储类型 	SvrId
	ads校验 	server_ads.AdsStatus
	ADS校验时间 	server_ads.AdsCheckTime
	cpu 	(select cast(group_concat(concat(PhysicalId, '/', Vendor, '/', Model, '/', Speed, '/', CoreNumber, '/', LogicalCoreCount) SEPARATOR ';') as char(3000)) from server_hard_cpu where serverid=t_device.id)
	harddisk 	(select cast(group_concat(concat(SN, '/', Vendor, '/', Model, '/',capacity, '/',FirmwareVersion) SEPARATOR ';') as char(3000)) from server_hard_disk where serverid=t_device.id)
	memory 	(select cast(group_concat(concat(SN, '/', Vendor, '/', Model, '/',capacity) SEPARATOR ';') as char(3000)) from server_hard_memory where serverid=t_device.id)
	networkcard 	(select cast(group_concat(concat(MacAddress, '/', Vendor, '/', Model, '/',MaxSpeed, '/',FirmwareVersion) SEPARATOR ';') as char(3000)) from server_hard_nic where serverid=t_device.id)
	macAddress 	(select cast(group_concat(concat(macaddress) SEPARATOR ';') as char(3000)) from server_hard_nic where serverid=t_device.id)
	raid信息 	(select cast(group_concat(concat(SN, '/', Vendor, '/', Model, '/',CacheSize, '/',FirmwareVersion) SEPARATOR ';') as char(3000)) from server_hard_raid where serverid=t_device.id)
	机房规划运营商 	t_idc.PlanOperationName
	电话 	t_idc.phone
	传真 	t_idc.fax
	备用传真 	t_idc.backupfax
	客服邮箱 	t_idc.SupportEmail
	授权方式 	t_idc.AuthoriseType
	状态更新时间 	server_change_log.ChangeTime
	虚拟机UUID 	t_device.uuid
	虚拟机标准CPU 	server_vm_hard.VmCpu
	虚拟机标准内存(M) 	server_vm_hard.VmMemory
	虚拟机标准硬盘(G) 	server_vm_hard.vmharddisk
	是否需要数据保护 	t_device.DataLeakProtect
	版本号机型ID 	t_device.ModelVersionId
	腾讯机型版本号 	server_typeclass.Version
	供应商机型代号 	td2.name
	供应商机型ID 	server_typeclass.DeviceTypeId
	腾讯机型代号 	server_typeclass.DeviceClassName
	设备描述 	server_typeclass.Description
	处理器型号 	server_typeclass.CpuModel
	处理器数量 	server_typeclass.CpuNumber
	单处理器核心数量 	server_typeclass.CpuCore
	单内存容量(GB) 	server_typeclass.MemoryVolume
	内存条数 	server_typeclass.MemoryNumber
	硬盘接口类型 	server_typeclass.DiskType
	硬盘规格(GB) 	server_typeclass.DiskVolume
	硬盘数量 	server_typeclass.DiskNumber
	硬盘固件版本 	server_typeclass.DiskFirmware
	硬盘接口类型2 	server_typeclass.DiskType2
	硬盘规格2(GB) 	server_typeclass.DiskVolume2
	硬盘数量2 	server_typeclass.DiskNumber2
	硬盘固件版本2 	server_typeclass.DiskFirmware2
	阵列卡品牌 	server_typeclass.RaidBrand
	阵列卡/HBA卡规格 	server_typeclass.RaidModel
	阵列卡/HBA卡固件 	server_typeclass.RaidFirmware
	阵列卡电池型号 	server_typeclass.RaidPowerModel
	支持阵列类型 	StrRaidTypeId
	HBA卡品牌 	server_typeclass.HbaBrand
	HBA卡规格 	server_typeclass.HbaModel
	HBA卡固件 	server_typeclass.HbaFirmware
	HBA卡数量 	server_typeclass.HbaNumber
	HBA卡形态 	server_typeclass.HbaShape
	网卡品牌 	server_typeclass.NicBrand
	网卡速率 	server_typeclass.NicSpeed
	网卡型号 	server_typeclass.NicModel
	网卡固件 	server_typeclass.NicFirmware
	电源规格 	server_typeclass.PowerSize
	电源数量 	server_typeclass.PowerNumber
	电源类型 	server_typeclass.PowerType
	BIOS版本 	server_typeclass.BiosVersion
	BMC版本 	server_typeclass.BmcVersion
	带外管理卡型号 	server_typeclass.OutBandInfo
	SideBand支持 	server_typeclass.SideBandSupport
	机箱结构 	server_typeclass.BoxType
	机箱宽度(cm) 	server_typeclass.Width
	机箱深度(cm) 	server_typeclass.Depth
	整机重量(kg) 	server_typeclass.Weight
	功耗(W) 	server_typeclass.Electricity
	综合性能 	server_typeclass.Score
	mini OS 性能 	server_typeclass.MiniOsScore
	备注 	server_typeclass.Memo
	厂商 	server_typeclass.Brand
	服务器制造商 	server_typeclass.Producer
	产品生命周期状态 	server_typeclass.LifeCycle
	内存属性 	server_typeclass.MemoryMemo
	硬盘型号 	server_typeclass.DiskModel
	硬盘属性 	server_typeclass.DiskMemo
	硬盘型号2 	server_typeclass.DiskModel2
	硬盘属性2 	server_typeclass.DiskMemo2
	阵列卡缓存大小(MB) 	server_typeclass.RaidCache
	阵列卡缓存数据保护方式 	server_typeclass.RaidProtection
	网卡接口类型 	server_typeclass.NicType
	带外管理卡属性 	server_typeclass.OutBandMemo
	阵列卡/HBA数量 	server_typeclass.RaidNumber
	SSD型号 	server_typeclass.SsdModel
	SSD规格 	server_typeclass.SsdType
	SSD接口类型 	server_typeclass.SsdPortType
	ssd数量 	server_typeclass.SsdNumber
	SSD固件 	server_typeclass.SsdFirmware
	SSD属性 	server_typeclass.SsdMemo
	GPU型号 	server_typeclass.GpuModel
	GPU数量 	server_typeclass.GpuNumber
	gpu固件 	server_typeclass.GpuFirmware
	gpu属性 	server_typeclass.GpuMemo
	带内网口数量 	server_typeclass.InBandNumber
	带内网卡属性 	server_typeclass.InBandMemo
	电源属性 	server_typeclass.PowerMemo
	不选部件 	server_typeclass.UnselectedParts
	电源线数量 	server_typeclass.PowerCordNumber
	电源线规格 	server_typeclass.PowerCordType
	导轨数量 	server_typeclass.GuideNumber
	导轨规格 	server_typeclass.GuideType
	风扇数量 	server_typeclass.FanNumber
	风扇型号 	server_typeclass.FanModel
	风扇属性 	server_typeclass.FanMemo
	标准型号 	server_typeclass.StandModel
	支持阵列类型(新) 	StrRaidTypeId
	支持阵列类型ID(新) 	server_typeclass.RaidTypeId
	硬盘尺寸 	server_typeclass.DiskSize
	硬盘尺寸2 	server_typeclass.DiskSize2
	阵列卡/HBA卡形态 	server_typeclass.RaidShape
	机位外网网络模块出口 	group_concat(distinct idc_exit.Name SEPARATOR ';')
	机位外网网络模块出口运营商ID 	group_concat(distinct idc_exit.OperationId SEPARATOR ';')
	机位外网网络模块出口运营商名称 	group_concat(distinct idc_exit_op.Name SEPARATOR ';')
	服务器锁ID 	server_lock.id
	服务器锁Type(值为status,department) 	server_lock.LockType
	服务器锁起始时间 	server_lock.LockStartTime
	服务器锁截止时间 	server_lock.LockEndTime
	服务器锁SystemId 	server_lock.LockSystemId
	服务器锁Operator 	server_lock.LockOperator
	服务器锁Key 	server_lock.LockKey
	服务器锁Reason 	server_lock.LockReason
	规划产品id 	product.PlanProductId
	规划产品名称 	bas_oms_plan_product.PlanProductName
	资产类型 	ifnull(itcloud.ItCloud,0)
	HealthStatus 	ifnull(itcloud.HealthStatus,0)
	Bios固件ID 	ifnull(itcloud.biosid,0)
	Bios固件名称 	ifnull(bios_firmware.name,0)
	SvrRedoTime 	ifnull(itcloud.RedoTime,'')
	SvrSuggestTime 	ifnull(itcloud.SuggestTime,'')
	OverlayIp 	ip_overlay.ipaddress
	OverlayIp_all 	group_concat(ifnull(ip_overlay.ipaddress,''))
	All_Overlay_IpInfo 	SvrId
	Bios说明 	ifnull(bios_firmware.description,0)
	服务器BIOS配置 	server_typeclass.BiosType
	云节点 	cloud_node_area_dictionary.name
	vip 	ifnull(itcloud.vip,'')
Schema : 设备状态
	状态ID 	t_status.id
	固资编号 	t_status.name
Schema : 已删服务器
	serverId 	t_device.id
	固资编号 	t_device.assetid
	最后更新 	t_device.lastUpdate
Schema : 设备IDC信息
	EqpId 	equipment_position.id
	IdcId 	equipment_position.IdcId
	RckId 	equipment_position.ShelfId
	PosId 	equipment_position.PositionId
	EquipmentId 	equipment_position.EquipmentId
	ConfigItemId 	equipment_position.ConfigItemId
	ConfigItemId 	equipment_position.ConfigItemId
	ConfigItemId 	equipment_position.ConfigItemId
	ParentId 	equipment_position.ParentId
Schema : 服务器IP状态查询
	服务器ID 	t_device.id
	固资编号 	t_device.assetid
	IP地址 	ip_equipment.ipaddress
	IP指定状态 	ip_equipment.enabled
	内外网IP标记 	ip_equipment.flag
	网段名称 	''
	运营商ID 	''
	运营商 	''
	用途 	''
	内外网 	''
	网关 	''
	子网掩码 	''
	网段说明 	''
	IP地址ID 	''
	IP地址 	''
	IP状态 	''
	IP地址说明 	''
	分配信息 	''
Schema : 服务器已分配未启用IP信息
	IP地址ID 	ip_equipment.ipid
	IP地址 	ip_equipment.ipaddress
	IP状态 	'Active'
	IP地址说明 	''
	网段名称 	ipaddress
	运营商 	ipaddress
	用途 	ipaddress
	内外网 	ipaddress
	网关 	ipaddress
	子网掩码 	ipaddress
	网段说明 	ipaddress
Schema : 服务器连接独立存储信息
	固资号 	singlestore.FixSerial
	型号 	equipment_model.Name
	主机名称 	singlestore.ComputerName
	存储容量 	singlestore.Content
Schema : 子存储信息
	子存储Id 	collectstorechild.id
	集中存储固资号 	collectstore.FixSerial
	集中存储Id 	collectstore.id
	主机名称 	collectstore.ComputerName
	LunNum 	collectstorechild.LunNum
	LunSize 	collectstorechild.LunSize
	服务器ID 	t_device.id
	固资编号 	t_device.assetid
Schema : 服务器分配集中存储信息
	固资号 	collectstore.FixSerial
	主机名称 	collectstore.ComputerName
	LunNum 	collectstorechild.LunNum
	LunSize 	collectstorechild.LunSize
Schema : 服务器连接光纤交换机信息
	固资号 	switchpc.FixSerial
	型号 	equipment_model.Name
	主机名称 	switchpc.ComputerName
	端口 	switchpcchild.PortName
Schema : 服务器间关系查询
	id 	server_relation.id
	是否虚拟机 	server_relation.IsVirtual
	父机 	server_relation.parentserverid
	子机 	server_relation.childserverid
Schema : 机房综合查询
	物理机房Id 	idc_parent.id
	物理机房名称 	idc_parent.name
	物理机房简称 	idc_parent.ShortName
	物理机房Code 	idc_parent.Code
	机房运营商 	idc_parent.OperationName
	运营商ID 	idc_parent.OperationId
	城市名称 	dic_idcparentcity.name
	城市ID 	dic_idcparentcity.id
	国家 	dic_idcparentcountry.CountryName
	国家ID 	dic_idcparentcountry.countryid
	省份 	dic_province.Name
	省份ID 	dic_province.id
	功能类型ID 	idc_parent.FunctionId
	功能类型 	idc_parent.FunctionName
	园区ID 	idc_parent.CampusId
	园区名称 	t_subzone.Name
	运维区域 	area.name
	运维区域ID 	area.id
	运营商联系方式 	idc_parent.OperationContact
	可用机架数 	idc_parent.SignupShelfCount
	付费机架数 	idc_parent.PayShelfCount
	机房逻辑区域 	(select group_concat(idc_safedomain.name) from idc_safedomain where idc_safedomain.IdcParentId=idc_parent.id)
	资源负责人 	idc_parent.operator
	机房备注 	idc_parent.memo
	机房管理单元ID 	t_idc.id
	机房管理单元 	t_idc.name
	单元负责人 	t_idc.operator
	网络资产负责人 	t_idc.NetOperators
	机房经理 	t_idc.Manager
	机房管理单元类型 	t_idc.type
	机房管理单元规模 	t_idc.Size
	电话 	t_idc.Phone
	传真 	t_idc.fax
	备用传真 	t_idc.BackupFax
	客服邮箱 	t_idc.SupportEmail
	工程邮箱 	t_idc.ProjectEmail
	授权方式 	t_idc.AuthoriseType
	覆盖范围 	t_idc.CoverScope
	地址 	t_idc.Address
	是否启用 	t_idc.IsActive
	单元启用时间 	t_idc.FirstUseTime
	单元备注 	t_idc.memo
	规划逻辑区域 	t_idc.PlanLogicAreaName
	规划出口运营商 	t_idc.PlanOperationName
	已录入机架数 	IdcId
	使用中机架数 	IdcId
	可用机架数 	IdcId
	可扩容机架数 	IdcId
	可用机位总数 	IdcId
	已开电机架总数 	IdcId
	机房管理单元覆盖运维部门 	IdcId
	一级机房覆盖运维部门 	IdcpId
	机房管理单元覆盖业务部门 	IdcId
	一级机房覆盖业务部门 	IdcpId
	机房管理单元覆盖业务集 	IdcId
	一级机房覆盖业务集 	IdcpId
	机房管理单元覆盖Module 	IdcId
	一级机房覆盖Module 	IdcpId
	机架ID 	t_equipment.id
	机架编号 	t_equipment.name
	机架SP编号 	t_equipment.sn
	腾讯机架标识 	t_equipment.tencentShelfID
	是否启用 	t_equipment.islay
	机架高度 	t_equipment.high
	机架额定电流 	t_equipment.ratedi
	峰值电流 	t_equipment.MaxI
	峰值功率 	t_equipment.MaxPower
	机架已用电流 	t_equipment.usedi
	机架规划设备类型 	t_equipment.PlanDeviceType
	机架业务类型 	t_equipment.RckBizType
	客户名称 	i.ClientName
	电源线类型 	i.LineType
	电源线数量 	i.LineNum
	机位TOR 	i.PosTOR
	机架备注 	t_equipment.memo
	机架下机位总数 	(select count(idc_position.id) from idc_position where idc_position.EquipmentId=t_equipment.id)
	机架下已使用机位数 	(select count(idc_position.id) from idc_position where idc_position.EquipmentId=t_equipment.id and idc_position.status=1)
	机架下空闲机位数 	(select count(idc_position.id) from idc_position where idc_position.EquipmentId=t_equipment.id and idc_position.status=0)
	机架下不可用机位数 	(select count(idc_position.id) from idc_position where idc_position.EquipmentId=t_equipment.id and idc_position.status=2)
	机架是否占用 	(select if( count(equipment_position.id) = 0, 0,1) as cnt from equipment_position where equipment_position.ShelfId=t_equipment.id)
	机架下关联服务器总数 	(select count(equipment_position.id) from equipment_position where equipment_position.ConfigItemId=2 and equipment_position.ShelfId=t_equipment.id)
	机架下关联网络设备总数 	(select count(equipment_position.id) from equipment_position where equipment_position.ConfigItemId=5 and equipment_position.ShelfId=t_equipment.id)
	机架放置网络设备 	(select group_concat(equipment_position.equipmentid) from equipment_position where equipment_position.ConfigItemId=5 and equipment_position.ShelfId=t_equipment.id)
	机架下关联存储总数 	(select count(equipment_position.id) from equipment_position where equipment_position.ConfigItemId=13 and equipment_position.ShelfId=t_equipment.id)
	机架是否开电 	t_equipment.isopen
	机架开电时间 	DATE_FORMAT(t_equipment.opentime,'%Y-%m-%d')
	机架关电时间 	DATE_FORMAT(t_equipment.downtime,'%Y-%m-%d')
	机架（预）启用时间 	DATE_FORMAT(t_equipment.usetime,'%Y-%m-%d')
	是否虚拟 	t_equipment.IsVirtual
	电流类型 	t_equipment.ElectricityType
	可用功率 	t_equipment.RatedPower
	市电 	t_equipment.CityPower
	柴油发电机 	t_equipment.DieselPower
	配电路由 	t_equipment.PowerRoute
	机房精密空调 	t_equipment.AirConditioner
	可用级别 	t_equipment.UseLevel
	是否支持SideBand 	t_equipment.IsSideBand
	ModuleId 	idc_safedomain.ModuleId
	ModuleName 	t_module.name
	SubZoneName 	t_subzone.name
	SubZoneID 	t_module.SubZoneId
	ZoneName 	t_zone.name
	ZoneID 	t_subzone.ZoneId
	机位ID 	i.id
	机位编号 	i.code
	机位状态 	i.status
	机位不可用原因 	i.UnuseableReason
	机位规划设备类型ID 	i.PlanDeviceType
	机位规划设备类型 	PosPlanDeviceTypeId
	机位高度 	i.Hight
	机位带外管理方式 	i.ManageType
	机位放置设备类型 	(select cast(group_concat(distinct equipment_position.ConfigItemId) as char(1000)) from equipment_position where equipment_position.PositionId=i.id)
	机位放置设备 	PosId
	设备运维部门 	PosId
	机位内网交换机端口 	i.InnerSwitchPort
	机位外网交换机端口 	i.OuterSwitchPort
	机位管理网交换机端口 	i.AdminSwitchPort
	机位其它端口 	i.OtherPort
	机位逻辑区域 	idc_safedomain.name
	机位逻辑区域ID 	i.SafeDomain
	客户名称 	tdb_customer.customer
	客户母公司 	tdb_customer.company
	网络结构版本 	idc_safedomain.StructureVersion
	网络结构版本名 	dic_net_version.name
	网络结构版本内网网速 	dic_net_version.speed
	内网络结构ID 	idc_safedomain.InnerNetworkStructureId
	内网络结构 	idc_safedomain.InnerNetworkStructureName
	外网络结构ID 	idc_safedomain.outerNetworkStructureId
	外网络结构 	idc_safedomain.outerNetworkStructureName
	逻辑区域外网网速 	idc_safedomain.OuterNetSpeed
	逻辑区域内网网速 	idc_safedomain.InnerNetSpeed
	机位外网网速 	PosId
	机位内网网速 	idc_safedomain.InnerNetSpeed
	机位说明 	i.Memo
	内网机房网络模块 	(select group_concat(idc_network.name SEPARATOR ';') from rs_idcnetworkmodule_position inner join idc_network on rs_idcnetworkmodule_position.IdcNetworkModuleId=idc_network.id where idc_network.type=0 and PositionId=i.id)
	外网机房网络模块 	(select group_concat(idc_network.name SEPARATOR ';') from rs_idcnetworkmodule_position inner join idc_network on rs_idcnetworkmodule_position.IdcNetworkModuleId=idc_network.id where idc_network.type=1 and PositionId=i.id)
	内网网络机房ID 	(select idc_network.id from idc_network inner join rs_idcnetworkmodule_position on idc_network.id= rs_idcnetworkmodule_position.idcnetworkmoduleid where idc_network.type=0 and rs_idcnetworkmodule_position.PositionId=i.Id limit 1)
	外网网络机房ID 	(select idc_network.id from idc_network inner join rs_idcnetworkmodule_position on idc_network.id= rs_idcnetworkmodule_position.idcnetworkmoduleid where idc_network.type=1 and rs_idcnetworkmodule_position.PositionId=i.Id limit 1)
	外网运营商 	(select group_concat(distinct idc_operation.name SEPARATOR ';') from rs_idcnetworkmodule_position inner join idc_network on rs_idcnetworkmodule_position.IdcNetworkModuleId=idc_network.id inner join idc_exit on idc_exit.IdcNetModuleId = idc_network.id left join idc_operation on idc_operation.id = idc_exit.OperationId where idc_network.type=1 and PositionId=i.id)
	外网运营商ID 	(select group_concat(distinct idc_exit.OperationId SEPARATOR ';') from rs_idcnetworkmodule_position inner join idc_network on rs_idcnetworkmodule_position.IdcNetworkModuleId=idc_network.id inner join idc_exit on idc_exit.IdcNetModuleId = idc_network.id where idc_network.type=1 and PositionId=i.id)
	预约设备 	i.PreOccupyAsset
	预约时间 	i.PreOccupyDateTime
	预启用时间 	i.PreStartDateTime
	预占用人 	i.PreOrder
	预占用原因 	i.PreReason
	机位连接信息 	PosId
	机位业务部门ID 	i.BusinessDeptId
	机位业务部门名称 	dept.DeptName
	机位是否虚拟 	i.IsVirtual
	机架用途 	t_equipment.rack_usage
	机位产品Id 	product.ProductId
	机位规划产品Id 	plan_product.PlanProductId
	机位产品 	product.ProductName
	机位规划产品 	plan_product.PlanProductName
	BG名称 	bg.BizGroupName
	Module业务类型 	t_module.BusinessType
	Module带宽类型 	t_module.BandType
	机房运营归属方 	idc_parent.OperationOwner
Schema : 物理机房
	物理机房Id 	idc_parent.id
	物理机房简称 	idc_parent.ShortName
	物理机房Code 	idc_parent.Code
	物理机房名称 	idc_parent.name
	机房运营商 	idc_parent.OperationName
	机房运营商ID 	idc_parent.OperationId
	网络运营商 	group_concat(distinct idc_operation.name)
	规划网络运营商 	group_concat(distinct t_idc.PlanOperationName)
	城市ID 	idc_parent.cityid
	城市名称 	idc_parent.cityname
	功能类型 	idc_parent.FunctionName
	功能类型ID 	idc_parent.FunctionId
	运维区域ID 	idc_parent.areaid
	运维区域 	area.name
	园区ID 	idc_parent.CampusId
	园区名称 	t_subzone.Name
	运营商联系方式 	idc_parent.OperationContact
	签约机架数 	idc_parent.SignupShelfCount
	已付费机架数 	idc_parent.PayShelfCount
	备注 	idc_parent.memo
	资源负责人 	idc_parent.Operator
	资源管理员 	idc_parent.operator
	一级机房覆盖运维部门 	IdcpId
	一级机房覆盖业务 	IdcpId
	所属物流分组 	idc_parent.logisticsGroup
	机房运营归属方 	idc_parent.OperationOwner
Schema : 机房管理单元
	机房管理单元ID 	t_idc.id
	机房管理单元 	t_idc.name
	覆盖范围 	t_idc.CoverScope
	机房管理单元SN 	t_idc.sn
	运营商ID 	idc_parent.OperationId
	运营商 	idc_parent.OperationName
	机房负责人 	t_idc.operator
	网络资产负责人 	t_idc.NetOperators
	机房经理 	t_idc.Manager
	机房管理单元类型 	t_idc.type
	机房管理单元规模 	t_idc.Size
	是否启用 	t_idc.IsActive
	启用时间 	t_idc.FirstUseTime
	规划逻辑区域ID 	t_idc.PlanLogicAreaId
	规划逻辑区域 	t_idc.PlanLogicAreaName
	规划出口运营商ID 	t_idc.PlanOperationId
	规划出口运营商 	t_idc.PlanOperationName
	电话 	t_idc.Phone
	传真 	t_idc.fax
	备用传真 	t_idc.BackupFax
	客服邮箱 	t_idc.SupportEmail
	工程邮箱 	t_idc.ProjectEmail
	授权方式 	t_idc.AuthoriseType
	地址 	t_idc.Address
	备注 	t_idc.memo
	采购收货人 	t_idc.Receiver
	运营商值班室 	t_idc.OperationShift
	工程负责人 	t_idc.EngineerPrincipal
	腾讯值班室 	t_idc.TencentShift
	签约机架总数 	t_idc.SignupEquipmentCount
	已付费机架数 	t_idc.PayShelf
	安全域 	idcId
	使用中机架 	idcId
	可用机架 	idcId
	可用机位 	idcId
	可扩容机架 	idcId
	已开电机架 	idcId
	IDC出口 	idcId
	IDC机架总数 	idcId
	机房 	idc_parent.name
	机房ID 	t_idc.IdcParentId
	机房简称 	idc_parent.ShortName
	机房Code 	idc_parent.Code
	机架名称 	t_equipment.name
	机架ID 	t_equipment.id
	机架SN 	t_equipment.sn
	是否启用 	t_equipment.islay
	机架是否开电 	t_equipment.isopen
	机架启用时间 	t_equipment.usetime
	机架额定电流 	t_equipment.RatedI
	机架已用电流 	t_equipment.usedi
	机架规划类型 	t_equipment.PlanDeviceType
	机架空闲率 	rackId
	机架统计信息 	rackId
	机架网络设备数 	rackId
	是否虚拟 	t_equipment.IsVirtual
	机位名称 	idc_position.code
	机位规划设备类型 	idc_position.PlanDeviceType
	机位ID 	idc_position.id
	机位状态 	idc_position.Status
	机位不可用原因 	idc_position.UnuseableReason
	机位高度 	idc_position.hight
	预约设备 	idc_position.PreOccupyAsset
	预约时间 	idc_position.PreOccupyDateTime
	逻辑区域 	idc_safedomain.id
	逻辑区域 	idc_safedomain.name
	内网网络机房信息 	positionId
	外网网络机房信息 	positionId
	内网网络机房ID 	(select idc_network.id from idc_network inner join rs_idcnetworkmodule_position on idc_network.id= rs_idcnetworkmodule_position.idcnetworkmoduleid where idc_network.type=0 and rs_idcnetworkmodule_position.PositionId=idc_position.Id limit 1)
	内网网络机房 	(select idc_network.name from idc_network inner join rs_idcnetworkmodule_position on idc_network.id= rs_idcnetworkmodule_position.idcnetworkmoduleid where idc_network.type=0 and rs_idcnetworkmodule_position.PositionId=idc_position.Id limit 1)
	外网网络机房ID 	(select idc_network.id from idc_network inner join rs_idcnetworkmodule_position on idc_network.id= rs_idcnetworkmodule_position.idcnetworkmoduleid where idc_network.type=1 and rs_idcnetworkmodule_position.PositionId=idc_position.Id limit 1)
	外网网络机房 	(select idc_network.name from idc_network inner join rs_idcnetworkmodule_position on idc_network.id= rs_idcnetworkmodule_position.idcnetworkmoduleid where idc_network.type=1 and rs_idcnetworkmodule_position.PositionId=idc_position.Id limit 1)
	内网交换机端口 	idc_position.InnerSwitchPort
	外网交换机端口 	idc_position.OuterSwitchPort
	管理网交换机端口 	idc_position.AdminSwitchPort
	内网交换机管理IP 	innerSwitchPort
	外网交换机管理IP 	outerSwitchPort
	功能类型 	idc_parent.functionName
	区域ID 	idc_parent.areaid
	区域 	area.name
	城市ID 	idc_parent.cityid
	城市 	idc_parent.cityname
	城市 	idc_parent.cityname
	功能类型ID 	idc_parent.FunctionId
	功能类型 	idc_parent.FunctionName
	内网络结构ID 	idc_safedomain.InnerNetworkStructureId
	内网络结构 	idc_safedomain.InnerNetworkStructureName
	外网络结构ID 	idc_safedomain.outerNetworkStructureId
	外网络结构 	idc_safedomain.outerNetworkStructureName
	网络结构版本ID 	idc_safedomain.StructureVersion
	网络结构版本 	netStructVer
	机房单元覆盖运维部门 	idcId
	机房单元覆盖业务 	idcId
Schema : 专线
	lineId 	special_line.id
	专线编号 	special_line.number
	专线名称 	special_line.name
	专线速率 	special_line.velocity
	运营商ID 	special_line.ISPID
	运营商 	idc_operation.name
	专线类型ID 	special_line.EquipmentTypeId
	专线类型 	equipment_type.name
	专线状态ID 	special_line.EquipmentStatusId
	专线状态 	equipment_status.name
	专线用途ID 	special_line.usesid
	专线用途 	special_line_uses.name
	负责人 	special_line.operator
	使用部门ID 	special_line.UserDept
	使用部门 	group_concat(t_dep.name)
	开通日期（停用） 	special_line.usetime
	启用日期 	special_line.usetime
	拆除日期 	special_line.trucetime
	运营商联系人 	special_line.m_operator
	运营商联系人电话 	special_line.m_phone
	运营商报障电话 	special_line.phone
	备注 	special_line.Memo
	专线起始设备ID 	netport_start.deviceid
	专线起始设备名 	netport_start.NetDeviceName
	起始设备端口ID 	netport_start.id
	起始设备端口名 	netport_start.PortName
	专线终点设备ID 	netport_end.deviceid
	专线终点设备名 	netport_end.NetDeviceName
	终点设备端口ID 	netport_end.id
	终点设备端口名 	netport_end.PortName
	起始机房网络模块 	group_concat(distinct startnetidc.name)
	终止机房网络模块 	group_concat(distinct endnetidc.name)
	起始机房网络模块负责人 	group_concat(distinct startnetidc.principal)
	终止机房网络模块负责人 	group_concat(distinct endnetidc.principal)
	起始机房ID 	phi_start_idc.idcid
	起始机房名称 	phi_start_idc.idcname
	终止机房ID 	phi_end_idc.idcid
	终止机房名称 	phi_end_idc.idcname
Schema : 网络设备
	设备ID 	net_device.id
	设备名称 	net_device.name
	固资编号 	net_device.assetId
	设备类型ID 	equipment_type.Id
	设备类型 	equipment_type.name
	设备状态ID 	net_device.statu_id
	设备状态 	equipment_status.name
	设备标准型号ID 	netdevice_model.id
	设备标准型号 	netdevice_model.name
	标准型号编码 	netdevice_model.code
	SCM型号 	equipment_model.name
	设备厂商 	netdevice_manufacturer.cname
	管理IP 	(select GROUP_CONCAT(ipaddress) from ip_equipment  where configitemid=5 and active=1 and ip_equipment.EquipmentId=net_device.id and ip_equipment.IpType=0)
	其他IP 	(select GROUP_CONCAT(ipaddress) from ip_equipment where ip_equipment.EquipmentId=net_device.id and configitemid=5  and active=1  and ip_equipment.IpType=1)
	端口互联IP 	(select GROUP_CONCAT(ipaddress) from ip_equipment where ip_equipment.EquipmentId=net_device.id and configitemid=5  and active=1 and ip_equipment.IpType=4)
	VlanIP 	(select GROUP_CONCAT(ipaddress) from ip_equipment where ip_equipment.EquipmentId=net_device.id and configitemid=5  and active=1 and ip_equipment.IpType=5)
	网关IP 	(select GROUP_CONCAT(ipaddress) from ip_equipment where ip_equipment.EquipmentId=net_device.id and configitemid=5  and active=1 and ip_equipment.IpType=3)
	LoopBackIp 	(select GROUP_CONCAT(ipaddress) from ip_equipment where ip_equipment.EquipmentId=net_device.id and configitemid=5  and active=1 and ip_equipment.IpType=2)
	设备是否已被ADS校验 	net_device.CheckByADS
	设备被ADS校验时间 	net_device.CheckDateTime
	设备所属部门ID 	net_device.dep_id
	设备所属部门名称 	t_dep.name
	堆叠设备ID 	net_device.logicdeviceid
	是否核心 	net_device.isCore
	SN 	net_device.serialnumber
	用途 	net_device.uses
	备注 	net_device.memo
	OS版本 	net_device.software_version
	机房管理单元ID 	equipment_position.idcid
	机房管理单元 	t_idc.name
	物理机房ID 	t_idc.IdcParentId
	物理机房名称 	idc_parent.name
	物理机房编码 	idc_parent.Code
	连接服务器 	netdeviceId
	连接网络设备 	netdeviceId
	连接出口 	netdeviceId
	连接专线 	netdeviceId
	内网机房网络模块 	netdeviceId
	外网机房网络模块 	netdeviceId
	设备维护人 	net_device.operator
	存放机架Id 	t_equipment.id
	存放机架 	t_equipment.name
	存放机架SP 	t_equipment.sn
	机架是否开电 	t_equipment.isopen
	机房网络模块 	netdeviceId
	机房网络模块(含ID，type:0/1) 	netdeviceId
	关联机位信息 	netdeviceId
	网段信息 	''
	网络设备部件ID 	net_device_parts.id
	影响到的所有下联服务器 	netdeviceId
	出库日期 	net_device.outtime
	合同号 	net_device.CompactNum
	服务类型 	net_device.ServiceType
	服务到期时间 	net_device.pacttime
	启用日期 	net_device.usetime
	故障信息 	net_device.faultreason
	拓扑层级 	net_device.TopoLevel
	设备更新时间 	net_device.lastupdatetime
	网络模块ID 	netdeviceId
	有无网络连接 	if(exists(select id from netdeviceport where net_device.id = netdeviceport.deviceid and linktype>0),  0,1)
Schema : 网络设备端口
	网络设备ID 	netdeviceport.DeviceId
	网络设备名称 	net_device.name
	网络设备固资号 	net_device.assetid
	设备维护人 	net_device.operator
	设备类型ID 	equipment_type.Id
	设备类型 	equipment_type.name
	设备状态 	equipment_status.name
	存放机架Id 	t_equipment.id
	存放机架 	t_equipment.name
	端口ID 	netdeviceport.id
	端口名称 	netdeviceport.PortName
	Raw端口名称 	netdeviceport.RawPortName
	端口IP 	netdeviceport.IPAddressFull
	端口标准速率 	netdeviceport.StdSpeed
	端口实际速率 	netdeviceport.RealSpeed
	下联设备ID 	netdeviceport.linknetdeviceid
	连接类型(0None1Net2Line3Exit4Srv) 	netdeviceport.linktype
	下联端口ID 	netdeviceport.linkPortId
	内网网络模块 	netdeviceId
	外网网络模块 	netdeviceId
	下联设备名称 	netdeviceport.linknetdevicename
	下联端口名称 	netdeviceport.linkPortName
	连接服务器ID 	netdeviceport.linkserverid
	连接服务器固资编号 	netdeviceport.linkServerAssetId
	关联出口ID 	netdeviceport.linkIdcExitId
	关联出口名称 	idc_exit.name
	关联专线ID 	netdeviceport.linkLineId
	关联专线名称 	special_line.name
	端口已分配IP 	(select group_concat(ipaddress) from ip_equipment where ip_equipment.iptype=4 and ip_equipment.active=1 and ip_equipment.typeid=netdeviceport.id)
	网络设备维护人 	net_device.operator
	机房网络模块负责人 	idc_network.principal
Schema : 设备IP
	ID 	ip_equipment.id
	IpId 	ip_equipment.ipid
Schema : ServerBusiness
	ID 	server_business.id
	SvrId 	server_business.serverid
	BsiId 	server_business.businessid
Schema : 日志信息
	操作时间 	t_log.logtime
	日志id 	t_log.id
	操作人 	t_log.operator
	审核人 	t_log.checker
	修改原因 	t_log.reason
	网络设备ID 	t_log.type_id
	设备固资 	t_device.assetid
	明细url 	t_baseset.infourl
	明细id 	t_log.type_id
	明细cname 	t_baseset.cname
	操作内容 	t_log.content
	log对象类型 	t_baseset.cname
Schema : 网络设备日志信息
	操作时间 	t_log.logtime
	日志id 	t_log.id
	操作人 	t_log.operator
	审核人 	t_log.checker
	修改原因 	t_log.reason
	网络设备ID 	t_log.type_id
	网络设备固资 	net_device.assetid
	操作方式 	t_log.operatorMode
	操作内容 	t_log.content
Schema : Idc日志信息
	操作时间 	FROM_UNIXTIME(log_idc.logtime)
	日志id 	log_idc.id
	操作人 	log_idc.operator
	修改原因 	log_idc.reason
	操作方式 	log_idc.OperateType
	操作内容 	log_idc.content
	转换操作内容 	log_idc.ConvertContent
Schema : Idc日志信息
	操作时间 	FROM_UNIXTIME(log_idc_$YY$.logtime)
	日志id 	log_idc_$YY$.id
	操作人 	log_idc_$YY$.operator
	修改原因 	log_idc_$YY$.reason
	操作方式 	log_idc_$YY$.OperateType
	操作内容 	log_idc_$YY$.content
	转换操作内容 	log_idc_$YY$.ConvertContent
	系统id 	log_idc_$YY$.source
	系统名称 	SysId
Schema : Business日志信息
	操作时间 	FROM_UNIXTIME(log_business.logtime)
	日志id 	log_business.id
	操作人 	log_business.operator
	修改原因 	log_business.reason
	操作方式 	log_business.OperateType
	操作内容 	log_business.content
	转换操作内容 	log_business.ConvertContent
Schema : Business日志信息
	操作时间 	FROM_UNIXTIME(log_business_$YY$.logtime)
	日志id 	log_business_$YY$.id
	操作人 	log_business_$YY$.operator
	修改原因 	log_business_$YY$.reason
	操作方式 	log_business_$YY$.OperateType
	操作内容 	log_business_$YY$.content
	转换操作内容 	log_business_$YY$.ConvertContent
	系统id 	log_business_$YY$.source
	系统名称 	SysId
Schema : 备件库日志信息
	操作时间 	FROM_UNIXTIME(log_spare_$YY$.logtime)
	日志id 	log_spare_$YY$.id
	操作人 	log_spare_$YY$.operator
	修改原因 	log_spare_$YY$.reason
	操作方式 	log_spare_$YY$.OperateType
	操作内容 	log_spare_$YY$.content
	转换操作内容 	log_spare_$YY$.ConvertContent
	系统id 	log_spare_$YY$.source
	系统名称 	SysId
Schema : 字典表日志信息
	操作时间 	FROM_UNIXTIME(log_dict.logtime)
	日志id 	log_dict.id
	操作人 	log_dict.operator
	修改原因 	log_dict.reason
	操作方式 	log_dict.OperateType
	操作内容 	log_dict.content
	转换操作内容 	log_dict.ConvertContent
	系统id 	log_dict.source
	系统名称 	api_system_info.SystemName
Schema : 字典表日志信息
	操作时间 	FROM_UNIXTIME(log_dict_$YY$.logtime)
	日志id 	log_dict_$YY$.id
	操作人 	log_dict_$YY$.operator
	修改原因 	log_dict_$YY$.reason
	操作方式 	log_dict_$YY$.OperateType
	操作内容 	log_dict_$YY$.content
	转换操作内容 	log_dict_$YY$.ConvertContent
	系统id 	log_dict_$YY$.source
	系统名称 	SysId
Schema : 服务器日志信息
	操作时间 	FROM_UNIXTIME(log_server.logtime)
	日志id 	log_server.id
	操作人 	log_server.operator
	修改原因 	log_server.reason
	操作方式 	log_server.OperateType
	操作内容 	log_server.content
	转换操作内容 	log_server.ConvertContent
	系统id 	log_server.source
	系统名称 	api_system_info.SystemName
Schema : 服务器日志信息
	操作时间 	FROM_UNIXTIME(log_server_$YY$.logtime)
	日志id 	log_server_$YY$.id
	操作人 	log_server_$YY$.operator
	修改原因 	log_server_$YY$.reason
	操作方式 	log_server_$YY$.OperateType
	操作内容 	log_server_$YY$.content
	转换操作内容 	log_server_$YY$.ConvertContent
	系统id 	log_server_$YY$.source
	系统名称 	SysId
Schema : 服务器历史日志信息
	操作时间 	FROM_UNIXTIME(cmdb_data_log.log_server.logtime)
	日志id 	cmdb_data_log.log_server.id
	操作人 	cmdb_data_log.log_server.operator
	修改原因 	cmdb_data_log.log_server.reason
	操作方式 	cmdb_data_log.log_server.OperateType
	操作内容 	cmdb_data_log.log_server.content
	转换操作内容 	cmdb_data_log.log_server.ConvertContent
	系统id 	cmdb_data_log.log_server.source
	系统名称 	api_system_info.SystemName
Schema : 存储日志信息
	日志id 	log_store.Id
	存储设备ID 	log_store.HostID
	操作人员 	log_store.Operator
	操作类型 	log_store.OperateType
	操作内容 	log_store.Content
	原因 	log_store.Reason
	操作时间 	FROM_UNIXTIME(log_store.LogTime)
Schema : 存储日志信息
	日志id 	log_store_$YY$.Id
	存储设备ID 	log_store_$YY$.HostID
	操作人员 	log_store_$YY$.Operator
	操作类型 	log_store_$YY$.OperateType
	操作内容 	log_store_$YY$.Content
	原因 	log_store_$YY$.Reason
	操作时间 	FROM_UNIXTIME(log_store_$YY$.LogTime)
	转换操作内容 	log_store_$YY$.ConvertContent
	系统id 	log_store_$YY$.source
	系统名称 	SysId
Schema : 网络设备日志信息
	操作时间 	FROM_UNIXTIME(log_netdevice.logtime)
	日志id 	log_netdevice.id
	操作人 	log_netdevice.operator
	修改原因 	log_netdevice.reason
	操作方式 	log_netdevice.OperateType
	操作内容 	log_netdevice.content
	转换操作内容 	log_netdevice.ConvertContent
	系统id 	log_netdevice.source
	系统名称 	api_system_info.SystemName
Schema : 网络设备日志信息
	操作时间 	FROM_UNIXTIME(log_netdevice_$YY$.logtime)
	日志id 	log_netdevice_$YY$.id
	操作人 	log_netdevice_$YY$.operator
	修改原因 	log_netdevice_$YY$.reason
	操作方式 	log_netdevice_$YY$.OperateType
	操作内容 	log_netdevice_$YY$.content
	转换操作内容 	log_netdevice_$YY$.ConvertContent
	系统id 	log_netdevice_$YY$.source
	系统名称 	SysId
Schema : 网络设备历史日志信息
	操作时间 	FROM_UNIXTIME(cmdb_data_log.log_netdevice.logtime)
	日志id 	cmdb_data_log.log_netdevice.id
	操作人 	cmdb_data_log.log_netdevice.operator
	修改原因 	cmdb_data_log.log_netdevice.reason
	操作方式 	cmdb_data_log.log_netdevice.OperateType
	操作内容 	cmdb_data_log.log_netdevice.content
	转换操作内容 	cmdb_data_log.log_netdevice.ConvertContent
	系统id 	cmdb_data_log.log_netdevice.source
	系统名称 	api_system_info.SystemName
Schema : 历史日志信息
	操作时间 	t_log.logtime
	日志id 	t_log.id
	操作人 	t_log.operator
	审核人 	t_log.checker
	修改原因 	t_log.reason
	网络设备ID 	t_log.type_id
	网络设备固资 	net_device.assetid
	操作方式 	t_log.operatorMode
	操作内容 	t_log.content
Schema : 机架信息
	机架ID 	t_equipment.id
	机架名称 	t_equipment.name
Schema : 智能网卡信息
	智能网卡ID 	t_smartcard.id
	主机名称 	t_smartcard.name
	智能网卡SN 	t_smartcard.sn
	智能网卡MAC 	t_smartcard.mac
	智能网卡创建时间 	t_smartcard.inputtime
	智能网卡关联服务器ID 	t_smartcard.server_id
	智能网卡选取BOM ID 	t_smartcard.bom_id
	智能网卡选取OS ID 	t_smartcard.os_id
	智能网卡说明 	t_smartcard.memo
	智能网卡启用日期 	t_smartcard.firstusetime
	智能网卡更新时间 	t_smartcard.LastUpdate
	固资编号 	t_device.assetid
	运维部门ID 	t_device.dep_id
	运维部门 	t_dep.name
	运维小组ID 	t_device.group_id
	运维小组 	t_groups.name
	业务部门 	group_concat(distinct bsi_dep.DeptName)
	业务部门ID 	bsi_dep.DeptId
	业务集合ID 	group_concat(distinct bs1.id SEPARATOR ';')
	业务集合 	group_concat(distinct bs1.chinesename)
	业务ID 	group_concat(distinct bs2.id SEPARATOR ';')
	业务 	group_concat(distinct bs2.chinesename)
	业务模块Uid 	group_concat(distinct bs3.Uid SEPARATOR ';')
	业务模块id 	group_concat(distinct bs3.id SEPARATOR ';')
	业务模块 	group_concat(distinct bs3.chinesename)
	负责人 	t_device.operator
	备份负责人 	t_device.bakoperator
	分级级别ID 	t_device.ClassifyLevel
	分级级别名称 	SvrClassifyLevel
	关联服务器上架时间 	t_device.firstUseTime
	关联服务器的SN 	t_device.sn
	关联服务器状态 	equipment_status.name
	IPv6 	SvrAssetId
	内网IP 	server_id
	外网IP 	server_id
Schema : 智能网卡OS信息
	智能网卡OSID 	t_smartcard_os.id
	OS名称 	t_smartcard_os.name
	OS Version 	t_smartcard_os.version
	OS描述 	t_smartcard_os.memo
Schema : 智能网卡BOM信息
	自增ID 	t_smartcard_bom.id
	智能网卡TXPN 	t_smartcard_bom.tx_pn
	BOM厂商 	t_smartcard_bom.brand
	BOM型号 	t_smartcard_bom.bom_version
	BOM原厂PN 	t_smartcard_bom.orign_pn
	BOM类型 	t_smartcard_bom.bom_type
	BOM设备ID 	t_smartcard_bom.device_id
	BOM子系统ID 	t_smartcard_bom.sub_system_id
	BOM硬件说明 	t_smartcard_bom.hard_desc
Schema : 智能网卡BOM信息与OS信息关系
	UUID 	t_smartcard_bom_os_relation.id
	BOM ID 	t_smartcard_bom_os_relation.bom_id
	OS ID 	t_smartcard_bom_os_relation.os_id
Schema : 智能网卡进程
	智能网卡ID 	t_smartcard_process.SmartCardId
	固资编号 	t_device.assetid
	服务器Ip 	ip_equipment.ipaddress
	进程ID 	t_smartcard_process.id
	进程名称 	t_smartcard_process.name
	进程状态 	t_smartcard_process.Status
	启动路径 	t_smartcard_process.startPath
	结束路径 	t_smartcard_process.endPath
	配置路径 	t_smartcard_process.configPath
	日志路径 	t_smartcard_process.logPath
	进程是否启用 	t_smartcard_process.isUsing
	启动数量 	t_smartcard_process.total
	负责人 	t_smartcard_process.operator
	进程说明 	t_smartcard_process.memo
	端口ID 	t_smartcard_port.id
	起始端口 	t_smartcard_port.Name
	终止端口 	t_smartcard_port.EndName
	协议 	t_smartcard_port.Protocol
	内外标志 	t_smartcard_port.Flag
	安全中心审核状态 	t_smartcard_port.state
	部门审核状态 	t_smartcard_port.DeptState
	IP限制 	t_smartcard_port.IpLimit
	绑定IP 	t_smartcard_port.IP
	业务ID 	t_smartcard_process.BusinessId
	业务名称 	BsiId
	端口是否启用 	t_smartcard_port.IsUsing
	端口说明 	t_smartcard_port.Memo
	服务名 	t_smartcard_port.ServiceName
	服务备注 	t_smartcard_port.ServiceMemo
	运维部门ID 	t_device.dep_id
	最后更新 	t_device.lastupdate
Schema : 智能网卡端口
	端口ID 	t_smartcard_port.id
	起始端口 	t_smartcard_port.Name
	终止端口 	t_smartcard_port.EndName
	协议 	t_smartcard_port.Protocol
	内外标志 	t_smartcard_port.Flag
	安全中心审核状态 	t_smartcard_port.State
	部门审核状态 	t_smartcard_port.DeptState
	IP限制 	t_smartcard_port.IpLimit
	绑定IP 	t_smartcard_port.IP
	端口是否启用 	t_smartcard_port.IsUsing
	端口说明 	t_smartcard_port.Memo
	服务名 	t_smartcard_port.ServiceName
	服务备注 	t_smartcard_port.ServiceMemo
	业务ID 	t_smartcard_port.BusinessId
	业务端口ID 	t_smartcard_port.BusinessportId
	端口号 	concat(t_smartcard_port.name,' - ',t_smartcard_port.EndName)
	业务名称 	BsiId
	进程ID 	t_smartcard_port.SmartCardProcessId
	进程名称 	t_smartcard_process.Name
	智能网卡ID 	t_smartcard_port.SmartCardId
Schema : 智能网卡端口审核
	ID 	t_smartcard_port_approve.id
	端口id 	t_smartcard_port_approve.PortId
	操作类型 	t_smartcard_port_approve.OperateMode
	申请人 	t_smartcard_port_approve.Operator
	申请时间 	t_smartcard_port_approve.RequestTime
	审批结果 	t_smartcard_port_approve.Pass
	审批时间 	t_smartcard_port_approve.CheckTime
	实际审批人 	t_smartcard_port_approve.RealChecker
	相关审批人 	t_smartcard_port_approve.PossibleChecker
	邮件备注 	t_smartcard_port_approve.EmailMemo
	操作SQL 	t_smartcard_port_approve.OperateSql
	操作记录 	t_smartcard_port_approve.LogMsg
	修改原因 	t_smartcard_port_approve.ModifyReason
	拒绝原因 	t_smartcard_port_approve.RefuseReason
	智能网卡ID 	t_smartcard_port.SmartCardId
	服务器固资 	t_device.Assetid
	服务器运维部门ID 	t_device.dep_id
	协议 	t_smartcard_port.Protocol
	端口号 	concat(t_smartcard_port.name,' - ',t_smartcard_port.EndName)
	IP 	t_smartcard_port.IP
	IP限制 	t_smartcard_port.IpLimit
	业务名称 	t_smartcard_port.ServiceName
	业务说明 	t_smartcard_port.ServiceMemo
	修改人 	t_smartcard_port.Mender
	修改时间 	t_smartcard_port.ModifyTime
	修改时间 	t_smartcard_port.CICode
	安全中心审核状态 	t_smartcard_port.State
	部门审核状态 	t_smartcard_port.DeptState
	Name 	t_smartcard_port.Name
	EndName 	t_smartcard_port.EndName
	Flag 	t_smartcard_port.Flag
	智能网卡进程ID 	t_smartcard_port.SmartCardProcessId
Schema : 网络设备 VLAN 信息
	Vlan id 	netdevicevlan.id
	Vlan值 	netdevicevlan.name
	IP 	ip_equipment.ipaddress
	备注 	netdevicevlan.memo
	设备ID 	netdevicevlan.netdeviceid
Schema : 网络设备部件
	网络设备部件ID 	net_device_parts.id
	部件固资号 	net_device_parts.assetid
	SN 	net_device_parts.sn
	厂商id 	net_device_parts.vendorId
	厂商名称 	net_device_parts.vendorName
	型号id 	net_device_parts.modelId
	型号名称 	net_device_parts.modelName
	状态id 	net_device_parts.statusId
	状态名称 	net_device_parts.statusName
	类型ID 	net_device_parts.typeId
	类型名称 	typeId
	维护人 	net_device_parts.operator
	入库时间 	net_device_parts.inputtime
	备注 	net_device_parts.memo
	网络设备ID 	net_device.id
	网络设备固资号 	net_device.assetid
	网络设备状态ID 	net_device.statu_id
	网络设备状态 	equipment_status.name
	机房管理单元ID 	t_idc.id
	机房管理单元 	t_idc.name
	物理机房ID 	idc_parent.id
	物理机房名称 	idc_parent.name
	由哪个系统新增而来 	net_device_parts.source
	是否经过ADS校验 	net_device_parts.isADSValid
	最后由哪个系统更新 	net_device_parts.lastUpdateSystem
	最后更新时间 	net_device_parts.lastUpdateTime
Schema : 部件厂商字典
	ID 	dic_parts_vendor.id
	名称 	dic_parts_vendor.name
Schema : 独立存储型号
	ModelId 	equipment_model.id
	名称 	equipment_model.name
Schema : 集中存储型号
	ModelId 	equipment_model.id
	名称 	equipment_model.name
Schema : 光纤交换机型号
	ModelId 	equipment_model.id
	名称 	equipment_model.name
Schema : 网络设备SCM型号
	scmModelId 	equipment_model.id
	SCM名称 	equipment_model.name
	标准型号ID 	equipment_model.ParentId
	标准型号名称 	netdevice_model.name
	配置项类型 	equipment_model.ConfigItemId
	备注 	equipment_model.Memo
Schema : 网络设备标准型号
	modelId 	netdevice_model.id
	名称 	netdevice_model.name
	编码 	netdevice_model.code
	厂商ID 	netdevice_model.Manufacturer
	厂商名称 	netdevice_manufacturer.cname
	端口模版 	netdevice_model.PortTemplate
	备注 	netdevice_model.Memo
Schema : 部件型号字典
	ID 	dic_parts_model.id
	名称 	dic_parts_model.name
Schema : 部件状态字典
	ID 	dic_parts_status.Id
	名称 	dic_parts_status.Name
Schema : 独立存储
	独立存储ID 	singlestore.id
	主机名称 	singlestore.ComputerName
	固资号 	singlestore.FixSerial
	维护人 	singlestore.vindicator
	备份维护人员 	singlestore.VindicatorBack
	响应级别 	singlestore.Levels
	状态 	singlestore.state
	IP地址 	ip_equipment.ipaddress
	型号ID 	singlestore.modelid
	型号名称 	equipment_model.name
	SN序列号 	singlestore.SNSerial
	运维部门ID 	t_dep.id
	运维部门名称 	t_dep.name
	小组ID 	t_groups.id
	小组名称 	t_groups.name
	机房ID 	idc_parent.id
	机房 	idc_parent.name
	机房管理单元ID 	equipment_position.idcid
	机房管理单元 	t_idc.name
	机架Id 	t_equipment.id
	所在机架 	t_equipment.name
	机架SP编号 	t_equipment.sn
	机位Id 	idc_position.id
	所在机位 	idc_position.code
	机房规划运营商 	t_idc.PlanOperationName
	电话 	t_idc.phone
	传真 	t_idc.fax
	备用传真 	t_idc.backupfax
	客服邮箱 	t_idc.SupportEmail
	授权方式 	t_idc.AuthoriseType
	机房运营商 	idc_parent.OperationName
	合同时间 	singlestore.Time0
	上架时间 	singlestore.Time1
	预算成本 	singlestore.IntendingMoney
	实际成本 	singlestore.FactMoney
	历史故障数 	singlestore.HistoryFacult
	硬件配置 	singlestore.Hardware
	RAID结构Id 	singlestore.Raid
	RAID结构 	Raid.name
	裸容量 	singlestore.Content
	备注 	singlestore.Comment
	设备号 	singlestore.DeviceId
	设备类型 	singlestore.DeviceClass
	服务器ID 	linkserver.ServerID
Schema : 集中存储
	存储ID 	collectstore.id
	名称 	collectstore.ComputerName
	固资号 	collectstore.FixSerial
	状态 	collectstore.State
	维护人 	collectstore.Vindicator
	备份维护人员 	collectstore.VindicatorBack
	响应级别 	collectstore.Levels
	IP地址 	ip_equipment.ipaddress
	型号ID 	collectstore.ModelId
	型号名称 	equipment_model.name
	SN序列号 	collectstore.SNSerial
	运维部门ID 	t_dep.id
	运维部门名称 	t_dep.name
	小组ID 	t_groups.id
	小组名称 	t_groups.name
	机房ID 	idc_parent.id
	机房 	idc_parent.name
	机房管理单元ID 	equipment_position.idcid
	机房管理单元 	t_idc.name
	机架Id 	t_equipment.id
	机架 	t_equipment.name
	机架SP编号 	t_equipment.sn
	机位Id 	idc_position.id
	所在机位 	idc_position.code
	机房规划运营商 	t_idc.PlanOperationName
	电话 	t_idc.phone
	传真 	t_idc.fax
	备用传真 	t_idc.backupfax
	客服邮箱 	t_idc.SupportEmail
	授权方式 	t_idc.AuthoriseType
	机房运营商 	idc_parent.OperationName
	合同时间 	collectstore.Time0
	上架时间 	collectstore.Time1
	预算成本 	collectstore.IntendingMoney
	实际成本 	collectstore.FactMoney
	历史故障数 	collectstore.HistoryFacult
	硬件配置 	collectstore.Hardware
	软件配置 	collectstore.Software
	RAID结构Id 	collectstore.Raid
	RAID结构 	Raid.name
	备注 	collectstore.Comment
	存储类型 	collectstore.Property
Schema : 光纤交换机
	交换机ID 	switchpc.id
	名称 	switchpc.ComputerName
	固资号 	switchpc.FixSerial
	SN 	switchpc.SNSerial
	状态 	switchpc.state
	维护人 	switchpc.Vindicator
	备份维护人 	switchpc.VindicatorBack
	响应级别 	switchpc.Levels
	型号ID 	switchpc.modelid
	型号名称 	equipment_model.name
	运维部门ID 	t_dep.id
	运维部门名称 	t_dep.name
	小组ID 	t_groups.id
	小组名称 	t_groups.name
	IP地址 	switchpc.ip
	机房ID 	idc_parent.id
	机房 	idc_parent.name
	机房管理单元ID 	equipment_position.idcid
	机房管理单元 	t_idc.name
	机架Id 	t_equipment.id
	机架 	t_equipment.name
	机位Id 	idc_position.id
	所在机位 	idc_position.code
	机房规划运营商 	t_idc.PlanOperationName
	电话 	t_idc.phone
	传真 	t_idc.fax
	备用传真 	t_idc.backupfax
	客服邮箱 	t_idc.SupportEmail
	授权方式 	t_idc.AuthoriseType
	机房运营商 	idc_parent.OperationName
	入库时间 	switchpc.Time0
	上架时间 	switchpc.Time1
	预算成本 	switchpc.IntendingMoney
	实际成本 	switchpc.FactMoney
	历史故障数 	switchpc.HistoryFacult
	硬件配置 	switchpc.Hardware
	软件版本 	switchpc.Software
	DomainID 	switchpc.DomainID
	端口数 	switchpc.Ports
	备注 	switchpc.Comment
Schema : 服务器数据日志
	操作时间 	t_log.logtime
	操作人 	t_log.operator
	审核人 	t_log.checker
	修改原因 	t_log.reason
	服务器ID 	t_log.operatorobj
	服务器固资 	t_device.assetid
	操作方式 	t_log.operatorMode
	操作内容 	t_log.content
Schema : 部门小组
	部门ID 	t_dep.id
	部门名称 	t_dep.name
	运维负责人 	t_dep.operator
	运维负责人id 	t_dep.OperatorId
	运维总监 	t_dep.principal
	部门说明 	t_dep.memo
	OA系统ID 	t_dep.oa_id
	OAFromID 	t_dep.oaIdFrom
	小组ID 	t_groups.id
	小组 	t_groups.name
	小组接口人 	t_groups.groupmaster
	OA部门ID 	GROUP_CONCAT(oa_dep_info.id)
	OA部门名称 	GROUP_CONCAT(oa_dep_info.name)
Schema : 产品表
	产品ID 	product.productid
	产品名称 	product.productname
	产品编码_废弃 	product.productname
	是否启用 	product.EnableFlag
	运维部门id 	product.OperateDeptId
	运维部门名称 	t_dep.name
	业务部门id 	bsi_dep.DeptId
	业务部门名称 	bsi_dep.DeptName
	产品集id 	product_set.productsetid
	产品集名称 	product_set.productsetname
	规划产品id 	product.PlanProductId
	规划产品名称 	bas_oms_plan_product.PlanProductName
	事业群id 	bsi_dep.BizGroupId
	事业群名称 	bas_oms_business_group.BizGroupName
Schema : 服务器软件
	服务器软件ID 	server_software.id
	服务器ID 	server_software.serverId
	服务器软件名称 	server_software.name
	服务器软件版本 	server_software.version
	软件类型 	server_software.type_id
	特性 	server_software.trait
	描述 	server_software.description
Schema : 进程端口
	服务器ID 	server_process.serverId
	固资编号 	t_device.assetid
	服务器Ip 	ip_equipment.ipaddress
	进程ID 	server_process.id
	进程名称 	server_process.name
	进程状态 	server_process.Status
	启动路径 	server_process.startPath
	结束路径 	server_process.endPath
	配置路径 	server_process.configPath
	日志路径 	server_process.logPath
	进程是否启用 	server_process.isUsing
	启动数量 	server_process.total
	负责人 	server_process.operator
	进程说明 	server_process.memo
	端口ID 	server_port.id
	起始端口 	server_port.Name
	终止端口 	server_port.EndName
	协议 	server_port.Protocol
	内外标志 	server_port.Flag
	安全中心审核状态 	server_port.state
	部门审核状态 	server_port.DeptState
	IP限制 	server_port.IpLimit
	绑定IP 	server_port.IP
	业务ID 	server_process.BusinessId
	业务名称 	BsiId
	端口是否启用 	server_port.IsUsing
	端口说明 	server_port.Memo
	服务名 	server_port.ServiceName
	服务备注 	server_port.ServiceMemo
	运维部门ID 	t_device.dep_id
	最后更新 	t_device.lastupdate
Schema : 服务器端口
	端口ID 	server_port.id
	起始端口 	server_port.Name
	终止端口 	server_port.EndName
	协议 	server_port.Protocol
	内外标志 	server_port.Flag
	安全中心审核状态 	server_port.State
	部门审核状态 	server_port.DeptState
	IP限制 	server_port.IpLimit
	绑定IP 	server_port.IP
	端口是否启用 	server_port.IsUsing
	端口说明 	server_port.Memo
	服务名 	server_port.ServiceName
	服务备注 	server_port.ServiceMemo
	业务ID 	server_port.BusinessId
	业务端口ID 	server_port.BusinessportId
	端口号 	concat(server_port.name,' - ',server_port.EndName)
	业务名称 	BsiId
	进程ID 	server_port.ServerProcessId
	进程名称 	server_process.Name
	服务器id 	server_port.serverId
Schema : 域名
	domainId 	domain_base.id
	域名名称 	domain_base.name
	负责人 	domain_base.proposer
	部门ID 	domain_base.deptid
	业务ID 	domain_base.businessid
	业务名称 	business.chinesename
	部门名称 	t_dep.name
	域名状态 	domain_base.state
	启用时间 	domain_base.startdate
	到期时间 	domain_base.enddate
	是否合作域名 	domain_base.ishz
	TTL 	domain_base.ttl
	域名备注 	domain_base.memo
	指向类型 	domain_target.type
	指向地址 	domain_target.address
	IDC名称 	domain_target.idcname
	指向说明 	domain_target.memo
Schema : 软件库
	软件ID 	software_library.id
	软件名称版本 	concat(software_library.name,' - ',software_library.version)
	软件名称 	software_library.name
	软件版本 	software_library.version
	操作系统类型 	software_library.type_id
	备注 	software_library.memo
Schema : IP资源
	IpeID 	ip_equipment.id
	网段名称 	ipaddress
	机房ID 	ipaddress
	运营商 	ipaddress
	用途 	ipaddress
	部门ID 	ipaddress
	内外网 	ipaddress
	网关 	ipaddress
	子网掩码 	ipaddress
	网段说明 	ipaddress
	IP地址ID 	ip_equipment.ipint
	IP地址 	ip_equipment.ipaddress
	IP状态 	'Active'
	IP地址说明 	''
	分配信息 	ipaddressId
	是否分配 	ipaddress
Schema : IP资源_废弃
	网段ID 	iprms_segment.id
	机房网络模块 	iprms_segment.IdcName
	网络模块负责人 	idc_network.principal
	网络模块IP负责人 	idc_network.ipoperator
	IP地址 	iprms_ip.address
	IP分配关系 	iprms_ip.id
Schema : 业务部门字典
	业务部门ID 	business_dept.DeptId
	业务部门名称 	business_dept.DeptName
	BusinessGroupId 	business_dept.BizGroupId
	Principal 	business_dept.Principal
	EnableFlag 	business_dept.EnableFlag
Schema : BusinessGroup
	BusinessGroupId 	business_group.BizGroupId
	BusinessGroupName 	business_group.BizGroupName
	EnableFlag 	business_group.EnableFlag
Schema : 规划产品字典表
	规划产品ID 	plan_product.PlanProductId
	规划产品名称 	plan_product.PlanProductName
	EnableFlag 	plan_product.EnableFlag
	Principal 	plan_product.Principal
	Type 	plan_product.Type
	DeptIds 	plan_product.DeptIds
	ProductSetIds 	plan_product.ProductSetIds
Schema : 产品集字典表
	产品集ID 	product_set.ProductSetId
	产品集名称 	product_set.ProductSetName
	EnableFlag 	product_set.EnableFlag
Schema : 产品字典表
	产品ID 	product.ProductId
	产品名称 	product.ProductName
	产品CODE 	product.ProductName
	是否有效 	product.EnableFlag
	业务部门ID 	bsi_dep.DeptId
	运维部门ID 	product.OperateDeptId
Schema : 父业务ID和名称字典
	父业务ID 	business.Id
	父业务UID 	business.Uid
Schema : 业务
	业务ID 	business.id
	业务审核状态 	business.IsVerify
	父业务ID 	b2.id
	业务UID 	business.Uid
	父业务UID 	business.ParentUid
	产品名称 	product.ProductName
	产品ID 	business.ProductId
	产品编码 	product.ProductName
	产品状态 	product.EnableFlag
	产品集 	product_set.ProductSetName
	产品集ID 	product_set.ProductSetId
	规划产品 	bas_oms_plan_product.PlanProductName
	规划产品ID 	bas_oms_plan_product.PlanProductId
	业务名称 	business.ChineseName
	初始运维部门 	t_dep.name
	初始运维部门ID 	product.OperateDeptId
	业务部门 	bsi_dept.DeptName
	业务部门ID 	bsi_dept.DeptId
	业务类型 	business.Depth
	业务描述 	business.Description
	业务状态 	business.State
	负责人 	business.operator
	启用标记 	business.flag
	TreeId 	business.TreeId
	产品业务部门OA ID 	business.ProductDeptOaId
	F6 	business.F6
	BsiLValue 	business.LValue
	BsiRValue 	business.RValue
	业务路径 	business.PathKV
	业务路径str 	business.Path
	业务L1 	business.L1
	业务L2 	business.L2
	业务L3 	business.L3
	敏感级别 	business.SensitiveLevel
Schema : 审核业务
	业务ID 	business.id
	业务审核状态 	business.IsVerify
	父业务ID 	b2.id
	业务UID 	business.Uid
	父业务UID 	business.ParentUid
	产品名称 	product.ProductName
	产品ID 	business.ProductId
	产品编码 	product.ProductName
	业务名称 	business.ChineseName
	初始运维部门 	t_dep.name
	所属业务部门ID_废弃 	business.DepartmentId
	所属业务 	BsiId
	业务类型 	business.Depth
	业务描述 	business.Description
	业务状态 	business.State
	负责人 	business.operator
	启用标记 	business.flag
	TreeId 	business.TreeId
	产品业务部门OA ID 	business.ProductDeptOaId
	F6 	business.F6
	BsiLValue 	business.LValue
	BsiRValue 	business.RValue
	业务路径 	business.PathKV
	业务路径str 	business.Path
	业务L1 	business.L1
	业务L2 	business.L2
	业务L3 	business.L3
Schema : 业务
	一级业务ID 	bs1.id
	一级业务UID 	bs1.uid
	一级业务 	bs1.ChineseName
	一级业务状态 	bs1_state.name
	产品编码 	product.ProductName
	产品名称 	product.ProductName
	二级业务ID 	bs2.id
	二级业务UID 	bs2.uid
	二级业务 	bs2.ChineseName
	三级业务ID 	bs3.id
	三级业务UID 	bs3.uid
	三级业务 	bs3.ChineseName
	初始运维部门 	t_dep.name
	初始运维部门ID 	product.OperateDeptId
	业务部门 	bsi_dept.DeptName
	业务部门ID 	bsi_dept.DeptId
	OAID 	t_dep.oa_id
Schema : 网络机房
	网络机房ID 	idc_network.id
	网络机房名 	idc_network.name
	网络机房类型 	idc_network.type
	机房管理单元 	t_idc.name
	机房管理单元ID 	t_idc.id
	网络机房出口ID 	idc_exit.id
	网络机房出口名称 	idc_exit.name
	网络机房专线 	netIdcId
	网络机房专线ID 	(select cast(GROUP_CONCAT(distinct  b.id ) as char(1000))  from netdeviceport a,special_line b, rs_netdevice_idcnetmodule c where a.linkLineId=b.id and (c.NetDeviceId=a.deviceid or c.NetDeviceId=a.linknetdeviceid) and idc_network.id =c.IdcNetModuleId)
	网络机房专线名称 	(select distinct  GROUP_CONCAT(distinct  b.name) from netdeviceport a,special_line b, rs_netdevice_idcnetmodule c where a.linkLineId=b.id and (c.NetDeviceId=a.deviceid or c.NetDeviceId=a.linknetdeviceid) and idc_network.id =c.IdcNetModuleId)
	所在城市 	idc_parent.cityname
	机房名称 	idc_parent.name
	机房ID 	idc_parent.id
Schema : 机房网络模块出口
	出口ID 	idc_exit.id
	出口名称 	idc_exit.name
	运营商ID 	idc_exit.operationId
	运营商名称 	idc_operation.name
	状态 	idc_exit.status
	启用时间 	idc_exit.StartTime
	物理链路 	idc_exit.LinkBandWidth
	商务可用带宽 	idc_exit_billing.AvaliableBW
	技术可用带宽 	idc_exit.TechnicBandWidth
	安全带宽比 	idc_exit.SafeBandPercent
	备注 	idc_exit.Memo
	出口对应设备ID 	netdeviceport.deviceid
	出口对应设备名称 	netdeviceport.netdevicename
	出口对应设备端口ID 	netdeviceport.id
	出口对应设备端口名称 	netdeviceport.PortName
	出口对应所有网络设备 	(select group_concat(a.netdevicename) from netdeviceport a where linktype=3 and a.LinkIdcExitId=idc_exit.id order by id)
	出口对应所有端口 	(select group_concat(a.portname) from netdeviceport a where linktype=3 and a.LinkIdcExitId=idc_exit.id order by id)
	机房网络模块名称 	idc_network.name
	机房网络模块ID 	idc_network.id
	机房网络模块负责人 	idc_network.principal
	商务计费出口ID 	idc_exit_billing.id
	商务计费出口名称 	idc_exit_billing.name
	计费模式ID 	idc_exit_billing.ChargingModel
	计费模式 	netIdcBillingModelId
	超额天数 	idc_exit_billing.OverLimitDays
	扩容带宽(G) 	idc_exit_billing.EnlargeBW
	商务经办人 	idc_exit_billing.Principal
	机房管理单元ID 	idc_exit.IdcId
	机房管理单元 	t_idc.Name
Schema : 商务计费出口
	计费出口id 	idc_exit_billing.id
	计费出口名称 	idc_exit_billing.name
	计费模式ID 	idc_exit_billing.ChargingModel
	计费模式 	BxtChargingModel
	超额天数 	idc_exit_billing.OverLimitDays
	扩容带宽(G) 	idc_exit_billing.EnlargeBW
	商务可用带宽 	idc_exit_billing.AvaliableBW
	商务经办人 	idc_exit_billing.Principal
	备注 	idc_exit_billing.memo
	机房出口id 	idc_exit.id
	机房出口名称 	idc_exit.name
	机房出口ids 	(select group_concat(distinct a.id) from idc_exit a where a.billingid=idc_exit_billing.id group by a.billingid)
	机房出口名称s 	(select group_concat(distinct a.name SEPARATOR ';') from idc_exit a where a.billingid=idc_exit_billing.id group by a.billingid)
Schema : 设备状态
	状态ID 	equipment_status.id
	状态名称 	equipment_status.name
	配置项类型 	equipment_status.ConfigItemId
	服务器状态的父状态ID 	es2.id
	服务器状态的父状态 	es2.name
	对应的网络设备部件状态ID 	equipment_status.PartsStatusId
	对应的网络设备部件状态名称 	dic_parts_status.Name
	对应的网络设备部件状态名称 	equipment_status.PartsStatusName
	是否运营中 	equipment_status.InUse
	使用场景 	equipment_status.foruse
	备注 	equipment_status.Memo
Schema : 设备状态
	状态名称 	equipment_status.name
Schema : 服务器状态名称
	状态名称 	equipment_status.name
	状态ID 	equipment_status.id
Schema : 设备类型
	类型ID 	equipment_type.Id
	类型名称 	equipment_type.Name
	配置项类型 	equipment_type.ConfigItemId
	备注 	equipment_type.Memo
Schema : Raid结构
	RaidID 	Raid.id
	Raid名称 	Raid.name
Schema : 服务器BIOS固件
	BIOS固件设置ID 	bios_firmware.id
	BIOS固件设置名称 	bios_firmware.name
	BIOS固件设置说明 	bios_firmware.description
Schema : Important
	重要级别ID 	importantlevel.id
	重要级别名称 	importantlevel.name
Schema : serverModel
	服务器型号ID 	t_devicetype.id
	服务器型号 	t_devicetype.name
	备注 	t_devicetype.memo
	配置项类型 	t_devicetype.basesetid
	服务器类型 	t_devicetype.servertypeid
	高度 	t_devicetype.height
Schema : 设备类型
	设备类型ID 	server_deviceclass.Id
	SCM设备类型 	server_deviceclass.ScmDeviceClass
	标准设备类型 	server_deviceclass.Name
	备注 	server_deviceclass.Memo
Schema : serverAlarmlevel
	响应级别ID 	alarmlevel.id
	响应级别 	alarmlevel.name
Schema : 设备类型
	设备类型(只有名称) 	t_device.deviceclass
Schema : 腾讯机型代号
	腾讯机型代号 	server_typeclass.deviceclassname
Schema : 服务器硬件2
	服务器ID 	t_device.id
	服务器固资号 	t_device.assetid
	CPUCicode 	server_cpu.cicode
	CPU型号 	server_cpu.model
	CPU厂商 	server_cpu.vendor
	CPU主频 	server_cpu.mainfrequence
	CPUSN 	server_cpu.sn
	Cpu Id 	server_cpu.cpuid
	内存CICode 	server_memory.cicode
	内存容量 	server_memory.capacity
	内存型号 	server_memory.model
	内存厂商 	server_memory.vendor
	内存SN 	server_memory.sn
	硬盘CICode 	server_harddisk.cicode
	硬盘容量 	server_harddisk.capacity
	硬盘型号 	server_harddisk.model
	硬盘厂商 	server_harddisk.vendor
	硬盘转速 	server_harddisk.RotationSpeed
	硬盘SN 	server_harddisk.sn
	raid卡CICode 	server_raidcard.cicode
	raid 	server_raidcard.structure
	raid卡型号 	server_raidcard.model
	raid卡厂商 	server_raidcard.vendor
	raid卡SN 	server_raidcard.sn
	网卡CICode 	server_networkcard.cicode
	网卡速度 	server_networkcard.speed
	网卡型号 	server_networkcard.model
	网卡厂商 	server_networkcard.vendor
	SN 	server_networkcard.sn
	MAC地址 	server_networkcard.macaddress
Schema : 服务器硬件
	服务器ID 	t_device.id
	服务器固资号 	t_device.assetid
	服务器CPU 	serverId
	服务器内存 	serverId
	服务器硬盘 	serverId
	服务器网卡 	serverId
Schema : 服务器硬盘
	ID 	server_hard_disk.id
	SN 	server_hard_disk.SN
	Vendor 	server_hard_disk.Vendor
	Model 	server_hard_disk.Model
	Type 	server_hard_disk.Type
	Capacity 	server_hard_disk.Capacity
	FirmwareVersion 	server_hard_disk.FirmwareVersion
	RaidType 	server_hard_disk.RaidType
	Symbol 	server_hard_disk.Symbol
	Slot 	server_hard_disk.Slot
	Memo 	server_hard_disk.Memo
	硬盘接口类型 	server_hard_disk.InterfaceType
	硬盘介质 	server_hard_disk.Material
	硬盘转速 	server_hard_disk.Rpm
	硬盘尺寸 	server_hard_disk.Size
	服务器ID 	server_hard_disk.ServerId
	固资编号 	server.AssetId
Schema : 服务器网卡
	ID 	server_hard_nic.Id
	厂商 	server_hard_nic.Vendor
	型号 	server_hard_nic.Model
	状态 0down 1up 	server_hard_nic.Status
	Mac地址 	server_hard_nic.MacAddress
	固件版本 	server_hard_nic.FirmwareVersion
	驱动 	server_hard_nic.Driver
	驱动版本 	server_hard_nic.DriverVersion
	最大速度 	server_hard_nic.MaxSpeed
	当前设置速度 	server_hard_nic.NetSpeed
	备注 	server_hard_nic.Memo
	接口类型 	server_hard_nic.BoardType
	网口名称 	server_hard_nic.Name
	服务器ID 	server_hard_nic.ServerId
	固资编号 	server.AssetId
Schema : 服务器CPU
	ID 	server_hard_cpu.Id
	SN 	server_hard_cpu.SN
	厂商 	server_hard_cpu.Vendor
	型号 	server_hard_cpu.Model
	CPU ID 	server_hard_cpu.CpuId
	PhysicalId 	server_hard_cpu.PhysicalId
	主频 	server_hard_cpu.Speed
	是否支持虚拟化 0 1 	server_hard_cpu.Vmx
	核心数 	server_hard_cpu.CoreNumber
	逻辑核心数 	server_hard_cpu.LogicalCoreCount
	缓存大小 	server_hard_cpu.CacheSize
	备注 	server_hard_cpu.Memo
	服务器ID 	server_hard_cpu.ServerId
	固资编号 	server.AssetId
Schema : 服务器内存
	ID 	server_hard_memory.Id
	SN 	server_hard_memory.SN
	厂商 	server_hard_memory.Vendor
	型号 	server_hard_memory.Model
	容量 	server_hard_memory.Capacity
	服务器ID 	server_hard_memory.ServerId
	槽位 	server_hard_memory.Slot
	备注 	server_hard_memory.Memo
	内存频率 	server_hard_memory.Hz
	内存类型 	server_hard_memory.Type
	服务器ID 	server_hard_memory.ServerId
	固资编号 	server.AssetId
Schema : 服务器Raid
	ID 	server_hard_raid.Id
	SN 	server_hard_raid.SN
	厂商 	server_hard_raid.Vendor
	型号 	server_hard_raid.Model
	固件版本 	server_hard_raid.FirmwareVersion
	缓存大小 	server_hard_raid.CacheSize
	电池状态 	server_hard_raid.BatteryStatus
	当前raid结构 	server_hard_raid.RaidCur
	支持raid结构 	server_hard_raid.Raids
	备注 	server_hard_raid.Memo
	电池型号 	server_hard_raid.BatteryModel
	电池FW 	server_hard_raid.BatteryFw
	服务器ID 	server_hard_raid.ServerId
	固资编号 	server.AssetId
Schema : 服务器BaseBoard
	ID 	server_hard_baseboard.Id
	SN 	server_hard_baseboard.SN
	厂商 	server_hard_baseboard.Vendor
	型号 	server_hard_baseboard.Model
	固件版本 	server_hard_baseboard.FirmwareVersion
	备注 	server_hard_baseboard.Memo
	服务器ID 	server_hard_baseboard.ServerId
	固资编号 	server.AssetId
Schema : 服务器BIOS
	ID 	server_hard_bios.Id
	厂商 	server_hard_bios.Vendor
	发布日期 	server_hard_bios.ReleaseDate
	固件版本 	server_hard_bios.FirmwareVersion
	备注 	server_hard_bios.Memo
	服务器ID 	server_hard_bios.ServerId
	固资编号 	server.AssetId
Schema : 服务器IPMI
	ID 	server_hard_ipmi.Id
	固件版本 	server_hard_ipmi.Version
	备注 	server_hard_ipmi.Memo
	服务器ID 	server_hard_ipmi.ServerId
	固资编号 	server.AssetId
Schema : 服务器BMC
	ID 	server_hard_bmc.Id
	厂商 	server_hard_bmc.Vendor
	固件版本 	server_hard_bmc.FirmwareVersion
	备注 	server_hard_bmc.Memo
	服务器ID 	server_hard_bmc.ServerId
	固资编号 	server.AssetId
Schema : 区域
	区域ID 	area.id
	区域名称 	area.name
	别名 	area.aka
	备注 	area.memo
Schema : 设备厂商
	ID 	netdevice_manufacturer.id
	名称 	netdevice_manufacturer.cname
	备注 	netdevice_manufacturer.remark
Schema : 机房运营商
	运营商ID 	dic_idcparentoperation.id
	运营商 	dic_idcparentoperation.name
Schema : 网络运营商
	网络运营商ID 	idc_operation.id
	网络运营商名称 	idc_operation.name
Schema : 专线用途
	专线用途ID 	special_line_uses.id
	专线用途名称 	special_line_uses.name
Schema : 城市
	城市ID 	dic_idcparentcity.id
	城市名称 	dic_idcparentcity.name
Schema : IP网段
	IP段ID 	ip_segment.id
	IP段名称 	ip_segment.SegmentName
	逻辑区域ID 	ip_segment.SafeRegionId
Schema : TopoRelation
	设备用途和拓扑id 	netdevice_uses_dictionary.id
	设备用途 	netdevice_uses_dictionary.Uses
	拓扑层级 	netdevice_uses_dictionary.TopoLevel
	备注 	netdevice_uses_dictionary.Memo
Schema : 小组信息
	小组信息id 	t_groups.id
	小组名称 	t_groups.name
	所属部门ID 	t_groups.dep_id
	所属部门 	t_dep.name
	小组负责人 	t_groups.groupmaster
	备注 	t_groups.memo
Schema : 所属小组名称
	小组名称 	t_groups.name
Schema : 服务器设备型号和类型对应关系
	ID 	server_typeclass.Id
	腾讯机型版本号 	server_typeclass.Version
	供应商型号ID 	server_typeclass.DeviceTypeId
	供应商机型代号 	t_devicetype.name
	服务器类型ID 	t_devicetype.servertypeid
	腾讯机型代号 	server_typeclass.DeviceClassName
	处理器型号 	server_typeclass.CpuModel
	处理器数量 	server_typeclass.CpuNumber
	单处理器核心数量 	server_typeclass.CpuCore
	单内存容量(GB) 	server_typeclass.MemoryVolume
	内存条数 	server_typeclass.MemoryNumber
	硬盘接口类型 	server_typeclass.DiskType
	硬盘规格(GB) 	server_typeclass.DiskVolume
	硬盘数量 	server_typeclass.DiskNumber
	硬盘固件版本 	server_typeclass.DiskFirmware
	硬盘接口类型2 	server_typeclass.DiskType2
	硬盘规格2(GB) 	server_typeclass.DiskVolume2
	硬盘数量2 	server_typeclass.DiskNumber2
	硬盘固件版本2 	server_typeclass.DiskFirmware2
	阵列卡品牌 	server_typeclass.RaidBrand
	阵列卡/HBA卡规格 	server_typeclass.RaidModel
	阵列卡/HBA卡固件 	server_typeclass.RaidFirmware
	阵列卡电池型号 	server_typeclass.RaidPowerModel
	支持阵列类型 	RaidId
	HBA卡品牌 	server_typeclass.HbaBrand
	HBA卡规格 	server_typeclass.HbaModel
	HBA卡固件 	server_typeclass.HbaFirmware
	HBA卡数量 	server_typeclass.HbaNumber
	HBA卡形态 	server_typeclass.HbaShape
	网卡品牌 	server_typeclass.NicBrand
	网卡速率 	server_typeclass.NicSpeed
	网卡型号 	server_typeclass.NicModel
	网卡固件 	server_typeclass.NicFirmware
	电源规格 	server_typeclass.PowerSize
	电源数量 	server_typeclass.PowerNumber
	电源类型 	server_typeclass.PowerType
	BIOS版本 	server_typeclass.BiosVersion
	BMC版本 	server_typeclass.BmcVersion
	带外管理卡型号 	server_typeclass.OutBandInfo
	SideBand支持 	server_typeclass.SideBandSupport
	机箱结构 	server_typeclass.BoxType
	机箱宽度(cm) 	server_typeclass.Width
	机箱深度(cm) 	server_typeclass.Depth
	整机重量(kg) 	server_typeclass.Weight
	功耗(W) 	server_typeclass.Electricity
	综合性能 	server_typeclass.Score
	mini OS 性能 	server_typeclass.MiniOsScore
	备注 	server_typeclass.Memo
	厂商 	server_typeclass.Brand
	服务器制造商 	server_typeclass.Producer
	产品生命周期状态 	server_typeclass.LifeCycle
	状态 	server_typeclass.Status
	设备描述 	server_typeclass.Description
	内存属性 	server_typeclass.MemoryMemo
	硬盘型号 	server_typeclass.DiskModel
	硬盘属性 	server_typeclass.DiskMemo
	硬盘型号2 	server_typeclass.DiskModel2
	硬盘属性2 	server_typeclass.DiskMemo2
	阵列卡缓存大小(MB) 	server_typeclass.RaidCache
	阵列卡缓存数据保护方式 	server_typeclass.RaidProtection
	网卡接口类型 	server_typeclass.NicType
	带外管理卡属性 	server_typeclass.OutBandMemo
	阵列卡/HBA数量 	server_typeclass.RaidNumber
	SSD1型号 	server_typeclass.SsdModel
	SSD1规格 	server_typeclass.SsdType
	SSD1接口类型 	server_typeclass.SsdPortType
	ssd1数量 	server_typeclass.SsdNumber
	SSD1固件 	server_typeclass.SsdFirmware
	SSD1属性 	server_typeclass.SsdMemo
	SSD1尺寸 	server_typeclass.SsdSize
	SSD2型号 	server_typeclass.SsdModel2
	SSD2规格 	server_typeclass.SsdType2
	SSD2接口类型 	server_typeclass.SsdPortType2
	ssd2数量 	server_typeclass.SsdNumber2
	SSD2固件 	server_typeclass.SsdFirmware2
	SSD2属性 	server_typeclass.SsdMemo2
	SSD2尺寸 	server_typeclass.SsdSize2
	GPU型号 	server_typeclass.GpuModel
	GPU数量 	server_typeclass.GpuNumber
	gpu固件 	server_typeclass.GpuFirmware
	gpu属性 	server_typeclass.GpuMemo
	带内网口数量 	server_typeclass.InBandNumber
	带内网卡属性 	server_typeclass.InBandMemo
	电源属性 	server_typeclass.PowerMemo
	不选部件 	server_typeclass.UnselectedParts
	电源线数量 	server_typeclass.PowerCordNumber
	电源线规格 	server_typeclass.PowerCordType
	导轨数量 	server_typeclass.GuideNumber
	导轨规格 	server_typeclass.GuideType
	风扇数量 	server_typeclass.FanNumber
	风扇型号 	server_typeclass.FanModel
	风扇属性 	server_typeclass.FanMemo
	标准型号 	server_typeclass.StandModel
	支持阵列类型(新) 	RaidId
	支持阵列类型ID(新) 	server_typeclass.RaidTypeId
	机型高度 	t_devicetype.height
	机型对应服务器类型ID 	t_devicetype.ServerTypeId
	硬盘尺寸 	server_typeclass.DiskSize
	硬盘尺寸2 	server_typeclass.DiskSize2
	阵列卡/HBA卡形态 	server_typeclass.RaidShape
	HBA卡品牌 	server_typeclass.HbaBrand
	HBA卡规格 	server_typeclass.HbaModel
	HBA卡数量 	server_typeclass.HbaNumber
	HBA卡固件 	server_typeclass.HbaFirmware
	HBA卡形态 	server_typeclass.HbaShape
	StrChildCount 	server_typeclass.ChildCount
	服务器BIOS配置” 	server_typeclass.BiosType
	盘序 	server_typeclass.DiskSequence
Schema : 待审核服务器设备型号和类型对应关系
	ID 	server_typeclass.Id
	腾讯机型版本号 	server_typeclass.Version
	供应商型号ID 	server_typeclass.DeviceTypeId
	供应商机型代号 	t_devicetype.name
	腾讯机型代号 	server_typeclass.DeviceClassName
	处理器型号 	server_typeclass.CpuModel
	处理器数量 	server_typeclass.CpuNumber
	单处理器核心数量 	server_typeclass.CpuCore
	单内存容量(GB) 	server_typeclass.MemoryVolume
	内存条数 	server_typeclass.MemoryNumber
	硬盘接口类型 	server_typeclass.DiskType
	硬盘规格(GB) 	server_typeclass.DiskVolume
	硬盘数量 	server_typeclass.DiskNumber
	硬盘固件版本 	server_typeclass.DiskFirmware
	硬盘接口类型2 	server_typeclass.DiskType2
	硬盘规格2(GB) 	server_typeclass.DiskVolume2
	硬盘数量2 	server_typeclass.DiskNumber2
	硬盘固件版本2 	server_typeclass.DiskFirmware2
	阵列卡品牌 	server_typeclass.RaidBrand
	阵列卡/HBA卡规格 	server_typeclass.RaidModel
	阵列卡/HBA卡固件 	server_typeclass.RaidFirmware
	阵列卡电池型号 	server_typeclass.RaidPowerModel
	支持阵列类型(旧) 	server_typeclass.RaidType
	支持阵列类型(新) 	RaidId
	支持阵列类型ID(新) 	server_typeclass.RaidTypeId
	HBA卡品牌 	server_typeclass.HbaBrand
	HBA卡规格 	server_typeclass.HbaModel
	HBA卡固件 	server_typeclass.HbaFirmware
	HBA卡数量 	server_typeclass.HbaNumber
	HBA卡形态 	server_typeclass.HbaShape
	网卡品牌 	server_typeclass.NicBrand
	网卡速率 	server_typeclass.NicSpeed
	网卡型号 	server_typeclass.NicModel
	网卡固件 	server_typeclass.NicFirmware
	电源规格 	server_typeclass.PowerSize
	电源数量 	server_typeclass.PowerNumber
	电源类型 	server_typeclass.PowerType
	BIOS版本 	server_typeclass.BiosVersion
	BMC版本 	server_typeclass.BmcVersion
	带外管理卡型号 	server_typeclass.OutBandInfo
	SideBand支持 	server_typeclass.SideBandSupport
	机箱结构 	server_typeclass.BoxType
	机箱宽度(cm) 	server_typeclass.Width
	机箱深度(cm) 	server_typeclass.Depth
	整机重量(kg) 	server_typeclass.Weight
	功耗(W) 	server_typeclass.Electricity
	综合性能 	server_typeclass.Score
	mini OS 性能 	server_typeclass.MiniOsScore
	备注 	server_typeclass.Memo
	厂商 	server_typeclass.Brand
	服务器制造商 	server_typeclass.Producer
	产品生命周期状态 	server_typeclass.LifeCycle
	状态 	server_typeclass.Status
	设备描述 	server_typeclass.Description
	内存属性 	server_typeclass.MemoryMemo
	硬盘型号 	server_typeclass.DiskModel
	硬盘属性 	server_typeclass.DiskMemo
	硬盘型号2 	server_typeclass.DiskModel2
	硬盘属性2 	server_typeclass.DiskMemo2
	阵列卡缓存大小(MB) 	server_typeclass.RaidCache
	阵列卡缓存数据保护方式 	server_typeclass.RaidProtection
	网卡接口类型 	server_typeclass.NicType
	带外管理卡属性 	server_typeclass.OutBandMemo
	阵列卡/HBA数量 	server_typeclass.RaidNumber
	SSD1型号 	server_typeclass.SsdModel
	SSD1规格 	server_typeclass.SsdType
	SSD1接口类型 	server_typeclass.SsdPortType
	ssd1数量 	server_typeclass.SsdNumber
	SSD1固件 	server_typeclass.SsdFirmware
	SSD1属性 	server_typeclass.SsdMemo
	SSD1尺寸 	server_typeclass.SsdSize
	SSD2型号 	server_typeclass.SsdModel2
	SSD2规格 	server_typeclass.SsdType2
	SSD2接口类型 	server_typeclass.SsdPortType2
	ssd2数量 	server_typeclass.SsdNumber2
	SSD2固件 	server_typeclass.SsdFirmware2
	SSD2属性 	server_typeclass.SsdMemo2
	SSD2尺寸 	server_typeclass.SsdSize2
	GPU型号 	server_typeclass.GpuModel
	GPU数量 	server_typeclass.GpuNumber
	gpu固件 	server_typeclass.GpuFirmware
	gpu属性 	server_typeclass.GpuMemo
	带内网口数量 	server_typeclass.InBandNumber
	带内网卡属性 	server_typeclass.InBandMemo
	电源属性 	server_typeclass.PowerMemo
	不选部件 	server_typeclass.UnselectedParts
	电源线数量 	server_typeclass.PowerCordNumber
	电源线规格 	server_typeclass.PowerCordType
	导轨数量 	server_typeclass.GuideNumber
	导轨规格 	server_typeclass.GuideType
	风扇数量 	server_typeclass.FanNumber
	风扇型号 	server_typeclass.FanModel
	风扇属性 	server_typeclass.FanMemo
	审核状态 	server_typeclass.ApproveStatus
	审核类型 	server_typeclass.ApproveType
	修改人 	server_typeclass.Editor
	标准型号 	server_typeclass.StandModel
	硬盘尺寸 	server_typeclass.DiskSize
	硬盘尺寸2 	server_typeclass.DiskSize2
	阵列卡/HBA卡形态 	server_typeclass.RaidShape
	操作原因 	server_typeclass.reason
	StrChildCount 	server_typeclass.ChildCount
	服务器BIOS配置 	server_typeclass.BiosType
	盘序 	server_typeclass.DiskSequence
Schema : IDC逻辑区域
	逻辑区域ID 	idc_safedomain.id
	逻辑区域名称 	idc_safedomain.name
	机房Id 	idc_safedomain.idcparentid
	机房名称 	idc_parent.name
	外网网络结构 	idc_safedomain.OuterNetworkStructureName
	外网网络结构Id 	idc_safedomain.OuterNetworkStructureId
	内网网络结构ID 	idc_safedomain.InnerNetworkStructureId
	内网网络结构 	idc_safedomain.InnerNetworkStructureName
	网络结构版本 	idc_safedomain.StructureVersion
	网络结构版本名 	dic_net_version.name
	规划业务类型 	idc_safedomain.PlanBusinessTypeName
	规划业务类型Id 	idc_safedomain.PlanBusinessTypeId
	备注 	idc_safedomain.memo
	外网网速 	idc_safedomain.OuterNetSpeed
	内网网速 	idc_safedomain.InnerNetSpeed
	Module ID 	idc_safedomain.ModuleId
	Module名称 	module.name
	Campus ID 	module.subzoneid
	Campus名称 	subzone.name
	Zone ID 	subzone.zoneid
	Zone 名称 	zone.name
	区域 ID 	zone.AreaId
	区域 名称 	area.name
Schema : 网络结构
	网络结构ID 	dic_networkstructure.id
	网络结构 	dic_networkstructure.name
	是否为内网 	dic_networkstructure.IsInner
	备注 	dic_networkstructure.memo
Schema : 存放机架
	机架名称 	t_equipment.name
Schema : IDC业务类型
	业务类型ID 	dic_businesstype.id
	业务类型 	dic_businesstype.name
	备注 	dic_businesstype.memo
Schema : 服务字典表
	服务ID 	dic_service.id
	服务名 	dic_service.name
Schema : 服务字典表
	服务ID 	dic_service.id
	服务名 	dic_service.name
Schema : IDC功能类型
	功能类型ID 	p.id
	功能类型 	p.name
Schema : 业务状态
	状态ID 	dictionary.gid
	状态 	dictionary.name
Schema : 机房网络模块
	模块ID 	idc_network.id
	模块 	idc_network.name
	类型 	idc_network.type
	负责人 	idc_network.Principal
	一级机房ID 	idc_network.idcparentid
	一级机房名称 	idc_parent.name
	核心房间号 	idc_network.RoomNum
	网络版本 	idc_network.NetVersion
	备注 	idc_network.memo
	IdcnStatus 	idc_network.Status
Schema : ADS服务器IP异常数据
	ADS-设备ID 	ads_field_difference.machineid
	ADS-固资号 	ads_server_info.assetid
	ADS-SN 	ads_server_info.SN
	ADS-操作系统 	adsMachineId
	ADS-型号 	ads_server_info.Model
	ADS-IP 	ads_server_info.IpAddressFull
	ErrorIP 	adsMachineId
	配置-设备编号 	t_device.deviceid
	配置-固资号 	t_device.assetid
	配置-SN 	t_device.sn
	配置-操作系统 	adsMachineId
	配置-IP 	adsMachineId
	配置-型号 	t_devicetype.name
	维护人 	t_device.operator
	服务器运维部门ID 	t_device.dep_id
	服务器运维部门 	t_dep.name
	小组ID 	t_device.group_id
	小组 	t_groups.name
	存放机架 	t_equipment.name
	机架ID 	equipment_position.shelfid
	机房ID 	equipment_position.IdcId
	机房 	t_idc.name
Schema : ADS服务器信息
	ADS-固资号 	ads_server_info.PackageId
	ADS-固资号 	ads_server_info.assetid
	ADS-SN 	ads_server_info.SN
	ADS-型号 	ads_server_info.Model
	ADS-内网IP 	ads_server_info.InnerIPAddress
	ADS-外网IP 	ads_server_info.OuterIPAddress
	ADS-OS 	ads_server_info.OS
	ADS-OSKernelVersion 	ads_server_info.OSKernelVersion
	ADS-操作系统Full 	adsMachineId
	ADS-硬盘逻辑总容量 	ads_server_info.HdLogicSize
	ADS-硬盘物理总容量 	ads_server_info.HdPhysicalSize
	ADS-内存逻辑总量 	ads_server_info.MemoryLogicSize
	ADS-内存物理总量 	ads_server_info.MemoryPhysicalSize
	ADS-采集包信息 	ads_server_info.PackageXml
	ADSJsonContent 	adsServerPackageXml
	ADS-最后更新时间 	ads_server_info.LastUpdateTime
Schema : 机房出口网络连接查询
	networkportId 	netdeviceport.id
	起始设备类型 	5
	起始设备ID 	netdeviceport.deviceid
	起始设备名称 	netdeviceport.netdevicename
	起始端口ID 	netdeviceport.id
	起始端口名称 	netdeviceport.portName
Schema : 网络连接查询
	networkportId 	netdeviceport.id
	连接类型 	netdeviceport.linktype
	起始网络设备ID 	netdeviceport.deviceid
	起始网络设备名称 	netdeviceport.netdevicename
	起始端口ID 	netdeviceport.id
	起始端口名称 	netdeviceport.portName
	终止网络设备ID 	netdeviceport.LinkNetDeviceId
	终止网络设备名称 	netdeviceport.LinkNetDeviceName
	终止端口ID 	netdeviceport.LinkPortId
	终止端口名称 	netdeviceport.LinkPortName
	终止服务器ID 	netdeviceport.LinkServerId
	终止服务器固资号 	netdeviceport.LinkServerAssetId
	终止服务器IP 	(select group_concat(ip_equipment.ipaddress) from ip_equipment where ip_equipment.equipmentid=netdeviceport.linkserverid and ip_equipment.configitemid=2  and ip_equipment.active=1  group by ip_equipment.equipmentid)
	网络专线ID 	netdeviceport.linkLineId
	网络专线名称 	special_line.name
	机房网络出口ID 	netdeviceport.LinkIdcExitId
	机房网络出口名称 	idc_exit.name
	网络设备维护人 	net_device.operator
	机房网络模块负责人 	idc_network.principal
Schema : 服务器修改审核
	ID 	server_approve.id
	服务器id 	server_approve.ServerId
	申请人 	server_approve.Operator
	申请时间 	server_approve.RequestTime
	审批结果 	server_approve.Pass
	审批时间 	server_approve.CheckTime
	实际审批人 	server_approve.RealChecker
	邮件备注 	server_approve.EmailMemo
	操作SQL 	server_approve.OperateSql
	操作字段 	server_approve.ModifyField
	操作记录 	server_approve.LogMsg
	修改原因 	server_approve.ModifyReason
	拒绝原因 	server_approve.RefuseReason
	服务器固资 	t_device.Assetid
	服务器运维部门ID 	t_device.dep_id
	服务器运维部门名称 	t_dep.name
	相关审核人 	t_dep.Operator
Schema : 服务器端口审核
	ID 	server_port_approve.id
	端口id 	server_port_approve.PortId
	操作类型 	server_port_approve.OperateMode
	申请人 	server_port_approve.Operator
	申请时间 	server_port_approve.RequestTime
	审批结果 	server_port_approve.Pass
	审批时间 	server_port_approve.CheckTime
	实际审批人 	server_port_approve.RealChecker
	相关审批人 	server_port_approve.PossibleChecker
	邮件备注 	server_port_approve.EmailMemo
	操作SQL 	server_port_approve.OperateSql
	操作记录 	server_port_approve.LogMsg
	修改原因 	server_port_approve.ModifyReason
	拒绝原因 	server_port_approve.RefuseReason
	服务器ID 	server_port.serverid
	服务器固资 	t_device.Assetid
	服务器运维部门ID 	t_device.dep_id
	协议 	server_port.Protocol
	端口号 	concat(server_port.name,' - ',server_port.EndName)
	IP 	server_port.IP
	IP限制 	server_port.IpLimit
	业务名称 	server_port.ServiceName
	业务说明 	server_port.ServiceMemo
	修改人 	server_port.Mender
	修改时间 	server_port.ModifyTime
	修改时间 	server_port.CICode
	安全中心审核状态 	server_port.State
	部门审核状态 	server_port.DeptState
	Name 	server_port.Name
	EndName 	server_port.EndName
	Flag 	server_port.Flag
	服务器进程ID 	server_port.ServerProcessId
Schema : 部门权限字典表
	ID 	approve_field.id
	属性中文名 	approve_field.ChineseName
	属性英文名 	approve_field.FieldName
	属性英文名 	approve_field.AliasFieldName
Schema : 部门权限字典表
	ID 	approve_field_dep.id
	属性ID 	approve_field_dep.ApproveFieldId
	属性中文名 	approve_field.ChineseName
	属性英文名 	approve_field.FieldName
	属性英文名 	approve_field.AliasFieldName
	部门ID 	approve_field_dep.DepartmentId
	部门权限级别 	approve_field_dep.DepLevel
	部门配置管理员 	t_dep.Operator
Schema : IP资源分配查询
	AssetId 	iprms_relation.equipmentname
	IP地址 	iprms_ip.Address
	IP类型 	iprms_segment.Type
	网关 	iprms_segment.Gateway
	子网掩码 	iprms_segment.SubnetMask
	VLAN 	iprms_segment.Vlan
	操作人员 	iprms_relation.Operator
Schema : 简化OS
	操作系统ID 	supportOS.id
	OS名称 	supportOS.name
	OS版本 	supportOS.version
	架构 	supportOS.CpuBite
	母盘名称 	supportOS.DiskName
	是否锁定 	supportOS.Locked
	OS名称架构 	concat(supportOS.name,'#',supportOS.CpuBite)
	分组属性 	supportOS.GroupName
Schema : OS镜像
	镜像ID 	server_osmirror.id
	镜像名称 	server_osmirror.name
	操作系统ID 	server_osmirror.osid
	镜像备注 	server_osmirror.memo
	OS名称 	supportOS.name
	OS版本 	supportOS.version
	架构 	supportOS.CpuBite
	母盘名称 	supportOS.DiskName
	是否锁定 	supportOS.Locked
	OS名称架构 	concat(supportOS.name,'#',supportOS.CpuBite)
Schema : 机型版本号与OS关系表
	ID 	server_typeclass_os.id
	版本号 	server_typeclass_os.Version
	供应商机型代号 	server_typeclass_os.DeviceTypeName
	腾讯机型代号 	server_typeclass_os.DeviceClassName
	阵列卡型号 	server_typeclass_os.RaidModel
	网卡品牌 	server_typeclass_os.NicBrand
	网卡型号 	server_typeclass_os.NicModel
	操作系统Id(废弃) 	server_typeclass_os.SupportOsId
	操作系统名称(废弃) 	StoSupportOsId
	操作系统版本(废弃) 	supportOS.version
	操作系统架构(废弃) 	supportOS.CpuBite
	OS名称架构(废弃) 	concat(supportOS.name,'#',supportOS.CpuBite)
	All OS名称版本架构 	SupportOsMirror
	RaidIds 	server_typeclass_os.RaidId
	阵列卡类型 	RaidId
	支持镜像ID 	server_typeclass_os.SupportOsMirror
	支持镜像名称 	SupportOsMirror
	镜像与系统合集 	SupportOsMirror
	分组属性 	server_typeclass_os.GroupName
	备注 	server_typeclass_os.Memo
Schema : 机位运营商信息
	机位ID 	idc_position.id
	机位编号 	idc_position.Code
	机位状态 	idc_position.Status
	机位规化设备类型 	idc_position.PlanDeviceType
	不可用原因 	idc_position.UnuseableReason
	机架ID 	idc_position.EquipmentId
	IdcId 	t_equipment.idc_id
	IdcName 	t_idc.name
	IdcParentId 	t_idc.IdcParentId
	运营商ID 	group_concat(distinct idc_exit.OperationId)
	运营商名称 	group_concat(distinct idc_operation.name)
	运营商数量 	count(distinct idc_exit.OperationId)
Schema : 增量更新查询
	ID 	modify_timestamp.id
	HostTypeId 	modify_timestamp.HostTypeId
	HostId 	modify_timestamp.HostCIId
	CITypeId 	modify_timestamp.CITypeId
	CIId 	modify_timestamp.CIId
	所属运维部门 	t_device.dep_id
	操作方式1I2D3U 	modify_timestamp.Mode
	更新时间 	modify_timestamp.UpdateTime
Schema : 带外管理卡
	outofbandID 	dic_outofband.id
	outofband类型 	dic_outofband.OutOfBandModel
Schema : 业务产品
	productName 	product.productname
Schema : 业务产品集
	productsetName 	product.productsetname
Schema : 角色基本信息列表
	角色名称 	pm_role_base.name
	最长使用时间（天） 	pm_role_base.maxUseTime
	状态 	pm_role_base.status
	能够使用的用户 	(select cast(group_concat(user SEPARATOR '; ') as char(1000)) from pm_role_user where expiretime>=now() and isgrant=0 and roleid=pm_role_base.id)
	能够授权的用户 	(select cast(group_concat(user SEPARATOR '; ') as char(1000)) from pm_role_user where isgrant=1 and roleid=pm_role_base.id)
	Uwork角色ID 	pm_role_base.uworkroleid
	备注 	pm_role_base.memo
	创建人 	pm_role_base.createuser
	创建时间 	pm_role_base.createtime
	自动授权 	pm_role_base.AutoLoad
Schema : 规则基本信息列表
	角色ID 	pm_rule.roleid
	角色名称 	pm_role_base.name
	资源CODE 	pm_rule.ResTypeCode
	资源名称 	pm_res_type.name
	操作类型 	pm_rule.operCode
	状态 	pm_rule.status
	是否显示总条数 	ShowTotal
	有所有资源项权限 	hasAllItem
	资源项 	resItemCode
	查询条件 	DynamicCondition
	查询条件说明 	conditionmemo
	创建人 	pm_rule.createuser
	创建时间 	pm_rule.createtime
	最后修改人 	pm_rule.lastUpdateUser
	最后修改时间 	pm_rule.lastUpdateTime
	作用部门ID 	pm_rule.DeptId
	作用部门 	DeptId
Schema : 授权列表
	用户 	pm_role_user.user
	角色ID 	pm_role_user.roleid
	角色名称 	pm_role_base.name
	角色备注 	pm_role_base.memo
	授权类型 	pm_role_user.isGrant
	授权人 	pm_role_user.grantUser
	授权时间 	pm_role_user.grantTime
	过期时间 	pm_role_user.expireTime
	是否过期 	if(pm_role_user.expireTime<= now(),1,0)
	部门名称 	t_dep.name
	授权原因 	pm_role_user.Reason
Schema : 资源类型列表
	资源类型ID 	pm_res_type.Id
	资源类型代码 	pm_res_type.typeCode
	资源类型名称 	pm_res_type.Name
	资源类型备注 	pm_res_type.memo
Schema : 资源项列表
	资源类型ID 	pm_res_item.TypeId
	资源类型代码 	pm_res_type.typeCode
	资源类型名称 	pm_res_type.Name
	资源项ID 	pm_res_item.Id
	资源项代码 	pm_res_item.itemCode
	资源项名称 	pm_res_item.Name
	资源项备注 	pm_res_item.memo
	查询关联文件 	pm_res_item.SchemasRead
	查询关联属性 	pm_res_item.ColumnsRead
	增删改关联文件 	pm_res_item.SchemasEdit
	增删改关联属性 	pm_res_item.ColumnsEdit
	导入关联文件 	pm_res_item.SchemasImport
	导入关联属性 	pm_res_item.ColumnsImport
Schema : 资源类型列表
	资源类型代码 	pm_res_type.typeCode
	资源类型名称 	pm_res_type.Name
	资源类型备注 	pm_res_type.memo
	资源类型ID 	pm_res_item.TypeId
	资源项ID 	pm_res_item.Id
	资源项编码 	pm_res_item.itemCode
	资源项名称 	pm_res_item.Name
	资源项备注 	pm_res_item.memo
	查询关联文件 	pm_res_item.SchemasRead
	查询关联属性 	pm_res_item.ColumnsRead
	增删改关联文件 	pm_res_item.SchemasEdit
	增删改关联属性 	pm_res_item.ColumnsEdit
	导入关联文件 	pm_res_item.SchemasImport
	导入关联属性 	pm_res_item.ColumnsImport
Schema : 机架
	机架ID 	t_equipment.id
	机架名称 	t_equipment.name
	腾讯机架标识 	t_equipment.tencentShelfID
	SP编号 	t_equipment.sn
	机架高度 	t_equipment.high
	额定电流 	t_equipment.ratedi
	已用电流 	t_equipment.usedi
	峰值电流 	t_equipment.MaxI
	峰值功率 	t_equipment.MaxPower
	是否启用 	t_equipment.islay
	启用时间 	DATE_FORMAT(t_equipment.usetime,'%Y-%m-%d')
	是否开电 	t_equipment.isopen
	开电时间 	DATE_FORMAT(t_equipment.opentime,'%Y-%m-%d')
	关电时间 	DATE_FORMAT(t_equipment.downtime,'%Y-%m-%d')
	规划设备类型 	t_equipment.PlanDeviceType
	电路类型 	t_equipment.powertype
	机架业务类型 	t_equipment.RckBizType
	电流类型 	t_equipment.ElectricityType
	可用功率 	t_equipment.RatedPower
	市电 	t_equipment.CityPower
	柴油发电机 	t_equipment.DieselPower
	配电路由 	t_equipment.PowerRoute
	机房精密空调 	t_equipment.AirConditioner
	可用级别 	t_equipment.UseLevel
	是否支持SideBand 	t_equipment.IsSideBand
	ModuleId_废弃 	''
	ModuleName_废弃 	''
	SubZoneName_废弃 	''
	SubZoneID_废弃 	''
	ZoneName_废弃 	''
	ZoneID_废弃 	''
	说明信息 	t_equipment.memo
	机位总数 	count(distinct i.id)
	已用机位数 	count(distinct if(i.status='1',i.id,null))
	空闲机位数 	count(distinct if(i.status='0',i.id,null))
	不可用机位数 	count(distinct if(i.status='2',i.id,null))
	预占用机位数 	count(distinct if(i.status='3',i.id,null))
	是否占用 	if( count(distinct p.id) = 0, 0,1)
	关联设备总数 	count(distinct p.id)
	关联网络设备数 	count(distinct if(p.configitemid='5',p.id,null))
	关联服务器数 	count(distinct if(p.configitemid='2',p.id,null))
	关联存储设备数 	count(distinct if(p.configitemid='13' or p.configitemid='17' or p.configitemid='18',p.id,null ))
	机房管理单元名称 	t.name
	机房管理单元ID 	t_equipment.idc_id
	一级机房ID 	t.idcparentid
	一级机房名称 	idc_parent.name
	是否虚拟 	t_equipment.IsVirtual
	关联PDU设备组 ID 	t_equipment.PduGroupId
	关联PDU设备组 	idc_part_groups.name
	0通用1租赁2托管 	t_equipment.rack_usage
Schema : 机架规划设备类型列表
	规划设备类型字典 	 t_equipment.PlanDeviceType 
Schema : 机位列表
	机位ID 	idc_position.id
	机位编号 	idc_position.code
	带外管理方式 	idc_position.ManageType
	机位状态* 	idc_position.status
	不可用原因 	idc_position.UnuseableReason
	机位高度* 	idc_position.Hight
	设备规划类型ID 	idc_position.PlanDeviceType
	预占用人 	idc_position.PreOrder
	客户名称 	idc_position.ClientName
	电源线类型 	idc_position.LineType
	电源线数量 	idc_position.LineNum
	机位TOR 	idc_position.PosTOR
	预占用原因 	idc_position.PreReason
	设备规划类型名称 	PosPlanDeviceTypeId
	机位放置设备类型 	group_concat( distinct equipment_position.ConfigItemId)
	机位放置设备 	PosId
	设备所属运维部门 	PosId
	机架ID 	idc_position.EquipmentId
	所属机架* 	t_equipment.name
	机架是否虚拟 	t_equipment.IsVirtual
	机房管理单元名称* 	t_idc.name
	机房管理单元ID* 	t_equipment.idc_id
	预约设备 	idc_position.PreOccupyAsset
	预约时间 	idc_position.PreOccupyDateTime
	预启用时间 	idc_position.PreStartDateTime
	内网交换机端口 	idc_position.InnerSwitchPort
	外网交换机端口 	idc_position.OuterSwitchPort
	管理网交换机端口 	idc_position.AdminSwitchPort
	其它端口 	idc_position.OtherPort
	网络结构版本 	idc_safedomain.StructureVersion
	机位逻辑区域ID 	idc_safedomain.id
	机位逻辑区域 	idc_safedomain.name
	备注 	idc_position.Memo
	内网机房网络模块ID 	(select group_concat(IdcNetworkModuleId SEPARATOR ';') from rs_idcnetworkmodule_position inner join idc_network on rs_idcnetworkmodule_position.IdcNetworkModuleId=idc_network.id where idc_network.type=0 and PositionId=idc_position.id)
	内网机房网络模块名称 	(select group_concat(idc_network.name SEPARATOR ';') from rs_idcnetworkmodule_position inner join idc_network on rs_idcnetworkmodule_position.IdcNetworkModuleId=idc_network.id where idc_network.type=0 and PositionId=idc_position.id)
	外网机房网络模块ID 	(select group_concat(IdcNetworkModuleId SEPARATOR ';') from rs_idcnetworkmodule_position inner join idc_network on rs_idcnetworkmodule_position.IdcNetworkModuleId=idc_network.id where idc_network.type=1 and PositionId=idc_position.id)
	外网机房网络模块名称 	(select group_concat(idc_network.name SEPARATOR ';') from rs_idcnetworkmodule_position inner join idc_network on rs_idcnetworkmodule_position.IdcNetworkModuleId=idc_network.id where idc_network.type=1 and PositionId=idc_position.id)
	一级机房名称 	idc_parent.name
	一级机房ID 	idc_parent.id
	机位布线信息 	PosId
	Module名称 	t_module.name
	ModuleId 	t_module.id
	客户ID 	idc_position.customerId
	机位业务部门ID 	idc_position.BusinessDeptId
	机位业务部门名称 	dept.DeptName
	机位是否虚拟 	idc_position.IsVirtual
Schema : 机房功能类型字典表
	功能类型ID 	dic_idcparentfunction.id
	功能类型名称 	dic_idcparentfunction.name
Schema : 机房所在城市字典表
	城市ID 	dic_idcparentcity.id
	城市名称 	dic_idcparentcity.name
	国家 	dic_idcparentcountry.CountryName
	省份 	dic_province.Name
Schema : 机房所在国家字典表
	国家ID 	dic_idcparentcountry.CountryId
	国家名称 	dic_idcparentcountry.CountryName
Schema : 机房所在省份字典表
	ProvinceID 	dic_province.Id
	Province名称 	dic_province.Name
Schema : 机房运营商字典表
	运营商ID 	dic_idcparentoperation.id
	运营商名称 	dic_idcparentoperation.name
Schema : 网络模块网络设备映射表
	ID 	rs_netdevice_idcnetmodule.id
	网络设备ID 	rs_netdevice_idcnetmodule.NetDeviceId
	网络模块ID 	rs_netdevice_idcnetmodule.IdcNetModuleId
Schema : 网络模块机位映射表
	ID 	rs_idcnetworkmodule_position.id
	机房ID 	rs_idcnetworkmodule_position.idcid
	机架ID 	rs_idcnetworkmodule_position.rackid
	机位ID 	rs_idcnetworkmodule_position.PositionId
	网络模块ID 	rs_idcnetworkmodule_position.IdcNetworkModuleId
Schema : 网络模块管理单元映射
	机房ID 	rs_idcnetworkmodule_position.idcid
	机房名称 	t_idc.name
	网络模块ID 	group_concat(distinct rs_idcnetworkmodule_position.IdcNetworkModuleId SEPARATOR ';')
	网络模块名称 	group_concat(distinct idc_network.name SEPARATOR ';')
Schema : 默认进程
	业务ID 	business_process.BusinessId
	进程ID 	business_process.id
	进程名称 	business_process.Name
	进程类型 	business_process.Status
	起始路径 	business_process.StartPath
	结束路径 	business_process.EndPath
	配置路径 	business_process.ConfigPath
	日志路径 	business_process.LogPath
	备注 	business_process.Memo
	负责人 	business_process.Operator
	起始端口 	group_concat(business_port.Name SEPARATOR ';')
	结束端口 	business_port.EndName
	启用状态 	business_process.IsUsing
	启动数量 	business_process.Total
Schema : 默认端口
	业务ID 	business_port.BusinessId
	业务端口ID 	business_port.Id
	业务进程Id 	business_port.ProcessId
	业务进程 	business_process.Name
	端口名称 	business_port.Name
	端口结束名称 	business_port.EndName
	绑定IP 	business_port.IP
	端口内外标记 	business_port.Flag
	端口协议 	business_port.Protocol
	端口备注  	business_port.Memo
	端口服务 	business_port.ServiceName
	端口服务备注 	business_port.ServiceMemo
	所属进程 	business_process.Name
	是否启用 	business_port.IsUsing
	是否IP限制 	business_port.IpLimit
Schema : 服务器端口进程
	服务器端口ID 	server_port.Id
	服务器进程ID 	server_process.Id
	服务器进程 	server_process.name
	服务器ID 	server_process.ServerId
	IPid 	''
	服务器IP 	ip_equipment.ipaddress
	IPv6 	SvrId
Schema : 业务端口进程
	业务端口ID 	business_port.Id
	业务进程Id 	business_process.Id
	业务进程 	business_process.Name
Schema : 业务服务器
	业务id 	business.id
	服务器ID 	t_device.id
	固资编号 	t_device.assetid
	服务器名称 	t_device.name
	负责人 	t_device.operator
	内网IP 	SvrId
	外网IP 	SvrId
	运维部门ID 	t_device.dep_id
	运维部门 	serverDeptId
	机房管理单元ID 	equipment_position.idcid
	机房管理单元 	serverIdcId
	一级机房 	serverIdcId
	机架ID 	equipment_position.shelfid
Schema : 部门字段权限
	ID 	approve_field_dep.Id
	字段ID 	approve_field_dep.ApproveFieldId
	字段中文名 	approve_field.ChineseName
	字段Filed名 	approve_field.FieldName
	权限 	approve_field_dep.DepLevel
Schema : 错误编码
	ID 	api_error_code.Id
	错误编码 	api_error_code.ErrorCode
	错误信息 	api_error_code.ErrorInfo
	备注 	api_error_code.ErrorMemo
	最近发生时间 	api_error_code.LastTime
	函数ID 	api_error_code.funcID
	函数名 	api_func.FuncName
	函数文件 	api_func.FuncFile
	日志ID 	api_error_code.LogId
	错误类型 	api_error_code.IsAvailable
	解决方法 	api_error_code.Solve
Schema : 接口函数
	ID 	api_func.Id
	name 	api_func.funcname
	file 	api_func.funcfile
	AfnType 	api_func.Type
	AfnIsOpen 	api_func.IsOpen
	memo 	api_func.funcmemo
	父函数名称 	parent_func.FuncName
	错误编码ID 	api_error_code.Id
	错误编码ID 	api_error_code.ErrorCode
	错误编码ID 	api_error_code.ErrorInfo
Schema : 函数关系
	ID 	api_func_relation.Id
	父函数名称 	api_func_parent.funcname
	父函数Id 	api_func_relation.ParentFuncId
	子函数名称 	api_func_child.funcname
	父函数Id 	api_func_relation.ChildFuncId
	memo 	api_func_relation.IsDirect
	错误编码ID 	api_error_code.Id
	错误编码ID 	api_error_code.ErrorCode
	错误编码ID 	api_error_code.ErrorInfo
	AfrIsValidate 	api_func_relation.IsValidate
Schema : 备件PN号
	PN号ID 	spare_puknumber.Id
	厂商 	spare_puknumber.Company
	PN号 	spare_puknumber.Puknumber
	备件类别 	spare_puknumber.Category
	用途类型 	spare_puknumber.Uses
	参数 	spare_puknumber.Param
	管理方式 	spare_puknumber.Management
	额定数量 	spare_puknumber.Ratednum
	备注 	spare_puknumber.Memo
Schema : 货架区号
	货架区号ID 	spare_shelf.Id
	备件库ID 	spare_shelf.SpareStorageId
	备件库 	spare_storage.Name
	货架区号 	spare_shelf.Name
	备注 	spare_shelf.Memo
Schema : 备件
	备件Id 	spare_parts.Id
	puknumber 	spare_parts.PuknumberId
	PN厂商 	spare_puknumber.Company
	PN号 	spare_puknumber.Puknumber
	备件类别 	spare_puknumber.Category
	用途类型 	spare_puknumber.Uses
	参数 	spare_puknumber.Param
	管理方式 	spare_puknumber.Management
	额定数量 	spare_puknumber.Ratednum
	状态 	spare_parts.Status
	逻辑库 	spare_parts.Logic
	数量 	spare_parts.Count
	备件SN号 	spare_parts.SN
	是否可用 	spare_parts.Enabled
	资产所有方 	spare_parts.Property
	备件所属货架ID 	spare_parts.ShelfId
	所属货架名称 	spare_shelf.Name
	所属备件库ID 	spare_shelf.SpareStorageId
	备件库名称 	spare_storage.Name
	序号 	spare_storage.Num
	库房种类 	spare_storage.IsStorage
	维护人 	spare_storage.Operator
	地 址 	spare_storage.Address
	是否启用 	spare_storage.IsUsing
	正式使用日期 	spare_storage.FirstUsingDate
	备注 	spare_storage.Memo
	一级机房ID 	spare_storage.IdcParentId
	一级机房 	idc_parent.name
	功能类型 	idc_parent.FunctionName
	运维区域 	area.name
	是否中央机房 	sparearea_idcparent_relation.IsCenter
	区域机房关系表ID 	sparearea_idcparent_relation.Id
	区域ID 	spare_area.Id
	区域编码 	spare_area.Name
	用途类型 	spare_area.Purpose
	备注 	spare_area.Memo
	首次入库时间 	spare_parts.Firsttime
	采购单号 	spare_parts.Orderno
	入库人 	spare_parts.Storager
	最后一次更新时间 	spare_parts.Latesttime
	备注 	spare_parts.Memo
Schema : 备件流水账
	备件Id 	spare_parts.id
	操作类型 	spare_parts_log.OperateType
	操作时间 	spare_parts_log.OperateTime
	操作人 	spare_parts_log.Operater
	数量 	spare_parts_log.Count
	puknumber 	spare_parts.PuknumberId
	PN厂商 	spare_puknumber.Company
	PN号 	spare_puknumber.Puknumber
	备件类别 	spare_puknumber.Category
	用途类型 	spare_puknumber.Uses
	参数 	spare_puknumber.Param
	管理方式 	spare_puknumber.Management
	额定数量 	spare_puknumber.Ratednum
	状态 	spare_parts.Status
	逻辑库 	spare_parts.Logic
	备件SN号 	spare_parts.SN
	是否可用 	spare_parts.Enabled
	资产所有方 	spare_parts.Property
	备件所属货架ID 	spare_parts.ShelfId
	所属货架名称 	spare_shelf.Name
	所属备件库ID 	spare_shelf.SpareStorageId
	备件库名称 	spare_storage.Name
	序号 	spare_storage.Num
	库房种类 	spare_storage.IsStorage
	维护人 	spare_storage.Operator
	地 址 	spare_storage.Address
	是否启用 	spare_storage.IsUsing
	正式使用日期 	spare_storage.FirstUsingDate
	备注 	spare_storage.Memo
	一级机房ID 	spare_storage.IdcParentId
	一级机房 	idc_parent.name
	功能类型 	idc_parent.FunctionName
	运维区域 	area.name
	是否中央机房 	sparearea_idcparent_relation.IsCenter
	区域机房关系表ID 	sparearea_idcparent_relation.Id
	区域ID 	spare_area.Id
	区域编码 	spare_area.Name
	用途类型 	spare_area.Purpose
	备注 	spare_area.Memo
	首次入库时间 	spare_parts.Firsttime
	采购单号 	spare_parts.Orderno
	入库人 	spare_parts.Storager
	最后一次更新时间 	spare_parts.Latesttime
	备注 	spare_parts.Memo
Schema : 区域备件库备件数量查询
	数量 	SUM(spare_parts.Count)
	备件所属货架ID 	spare_parts.ShelfId
	所属货架名称 	spare_shelf.Name
	所属备件库ID 	spare_shelf.SpareStorageId
	备件库名称 	spare_storage.Name
	一级机房ID 	spare_storage.IdcParentId
	一级机房 	idc_parent.name
	区域机房关系表ID 	sparearea_idcparent_relation.Id
	区域ID 	spare_area.Id
	区域编码 	spare_area.Name
	PN号 	spare_puknumber.Puknumber
	备件类别 	spare_puknumber.Category
	PN厂商 	spare_puknumber.Company
	用途类型 	spare_puknumber.Uses
	参数 	spare_puknumber.Param
	逻辑库 	spare_parts.Logic
	资产所有方 	spare_parts.Property
	是否可用 	spare_parts.Enabled
Schema : 备件类别字典表
	备件类别ID 	dic_sparecategory.Id
	备件类别名称 	dic_sparecategory.Category
Schema : 备件厂商字典表
	备件厂商ID 	dic_sparecompany.Id
	备件类别名称 	dic_sparecompany.Name
Schema : 备件逻辑库字典表
	逻辑库ID 	dic_sparepartslogic.Id
	逻辑库名称 	dic_sparepartslogic.Logic
Schema : 备件区域
	区域ID 	spare_area.Id
	区域编码 	spare_area.Name
	用途类型 	spare_area.Purpose
	备注 	spare_area.Memo
Schema : 备件区域和机房关系
	区域机房关系表ID 	sparearea_idcparent_relation.Id
	备件区域ID 	sparearea_idcparent_relation.SpareAreaId
	区域编码 	spare_area.Name
	用途类型 	spare_area.Purpose
	一级机房ID 	sparearea_idcparent_relation.IdcParentId
	所属机房名称 	idc_parent.name
	是否中央机房 	sparearea_idcparent_relation.IsCenter
	功能类型 	idc_parent.FunctionName
	运维区域 	area.name
Schema : 备件区域和机房关系
	区域机房关系表ID 	sparearea_idcparent_relation.Id
	一级机房ID 	sparearea_idcparent_relation.IdcParentId
	所属机房名称 	idc_parent.name
Schema : 备件区域机房和备件库综合查询
	区域机房关系表ID 	sparearea_idcparent_relation.Id
	备件区域ID 	sparearea_idcparent_relation.SpareAreaId
	区域编码 	spare_area.Name
	一级机房ID 	sparearea_idcparent_relation.IdcParentId
	所属机房名称 	idc_parent.name
	备件库ID 	spare_storage.Id
	备件库名称 	spare_storage.Name
	序号 	spare_storage.Num
	库房种类 	spare_storage.IsStorage
	维护人 	spare_storage.Operator
	地址 	spare_storage.Address
	是否启用 	spare_storage.IsUsing
	正式使用日期 	spare_storage.FirstUsingDate
	备注 	spare_storage.Memo
Schema : 备件库
	备件库ID 	spare_storage.Id
	一级机房ID 	spare_storage.IdcParentId
	所属机房名称 	idc_parent.name
	备件库名称 	spare_storage.Name
	序号 	spare_storage.Num
	库房种类 	spare_storage.IsStorage
	维护人 	spare_storage.Operator
	地址 	spare_storage.Address
	是否启用 	spare_storage.IsUsing
	正式使用日期 	spare_storage.FirstUsingDate
	备注 	spare_storage.Memo
Schema : 货架区号
	货架区号ID 	spare_shelf.Id
	备件库ID 	spare_shelf.SpareStorageId
	备件库 	spare_storage.Name
	货架区号 	spare_shelf.Name
	备注 	spare_shelf.Memo
Schema : 备件库综合查询
	区域ID 	spare_area.Id
	区域编码 	spare_area.Name
	用途类型 	spare_area.Purpose
	备注 	spare_area.Memo
	区域机房关系表ID 	sparearea_idcparent_relation.Id
	一级机房ID 	sparearea_idcparent_relation.IdcParentId
	所属机房名称 	idc_parent.name
	功能类型 	idc_parent.FunctionName
	运维区域 	area.name
	是否中央机房 	sparearea_idcparent_relation.IsCenter
	备件库ID 	spare_storage.Id
	备件库名称 	spare_storage.Name
	序号 	spare_storage.Num
	库房种类 	spare_storage.IsStorage
	维护人 	spare_storage.Operator
	地址 	spare_storage.Address
	是否启用 	spare_storage.IsUsing
	正式使用日期 	spare_storage.FirstUsingDate
	备注 	spare_storage.Memo
	货架区号ID 	spare_shelf.Id
	货架区号 	spare_shelf.Name
	备注 	spare_shelf.Memo
Schema : Zone
	ID 	t_zone.Id
	名称 	t_zone.Name
	别名 	t_zone.aka
	片区ID 	t_zone.AreaId
	片区名称 	area.Name
	城市ID 	t_zone.CityId
	城市名称 	city.Name
	类型 	t_zone.Type
	创建时间 	t_zone.CreateTime
	启用时间 	t_zone.FirstUseTime
	状态 	t_zone.Status
	备注 	t_zone.Memo
Schema : SubZone
	ID 	t_subzone.Id
	名称 	t_subzone.Name
	别名 	t_subzone.aka
	Zone ID 	t_subzone.ZoneId
	Zone名称 	t_zone.Name
	城市ID 	t_subzone.CityId
	城市 	dic_idcparentcity.Name
	类型 	t_subzone.Type
	创建时间 	t_subzone.CreateTime
	启用时间 	t_subzone.FirstUseTime
	状态 	t_subzone.Status
	备注 	t_subzone.Memo
Schema : Module
	ID 	t_module.Id
	名称 	t_module.Name
	LifeStep 	t_module.LifeStep
	LifeStatus 	t_module.LifeStatus
	别名 	t_module.aka
	SubZone ID 	t_module.SubZoneId
	SubZone名称 	t_subzone.Name
	Zone ID 	t_subzone.ZoneId
	Zone名称 	t_zone.Name
	片区ID 	t_zone.AreaId
	片区名称 	area.Name
	城市ID 	t_zone.CityId
	城市名称 	city.Name
	负责人 	t_module.Operator
	网络版本 	t_module.NetVersion
	创建时间 	t_module.CreateTime
	启用时间 	t_module.FirstUseTime
	状态 	t_module.Status
	OS扁平化 0老机房支持windows 1新机房 	t_module.OsNew
	备注 	t_module.Memo
	机房网络模块ID 	t_module.IdcNetworkModuleId
	机房网络模块 	idc_network.Name
	是否可匹配 	t_module.CanMap
	资源云版本 	t_module.CloudVersion
	业务规划属性 	t_module.PlanType
	生命周期 	t_module.Season
	是否接入微模块 	t_module.SupportMicroModule
	机房管理单元 	(select group_concat(distinct ifnull(t_idc.name,'') SEPARATOR ';') from t_idc  inner join t_equipment r on r.idc_id=t_idc.id inner join idc_position ip on ip.equipmentid=r.id inner join idc_safedomain sd on sd.id= ip.safedomain where sd.moduleid= t_module.id)
	机房网络模块 	(select group_concat(distinct ifnull(idc_network.name,'') SEPARATOR ';') from idc_network left join rs_idcnetworkmodule_position r on r.IdcNetworkModuleId=idc_network.id left join idc_position ip on ip.id=r.positionid left join idc_safedomain sd on sd.id= ip.safedomain where sd.moduleid= t_module.id)
	网络结构版本 	(select group_concat(distinct ifnull(nv.name,'') SEPARATOR ';') from  idc_safedomain sd left join dic_net_version nv  on sd.StructureVersion = nv.id where sd.moduleid= t_module.id)
	规划出口运营商 	(select group_concat(distinct ifnull(t_idc.PlanOperationName,'') SEPARATOR ';') from t_idc left join t_equipment r on r.idc_id=t_idc.id left join idc_position ip on ip.equipmentid=r.id left join idc_safedomain sd on sd.id= ip.safedomain where sd.moduleid= t_module.id)
	规划出口运营商ID 	 group_concat(distinct idc_operation.id SEPARATOR ';') 
	规划出口运营商 	 group_concat(distinct idc_operation.Name SEPARATOR ';')
	配电信息 	(select group_concat(distinct ifnull(r.ElectricityType,'') SEPARATOR ';') from t_equipment r  inner join idc_position ip on ip.equipmentid=r.id inner join idc_safedomain sd on sd.id= ip.safedomain where sd.moduleid= t_module.id)
	是否支持SideBand 	(select if(count(r.id)>0,1,0) from  t_equipment r  inner join idc_position ip on ip.equipmentid=r.id inner join idc_safedomain sd on sd.id= ip.safedomain where sd.moduleid= t_module.id and r.IsSideBand=1)
	Module业务类型 	t_module.BusinessType
	Module带宽类型 	t_module.BandType
	Module业务类型Name 	dic_module_busi.dic_value
	Module带宽类型Name 	dic_band_type.dic_value
	管控级别类型 	t_module.ManageLevelType
Schema : IdcParts
	ID 	idc_parts.Id
	名称 	idc_parts.Name
	类型Id 	idc_parts.TypeId
	类型 	dic_idc_part_types.Name
	品牌 	idc_parts.Brand
	型号 	idc_parts.Model
	功率(KW) 	idc_parts.Power
	功率容量(KW) 	idc_parts.PowerCapacity
	后备时间(分钟) 	idc_parts.BackupMinute
	标准输出电压 	idc_parts.OutV
	接口位数 	idc_parts.PortNumber
	其他参数 	idc_parts.OtherInfo
	启用时间 	idc_parts.FirstUseTime
	过保时间 	idc_parts.ProtectOverTime
	维保单位与联系人 	idc_parts.ProtectOperator
	历史维保信息 	idc_parts.HistoryService
	当前维保状态 	idc_parts.ProtectStatus
	维护服务等级 	idc_parts.ProtectLevel
	问题响应时间(小时) 	idc_parts.ResponseTime
	线路类别 	idc_parts.LineType
	邮箱储油量 	idc_parts.TankCapacity
	电池品牌/型号 	idc_parts.BatteryModel
	电池数量 	idc_parts.BatteryNumber
	列头开关输出微断开关容量/数量 	idc_parts.SwitchNumber
	是否带位开关 	idc_parts.DwSwitch
	端口制式 	idc_parts.PortType
	空调类型 	idc_parts.AcType
	空调制冷方式 	idc_parts.CoolType
	送风方式 	idc_parts.WindType
	设备组ID 	idc_parts.GroupId
	设备组名称 	idc_part_groups.Name
	机房管理单元ID 	idc_parts.IdcId
	机房管理单元名称 	group_concat(t_idc.name separator ';')
	一级机房ID 	group_concat(t_idc.IdcParentId separator ';')
	一级机房名称 	group_concat(idc_parent.name separator ';')
	备注 	idc_parts.Memo
	精密空调下联影响机房单元 	IptsId
	PDU下联影响机架 	IptsId
Schema : IDC基础设施设备组
	ID 	idc_part_groups.Id
	名称 	idc_part_groups.Name
	设备类型ID 	idc_part_groups.PartTypeId
	设备类型 	dic_idc_part_types.Name
	并机方式 	idc_part_groups.GroupType
	容量 	idc_part_groups.GroupCapacity
Schema : 基础设施设备组关系
	ID 	idc_part_group_relation.Id
	设备组ID 	idc_part_group_relation.GroupId
	设备组名称 	idc_part_groups1.Name
	上联设备组ID 	idc_part_group_relation.ParentGroupId
	上联设备组名称 	idc_part_groups2.Name
Schema : 基础设施类型字典表
	ID 	dic_idc_part_types.Id
	Name 	dic_idc_part_types.Name
Schema : 服务器锁
	ID 	server_lock.Id
	服务器ID 	server_lock.LockServerId
	Lock Type(值为status,department) 	server_lock.LockType
	起始时间 	server_lock.LockStartTime
	截止时间 	server_lock.LockEndTime
	SystemId 	server_lock.LockSystemId
	Operator 	server_lock.LockOperator
	Key 	server_lock.LockKey
	Reason 	server_lock.LockReason
	服务器固资号 	t_device.assetid
	服务器运维部门 	t_device.dep_id
Schema : 网络版本
	ID 	dic_net_version.Id
	Name 	dic_net_version.Name
Schema : 故障列表
	ID 	log_down.Id
	Title 	log_down.Title
	Content 	log_down.Content
	DownTime 	log_down.DownTime
	IdcIds 	log_down.IdcIds
	IdcNames 	group_concat(distinct t_idc.name separator ';')
	ModuleIds 	log_down.ModuleIds
	ModuleNames 	group_concat(distinct t_module.name separator ';')
Schema : 服务器ADS信息
	ID 	server_ads.Id
	SvrId 	server_ads.SvrId
	AdsStatus 	server_ads.AdsStatus
	AdsVersion 	server_ads.AdsVersion
	AdsCheckTime 	server_ads.AdsCheckTime
Schema : 服务器VM标准硬件信息
	ID 	server_vm_hard.Id
	SvrId 	server_vm_hard.ServerId
	VmCpu 	server_vm_hard.VmCpu
	VmMemory 	server_vm_hard.VmMemory
	VmHarddisk 	server_vm_hard.vmharddisk
Schema : 服务器更多信息
	ID 	server_more_field.Id
	SvrId 	server_more_field.ServerId
	资产类型 	server_more_field.ItCloud
	改造时间 	server_more_field.RedoTime
	建议使用时间 	server_more_field.SuggestTime
Schema : 服务器生命周期流程
	ID 	server_timeline_process.Id
	TimepName 	server_timeline_process.name
	TimepVersion 	server_timeline_process.Version
	TimepInputTime 	server_timeline_process.InputTime
Schema : 服务器生命周期状态
	ID 	server_timeline_status.Id
	TimesName 	server_timeline_status.name
	TimesDepth 	server_timeline_status.Depth
	TimesParentId 	server_timeline_status.ParentId
	TimesInputTime 	server_timeline_status.InputTime
	TimesMemo 	server_timeline_status.Memo
Schema : 服务器生命周期行为
	ID 	server_timeline_rule.Id
	TimerName 	server_timeline_rule.name
	TimerFromStatusId 	server_timeline_rule.FromStatusId
	TimerFromStatusName 	server_timeline_status_from.name
	TimerToStatusName 	server_timeline_status_to.name
	TimerToStatusId 	server_timeline_rule.ToStatusId
	TimerVersion 	server_timeline_rule.Version
	TimerInputTime 	server_timeline_rule.InputTime
	TimerMemo 	server_timeline_rule.Memo
Schema : 机位端口类型
	ID 	dic_port_type.Id
	类型名称 	dic_port_type.name
	EthName 	dic_port_type.ethname
Schema : 业务部门
	BusinessDeptId 	bas_dis_dept.DeptId
	BusinessDeptName 	bas_dis_dept.DeptName
Schema : 服务器Bios字典表
	BiosId 	bios_firmware.Id
	BiosName 	bios_firmware.Name
Schema : Customer
	CustomerId 	tdb_customer.Id
	CustomerName 	tdb_customer.customer
Schema : 通用字典查询
	字典ID 	dic_common.id
	字典Key 	dic_common.dic_key
	字典Value 	dic_common.dic_value
	字典类型 	dic_common.dic_type
Schema : 云节点区域
	id 	cloud_node_area_dictionary.id
	name 	cloud_node_area_dictionary.name
	memo 	cloud_node_area_dictionary.memo
	operator 	cloud_node_area_dictionary.operator
	lastupdatetime 	cloud_node_area_dictionary.lastupdatetime
