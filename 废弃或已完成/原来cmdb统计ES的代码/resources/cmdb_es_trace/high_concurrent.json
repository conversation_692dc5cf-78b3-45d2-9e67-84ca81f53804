{
  "size": 0, // size为0表示 不显示具体内容
  "query": { // 先过滤掉请求得比较慢的
    "range": {
      "duration": {
        "lt":  60000 // 超过60秒的不要
      }
    }
  },
  "aggs":{ // 聚合
    "aggs_by_time": { // 聚合的名称，自己取名
      "date_histogram": { // date_histogram是es提供的时间聚合函数
        "field": "@timestamp", // 时间字段的名称，只要是date类型就行
        "interval": "1m", // 按多长的时间进行聚合，例如5m是5分钟
        "time_zone":"+08:00" // 设置时区
      },
      "aggs": { // 时间聚合之后，对每份时间聚合后的数据，再进行聚合
        "group_by_system_id": { // 聚合的名称，自己取名
          "terms": { // terms是es提供的函数
            "field": "system_id"
          },
          "aggs": { // 再聚合，求请求平均响应时间
            "avg_duration": { // 聚合的名称，自己取
              "avg": { // avg是es提供的函数
                "field": "duration" // 取平均值的字段
              }
            }
          }
        }
      }
    }
  }
}