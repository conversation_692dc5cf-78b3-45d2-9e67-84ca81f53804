package com.nickxie.others.cmdb_es_trace;

import com.nickxie.others.cmdb_es_trace.model.HighConcurrentDTO;
import com.nickxie.others.model.ESResultDTO;
import com.nickxie.others.utils.ESJsonUtils;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.net.Browser;
import com.pugwoo.wooutils.net.HttpResponse;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 追查高并发的
 */
@SpringBootTest
public class TraceHighConcurrent {

    @Value("${es_url}")
    private String esUrl;

    @Test
    public void traceHighConcurrent() throws Exception {
        String url = esUrl + "index_cmdb-2021.02.05/_search"; // 查询的索引
        String param = ESJsonUtils.getJson("/cmdb_es_trace/high_concurrent.json");

        Browser browser = new Browser();
        browser.disableGzip();
        browser.addRequestHeader("Authorization", "Basic bG9nc3Rhc2g6bG9nc3Rhc2g=");
        browser.addRequestHeader("Content-Type", "application/json");
        HttpResponse resp = browser.post(url, param.getBytes());

        ESResultDTO result = JSON.parse(resp.getContentString(), ESResultDTO.class);

        List<HighConcurrentDTO> list = trans(result);

        for (HighConcurrentDTO highConcurrentDTO : list) {
            // 计算出并发数(估算) = 1分钟请求数 / 60 * 平均响应时间
            int concurrent = (int) (highConcurrentDTO.getTotalRequest() / 60.0
                    * highConcurrentDTO.getAvgCostSeconds());

            if (concurrent > 50) {
                System.out.println(JSON.toJson(highConcurrentDTO) + ",concurrent:" + concurrent);
            }
        }
    }

    /**抽取需要的数据*/
    private List<HighConcurrentDTO> trans(ESResultDTO resultDTO) {
        Map<String, Object> aggsByTime = (Map) resultDTO.getAggregations().get("aggs_by_time");
        List<Map<String, Object>> buckets = (List) aggsByTime.get("buckets");

        List<HighConcurrentDTO> result = new ArrayList<>();

        for (Map<String, Object> b1 : buckets) {
            Long timeAsString = (Long) b1.get("key");
            Date time = new Date(timeAsString);
            Map<String, Object> groupBySystemId = (Map) b1.get("group_by_system_id");
            List<Map<String, Object>> buckets2 = (List) groupBySystemId.get("buckets");

            for(Map<String, Object> b2 : buckets2) {
                String systemId = (String) b2.get("key");
                Integer totalRequest = (Integer) b2.get("doc_count");
                Map<String, Object> avgDuration = (Map) b2.get("avg_duration");
                Double avgCostMs = (Double) avgDuration.get("value");

                HighConcurrentDTO r = new HighConcurrentDTO();
                r.setTime(time);
                r.setSystemId(systemId);
                r.setTotalRequest(totalRequest);
                r.setAvgCostSeconds(avgCostMs / 1000.0);
                result.add(r);
            }
        }

        return result;
    }

}
