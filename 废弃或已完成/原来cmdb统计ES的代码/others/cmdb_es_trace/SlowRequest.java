package com.nickxie.others.cmdb_es_trace;

import com.nickxie.others.cmdb_es_trace.model.CmdbRequestDTO;
import com.nickxie.others.model.ESHitDTO;
import com.nickxie.others.model.ESResultDTO;
import com.nickxie.others.utils.ESJsonUtils;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import com.pugwoo.wooutils.net.Browser;
import com.pugwoo.wooutils.net.HttpResponse;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 查找慢请求的
 */
@SpringBootTest
public class SlowRequest {

    @Value("${es_url}")
    private String esUrl;

    /**按系统ID来计数*/
    private Map<String, Integer> systemIdCount = new HashMap<>();

    @Test
    public void findSlowRequest() throws Exception {
        String url = esUrl + "index_cmdb-2021.02.10/_search"; // 查询的索引

        int pageSize = 1000;
        int offset = 0;

        int total = 0;

        while(true) {
            String param = ESJsonUtils.getJson("/cmdb_es_trace/slow_request.json");
            param = param.replace("${size}", String.valueOf(pageSize));
            param = param.replace("${from}", String.valueOf(offset));

            Browser browser = new Browser();
            browser.disableGzip();
            browser.addRequestHeader("Authorization", "Basic bG9nc3Rhc2g6bG9nc3Rhc2g=");
            browser.addRequestHeader("Content-Type", "application/json");
            HttpResponse resp = browser.post(url, param.getBytes());

            ESResultDTO result = JSON.parse(resp.getContentString(), ESResultDTO.class);

            List<CmdbRequestDTO> list = trans(result);
            total += list.size();
            // System.out.println("add " + list.size());

            found(list);

            if (list.isEmpty()) {
                break;
            }

            offset += pageSize;
        }

        System.out.println("done:" + total);

        // 打印出所有有问题的系统id
        int totalSlow = 0;
        for (Map.Entry<String, Integer> system : systemIdCount.entrySet()) {
            System.out.println("systemId:" + system.getKey() + ",count:" + system.getValue());
            totalSlow += system.getValue();
        }
        System.out.println("total Slow:" + totalSlow);
    }

    private void systemIdCount(String systemId) {
        Integer integer = systemIdCount.get(systemId);
        if (integer == null) {
            systemIdCount.put(systemId, 1);
        } else {
            systemIdCount.put(systemId, integer + 1);
        }
    }

    /**查找不合理的请求*/
    private void found(List<CmdbRequestDTO> list) {
        for (CmdbRequestDTO request : list) {
            if (request.getCostMs() > 60000) {
                if (request.getOffset() != null && request.getOffset() > 10000) {
                    systemIdCount(request.getSystemId());
                    System.out.println(request.getRawRequest());
                }
            }
        }
    }

    private List<CmdbRequestDTO> trans(ESResultDTO result) {
        List<ESHitDTO> hits = result.getHits().getHits();
        List<CmdbRequestDTO> list = new ArrayList<>();

        for (ESHitDTO hit : hits) {
            Map<String, Object> source = hit.get_source();
            String logId = (String) source.get("log_id");
            Date time = DateUtils.parse((String) source.get("@timestamp"));
            String systemId = source.get("system_id").toString();
            String sceneId = source.get("scene_id").toString();
            String clientIp = (String) source.get("client_ip");
            Integer duration = (Integer) source.get("duration");
            Integer retRows = (Integer) source.get("ret_rows");
            String rawRequest = (String) source.get("request");

            Map<String, Object> requestObj = null;
            try {
                requestObj = (Map) JSON.parse(rawRequest);
            } catch (Exception e) {
            }

            List<String> columns = new ArrayList<>();
            boolean queryTotal = false;
            Integer startIndex = null;
            Integer pageSize = null;

            if (requestObj != null) {
                if (requestObj.get("params") == null || !(requestObj.get("params") instanceof Map)) {
                    continue;
                }
                Map<String, Object> params = (Map) requestObj.get("params");
                Map<String, Object> content = (Map) params.get("content");
                Map<String, Object> resultColumn = (Map) content.get("resultColumn");
                if (resultColumn != null) {
                    for (String key : resultColumn.keySet()) {
                        columns.add(key);
                    }
                }

                Map<String, Object> pagingInfo = (Map) content.get("pagingInfo");
                if (pagingInfo != null) {
                    queryTotal = Objects.equals(1, pagingInfo.get("returnTotalRows"));
                    startIndex = NumberUtils.parseInt(pagingInfo.get("startIndex"));
                    pageSize = NumberUtils.parseInt(pagingInfo.get("pageSize"));
                }
            }

            CmdbRequestDTO requestDTO = new CmdbRequestDTO();
            requestDTO.setLogId(logId);
            requestDTO.setTime(time);
            requestDTO.setSystemId(systemId);
            requestDTO.setSceneId(sceneId);
            requestDTO.setClientIp(clientIp);
            requestDTO.setCostMs(duration);
            requestDTO.setReturnRows(retRows);
            requestDTO.setRawRequest(rawRequest);
            requestDTO.setColumns(columns);
            requestDTO.setPageSize(pageSize);
            requestDTO.setOffset(startIndex);
            requestDTO.setQueryTotal(queryTotal);
            requestDTO.setRequestContentComplete(requestObj != null);

            list.add(requestDTO);
        }

        return list;
    }

}
