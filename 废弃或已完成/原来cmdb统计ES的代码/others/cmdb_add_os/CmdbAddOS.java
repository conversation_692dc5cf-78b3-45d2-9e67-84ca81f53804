package com.nickxie.others.cmdb_add_os;


import com.pugwoo.wooutils.io.IOUtils;
import com.pugwoo.wooutils.string.StringTools;

import java.io.FileInputStream;

public class CmdbAddOS {

    public static void main(String[] args) throws Exception {

        String data = IOUtils.readAllAndClose(new FileInputStream("d:/os.csv"), "utf-8");

        // System.out.println(data);

        String[] strings = StringTools.splitLines(data);
        for (String line : strings) {
            String[] strs = line.split(",");
            //System.out.println(strs[0] + strs[1] + strs[2]);


            System.out.println("curl -X POST -d '{\"params\": {\"content\": {\"requestInfo\": {\"sceneId\": \"1\", \"operator\": \"jenkzhang\", \"systemId\": \"200911151\", \"requestModule\": \"\"}, \"version\": \"1.0\", \"type\": \"Json\", \"actions\": [{\"modify_server_no_approve\": {\"reason\": \"modify by jenkzhang via nickxie\", \"data\": {\"SfwName\": \"" + strs[2] + " \", \"SfwVersion\": \"" + strs[1] + "\"}, \"condition\": {\"SvrAssetId\": \"" + strs[0] + "\"}}}], \"schemeId\": \"server\"}}}' http://api.cmdb.oa.com/api/modify/modify");
        }
    }

}
