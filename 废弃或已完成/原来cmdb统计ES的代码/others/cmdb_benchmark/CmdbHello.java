package com.nickxie.others.cmdb_benchmark;

import com.pugwoo.wooutils.net.Browser;
import com.pugwoo.wooutils.net.HttpResponse;

public class CmdbHello {

    public static void main(String[] args) throws Exception {

        <PERSON><PERSON><PERSON> browser = new <PERSON>rowser();
        browser.disableGzip();

        String data = "{\"params\":{\"content\":{\"schemeId\":\"Server\",\"type\":\"Json\",\"version\":\"1.0\",\"dataFormat\":\"list\",\"requestInfo\":{\"systemId\":\"200911151\",\"sceneId\":\"1\",\"requestModule\":\"\",\"operator\":\"\"},\"resultColumn\":{ \"SvrId\":\"\", \"SvrAssetId\":\"\", \"SvrName\":\"\", \"SvrOperator\":\"\", \"SvrBakOperator\":\"\", \"SvrFirstUseTime\":\"\", \"DeptId\":\"\", \"DeptName\":\"\", \"serverAllIP\":\"\", \"StrMemo\":\"\"},\"pagingInfo\":{\"startIndex\":\"0\",\"pageSize\":\"1000\",\"returnTotalRows\":\"0\"},\"orderBy\":\"\",\"conditionLogical\":\"\",\"searchCondition\":{}}}}";


        String ingress = "http://*************/api/query/get";
        String pod = "http://************:8080/api/query/get";

        long start = System.currentTimeMillis();
        HttpResponse resp = browser.post(ingress, data.getBytes());
        long end = System.currentTimeMillis();
        System.out.println("cost:" + (end - start) + "ms");
        System.out.println(resp.getContentString());

    }

}
