package com.nickxie.others.cmdb_benchmark;

import com.pugwoo.wooutils.net.Browser;
import com.pugwoo.wooutils.net.HttpResponse;
import com.pugwoo.wooutils.task.ExecuteThem;

import java.io.IOException;
import java.util.Random;
import java.util.concurrent.atomic.AtomicInteger;

public class CmdbBenchmark {

    public static void main(String[] args) {

        int THREAD = 50;

        ExecuteThem executeThem = new ExecuteThem(THREAD);

        AtomicInteger count = new AtomicInteger();

        for (int i = 0; i < THREAD; i++) {
            executeThem.add(new Runnable() {
                @Override
                public void run() {

                    while(true) {
                        Browser browser = new Browser();
                        browser.setConnectTimeoutSeconds(60);
                        browser.setReadTimeoutSeconds(180);
                        browser.disableGzip();

                        int pageSize = new Random().nextInt(5000) + 1;

                        String data = "{\"params\":{\"content\":{\"schemeId\":\"Server\",\"type\":\"Json\",\"version\":\"1.0\",\"dataFormat\":\"list\",\"requestInfo\":{\"systemId\":\"200911151\",\"sceneId\":\"1\",\"requestModule\":\"\",\"operator\":\"\"},\"resultColumn\":{ \"SvrId\":\"\", \"SvrAssetId\":\"\", \"SvrName\":\"\", \"SvrOperator\":\"\", \"SvrBakOperator\":\"\", \"SvrFirstUseTime\":\"\", \"DeptId\":\"\", \"DeptName\":\"\", \"StrMemo\":\"\"},\"pagingInfo\":{\"startIndex\":\"0\",\"pageSize\":\"" + pageSize + "\",\"returnTotalRows\":\"0\"},\"orderBy\":\"\",\"conditionLogical\":\"\",\"searchCondition\":{}}}}";

                        try {
                            String ingress = "http://*************/api/query/get";
                            String pod = "http://************:8080/api/query/get";

                            HttpResponse resp = browser.post(ingress, data.getBytes());
                            int a = count.incrementAndGet();
                            int responseCode = resp.getResponseCode();
                            if (responseCode == 200) {
                                System.out.println("result:" + resp.getResponseCode() + ",count:" + a + ",len:" +
                                        resp.getContentString().length());
                            } else {
                                System.err.println("result:" + resp.getResponseCode() + ",count:" + a);
                            }
                        } catch (IOException e) {
                        }
                    }
                }
            });
        }

        executeThem.waitAllTerminate();

    }

}
