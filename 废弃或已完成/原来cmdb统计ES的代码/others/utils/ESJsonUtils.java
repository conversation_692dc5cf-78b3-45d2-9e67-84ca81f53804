package com.nickxie.others.utils;

import com.pugwoo.wooutils.io.IOUtils;

import java.io.InputStream;

/**
 * 获取es post文件和去掉里面的注释的工具
 */
public class ESJsonUtils {

    /**
     * 从classpath读取json请求参数
     * @param classFilePath 必须用绝对路径，/开头
     * @return
     */
    public static String getJson(String classFilePath) {
        InputStream in = ESJsonUtils.class.getResourceAsStream(classFilePath);
        String json = IOUtils.readAll(in, "utf-8");
        json = json.replaceAll("\\/\\/.*", ""); // 去掉 // 后面的注释
        return json;
    }

}
