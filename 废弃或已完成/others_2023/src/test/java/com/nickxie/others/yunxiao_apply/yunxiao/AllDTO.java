package com.nickxie.others.yunxiao_apply.yunxiao;

import lombok.Data;

@Data
public class AllDTO {

    private String region;
    private String regionName;
    private String regionState;
    private String regionAlias;

    private String zone;
    private String zoneId;
    private String zoneState;
    private String zoneName;

    private String instanceFamily;
    private String instanceFamilyName;

    private String instanceModel;

    public String key() {
        return String.join("@", region, zone, instanceFamily, instanceModel);
    }

    public void setRegion(RegionDTO dto) {
        this.region = dto.getRegion();
        this.regionName = dto.getRegionName();
        this.regionState = dto.getRegionState();
        this.regionAlias = dto.getRegionAlias();
    }

    public void setZone(ZoneDTO dto) {
        this.zone = dto.getZone();
        this.zoneId = dto.getZoneId();
        this.zoneName = dto.getZoneName();
        this.zoneState = dto.getZoneState();
    }

    public void setInstanceType(InstanceTypeDTO dto) {
        this.instanceFamily = dto.getInstanceFamily();
        this.instanceFamilyName = dto.getInstanceFamilyName();
    }
}
