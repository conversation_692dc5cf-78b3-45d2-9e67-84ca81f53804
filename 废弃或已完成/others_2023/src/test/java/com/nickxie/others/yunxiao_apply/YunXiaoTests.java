package com.nickxie.others.yunxiao_apply;

import com.fasterxml.jackson.core.type.TypeReference;
import com.nickxie.others.yunxiao_apply.yunxiao.BaseDataDTO;
import com.nickxie.others.yunxiao_apply.yunxiao.PageDataDTO;
import com.nickxie.others.yunxiao_apply.yunxiao.RegionDTO;
import com.pugwoo.wooutils.json.JSON;
import lombok.Data;
import org.junit.jupiter.api.Test;

import java.util.HashMap;

public class YunXiaoTests {

    /**
     * 查询云霄的所有region
     */
    @Test
    public void queryAllRegion() {
        BaseDataDTO<RegionDTO> result = YunXiaoUtil.get("/data360/region", new TypeReference<BaseDataDTO<RegionDTO>>() {
        });
        System.out.println(JSON.toJsonFormatted(result));
    }

    @Data
    public static class OrderDTO {
        private String orderId;
    }

    /**
     * 查询云霄的预约单列表
     */
    @Test
    public void queryYunxiaoOrderList() {
        PageDataDTO<OrderDTO> body = YunXiaoUtil.postForPageData("/rubik/order", new HashMap<>(), OrderDTO.class);
        System.out.println(JSON.toJsonFormatted(body));
    }



}
