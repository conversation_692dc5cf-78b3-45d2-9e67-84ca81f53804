package com.nickxie.others.yunxiao_apply;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nickxie.others.yunxiao_apply.yunxiao.AllDTO;
import com.nickxie.others.yunxiao_apply.yunxiao.BaseDataDTO;
import com.nickxie.others.yunxiao_apply.yunxiao.InstanceModelDTO;
import com.nickxie.others.yunxiao_apply.yunxiao.InstanceTypeDTO;
import com.nickxie.others.yunxiao_apply.yunxiao.MainInstanceTypeDTO;
import com.nickxie.others.yunxiao_apply.yunxiao.PageDataDTO;
import com.nickxie.others.yunxiao_apply.yunxiao.RegionDTO;
import com.nickxie.others.yunxiao_apply.yunxiao.ZoneDTO;
import com.pugwoo.wooutils.json.JSON;
import okhttp3.Call;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Request.Builder;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.springframework.util.CollectionUtils;

import java.math.BigInteger;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.TimeUnit;

public class YunXiaoUtil {

    /**云霄给的key*/
    private static String key = "ak.comd";
    /**云霄给的token*/
    private static String token = "sk.6343e98573a8251cb86cdb02";
    /**api，这个是生产的*/
    private static String urlPrefix = "http://api.yunxiao.vstation.woa.com";
    // 测试环境，用不了
    //private static String urlPrefix = "http://***********";

    private static Proxy proxy = new Proxy(Proxy.Type.HTTP,
            new InetSocketAddress("localhost", 8888));

    private static final OkHttpClient client = new OkHttpClient.Builder()
            .connectTimeout(2, TimeUnit.SECONDS)
            .proxy(proxy)
            .build();
    private static final ObjectMapper mapper = new ObjectMapper();

    private static String sha1(String value) {
        String sha1 = "";
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-1");
            digest.reset();
            digest.update(value.getBytes(StandardCharsets.UTF_8));
            sha1 = String.format("%040x", new BigInteger(1, digest.digest()));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return sha1;
    }

    public static Request.Builder withSecretHeaderBuilder(Request.Builder builder) {
        if (builder == null) {
            builder = new Builder();
        }
        Random random = new Random();
        String randomInt = String.valueOf(random.nextInt(99999) + 1);
        String timestamp = String.valueOf(System.currentTimeMillis() / 1000);
        String sha1 = sha1(timestamp + randomInt + token);

        builder.addHeader("random", randomInt)
                .addHeader("appkey", key)
                .addHeader("timestamp", timestamp)
                .addHeader("signature", sha1);
        return builder;
    }

    public static String rawGet(String uri) {
        try {
            Request.Builder builder = withSecretHeaderBuilder(null);
            String url = urlPrefix + uri;
            Request request = builder.url(url).build();
            Call call = client.newCall(request);
            Response response = call.execute();
            String json = response.body().string();
            response.close();
            return json;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static <T> T get(String uri, TypeReference<T> tTypeReference) {
        try {
            Request.Builder builder = withSecretHeaderBuilder(null);
            String url = urlPrefix + uri;
            Request request = builder.url(url).build();
            Call call = client.newCall(request);
            Response response = call.execute();
            String json = response.body().string();
            response.close();
            return mapper.readValue(json, tTypeReference);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /*
    * POST请求
     */
    public static <T> PageDataDTO<T> postForPageData(String uri, Map<String, Object> param, Class<T> clazz) {
        try {
            Request.Builder builder = withSecretHeaderBuilder(null);
            String url = urlPrefix + uri;
            Request request = builder.url(url)
                    .post(RequestBody.create(MediaType.parse("application/json"),
                            JSON.toJson(param)))
                    .build();
            Call call = client.newCall(request);
            Response response = call.execute();
            String resp = response.body().string();
            response.close();
            return JSON.parse(resp, PageDataDTO.class, clazz);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static BaseDataDTO<RegionDTO> queryAllRegion() {
        return get("/region", new TypeReference<BaseDataDTO<RegionDTO>>() {
        });
    }

    public static BaseDataDTO<ZoneDTO> queryAllZone(String region) {
        return get("/zone?region=" + region, new TypeReference<BaseDataDTO<ZoneDTO>>() {
        });
    }

    public static BaseDataDTO<InstanceTypeDTO> queryAllInstanceType(String zone) {
        return get("/instance-type-family?zone=" + zone, new TypeReference<BaseDataDTO<InstanceTypeDTO>>() {
        });
    }

    public static BaseDataDTO<InstanceModelDTO> queryAllInstanceModel(String zone, String instanceFamily) {
        return get("/instance-type?zone=" + zone + "&instanceFamily=" + instanceFamily, new TypeReference<BaseDataDTO<InstanceModelDTO>>() {
        });
    }

    public static BaseDataDTO<MainInstanceTypeDTO> queryMainInstanceType(){
        return get("/instance-type/main", new TypeReference<BaseDataDTO<MainInstanceTypeDTO>>() {
        });
    }

    public static List<AllDTO> queryAll() {
        BaseDataDTO<RegionDTO> regionAll = queryAllRegion();
        if (regionAll == null || CollectionUtils.isEmpty(regionAll.getData())) {
            return new ArrayList<>();
        }
        List<AllDTO> allDTOS = new LinkedList<>();
        for (RegionDTO regionDTO : regionAll.getData()) {
            BaseDataDTO<ZoneDTO> zoneAll = queryAllZone(regionDTO.getRegion());
            if (zoneAll == null || CollectionUtils.isEmpty(zoneAll.getData())) {
                AllDTO allDTO = new AllDTO();
                allDTO.setRegion(regionDTO);
                allDTOS.add(allDTO);
                continue;
            }
            for (ZoneDTO zoneDTO : zoneAll.getData()) {
                BaseDataDTO<InstanceTypeDTO> instanceTypeAll = queryAllInstanceType(zoneDTO.getZone());
                if (instanceTypeAll == null || CollectionUtils.isEmpty(instanceTypeAll.getData())) {
                    AllDTO allDTO = new AllDTO();
                    allDTO.setRegion(regionDTO);
                    allDTO.setZone(zoneDTO);
                    allDTOS.add(allDTO);
                    continue;
                }
                for (InstanceTypeDTO instanceTypeDTO : instanceTypeAll.getData()) {
                    BaseDataDTO<InstanceModelDTO> instanceModelAll = queryAllInstanceModel(zoneDTO.getZone(),
                            instanceTypeDTO.getInstanceFamily());
                    if (instanceModelAll == null || CollectionUtils.isEmpty(instanceModelAll.getData())) {
                        AllDTO allDTO = new AllDTO();
                        allDTO.setRegion(regionDTO);
                        allDTO.setZone(zoneDTO);
                        allDTO.setInstanceType(instanceTypeDTO);
                        allDTOS.add(allDTO);
                        continue;
                    }
                    for (InstanceModelDTO instanceModelDTO : instanceModelAll.getData()) {
                        AllDTO allDTO = new AllDTO();
                        allDTO.setRegion(regionDTO);
                        allDTO.setZone(zoneDTO);
                        allDTO.setInstanceType(instanceTypeDTO);
                        allDTO.setInstanceModel(instanceModelDTO.getInstanceModel());
                        allDTOS.add(allDTO);
                    }
                }



            }
        }
        return allDTOS;
    }
}
