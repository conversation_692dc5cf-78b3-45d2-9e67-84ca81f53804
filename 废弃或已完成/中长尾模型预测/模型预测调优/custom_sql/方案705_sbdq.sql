select year,month,
    any(month_start_date1) as first_day,
    any(month_end_date1) as last_day,
    '一个地域' as customhouse_title,
    '一个地域' as region,
    '一个地域' as region_name,
    '一个机型' as gins_family,
    sum(case when diff_bill_num>0 then diff_bill_num else 0 end) as new_diff, -- 增量：max-月初，>0部分
    sum(case when diff_bill_num<0 then -diff_bill_num else 0 end) as ret_diff, --   退回：max-月末，>0部分
    sum(cur_core) as last_day_num -- 当前：月末
from (
    select year,month,
    any(month_start_date) as month_start_date1,
    any(month_end_date) as month_end_date1,
    any(customhouse_title) as customhouse_title1,
    any(region) as region1,
    any(region_name) as region_name1,
    any(instance_type) as instance_type1,
    sum(case when stat_time=month_end_date then change_bill_service_core_from_last_month else 0 end) as diff_bill_num,
    sum(case when stat_time=month_end_date then cur_bill_service_core else 0 end) as cur_core
    from dwd_txy_scale_df
    where 1=1

    -- condition 的条件格式不可以改变， WEB 查询会 append  " and sql "


    and cpu_or_gpu = 'CPU' and biz_type = 'cvm'
    and app_role != 'LH'
    and instance_type not like 'RS%' and instance_type not like 'RM%'
    and paymode_range_type in ('包年包月','弹性') and paymode!='5'
    and biz_range_type in ('外部业务')
    and zone_name in ('上海一区','上海七区','上海三区','上海二区','上海五区','上海八区','上海六区','上海四区','东京一区','东京二区','北京一区','北京七区','北京三区','北京二区','北京五区','北京八区','北京六区','北京四区','南京一区','南京三区','南京二区','台北一区','圣保罗一区','多伦多一区','天津三区','天津二区','孟买一区','孟买二区','广州七区','广州三区','广州二区','广州五区','广州八区','广州六区','广州四区','弗吉尼亚一区','弗吉尼亚二区','成都一区','成都二区','新加坡一区','新加坡三区','新加坡二区','新加坡四区','曼谷一区','曼谷二区','欧洲东北一区','法兰克福一区','法兰克福三区','法兰克福二区','深圳一区','深圳三区','深圳二区','深圳四区','硅谷一区','硅谷二区','贵阳一区','重庆一区','雅加达一区','雅加达二区','首尔一区','首尔二区','香港一区','香港三区','香港二区')
    and instance_type not in ('BC1','VSCVMMS','I6t','IA3se','SH2','BF1','BS1','C2','CN2','D1','DR1','DS2','DSW13','DSW14','FX4','HC20','HI1','HI20','HM1','HM20','HS10','HS1A','HS20','I1','I2','IT2','M1','M2','S1','S2','S2ne','SA1','SH1','SHARED','SK1','SN2','SPECIAL','SR1','TS2','TS3','VSCCS','VSCNAS','VSVPN','S5nt','VSVPNDPDK','SA1','SK1','SH1','SR1','OCA2','OC1','HS51','S3','S2','S1','TS3','TS2','HS30','HS20','HS10','S2ne','HS31','SN3ne','S4','TS4','M3','M2','M1','HM20','TM3','M4','C5','C3','C2','HC20','TC3','TC3ne','C4','C4ne','TC4','C4t','TC4t','IT5','IT5c','I3','I2','I1','HI20','IT3','IT3c','ITA3','CN3','CN2','TCN3','TCN3ne','D2','D1','D3','M3','M2','M1','HM20','TM3','M4') -- 黑名单机型

    and app_id not in (1258344706, 1251316161) -- 内部的2个appid，王丽丽给的，目前固定的，排除掉的

    and customer_short_name not in ('伊普西龙信息科技(北京)有限公司','四川链家房地产经纪有限公司','苏州闹海科技有限公司','深圳市腾讯计算机系统有限公司','贝壳找房科技有限公司','滴滴出行','贝壳找房','上海汉涛信息咨询有限公司','德佑房地产经纪有限公司上海第二千三百九十六分公司','京东城市南通','上海菁卡信息技术有限公司','四川链家房地产经纪有限公司丽景路第一分公司','滴滴出行科技有限公司','长春市贝壳房地产经纪有限公司','滴滴鸿易','北京小桔新能源汽车科技有限公司','北京钛氪新媒体科技有限公司','美团点评-黑石ARM','德佑房地产经纪有限公司上海第二千二百九十六分公司','京东物流','杭州青奇科技有限公司','链家视频看房','四川链家房地产经纪有限公司温江鱼凫路分公司','杭州微米','杭州弹指宇宙科技有限公司','车胜','Galacurz','淮安康祥福文化传播有限公司','成都方源房地产咨询服务有限公司','超参数','杭州桔研科技有限公司','贝壳--惠贝','京东云硬件','快手-子公司','京东金融','上海小桁网络科技有限公司','天津三快飞跃科技有限公司','北京桔果软件','广东链家房地产经纪有限公司广州云步分店','三河市链家房产经纪有限公司燕郊第二十二分公司','拼多多','德佑房地产经纪有限公司上海第二千零一十九分公司','超参数子公司','饭否','屏芯','上饶市桔籽科技有限公司','北京京东叁佰陆拾度电子商务有限公司','上海别样红信息技术有限公司','四川快手互联网信息有限公司','德佑房地产经纪有限公司上海第二千三百二十八分公司','乐读-好未来K9','杭州欣霁广告有限公司','德佑房地产经纪有限公司上海第二千三百二十五分公司','好未来-集团','Garena-国内主体','北京灵隆科技有限公司','厦门三快在线科技有限公司','武汉快手多媒体技术有限公司','上海欣星多媒体科技有限公司','安准信息科技(武汉)有限公司','中央广播电视总台','北京链家置地房地产经纪有限公司朝阳第二百零一分公司','北京达佳互联信息技术有限公司深圳分公司','腾讯科技','北京运达无限科技有限公司','天津海贝科技服务有限公司北京分公司','京东新程科技(香港)有限公司','滴滴金科','作业帮','德佑房地产经纪有限公司上海第二千三百二十九分公司','Futu JP','北京乐学帮网络技术有限公司','北京宝宝爱吃餐饮管理有限公司','北京小桔智能汽车科技有限公司','德佑房地产经纪有限公司第四百七十三分公司','杭州小木吉软件科技有限公司','滴滴','Mobileye','德佑房地产经纪有限公司上海第二千四百零三分公司','京东云','江苏华福视频科技有限公司','薯一薯二文化传媒(上海)有限公司','富途香港','荣耀终端有限公司','DIDI GLOBAL PROCUREMENT MANAGEMENT (HK) LIMITED','京东海嘉','德佑房地产经纪有限公司上海第一千四百八十八分公司','美团点评-综合业务','北京骑胜科技有限公司','四川信橙','北京全装美家装饰有限公司','合肥链家宝业房地产经纪有限公司翡翠府邸分公司','德佑房地产经纪有限公司上海第二千二百九十三分公司','上海欣霁广告有限公司','Futu fin','京东大药房','蔚来科技安徽','北京中博科远科技有限公司','北京京东尚科信息技术有限公司','宁波嘉扬科技有限公司','黄小兜','链家广州分公司','德佑房地产经纪有限公司上海第二千四百一十五分公司','广州欣星广告有限公司','跨越','重庆链家房地产经纪有限公司翡翠公园分公司','北京链家置地房地产经纪有限公司西城手帕口南街店','山东天开文化传播有限公司','Moomoo Technologies Inc.','无锡闹海房地产咨询服务有限公司江阴分公司','沈阳链家宝业房地产经纪有限公司赤山路第二分公司','北京轻雀科技有限公司','京东钼媒','小红书-超超世世','德佑房地产经纪有限公司上海第二千三百七十五分公司','德佑房地产经纪有限公司上海第二千三百一十九分公司','小红书武汉分公司','成都西山居世游科技有限公司','北京链家置地房地产经纪有限公司顺义第七十九分公司','滴滴车服','滴滴出行英国实体','快手-新加坡','北京京东世纪贸易有限公司','京东金融IDC','链家贝壳','德佑房地产经纪有限公司上海第二千四百三十八分公司','山东链家房地产经纪有限公司济南第二十七分公司','深圳小桔熊科技有限公司','德佑房地产经纪有限公司上海第二千四百八十分公司','橙心优选(北京)科技发展有限公司','贝壳找房(杭州)科技有限公司','贝壳金服','北京云掣科技有限公司','万链装饰','钱袋宝','北京三快科技有限公司','IEGG','SPARK FINTECH LIMITED','北京京东世纪信息技术有限公司','重庆闹海房江湖信息科技有限公司','德佑房地产经纪有限公司上海第二千二百七十五分公司','京东集团信息安全','西安链家宝业房地产经纪有限公司第四十分公司','泰国哆啦宝','新项目上新','如你所视(北京)科技有限公司','上海路团科技有限公司','阡陌通达','京东7 fresh','Bybit','宽带山','天津海贝科技服务有限公司','合肥链家宝业房地产经纪有限公司九玺花园分公司','北京华艺汇龙网络科技有限公司','武汉链家宏业房地产经纪有限公司金樱街分公司','上海三快省心购科技有限公司','德佑房地产经纪有限公司上海第二千一百三十八分公司','德佑房地产经纪有限公司上海第二千一百零七分公司','京桔新能源汽车科技有限公司','北京欢快科技有限公司','贵州省梵快文化传播有限公司','蔡凯','南京链家房地产经纪有限公司集贤路店','武汉链家宏业房地产经纪有限公司关山三店','德佑房地产经纪有限公司上海第二千三百三十二分公司','南京链家房地产经纪有限公司银河湾花园二店','北京橙心无限科技发展有限公司','惠迪','北京链家置地房地产经纪有限公司福长街店','德佑房地产经纪有限公司上海第二千四百五十二分公司','北京粒粒橙','臻乐尔','北京京东乾石科技有限公司','北京桔财动力','如你之视(北京)科技有限公司','深圳美团优选网络科技有限公司','北京房江湖科技有限公司','美团','富途网络科技（深圳）有限公司','北京小桔国际旅行社','成都链家高策房地产经纪有限公司','德佑房地产经纪有限公司上海第一千八百四十五分公司','云南链家旅居科技服务有限公司','成都众亨源商贸有限公司','长沙快手文化传播有限公司','德佑房地产经纪有限公司上海第一千一百四十六分公司','滴滴公益基金','cloud whale','廊坊市贝壳房地产经纪有限公司','北京理房通支付科技有限公司','小红书','广州库洛科技有限公司','PROXIMA BETA PTE. LIMITED','GOTO','贝壳技术有限公司','东莞链家房地产经纪有限公司虎门云城分店','淮安双馨文化传播有限公司','滴滴云计算有限公司','德佑房地产经纪有限公司上海第三百一十七分公司','荣耀终端','杭州埃米网络科技有限公司','曦昶健康科技(上海)有限公司','深圳美西西餐饮管理有限公司','SNAPVERSE PTE. LTD.','上海融贝云科技有限公司','比特大陆','银川京东互联网医院有限公司','腾讯互动娱乐事业群','德佑房地产经纪有限公司第一百零一分公司','北京橙际科技有限公司','北京菲尔雅肯科技有限公司','北京链家置地房地产经纪有限公司东城第三十七分公司','圆迈贸易','北京嘀嘀无限科技发展有限公司','北京链家房地产经纪有限公司','GGG','北京摩拜科技有限公司','美团点评','重庆闹海科技有限公司','完美世界','泓书信息科技(上海)有限公司','滴滴出行科技有限公司兰州分公司','贝壳小贷','西安链家宝业房地产经纪有限公司第一百五十二分公司','星臻科技(上海)有限公司','北京畅游时代数码技术有限公司','广东链家房地产经纪有限公司','深圳货拉拉科技有限公司','杭州快智科技有限公司','芜湖保时网络科技有限公司','北京链家置地房地产经纪有限公司周庄分公司','SEASUN GAMES CORPORATION LIMITED','完美世界征奇(上海)多媒体科技有限公司','滴图（北京）科技有限公司','acfun','滴滴青行','京东城市（南京）科技有限公司','摩拜','深圳航路旅行科技有限公司','猫酷','天津链家宝业房地产经纪有限公司日华里店','京东数科','大连链家房地产经纪有限公司大华店','厦门榛果假期旅行社有限公司','北京恒久无限科技有限公司','天津跨越科技有限公司','上海三快好物科技有限公司','南京链家房地产经纪有限公司湖城艺境店','合肥链家宝业房地产经纪有限公司御景城分公司','广州翼之声科技有限公司','北京长亭科技有限公司','小白租赁','北京航迹科技有限公司','猿题库','WECHAIN FINTECH PTE. LTD.','西山居','北京红番茄联众通信技术有限公司','链家（贝壳金控）','ELEMENTARY INNOVATION PTE.LTD','上海德佑物业顾问有限公司','天津舒行科技有限公司','贝壳找房(珠海)科技有限公司','滴滴商业服务有限公司','江苏京东旭科信息技术有限公司','贵州省梵心灵指信息技术有限公司','快手','北京算力大陆科技有限公司','重庆小木吉网络科技有限公司','大连链家房地产经纪有限公司华府黄河路店','北京三快在线科技有限公司','伊普西龙信息科技（上海）有限公司','北京快智','天津卡尔狗科技有限公司','腾讯游戏(IEG CDN)','成都磁力引擎传媒有限公司','付费通','成都遂意文化传播有限公司','德佑房地产经纪有限公司第一百四十七分公司','四川链家房地产经纪有限公司湖秀一路第一分公司','链家网','北京再造科技有限公司','小红书相关主体','张家口高策房地产经纪有限公司','光年之外','小桔畅行（天津）科技有限公司','超参数科技(深圳)有限公司','北京京东至康综合门诊部有限公司','宿迁际晖','北京小桔科技有限公司','嘀嘀出行','互诚信息','豪腾嘉科','天津屋客网络科技有限公司','中安风尚','京东回收')


    and (year,month,uin,zone_name,instance_type) global not in (select year,month,customer_uin,zone_name,instance_type  from cloud_demand.ppl_forecast_input_excluded_spike where batch_uuid='77aa75c128414daf9cc4840b1b29d4e6') -- 剔除毛刺

    and stat_time < :predictDate

    -- performance optimize
    and stat_time in (
    SELECT toDate(formatDateTime(subtractDays(addMonths(toDate('2020-01-01'), number + 1), 1), '%Y-%m-%d')) AS last_day_of_month
    FROM numbers(dateDiff('month', toDate('2020-01-01'), toDate('2050-12-31')) + 1)
    )

    -- 因为数据量太大，导致ck查询超过内存限制，因此分阶段查询

    -- 下面 WEB_CONDITION 是给版本ppl 用的，不可删除
    /*${WEB_CONDITION}*/


    group by year,month,uin,zone_name,instance_model
    )
group by year,month--,gins_family,region_name