package com.pugwoo.dboperate.archived.模型预测调优;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dboperate.entity.PplForecastInputCompensationDO;
import com.pugwoo.dboperate.entity.PplForecastPredictCompensationLogDO;
import com.pugwoo.dboperate.entity.PplForecastPredictTaskDO;
import com.pugwoo.wooutils.collect.ListUtils;
import jakarta.annotation.Resource;
import org.apache.logging.log4j.util.Strings;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;
import java.util.Map;

@SpringBootTest
public class 一次性生产运维_重构补偿表 {

    @Resource
    private DBHelper cloudDemandIdcDBHelper;

    @Test
    public void test() {
        List<String> categoryList = cloudDemandIdcDBHelper.getRaw(String.class,
                "select distinct category from ppl_forecast_input_compensation where compensation_type='INPUT' and is_enable=1 and deleted=0");

        for (String category : categoryList) {
            List<PplForecastPredictTaskDO> tasks = cloudDemandIdcDBHelper.getAll(PplForecastPredictTaskDO.class,
                    "where category like ? and is_enable=1 order by id desc", category + "%");
            if (ListUtils.isEmpty(tasks)) {
                continue;
            }

            List<PplForecastInputCompensationDO> compensations = cloudDemandIdcDBHelper.getAll(PplForecastInputCompensationDO.class,
                    "where category=? and compensation_type='INPUT' and is_enable=1 and deleted=0", category);
            // 填充一下compensations的customhouse_title
            fillCompensationCustomhouseTitle(compensations, tasks.getFirst().getId());

            for (PplForecastPredictTaskDO task : tasks) {
                List<PplForecastPredictCompensationLogDO> logs = ListUtils.transform(
                        compensations, compensation -> transCompensation(compensation, task.getId()));
                cloudDemandIdcDBHelper.insertBatchWithoutReturnId(logs);

                System.out.println("handle done:" + task.getCategory() + ",taskId:" + task.getId());
            }

        }
    }

    private void fillCompensationCustomhouseTitle(List<PplForecastInputCompensationDO> compensations,
                                                  Long taskId) {
        List<Map> list = cloudDemandIdcDBHelper.getRaw(Map.class,
                "select distinct region_name,customhouse_title from ppl_forecast_input_detail where task_id=?", taskId);
        Map<String, String> map = ListUtils.toMap(list, o -> o.get("region_name").toString(), o -> o.get("customhouse_title").toString());
        for (PplForecastInputCompensationDO compensation : compensations) {
            if (Strings.isBlank(compensation.getCustomhouseTitle())) {
                compensation.setCustomhouseTitle(map.get(compensation.getRegionName()));
            }
        }
    }

    private static PplForecastPredictCompensationLogDO transCompensation(PplForecastInputCompensationDO compensation,
                                                                         Long taskId) {
        PplForecastPredictCompensationLogDO pplForecastPredictCompensationLogDO = new PplForecastPredictCompensationLogDO();
        pplForecastPredictCompensationLogDO.setTaskId(taskId);
        pplForecastPredictCompensationLogDO.setSourceCompensationId(compensation.getId());
        pplForecastPredictCompensationLogDO.setCompensationType(compensation.getCompensationType());
        pplForecastPredictCompensationLogDO.setCompensationSource(compensation.getCompensationSource());
        pplForecastPredictCompensationLogDO.setCategory(compensation.getCategory());
        pplForecastPredictCompensationLogDO.setYear(compensation.getYear());
        pplForecastPredictCompensationLogDO.setMonth(compensation.getMonth());
        pplForecastPredictCompensationLogDO.setGinsFamily(compensation.getGinsFamily());
        pplForecastPredictCompensationLogDO.setRegionName(compensation.getRegionName());
        pplForecastPredictCompensationLogDO.setCustomhouseTitle(compensation.getCustomhouseTitle());
        pplForecastPredictCompensationLogDO.setCoreNum(compensation.getCoreNum());
        pplForecastPredictCompensationLogDO.setSeqType(compensation.getSeqType());
        pplForecastPredictCompensationLogDO.setNote(compensation.getNote());
        return pplForecastPredictCompensationLogDO;
    }

}
