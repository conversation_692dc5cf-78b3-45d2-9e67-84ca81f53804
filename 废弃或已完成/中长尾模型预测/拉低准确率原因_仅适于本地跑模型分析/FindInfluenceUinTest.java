package com.pugwoo.dboperate.archived.拉低准确率原因_仅适于本地跑模型分析;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dboperate.common.ForecastCommonService;
import com.pugwoo.dboperate.forecast_compute.entity.ForecastComputeTaskInputTestDatasetDO;
import com.pugwoo.dboperate.archived.中长尾预测.ForecastTest;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import lombok.Data;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 新版：直接找到影响准确率的uin
 * 输出的表格：对于给定的taskId
 * 客户、地域、机型、拉低当月准确率、历史最近3个月平均执行量、当月执行量、拉低原因（突增、突减）
 */
@SpringBootTest
public class FindInfluenceUinTest {

    @Resource
    private DBHelper cloudDemandCommonDevDBHelper;
    @Resource
    private ForecastCommonService forecastCommonService;

    private double regionNameInstanceTypeLowValve = -0.1; // 扫描拉低准确率的阈值，小于阈值的加入

    private boolean isEnableBlackList = true; /*计算准确率时，是否排除掉黑名单机型，目前页面是排除了的*/

    @Test
    public void findInfluence() {
        Integer taskId = 23096;

        List<LowAccuracyCaseDTO> lowCases = findLowCase(taskId);

        // 打印出结果
        System.out.println("年,月,客户,地域,机型,拉低准确率,最近3个月平均执行量,当月执行量,拉低原因");
        for (LowAccuracyCaseDTO low : lowCases) {
            System.out.println(low.getYear() + "," + low.getMonth() + "," +
                    low.getUin() + "," + low.getRegionName() + "," + low.getInstanceType() + "," + low.getLowAccuracyRate() + "," +
                    low.getLastThreeMonthCore() + "," + low.getCurrentMonthCore() + "," + low.getReason());
        }
        System.out.println("total:" + NumberUtils.sum(lowCases, o -> o.getLowAccuracyRate()));
        System.out.println("total count:" + lowCases.size());
    }

    private List<LowAccuracyCaseDTO> findLowCase(Integer taskId) {
        cloudDemandCommonDevDBHelper.setSlowSqlWarningValve(1000000);

        ForecastTest.CalAccuracyRateResult base = calAccuracyRate(taskId, null);// 基准的准确率

        // 目前测试集只有一个月，所以测试集里的任意一个元素就是测试的年月
        if (ListUtils.isEmpty(base.getTestDataset())) {
            return new ArrayList<>();
        }
        Integer year = DateUtils.getYear(base.getTestDataset().get(0).getDate());
        Integer month = DateUtils.getMonth(base.getTestDataset().get(0).getDate());

        for (ForecastTest.ForecastComputeTaskInputTestDatasetVO combination : base.getTestDataset()) {
            BigDecimal accuracyRate = calAccuracyRate(taskId,
                    " and !(dim1='" + combination.getDim1() +
                            "' and dim2='" + combination.getDim2() + "')").getAccuracyRate(); // 剔除
            BigDecimal diff =base.getAccuracyRate().subtract(accuracyRate);

            combination.setGlobalAccuracyRate(base.getAccuracyRate().doubleValue());
            combination.setGlobalAccuracyRateIfRemove(accuracyRate.doubleValue());
            combination.setDiffAccuracyRate(diff.doubleValue());
        }

        List<ForecastTest.ForecastComputeTaskInputTestDatasetVO> lowAccuracyCombine =
                ListUtils.filter(base.getTestDataset(), o -> o.getDiffAccuracyRate() <= regionNameInstanceTypeLowValve);

        List<LowAccuracyCaseDTO> lowCases = new ArrayList<>();
        // 对于每一个拉低的结果

        for (ForecastTest.ForecastComputeTaskInputTestDatasetVO combination : lowAccuracyCombine) {
            boolean isIncrease = combination.getValue().compareTo(combination.getPredictValue()) > 0;
            String reason = isIncrease ? "突增" : "突减";
            BigDecimal gapCore = isIncrease ? combination.getValue().subtract(combination.getPredictValue()) :
                    combination.getPredictValue().subtract(combination.getValue()); // 这个值是正数
            double diffAccuracyRate = combination.getDiffAccuracyRate(); // 总拉低的准确率
            double leftDiffAccuracyRate = diffAccuracyRate; // 分摊剩余的拉低准确率，这个是个负数

            // 然后读取当前地域+机型大类维度下的所有uin 最近3个月的平均值
            List<UinAndValue> lastThreeMonth = cloudDemandCommonDevDBHelper.getRaw(UinAndValue.class,
                    "SELECT uin,AVG(VALUE) AS VALUE\n" +
                            "FROM forecast_compute_task_input_uin WHERE task_id=?\n" +
                            "AND dim1=? AND dim2=? \n" +
                            "AND DATE IN \n" +
                            "(SELECT DATE FROM (SELECT DISTINCT DATE FROM forecast_compute_task_input WHERE task_id=? ORDER BY DATE DESC LIMIT 3) t) \n" +
                            "GROUP BY uin", taskId, combination.getDim1(), combination.getDim2(), taskId);

            // 读取当月的地域+机型大类的所有uin的当月值
            List<UinAndValue> currentMonth = cloudDemandCommonDevDBHelper.getRaw(UinAndValue.class,
                    "SELECT uin,VALUE FROM forecast_compute_task_input_test_dataset_uin WHERE task_id=? AND dim1=? AND dim2=?",
                    taskId, combination.getDim1(), combination.getDim2());

            List<UinAndValue> merged = ListUtils.merge(lastThreeMonth, currentMonth, o -> o.getUin().toString(),
                    o -> o.getUin().toString(), (left, right) -> {
                        UinAndValue result = new UinAndValue();
                        BigDecimal thisMonthValue = ListUtils.isEmpty(right) ? BigDecimal.ZERO : right.get(0).getValue();
                        BigDecimal lastThreeMonthValue = ListUtils.isEmpty(left) ? BigDecimal.ZERO : left.get(0).getValue();
                        result.setUin(ListUtils.isEmpty(left) ? right.get(0).getUin() : left.get(0).getUin());
                        result.setValue(thisMonthValue.subtract(lastThreeMonthValue));
                        result.setThisMonthValue(thisMonthValue);
                        result.setLastThreeMonthValue(lastThreeMonthValue);
                        return result;
                    });

            // 对于突增的，只要merged里面的新增；反之，只要merged里面的减少的
            merged = ListUtils.filter(merged, o -> isIncrease ? o.getValue().compareTo(BigDecimal.ZERO) > 0 :
                    o.getValue().compareTo(BigDecimal.ZERO) < 0);
            // merge按照abs绝对值从大到小排列
            ListUtils.sortDescNullLast(merged, o -> Math.abs(o.getValue().doubleValue()));

            for (UinAndValue uv : merged) {
                LowAccuracyCaseDTO dto = new LowAccuracyCaseDTO();
                dto.setYear(year);
                dto.setMonth(month);
                dto.setUin(uv.getUin());
                dto.setRegionName(combination.getDim2());
                dto.setInstanceType(combination.getDim1());

                double diffRate = diffAccuracyRate *
                        Math.min(1, Math.abs(uv.getThisMonthValue().doubleValue() - uv.getLastThreeMonthValue().doubleValue()) / gapCore.doubleValue());
                diffRate = Math.max(diffRate, leftDiffAccuracyRate); // 它是个负数，所以用max

                dto.setLowAccuracyRate(diffRate);
                dto.setReason(reason);
                dto.setLastThreeMonthCore(uv.getLastThreeMonthValue());
                dto.setCurrentMonthCore(uv.getThisMonthValue());
                lowCases.add(dto);

                leftDiffAccuracyRate += -diffRate;
                if (leftDiffAccuracyRate >= 0) {
                    break;
                }
            }
        }

        // 完成，剔除影响小于阈值十分之一的，按逆序排列
        lowCases = ListUtils.filter(lowCases, o -> o.getLowAccuracyRate() <= regionNameInstanceTypeLowValve / 10); // 负数，所以是小于
        ListUtils.sortDescNullLast(lowCases, o -> Math.abs(o.getLowAccuracyRate()));

        return lowCases;
    }

    public ForecastTest.CalAccuracyRateResult calAccuracyRate(Integer taskId, String testDatasetExtraWhere) {
        // 1. 先读取测试集
        // 【特别说明】
        // 1) 现在是以真实值作为权重，也就是说，如果有预测，实际结果没有出现，还是认为准的，所以这个计算是按这种算法来简化了
        // 2) 现在是每次只测试一个月，所以直接对结果进行加权，不用分月份
        List<ForecastTest.ForecastComputeTaskInputTestDatasetVO> testDataset = cloudDemandCommonDevDBHelper.getAll(
                ForecastTest.ForecastComputeTaskInputTestDatasetVO.class,
                "where task_id=? " + (testDatasetExtraWhere == null ? "" : testDatasetExtraWhere), taskId);

        // 2. 再读取预测结果
        List<ForecastTest.PredictResultDTO> predictResults = cloudDemandCommonDevDBHelper.getRaw(ForecastTest.PredictResultDTO.class,
                "SELECT a.date,a.value,b.dim1,b.dim2,b.dim3,b.dim4,b.dim5\n" +
                        "FROM `forecast_compute_task_run_output` a\n" +
                        "LEFT JOIN `forecast_compute_task_run` b\n" +
                        "ON a.`task_run_id`=b.`id`\n" +
                        "WHERE a.task_id=?", taskId);

        // 3.1 合并数据
        testDataset = ListUtils.merge(testDataset, predictResults,
                o -> o.groupKey(), o -> o.groupKey(),
                (test, predict) -> {
                    if (ListUtils.isNotEmpty(test) && ListUtils.isNotEmpty(predict)) {
                        test.get(0).setPredictValue(predict.get(0).getValue());
                        return test.get(0);
                    } else {
                        return null;
                    }
                });
        testDataset = ListUtils.filter(testDataset, o -> o != null);

        // 3.2 过滤黑名单机型
        if (isEnableBlackList) {
            List<String> blackList = forecastCommonService.getBlacklistInstanceType();
            testDataset = ListUtils.filter(testDataset, o -> !blackList.contains(o.getDim1()));
        }

        // 4. 计算准确率
        ListUtils.forEach(testDataset, o -> {
            BigDecimal min = NumberUtils.min(o.getPredictValue(), o.getValue());
            BigDecimal max = NumberUtils.max(o.getPredictValue(), o.getValue());
            if (max.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal rate = NumberUtils.divide(min.multiply(BigDecimal.valueOf(100)), max, 6);
                o.setAccuracyRate(rate.doubleValue());
            } else {
                BigDecimal rate = BigDecimal.valueOf(100);
                o.setAccuracyRate(rate.doubleValue());
            }
        });

        // 5. 输出准确率，加权【现在是每次只测试一个月，所以直接对结果进行加权，不用分月份】
        BigDecimal total = NumberUtils.sum(testDataset, ForecastComputeTaskInputTestDatasetDO::getValue);
        ListUtils.forEach(testDataset, o -> {
            double rate = NumberUtils.divide(o.getValue(), total, 6).multiply(BigDecimal.valueOf(o.getAccuracyRate())).doubleValue();
            o.setWeightedAccuracyRate(rate);
        });

        ForecastTest.CalAccuracyRateResult result = new ForecastTest.CalAccuracyRateResult();
        result.setTestDataset(testDataset);
        result.setAccuracyRate(NumberUtils.sum(testDataset, o -> o.getWeightedAccuracyRate()));

        return result;
    }

    @Data
    public static class LowAccuracyCaseDTO {
        private Integer year;
        private Integer month;
        private Long uin;
        private String regionName;
        private String instanceType;
        private Double lowAccuracyRate; // 拉低的准确率
        private BigDecimal lastThreeMonthCore;
        private BigDecimal currentMonthCore;
        private String reason; // 突增、突减
    }

    @Data
    public static class UinAndValue {
        @Column("uin")
        private Long uin;
        @Column("value")
        private BigDecimal value;

        private BigDecimal thisMonthValue;
        private BigDecimal lastThreeMonthValue;
    }
}
