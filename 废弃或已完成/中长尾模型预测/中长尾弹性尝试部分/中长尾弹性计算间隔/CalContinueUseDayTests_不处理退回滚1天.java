package com.pugwoo.dboperate.archived.中长尾弹性尝试部分.中长尾弹性计算间隔;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import com.pugwoo.wooutils.string.StringTools;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@SpringBootTest
public class CalContinueUseDayTests_不处理退回滚1天 {

    @Resource
    private DBHelper ckStdCrpNewIdcDBHelper;

    private void fixData(List<ScaleDTO> list) {
        for (ScaleDTO scale : list) {
            if (scale.getNewCore().compareTo(BigDecimal.ZERO) > 0
               && scale.getNewCore().compareTo(scale.getRetCore()) == 0
               && (scale.getNewCore().subtract(BigDecimal.valueOf(scale.getNewCore().intValue())).compareTo(BigDecimal.ZERO)) == 0) {
                scale.setNewCore(BigDecimal.ZERO);
                scale.setRetCore(BigDecimal.ZERO);
            }
        }
    }

    @Test
    public void test() {
        // 1. 假设现在要计算5-7月平均使用天数，那么数据应该从5月开始，一直到8月1号（7月31号退的会体现在8月1号）
        List<ScaleDTO> all = ckStdCrpNewIdcDBHelper.getAll(ScaleDTO.class,
                " and stat_time between '2023-05-01' and '2023-08-01'");

        // 1.1 对数据进行修正，如果newCore==retCore且newCore>0且newCore为整数，则都置为0
        fixData(all);

        // 2. 按照uin,region_name,zone_name,instance_model,app_role,demand_type分组
        Map<String, List<ScaleDTO>> groups = ListUtils.toMapList(all, o -> StringTools.join("@", o.getAppid(), o.getRegionName(), o.getZoneName(),
                o.getInstanceModel(), o.getAppRole()), o -> o);

        // 3. 对于每一组，对退回进行预处理，最后移除掉8月1号的数据
        for (List<ScaleDTO> group : groups.values()) {
//            System.out.println(group.get(0).getAppid() +
//                    group.get(0).getRegionName() +
//                    group.get(0).getZoneName() +
//                    group.get(0).getInstanceModel() +
//                    group.get(0).getAppRole() +
//                    ":" + group.size());

            // 按照stat_time排序
            ListUtils.sortAscNullLast(group, o -> o.getStatTime());
            // 对退回做处理：时间从后往前，退回都往前滚一天，如果一个退回已经被退回覆盖过了，则不再往前滚

//            for (int i = group.size() - 1; i >=1; i--) {
//                ScaleDTO dto = group.get(i);
//                if (dto.isAddRet()) {
//                    continue;
//                }
//                if (dto.getRetCore() != null && dto.getRetCore().compareTo(BigDecimal.ZERO) > 0) {
//                    // 找到前一个值，看看日期是不是刚好当前的前一天
//                    ScaleDTO pre = group.get(i - 1);
//                    if (pre.getStatTime().equals(dto.getStatTime().minusDays(1))) {
//                        // 如果是，那么前一个值的cur_core要加上这个ret_core
//                        pre.setRetCore(pre.getRetCore().add(dto.getRetCore()));
//                        pre.setAddRet(true);
//                        dto.setRetCore(BigDecimal.ZERO);
//                    } else {
//                        // 只有一处有问题：1317898848硅谷硅谷一区S3.MEDIUM4官网领用，7月4号的数据，量只有4核，不影响结果，先忽略
////                        System.err.println("数据异常" + group.get(0).getAppid() +
////                                group.get(0).getRegionName() +
////                                group.get(0).getZoneName() +
////                                group.get(0).getInstanceModel() +
////                                group.get(0).getAppRole());
//                    }
//                }
//            }

            // 最后，移除8月1号的数据
//            group.removeIf(o -> DateUtils.formatDate(o.getStatTime()).equals("2023-08-01"));
        }

        // 4. 对于每一组，从前到后处理退回，对于每一个退回，由后往前找到匹配的新增
//        AtomicInteger hasLeft = new AtomicInteger(0);
//        AtomicInteger noLeft = new AtomicInteger(0);

        for (List<ScaleDTO> group : groups.values()) {
            for (int i = 0; i <group.size(); i++) {
                ScaleDTO dto = group.get(i);

                // 【特殊逻辑】月末的认为全退 【不可以这样认为，因为强制认为月末退，会导致强制缩短实际的使用时长】
                //if (dto.getStatTime().equals(DateUtils.parseLocalDate("2023-07-31"))) {
                //    dto.setRetCore(dto.getCurCore());
                //}
                if (dto.getRetCore() != null && dto.getRetCore().compareTo(BigDecimal.ZERO) > 0) {
                    BigDecimal retCore = dto.getRetCore();
                    LocalDate lastDate = null;
                    for (int j = i - 1; j >= 0; j--) { // 这里应该从i-1开始，因为前面没有处理了退回提前1天
                        ScaleDTO pre = group.get(j);
                        if (pre.getNewCore() != null && pre.getNewCore().compareTo(BigDecimal.ZERO) > 0) {
                            if (pre.getNewCore().compareTo(retCore) >= 0) {
                                // 如果新增大于等于退回，那么退回就完全匹配了
                                pre.setNewCore(pre.getNewCore().subtract(retCore));
                                retCore = BigDecimal.ZERO;
                                lastDate = pre.getStatTime();
                                break;
                            } else {
                                // 部分匹配
                                retCore = retCore.subtract(pre.getNewCore());
                                pre.setNewCore(BigDecimal.ZERO);
                                lastDate = pre.getStatTime();
                            }
                        }
                    }
                    // 如果retCore还大于0，那就取lastDate为最远的日期了
                    if (lastDate == null) {
                        lastDate = group.get(0).getStatTime();
                    }

                    dto.setMatchedNewDate(lastDate);

                    // 只统计7月的
//                    if (dto.getStatTime().compareTo(LocalDate.of(2023, 7, 1)) >= 0) {
//                        if (retCore.compareTo(BigDecimal.ZERO) > 0) {
//                            hasLeft.incrementAndGet();
//                            // System.err.println("匹配过后仍有剩余:" + JSON.toJson(dto));
//                        } else {
//                            noLeft.incrementAndGet();
//                            //  System.out.println("匹配过后没有剩余:" + JSON.toJson(dto));
//                        }
//                    }
                }
            }
        }

//        System.out.println("有剩余的退回:" + hasLeft.get());
//        System.out.println("没有剩余的退回:" + noLeft.get());

        // 5. 输出
        Map<Integer, List<BigDecimal>> dayUse = new HashMap<>();
        for (List<ScaleDTO> group : groups.values()) {
            for (ScaleDTO dto : group) {
                if (!dto.getStatTime().isBefore(LocalDate.of(2023, 7, 1))) {
                    if (dto.getRetCore().compareTo(BigDecimal.ZERO) > 0) {
                        Integer diff = diffDay(dto.getMatchedNewDate(), dto.getStatTime());
//                        System.out.println(dto.getAppid() +
//                                dto.getRegionName() +
//                                dto.getZoneName() +
//                                dto.getInstanceModel() +
//                                dto.getAppRole() +
//                                ":" + "退回核天:" + dto.getRetCore() + ",使用天数:" +
//                                diff);

                        // 大于31天的，按照31天算
                        if (diff > 31) {
                            diff = 31;
                        }

                        List<BigDecimal> bigDecimals = dayUse.computeIfAbsent(diff, k -> new ArrayList<>());

                        // 对于使用1天的，核天要换成峰值核
//                        if (diff.equals(1)) {
//                            int ret = dto.getRetCore().divide(BigDecimal.valueOf(dto.getInstanceModelCpu()), 0, BigDecimal.ROUND_UP)
//                                    .multiply(BigDecimal.valueOf(dto.getInstanceModelCpu())).intValue();
//                            bigDecimals.add(BigDecimal.valueOf(ret));
//                        } else {
//                            bigDecimals.add(dto.getRetCore());
//                        }
                        bigDecimals.add(dto.getRetCore());
                    }
                }
            }
        }

        for (Map.Entry<Integer, List<BigDecimal>> entry : dayUse.entrySet()) {
            System.out.println(entry.getKey() + "," + NumberUtils.sum(entry.getValue()));
        }
    }


    private static int diffDay(LocalDate d1, LocalDate d2) {
        return DateUtils.diffDays(DateUtils.parse(DateUtils.formatDate(d1)),
                DateUtils.parse(DateUtils.formatDate(d2)));
    }

}
