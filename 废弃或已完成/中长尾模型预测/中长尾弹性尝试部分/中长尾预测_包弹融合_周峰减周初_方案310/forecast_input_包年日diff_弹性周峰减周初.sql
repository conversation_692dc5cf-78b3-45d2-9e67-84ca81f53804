select stime,region_name,gins_family,
       ${predictCore}
   -- sum(new_cores) as cores -- 新增
    -- sum(ret_cores) as cores -- 退回
from
(
-- 不区分地域和可用区
select  week_end_date as stime, -- 注意这里不要取stat_time，因为日期可能不完全
        -- 默认用机型收敛
        region_name as region_name,
        ${instanceTypeMergeCaseWhen} as gins_family,

  sum (case when change_bill_service_core>0 then change_bill_service_core else 0 end) as new_cores, -- 新增
  sum (case when change_bill_service_core<0 then -change_bill_service_core else 0 end) as ret_cores -- 退回

from dwd_txy_scale_df
where paymode_range_type !='弹性'
  and customer_tab_type in ('中长尾客户','报备客户')
  and zone_name in ('上海一区','上海七区','上海三区','上海二区','上海五区','上海八区','上海六区','上海四区','东京一区','东京二区','北京一区','北京七区','北京三区','北京二区','北京五区','北京八区','北京六区','北京四区','南京一区','南京三区','南京二区','台北一区','圣保罗一区','多伦多一区','天津三区','天津二区','孟买一区','孟买二区','广州七区','广州三区','广州二区','广州五区','广州八区','广州六区','广州四区','弗吉尼亚一区','弗吉尼亚二区','成都一区','成都二区','新加坡一区','新加坡三区','新加坡二区','新加坡四区','曼谷一区','曼谷二区','欧洲东北一区','法兰克福一区','法兰克福三区','法兰克福二区','深圳一区','深圳三区','深圳二区','深圳四区','硅谷一区','硅谷二区','贵阳一区','重庆一区','雅加达一区','雅加达二区','首尔一区','首尔二区','香港一区','香港三区','香港二区')
  and cpu_or_gpu = 'CPU'
  and biz_type = 'cvm'
  and app_role!='LH'
  and instance_type not like 'RS%' and instance_type not like 'RM%'

    ${blackInstanceType} -- 黑名单机型

    ${timeRange} -- 时间范围
group by week_end_date
        , region_name
        , ${instanceTypeMergeCaseWhen}

----------------------------- 以上是包年包月日diff
union all
----------------------------- 以下是弹性
select  week_end_date as stime,
    region_name,${instanceTypeMergeCaseWhen} as gins_family,
    sum(case when new_core>0 then new_core else 0 end) as new_cores, -- 增量：max-月初，>0部分
    sum(case when ret_core>0 then ret_core else 0 end) as ret_cores --   退回：max-月末，>0部分
    from
  (
    select week_end_date,
    zone_name,any(region_name) as region_name,instance_type,
    sum(for_new_core) as new_core, sum(for_ret_core) as ret_core
    from
    ( -- 周峰
    select week_end_date1 as week_end_date, zone_name,any(region_name1) as region_name,
    instance_model,any(instance_type1) as instance_type,
    max(cur_bill_service_core1) as for_new_core, max(cur_bill_service_core1) as for_ret_core
    from
    (
    select stat_time, max(week_end_date) as week_end_date1,
    zone_name,any(region_name) as region_name1,instance_model,any(instance_type) as instance_type1,
    sum(cur_bill_service_core) as cur_bill_service_core1
    from dwd_txy_scale_df
    where cpu_or_gpu = 'CPU'
    and biz_type = 'cvm'
    and app_role!='LH'
    and instance_type not like 'RS%' and instance_type not like 'RM%'
    and customer_tab_type in ('中长尾客户','报备客户')
    and zone_name in ('上海一区','上海七区','上海三区','上海二区','上海五区','上海八区','上海六区','上海四区','东京一区','东京二区','北京一区','北京七区','北京三区','北京二区','北京五区','北京八区','北京六区','北京四区','南京一区','南京三区','南京二区','台北一区','圣保罗一区','多伦多一区','天津三区','天津二区','孟买一区','孟买二区','广州七区','广州三区','广州二区','广州五区','广州八区','广州六区','广州四区','弗吉尼亚一区','弗吉尼亚二区','成都一区','成都二区','新加坡一区','新加坡三区','新加坡二区','新加坡四区','曼谷一区','曼谷二区','欧洲东北一区','法兰克福一区','法兰克福三区','法兰克福二区','深圳一区','深圳三区','深圳二区','深圳四区','硅谷一区','硅谷二区','贵阳一区','重庆一区','雅加达一区','雅加达二区','首尔一区','首尔二区','香港一区','香港三区','香港二区')
    and paymode_range_type='弹性' and paymode='2'

    ${blackInstanceType} -- 黑名单机型

    ${timeRange} -- 时间范围

    group by stat_time,zone_name,instance_model
    )
    group by week_end_date1, zone_name, instance_model

    union all

    select  week_end_date as week_end_date1,
    zone_name,any(region_name) as region_name1,instance_model, any(instance_type) as instance_type1,
    -sum(case when stat_time=week_start_date then cur_bill_service_core else 0 end) as for_new_core,
    -sum(case when stat_time=week_end_date then cur_bill_service_core else 0 end) as for_ret_core
    from dwd_txy_scale_df
    where cpu_or_gpu = 'CPU'
    and biz_type = 'cvm'
    and app_role!='LH'
    and instance_type not like 'RS%' and instance_type not like 'RM%'
    and customer_tab_type in ('中长尾客户','报备客户')
    and zone_name in ('上海一区','上海七区','上海三区','上海二区','上海五区','上海八区','上海六区','上海四区','东京一区','东京二区','北京一区','北京七区','北京三区','北京二区','北京五区','北京八区','北京六区','北京四区','南京一区','南京三区','南京二区','台北一区','圣保罗一区','多伦多一区','天津三区','天津二区','孟买一区','孟买二区','广州七区','广州三区','广州二区','广州五区','广州八区','广州六区','广州四区','弗吉尼亚一区','弗吉尼亚二区','成都一区','成都二区','新加坡一区','新加坡三区','新加坡二区','新加坡四区','曼谷一区','曼谷二区','欧洲东北一区','法兰克福一区','法兰克福三区','法兰克福二区','深圳一区','深圳三区','深圳二区','深圳四区','硅谷一区','硅谷二区','贵阳一区','重庆一区','雅加达一区','雅加达二区','首尔一区','首尔二区','香港一区','香港三区','香港二区')
    and paymode_range_type='弹性' and paymode='2'

    ${blackInstanceType} -- 黑名单机型

    ${timeRange} -- 时间范围

    group by zone_name,instance_model, week_end_date
    )
    group by zone_name,instance_type,week_end_date
 )
 group by region_name,week_end_date,${instanceTypeMergeCaseWhen}

) group by stime,region_name,gins_family
