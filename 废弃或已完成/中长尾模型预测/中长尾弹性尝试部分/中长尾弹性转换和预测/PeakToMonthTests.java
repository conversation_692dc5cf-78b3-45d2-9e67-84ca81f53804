package com.pugwoo.dboperate.archived.中长尾弹性尝试部分.中长尾弹性转换和预测;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import com.pugwoo.wooutils.string.StringTools;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import jakarta.annotation.Resource;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

@Slf4j
@SpringBootTest
public class PeakToMonthTests {

    @Resource
    private DBHelper cloudDemandDevDBHelper;

    @Test
    public void test() {
        for (int topN = 1; topN <= 31; topN++) {
            LocalDate start = DateUtils.parseLocalDate("2021-01-01");
            LocalDate end = DateUtils.parseLocalDate("2023-08-01"); // 开区间

            while(start.isBefore(end)) {
                genMonthPeak(DateUtils.format(start, "yyyy-MM"), topN);
                System.out.println("== finish " + start);
                start = start.plusMonths(1);
            }

            System.out.println("topN:" + topN + " done");
        }
    }

    private void genMonthPeak(String yearMonth, int topN) {
        LocalDate beginOfMonth = DateUtils.parseLocalDate(yearMonth);
        LocalDate lastDayOfMonth = beginOfMonth.withDayOfMonth(1).plusMonths(1).minusDays(1);

        String maxDateSql = "SELECT stat_time\n" +
                "FROM \n" +
                "(\n" +
                "  SELECT stat_time,SUM(peak_core) AS sum_peak_core FROM `ppl_forecast_peak_df`\n" +
                "  WHERE stat_time BETWEEN ? AND ?\n" +
                "  GROUP BY stat_time ORDER BY stat_time\n" +
                ") t ORDER BY sum_peak_core DESC LIMIT " + topN;
        List<String> maxDateOfMonth = cloudDemandDevDBHelper.getRaw(String.class, maxDateSql, beginOfMonth, lastDayOfMonth);

        if (ListUtils.isEmpty(maxDateOfMonth)) {
            log.error("没有找到峰值日期，无法计算差异，beginOfMonth={}", beginOfMonth);
            return;
        }

        List<PplForecastPeakDfDO> thisMonthDetails = cloudDemandDevDBHelper.getAll(PplForecastPeakDfDO.class,
                "where stat_time in (?)", maxDateOfMonth);

        // 去掉appid/uin粒度，并输出结果
        Function<PplForecastPeakDfDO, String> keyMapper = (PplForecastPeakDfDO item) ->
                StringTools.join("@", item.getZoneName(), item.getInstanceType());
        Map<String, List<PplForecastPeakDfDO>> groups = ListUtils.groupBy(thisMonthDetails, keyMapper);

        List<PplForecastPeakZoneInstanceTypeMfDO> results = new ArrayList<>();
        for (List<PplForecastPeakDfDO> group : groups.values()) {
            PplForecastPeakZoneInstanceTypeMfDO result = new PplForecastPeakZoneInstanceTypeMfDO();
            result.setStatTime(lastDayOfMonth);
            result.setZoneName(group.get(0).getZoneName());
            result.setRegionName(group.get(0).getRegionName());
            result.setInstanceType(group.get(0).getInstanceType());
            result.setPeakCore(NumberUtils.divide(NumberUtils.sum(group, PplForecastPeakDfDO::getPeakCore),
                    maxDateOfMonth.size(), 0).intValue());
            result.setTopN(topN); // 这里用topN而不是实际值是因为topN是用来区分不同的topN计算的结果
            results.add(result);
        }

        cloudDemandDevDBHelper.executeRaw("delete from ppl_forecast_peak_zone_instance_type_mf where stat_time=? and top_n=?",
                lastDayOfMonth, topN);
        cloudDemandDevDBHelper.insertBatchWithoutReturnId(results);
    }

}
