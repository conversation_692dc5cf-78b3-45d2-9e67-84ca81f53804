package com.pugwoo.dboperate.archived.中长尾弹性尝试部分.中长尾弹性转换和预测;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import com.pugwoo.wooutils.string.StringTools;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.List;
import java.util.function.Function;

/**
 * 转换成两个月的峰值差异，包含uin粒度
 */
@Slf4j
@SpringBootTest
public class PeakDiffTests {

    @Resource
    private DBHelper cloudDemandDevDBHelper;

    @Test
    public void test() {
        for (int topN = 1; topN <= 31; topN++) {
            LocalDate start = DateUtils.parseLocalDate("2021-02-01");
            LocalDate end = DateUtils.parseLocalDate("2023-08-01"); // 开区间

            while(start.isBefore(end)) {
                genDiff3(DateUtils.format(start, "yyyy-MM"), topN);
                System.out.println("== finish " + start);
                start = start.plusMonths(1);
            }

            System.out.println("finish topN=" + topN);
        }
    }

    /**
     * 生成指定月份的差异数据
     */
    private void genDiff(String yearMonth) {
        // 0. 确定时间范围
        LocalDate beginOfMonth = DateUtils.parseLocalDate(yearMonth);
        LocalDate lastDayOfMonth = beginOfMonth.withDayOfMonth(1).plusMonths(1).minusDays(1);

        LocalDate beginOfLastMonth = beginOfMonth.minusMonths(1);
        LocalDate lastDayOfLastMonth = beginOfMonth.minusDays(1);

        // 1. 计算这个月和上月份的峰值日期
        String maxDateSql = "SELECT stat_time\n" +
                "FROM \n" +
                "(\n" +
                "  SELECT stat_time,SUM(peak_core) AS sum_peak_core FROM `ppl_forecast_peak_df`\n" +
                "  WHERE stat_time BETWEEN ? AND ?\n" +
                "  GROUP BY stat_time ORDER BY stat_time\n" +
                ") t ORDER BY sum_peak_core DESC LIMIT 1";

        String maxDateOfLastMonth = cloudDemandDevDBHelper.getRawOne(String.class, maxDateSql, beginOfLastMonth, lastDayOfLastMonth);
        String maxDateOfMonth = cloudDemandDevDBHelper.getRawOne(String.class, maxDateSql, beginOfMonth, lastDayOfMonth);

        if (StringTools.isBlank(maxDateOfLastMonth) || StringTools.isBlank(maxDateOfMonth)) {
            log.error("没有找到峰值日期，无法计算差异，beginOfMonth={}", beginOfMonth);
            return;
        }

        // 2. diff这两个日期的最细粒度的数据
        List<PplForecastPeakDfDO> lastMonthDetails = cloudDemandDevDBHelper.getAll(PplForecastPeakDfDO.class, "where stat_time=?", maxDateOfLastMonth);
        List<PplForecastPeakDfDO> thisMonthDetails = cloudDemandDevDBHelper.getAll(PplForecastPeakDfDO.class, "where stat_time=?", maxDateOfMonth);

        Function<PplForecastPeakDfDO, String> keyMapper = (PplForecastPeakDfDO item) ->
                StringTools.join("@", item.getAppId(), item.getZoneName(), item.getInstanceType());

        // 3. 计算差异
        List<PplForecastPeakDiffMfDO> diffs = ListUtils.merge(lastMonthDetails, thisMonthDetails,
                keyMapper, keyMapper,
                (last, now) -> {

                    PplForecastPeakDfDO lastOne = null;
                    if (ListUtils.isEmpty(last)) {
                        lastOne = new PplForecastPeakDfDO();
                        lastOne.setPeakCore(0);
                        lastOne.setAppId(now.get(0).getAppId());
                        lastOne.setUin(now.get(0).getUin());
                        lastOne.setZoneName(now.get(0).getZoneName());
                        lastOne.setRegionName(now.get(0).getRegionName());
                        lastOne.setInstanceType(now.get(0).getInstanceType());
                    } else {
                        lastOne = last.get(0);
                        lastOne.setPeakCore(NumberUtils.sum(last, PplForecastPeakDfDO::getPeakCore).intValue());
                    }

                    PplForecastPeakDfDO nowOne = null;
                    if (ListUtils.isEmpty(now)) {
                        nowOne = new PplForecastPeakDfDO();
                        nowOne.setPeakCore(0);
                        nowOne.setAppId(last.get(0).getAppId());
                        nowOne.setUin(last.get(0).getUin());
                        nowOne.setZoneName(last.get(0).getZoneName());
                        nowOne.setRegionName(last.get(0).getRegionName());
                        nowOne.setInstanceType(last.get(0).getInstanceType());
                    } else {
                        nowOne = now.get(0);
                        nowOne.setPeakCore(NumberUtils.sum(now, PplForecastPeakDfDO::getPeakCore).intValue());
                    }

                    PplForecastPeakDiffMfDO diff = new PplForecastPeakDiffMfDO();
                    diff.setStatTime(lastDayOfMonth);
                    diff.setAppId(nowOne.getAppId());
                    diff.setUin(nowOne.getUin());
                    diff.setZoneName(nowOne.getZoneName());
                    diff.setRegionName(nowOne.getRegionName());
                    diff.setInstanceType(nowOne.getInstanceType());
                    diff.setDiffCore(nowOne.getPeakCore() - lastOne.getPeakCore());

                    return diff;
                });

        // 4. 保存到db
        cloudDemandDevDBHelper.executeRaw("delete from ppl_forecast_peak_diff_mf where stat_time=?", lastDayOfMonth);
        cloudDemandDevDBHelper.insertBatchWithoutReturnId(diffs);
    }

    /**
     * 生成指定月份的差异数据
     */
    private void genDiff3(String yearMonth, int topN) {
        // 0. 确定时间范围
        LocalDate beginOfMonth = DateUtils.parseLocalDate(yearMonth);
        LocalDate lastDayOfMonth = beginOfMonth.withDayOfMonth(1).plusMonths(1).minusDays(1);

        LocalDate beginOfLastMonth = beginOfMonth.minusMonths(1);
        LocalDate lastDayOfLastMonth = beginOfMonth.minusDays(1);

        // 1. 计算这个月和上月份的峰值日期
        String maxDateSql = "SELECT stat_time\n" +
                "FROM \n" +
                "(\n" +
                "  SELECT stat_time,SUM(peak_core) AS sum_peak_core FROM `ppl_forecast_peak_df`\n" +
                "  WHERE stat_time BETWEEN ? AND ?\n" +
                "  GROUP BY stat_time ORDER BY stat_time\n" +
                ") t ORDER BY sum_peak_core DESC LIMIT " + topN;

        List<String> maxDateOfLastMonth = cloudDemandDevDBHelper.getRaw(String.class, maxDateSql, beginOfLastMonth, lastDayOfLastMonth);
        List<String> maxDateOfMonth = cloudDemandDevDBHelper.getRaw(String.class, maxDateSql, beginOfMonth, lastDayOfMonth);

        if (ListUtils.isEmpty(maxDateOfLastMonth) || ListUtils.isEmpty(maxDateOfMonth)) {
            log.error("没有找到峰值日期，无法计算差异，beginOfMonth={}", beginOfMonth);
            return;
        }

        // 2. diff这两个日期的最细粒度的数据
        List<PplForecastPeakDfDO> lastMonthDetails = cloudDemandDevDBHelper.getAll(PplForecastPeakDfDO.class, "where stat_time in (?)", maxDateOfLastMonth);
        List<PplForecastPeakDfDO> thisMonthDetails = cloudDemandDevDBHelper.getAll(PplForecastPeakDfDO.class, "where stat_time in (?)", maxDateOfMonth);

        Function<PplForecastPeakDfDO, String> keyMapper = (PplForecastPeakDfDO item) ->
                StringTools.join("@", item.getAppId(), item.getZoneName(), item.getInstanceType());

        // 3. 计算差异
        List<PplForecastPeakDiffMfDO> diffs = ListUtils.merge(lastMonthDetails, thisMonthDetails,
                keyMapper, keyMapper,
                (last, now) -> {

                    PplForecastPeakDfDO lastOne = null;
                    if (ListUtils.isEmpty(last)) {
                        lastOne = new PplForecastPeakDfDO();
                        lastOne.setPeakCore(0);
                        lastOne.setAppId(now.get(0).getAppId());
                        lastOne.setUin(now.get(0).getUin());
                        lastOne.setZoneName(now.get(0).getZoneName());
                        lastOne.setRegionName(now.get(0).getRegionName());
                        lastOne.setInstanceType(now.get(0).getInstanceType());
                    } else {
                        lastOne = last.get(0);
                        lastOne.setPeakCore(NumberUtils.sum(last, PplForecastPeakDfDO::getPeakCore).divide(new BigDecimal(
                                maxDateOfLastMonth.size()), 0, RoundingMode.HALF_UP).intValue());
                    }

                    PplForecastPeakDfDO nowOne = null;
                    if (ListUtils.isEmpty(now)) {
                        nowOne = new PplForecastPeakDfDO();
                        nowOne.setPeakCore(0);
                        nowOne.setAppId(last.get(0).getAppId());
                        nowOne.setUin(last.get(0).getUin());
                        nowOne.setZoneName(last.get(0).getZoneName());
                        nowOne.setRegionName(last.get(0).getRegionName());
                        nowOne.setInstanceType(last.get(0).getInstanceType());
                    } else {
                        nowOne = now.get(0);
                        nowOne.setPeakCore(NumberUtils.sum(now, PplForecastPeakDfDO::getPeakCore).divide(new BigDecimal(
                                maxDateOfMonth.size()), 0, RoundingMode.HALF_UP).intValue());
                    }

                    PplForecastPeakDiffMfDO diff = new PplForecastPeakDiffMfDO();
                    diff.setStatTime(lastDayOfMonth);
                    diff.setAppId(nowOne.getAppId());
                    diff.setUin(nowOne.getUin());
                    diff.setZoneName(nowOne.getZoneName());
                    diff.setRegionName(nowOne.getRegionName());
                    diff.setInstanceType(nowOne.getInstanceType());
                    diff.setDiffCore(nowOne.getPeakCore() - lastOne.getPeakCore());
                    diff.setTopN(topN); // 这里不用实际的天数，而是topN，因为这个topN是用来区分不同的批次计算的

                    return diff;
                });

        // 4. 保存到db
        cloudDemandDevDBHelper.executeRaw("delete from ppl_forecast_peak_diff_mf where stat_time=? and top_n=?", lastDayOfMonth, topN);
        cloudDemandDevDBHelper.insertBatchWithoutReturnId(diffs);
    }
}
