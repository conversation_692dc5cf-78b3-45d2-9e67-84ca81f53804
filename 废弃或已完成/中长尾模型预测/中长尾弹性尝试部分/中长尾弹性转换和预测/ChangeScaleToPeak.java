package com.pugwoo.dboperate.archived.中长尾弹性尝试部分.中长尾弹性转换和预测;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.io.IOUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@SpringBootTest
public class ChangeScaleToPeak {

    @Resource
    private DBHelper ckStdCrpNewIdcDBHelper;
    @Resource
    private DBHelper cloudDemandDevDBHelper;

    @Test
    public void test() throws Exception {
        LocalDate start = DateUtils.parseLocalDate("2023-08-23");
        LocalDate end = DateUtils.parseLocalDate("2023-08-24"); // 开区间

        while(start.isBefore(end)) {
            doTransform(start.toString());
            System.out.println("== finish " + start);
            start = start.plusDays(1);
        }
    }

    private void doTransform(String statTime) throws Exception {
        // 0. 清理掉数据
        cloudDemandDevDBHelper.executeRaw("delete from ppl_forecast_peak_df where stat_time = ?", statTime);

        // 1. 查询以可用区+机型大类为维度的scale核数
        String sql = IOUtils.readClasspathResourceAsString("/废弃或已完成/db-operate/resources/scale_to_peak/read_scale_group_by_zone_instance_type.sql");
        List<ScaleGroupByZoneInstanceTypeDTO> packages = ckStdCrpNewIdcDBHelper.getRaw(ScaleGroupByZoneInstanceTypeDTO.class, sql, statTime);

        // 2. 结果乘以比例1.1542，只留下大于0的
        for (ScaleGroupByZoneInstanceTypeDTO dto : packages) {
            dto.setScaleCore(dto.getScaleCore().multiply(new BigDecimal("1.1542")));
        }
        packages.removeIf(dto -> dto.getScaleCore().compareTo(BigDecimal.ZERO) <= 0);

        // 3. 按比例拆分到uin、可用区、机型大类
        LocalDate localDate = DateUtils.parseLocalDate(statTime);
        List<PplForecastPeakDfDO> result = new ArrayList<>();
        for (ScaleGroupByZoneInstanceTypeDTO dto : packages) {
            // 查询到当前可用区+机型大类的uin明细
            String sqlRatio = IOUtils.readClasspathResourceAsString("/废弃或已完成/db-operate/resources/scale_to_peak/read_scale_uin_ratio.sql");
            List<ScaleUinRatioDTO> ratios = ckStdCrpNewIdcDBHelper.getRaw(ScaleUinRatioDTO.class,
                    sqlRatio, statTime, dto.getZoneName(), dto.getInstanceType());

            BigDecimal total = NumberUtils.sum(ratios, ScaleUinRatioDTO::getScaleCore);
            if (total.compareTo(BigDecimal.ZERO) == 0) {
                // 不应该出现的
                log.error("total is zero, zoneName={}, instanceType={}", dto.getZoneName(), dto.getInstanceType());
                continue;
            }

            for (ScaleUinRatioDTO r : ratios) {
                PplForecastPeakDfDO peak = new PplForecastPeakDfDO();
                peak.setStatTime(localDate);
                peak.setAppId(r.getAppId());
                peak.setUin(r.getUin());
                peak.setZoneName(dto.getZoneName());
                peak.setRegionName(r.getRegionName());
                peak.setInstanceType(dto.getInstanceType());
                peak.setPeakCore(dto.getScaleCore().multiply(r.getScaleCore()).divide(
                        total, 0, RoundingMode.HALF_UP ).intValue());

                result.add(peak);
            }
        }

        // 4. 插入到ppl_forecast_peak_df表
        cloudDemandDevDBHelper.insertBatchWithoutReturnId(result);
    }

}
