package com.pugwoo.dboperate.archived.中长尾弹性尝试部分.中长尾弹性转换和预测;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dboperate.forecast_compute.entity.ForecastComputeTaskDO;
import com.pugwoo.dboperate.forecast_compute.entity.ForecastComputeTaskInputTestDatasetDO;
import com.pugwoo.dboperate.forecast_compute.enums.SerialIntervalEnum;
import com.pugwoo.dboperate.forecast_compute.enums.TaskTypeEnum;
import com.pugwoo.dboperate.forecast_compute.model.CreateTaskDTO;
import com.pugwoo.dboperate.forecast_compute.model.CreateTaskInputDataDTO;
import com.pugwoo.dboperate.archived.中长尾预测.ForecastTest;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.io.IOUtils;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import com.pugwoo.wooutils.net.Browser;
import com.pugwoo.wooutils.net.HttpResponse;
import lombok.Data;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@SpringBootTest
public class ForecastRateTests {

    @Resource
    private DBHelper cloudDemandDevDBHelper;
    @Resource
    private DBHelper cloudDemandCommonDevDBHelper;

    // 先直接测弹性峰值的，按地域+机型粒度
    @Test
    public void test() throws Exception {
        cloudDemandCommonDevDBHelper.setSlowSqlWarningValve(100000);
        cloudDemandDevDBHelper.setSlowSqlWarningValve(100000);


        for (int topN = 7; topN <= 7; topN++) {
            LocalDate start = DateUtils.parseLocalDate("2023-01");
            List<BigDecimal> accuracyRates = ListUtils.newArrayList();

            Map<Integer, LocalDate> taskIds = new LinkedHashMap<>();
            while (start.isBefore(DateUtils.parseLocalDate("2023-08-01"))) {
                Integer taskId = createTask("13周中长尾需求预测-弹性部分", start, 2, topN); // 提前N个月预测
                taskIds.put(taskId, start);
                start = start.plusMonths(1);
            }

            for (Integer taskId : taskIds.keySet()) {
                // 等待任务完成
                while(true) {
                    ForecastComputeTaskDO task = cloudDemandCommonDevDBHelper.getOne(ForecastComputeTaskDO.class, "where id=?", taskId);
                    if (!"DONE".equals(task.getStatus())) {
                        Thread.sleep(1000);
                    } else {
                        break;
                    }
                }

                BigDecimal accuracyRate = calAccuracyRate(taskId, null).getAccuracyRate();
                accuracyRates.add(accuracyRate);
                System.out.println(taskIds.get(taskId) + "准确率:" + accuracyRate + "% taskId:" + taskId);
            }

            System.out.println("--------------");
            System.out.println("topN=" + topN + "平均准确率:" + NumberUtils.avg(accuracyRates, 6) + "%");
        }
    }

    @Data
    public static class InputDTO {
        @Column("stat_time")
        private Date statTime;
        @Column("instance_type")
        private String instanceType;
        @Column("region_name")
        private String regionName;
        @Column("peak_core")
        private BigDecimal peakCore;
    }

    private Integer createTask(String taskName, LocalDate predictMonth, int beforeNMonth, int topN) throws Exception {
        CreateTaskDTO task = new CreateTaskDTO();

        task.setCreateUser("nickxie");
        task.setTaskName(taskName);
        task.setTaskType(TaskTypeEnum.PREDICT.getCode());
        task.setSerialInterval(SerialIntervalEnum.MONTH.getCode());
        task.setInputDims(2);
        task.setInputDimsName("机型族,地域");
        task.setIsAutoFillData(true);
        task.setPredictIndexStart(1);
        task.setPredictIndexEnd(6); // 预测未来1到6个月

        CreateTaskDTO.Algorithm algorithm = new CreateTaskDTO.Algorithm(
                //       CreateTaskDTO.AlgorithmEnum.MA.getName(), ListUtils.newList(3));
                //       CreateTaskDTO.AlgorithmEnum.MAX.getName(), ListUtils.newList(3));
                //       CreateTaskDTO.AlgorithmEnum.ARIMA.getName(), ListUtils.newList(0,1,5));
                 CreateTaskDTO.AlgorithmEnum.ARIMAX.getName(), ListUtils.newList(0,1,2,0,1,6,0.2));
        task.setAlgorithms(ListUtils.newList(algorithm));

        String sql = IOUtils.readClasspathResourceAsString("/废弃或已完成/db-operate/resources/scale_to_peak/forecast_elastic_peak.sql");

        task.setInputDataDatasource("std_crp");

        LocalDate inputBefore = predictMonth.minusMonths(beforeNMonth - 1);
        inputBefore = inputBefore.withDayOfMonth(1);
        String inputSql = sql.replace("${timeRange}", "and stat_time < '" + DateUtils.formatDate(inputBefore) +"'"); // 用提前的数据来预测
        task.setInputDataSql(inputSql);
        List<InputDTO> all = cloudDemandDevDBHelper.getRaw(InputDTO.class, inputSql, topN);

        task.setInputData(ListUtils.transform(all, o -> {
            CreateTaskInputDataDTO d = new CreateTaskInputDataDTO();
            d.setDate(o.getStatTime());
            d.setValue(o.getPeakCore());
            d.setDim1(o.getInstanceType());
            d.setDim2(o.getRegionName());
            d.setDim3("");
            d.setDim4("");
            d.setDim5("");
            return d;
        }));

        // 测试集，测试集只留下要测试的数据接口，其它的不要，【所以】这里要测试的是5月，也就是提前1个月做预测
        LocalDate monthLastDay = predictMonth.withDayOfMonth(1).plusMonths(1).minusDays(1);
        String testSql = sql.replace("${timeRange}", "and stat_time='" + DateUtils.format(monthLastDay, "yyyy-MM-dd") +"'");
        task.setTestDatasetSql(testSql);
        List<InputDTO> test = cloudDemandDevDBHelper.getRaw(InputDTO.class, testSql, topN);
        task.setTestDataset(ListUtils.transform(test, o -> {
            CreateTaskInputDataDTO d = new CreateTaskInputDataDTO();
            d.setDate(o.getStatTime());
            d.setValue(o.getPeakCore());
            d.setDim1(o.getInstanceType());
            d.setDim2(o.getRegionName());
            d.setDim3("");
            d.setDim4("");
            d.setDim5("");
            return d;
        }));

        Browser browser = new Browser();
        browser.addRequestHeader("Cookie", IOUtils.readClasspathResourceAsString("/废弃或已完成/db-operate/resources/exp-crp-cookie.txt"));

        HttpResponse resp = browser.postJson(
                "https://exp-crp.woa.com/cloud-demand-common/ops/submitForecastTask", task);
        ForecastTest.SubmitResult parsed = JSON.parse(resp.getContentString(), ForecastTest.SubmitResult.class);
        return parsed.getTaskId();
    }

    public ForecastTest.CalAccuracyRateResult calAccuracyRate(Integer taskId, String testDatasetExtraWhere) {
        // 1. 先读取测试集
        // 【特别说明】
        // 1) 现在是以真实值作为权重，也就是说，如果有预测，实际结果没有出现，还是认为准的，所以这个计算是按这种算法来简化了
        // 2) 现在是每次只测试一个月，所以直接对结果进行加权，不用分月份
        List<ForecastTest.ForecastComputeTaskInputTestDatasetVO> testDataset = cloudDemandCommonDevDBHelper.getAll(
                ForecastTest.ForecastComputeTaskInputTestDatasetVO.class,
                "where task_id=? " + (testDatasetExtraWhere == null ? "" : testDatasetExtraWhere), taskId);

        // 2. 再读取预测结果
        List<ForecastTest.PredictResultDTO> predictResults = cloudDemandCommonDevDBHelper.getRaw(ForecastTest.PredictResultDTO.class,
                "SELECT a.date,a.value,b.dim1,b.dim2,b.dim3,b.dim4,b.dim5\n" +
                        "FROM `forecast_compute_task_run_output` a\n" +
                        "LEFT JOIN `forecast_compute_task_run` b\n" +
                        "ON a.`task_run_id`=b.`id`\n" +
                        "WHERE a.task_id=?", taskId);

        // 3. 合并数据
        testDataset = ListUtils.merge(testDataset, predictResults,
                o -> o.groupKey(), o -> o.groupKey(),
                (test, predict) -> {
                    if (ListUtils.isNotEmpty(test) && ListUtils.isNotEmpty(predict)) {
                        test.get(0).setPredictValue(predict.get(0).getValue());
                        return test.get(0);
                    } else {
                        return null;
                    }
                });
        testDataset = ListUtils.filter(testDataset, o -> o != null);

        // 4. 计算准确率
        ListUtils.forEach(testDataset, o -> {
            BigDecimal min = NumberUtils.min(o.getPredictValue(), o.getValue());
            BigDecimal max = NumberUtils.max(o.getPredictValue(), o.getValue());
            if (max.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal rate = NumberUtils.divide(min.multiply(BigDecimal.valueOf(100)), max, 6);
                o.setAccuracyRate(rate.doubleValue());
            } else {
                BigDecimal rate = BigDecimal.valueOf(100);
                o.setAccuracyRate(rate.doubleValue());
            }
        });

        // 5. 输出准确率，加权【现在是每次只测试一个月，所以直接对结果进行加权，不用分月份】
        BigDecimal total = NumberUtils.sum(testDataset, ForecastComputeTaskInputTestDatasetDO::getValue);
        ListUtils.forEach(testDataset, o -> {
            double rate = NumberUtils.divide(o.getValue(), total, 6).multiply(BigDecimal.valueOf(o.getAccuracyRate())).doubleValue();
            o.setWeightedAccuracyRate(rate);
        });

        ForecastTest.CalAccuracyRateResult result = new ForecastTest.CalAccuracyRateResult();
        result.setTestDataset(testDataset);
        result.setAccuracyRate(NumberUtils.sum(testDataset, o -> o.getWeightedAccuracyRate()));

        return result;
    }
}
