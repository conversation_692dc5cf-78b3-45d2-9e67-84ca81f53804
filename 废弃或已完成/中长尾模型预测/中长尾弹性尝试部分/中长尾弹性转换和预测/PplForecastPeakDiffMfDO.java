package com.pugwoo.dboperate.archived.中长尾弹性尝试部分.中长尾弹性转换和预测;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDate;

@Data
@ToString
@Table("ppl_forecast_peak_diff_mf")
public class PplForecastPeakDiffMfDO {

    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    /** 按月份来，用月末代表当月<br/>Column: [stat_time] */
    @Column(value = "stat_time")
    private LocalDate statTime;

    @Column(value = "app_id")
    private Long appId;

    @Column(value = "uin")
    private Long uin;

    @Column(value = "zone_name")
    private String zoneName;

    @Column(value = "region_name")
    private String regionName;

    @Column(value = "instance_type")
    private String instanceType;

    /** 大于0新增，小于0退回<br/>Column: [diff_core] */
    @Column(value = "diff_core")
    private Integer diffCore;

    @Column(value = "top_n")
    private Integer topN;

}