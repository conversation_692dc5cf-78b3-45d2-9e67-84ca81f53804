package com.pugwoo.dboperate.archived.中长尾弹性尝试部分.中长尾弹性转换和预测;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDate;

@Data
@ToString
@Table("ppl_forecast_peak_zone_instance_type_mf")
public class PplForecastPeakZoneInstanceTypeMfDO {

    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    @Column(value = "stat_time")
    private LocalDate statTime;

    @Column(value = "zone_name")
    private String zoneName;

    @Column(value = "region_name")
    private String regionName;

    @Column(value = "instance_type")
    private String instanceType;

    /** 峰值核<br/>Column: [peak_core] */
    @Column(value = "peak_core")
    private Integer peakCore;

    @Column(value = "top_n")
    private Integer topN;
}