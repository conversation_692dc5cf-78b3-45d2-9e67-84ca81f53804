package com.pugwoo.dboperate.archived.中长尾弹性尝试部分.中长尾包年包月计算间隔;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

@Table(value = "", virtualTablePath = "/废弃或已完成/db-operate/resources/scale_to_peak/for_cal_monthly_sub_distance_days.sql")
@Data
public class ScaleDTO {

    @Column("stat_time")
    private LocalDate statTime;

    @Column(value = "app_id")
    private Long appid;

    @Column(value = "region_name")
    private String regionName;

    @Column(value = "zone_name")
    private String zoneName;

    @Column(value = "instance_model")
    private String instanceModel;

    @Column(value = "app_role")
    private String appRole;

    @Column("instance_model_cpu")
    private Integer instanceModelCpu;

    @Column(value = "cur_core")
    private BigDecimal curCore;

    @Column(value = "new_core")
    private BigDecimal newCore;

    @Column(value = "ret_core")
    private BigDecimal retCore;

    /*标记这个ret是否被加过*/
    private boolean isAddRet = false;

    /**这个退回匹配到的新增日期，特别的，最后一天退回后剩余的cur_core，按时按照退回来看时间，所以最后一天实际上看cur_core的使用时长*/
    private LocalDate matchedNewDate;
}
