select zone_name, instance_type,sum(num) as execute_core
from
    (select zone_name, instance_type,
            sum(case when biz_range_type='外部业务' then cur_bill_core else cur_service_core end) as num
     from dwd_txy_scale_df
     where stat_time between :firstDayOfMonth and :lastDayOfMonth
       and customer_tab_type in ('名单客户', '报备客户')
     group by zone_name, instance_type

     union all

     select distinct zone_name, instance_type, 0 as num
     from dwd_crp_ppl_item_cf
     where begin_buy_date between :firstDayOfMonth and :lastDayOfMonth
    )
group by zone_name, instance_type
order by sum(num) desc