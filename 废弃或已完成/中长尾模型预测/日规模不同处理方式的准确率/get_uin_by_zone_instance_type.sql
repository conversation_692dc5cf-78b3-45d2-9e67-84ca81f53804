select distinct uin
from
    (select distinct uin
     from dwd_txy_scale_df
     where stat_time between :firstDayOfMonth and :lastDayOfMonth
       and customer_tab_type in ('名单客户', '报备客户')
       and zone_name=:zoneName and instance_type=:instanceType

     union all

     select distinct toInt64(customer_uin)
     from dwd_crp_ppl_item_cf
     where begin_buy_date between :firstDayOfMonth and :lastDayOfMonth
       and customer_uin != '(空值)'
    and zone_name=:zoneName and instance_type=:instanceType
    )