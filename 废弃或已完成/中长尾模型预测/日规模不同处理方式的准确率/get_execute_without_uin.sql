select cast(sum(case when month_change_core>0 then month_change_core else 0 end) as Int32) as new_core
     -- ,cast(sum(case when month_change_core<0 then -month_change_core else 0 end) as Int32) as ret_core
from (
         select zone_name,instance_type,
                sum(case when biz_range_type='外部业务' then change_bill_core else change_service_core end) month_change_core
         from dwd_txy_scale_df
         where stat_time between ? and ?
           and paymode != '5' -- 不要竞价计费的
  and cpu_or_gpu = 'CPU'
  and biz_type = 'cvm'
  and app_role!='LH'
  and instance_type not like 'RS%' and instance_type not like 'RM%'

           and zone_name=? and instance_type=?
  and uin in (?, ?)

         group by zone_name,instance_type
     )