package com.pugwoo.dboperate.archived.日规模不同处理方式的准确率;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dboperate.common.ReadFileUtils;
import com.pugwoo.wooutils.collect.MapUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import lombok.Data;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.Map;

@SpringBootTest
public class TestDifferentWayForecastAccuracy {

    @Resource
    private DBHelper ckStdCrpNewIdcDBHelper;

    @Data
    public static class ZoneAndInstanceTypeDTO {
        @Column("zone_name")
        private String zoneName;
        @Column("instance_type")
        private String instanceType;
        @Column("execute_core")
        private BigDecimal executeCore;
    }

    @Test
    public void test() {
        ckStdCrpNewIdcDBHelper.setSlowSqlWarningValve(1000000);

        String month = "2023-08";
        Date date = DateUtils.parse(month);
        LocalDate firstDayOfMonth = DateUtils.getFirstDayOfMonth(date);
        LocalDate lastDayOfMonth = DateUtils.getLastDayOfMonth(date);

        // 1. 查询出月份的执行数的可用区+机型大类的组合，再查出最新的新增ppl中当前月份的可用区+机型大类的组合
        List<ZoneAndInstanceTypeDTO> combinations = getAllZoneAndInstanceType(firstDayOfMonth, lastDayOfMonth);

        // 2. 对于每一个组合，查出这对组合里的所有头部的uin
        for (ZoneAndInstanceTypeDTO combination : combinations) {
            String zoneName = combination.getZoneName();
            String instanceType = combination.getInstanceType();

            List<Long> uins = getUin(firstDayOfMonth, lastDayOfMonth, zoneName, instanceType);

            // 3. 对于每一组uin，计算其抹除uin抵消的新增准确率，和没有抹除uin的新增准确率
            BigDecimal goodDiff = BigDecimal.ZERO;
            BigDecimal badDiff = BigDecimal.ZERO;

            for (int i = 0; i < uins.size() - 1; i++) {
                for (int j = i + 1; j < uins.size(); j++) {
                    Long uin1 = uins.get(i);
                    Long uin2 = uins.get(j);

                    // 查询两个uin合起来的ppl新增数
                    String pplSql = ReadFileUtils.read("get_ppl_new_amount.sql");
                    BigDecimal pplNew = ckStdCrpNewIdcDBHelper.getRawOne(BigDecimal.class,
                            pplSql, firstDayOfMonth, lastDayOfMonth, zoneName, instanceType, uin1.toString(), uin2.toString());

                    BigDecimal withUinRate = calRateWithUin(firstDayOfMonth, lastDayOfMonth, zoneName, instanceType, uin1, uin2, pplNew);
                    BigDecimal withoutUinRate = calRateWithoutUin(firstDayOfMonth, lastDayOfMonth, zoneName, instanceType, uin1, uin2, pplNew);
                    BigDecimal diff = withUinRate.subtract(withoutUinRate);

                    if (diff.compareTo(BigDecimal.ZERO) > 0) {
                        goodDiff = goodDiff.add(diff);
                    } else if (diff.compareTo(BigDecimal.ZERO) < 0) {
                        badDiff = badDiff.add(diff);
                    }

                    if (Math.abs(diff.intValue()) > 70) {
                        System.out.println("zoneName: " + zoneName + ", instanceType: " + instanceType + ", uin1: " + uin1 + ", uin2: " + uin2 + ", withUinRate: " + withUinRate + ", withoutUinRate: " + withoutUinRate + ", diff: " + diff);
                    }

                }
            }

            System.out.println("zoneName: " + zoneName + ", instanceType: " + instanceType + ", goodDiff: " + goodDiff + ", badDiff: " + badDiff);
        }

        // 4. 看看准确率差异，从大到小排序  TODO 待收集排序

    }

    /**计算保留了uin的准确率，月末减月初，包年包月+按量计费*/
    private BigDecimal calRateWithUin(LocalDate firstDayOfMonth,
                                      LocalDate lastDayOfMonth, String zoneName, String instanceType,
                                      Long uin1, Long uin2, BigDecimal pplNew) {

        String sql = ReadFileUtils.read("get_execute_with_uin.sql");
        BigDecimal execute = ckStdCrpNewIdcDBHelper.getRawOne(BigDecimal.class,
                sql, firstDayOfMonth, lastDayOfMonth, zoneName, instanceType, uin1, uin2);
        return minMax(pplNew, execute);
    }

    /**计算抹除了uin的准确率，月末减月初，包年包月+按量计费*/
    private BigDecimal calRateWithoutUin(LocalDate firstDayOfMonth,
                                         LocalDate lastDayOfMonth, String zoneName, String instanceType,
                                         Long uin1, Long uin2, BigDecimal pplNew) {
        String sql = ReadFileUtils.read("get_execute_without_uin.sql");
        BigDecimal execute = ckStdCrpNewIdcDBHelper.getRawOne(BigDecimal.class,
                sql, firstDayOfMonth, lastDayOfMonth, zoneName, instanceType, uin1, uin2);
        return minMax(pplNew, execute);
    }

    private BigDecimal minMax(BigDecimal b1, BigDecimal b2) {
        BigDecimal min = b1.min(b2);
        BigDecimal max = b1.max(b2);
        if (max.compareTo(BigDecimal.ZERO) <= 0) {
            return BigDecimal.valueOf(0);
        }
        return min.divide(max, 6, BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100));
    }

    private List<ZoneAndInstanceTypeDTO> getAllZoneAndInstanceType(LocalDate firstDayOfMonth,
                                                                   LocalDate lastDayOfMonth) {
        Map<String, Object> params = MapUtils.of(
                "firstDayOfMonth", firstDayOfMonth,
                "lastDayOfMonth", lastDayOfMonth);
        String sql = ReadFileUtils.read("get_ppl_execute_zone_instance_type.sql");
        return ckStdCrpNewIdcDBHelper.getRaw(ZoneAndInstanceTypeDTO.class, sql, params);
    }

    private List<Long> getUin(LocalDate firstDayOfMonth,
                              LocalDate lastDayOfMonth, String zoneName, String instanceType) {
        Map<String, Object> params = MapUtils.of(
                "firstDayOfMonth", firstDayOfMonth,
                "lastDayOfMonth", lastDayOfMonth,
                "zoneName", zoneName, "instanceType", instanceType);
        String sql = ReadFileUtils.read("get_uin_by_zone_instance_type.sql");
        return ckStdCrpNewIdcDBHelper.getRaw(Long.class, sql, params);
    }

}
