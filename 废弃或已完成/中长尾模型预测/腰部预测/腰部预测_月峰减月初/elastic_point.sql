-- 弹性的异常点，这里会找到指定年、月、uin、zone、机型规格，以这个最细的粒度对预测输入数据进行剔除
-- 这里有两个参数：新增量，取>100可以覆盖80%的量；异常倍数，取30倍可以剔除异常10%的量
-- 这两个参数可以调节
select year,month,uin,zone_name,instance_model
from dwd_txy_scale_df
where cpu_or_gpu = 'CPU'
  and biz_type = 'cvm'
  and app_role != 'LH'
  and instance_type not like 'RS%'
  and instance_type not like 'RM%'
  and customer_tab_type in ('名单客户')
  and biz_range_type = '外部业务'
  and industry_dept != '战略客户部'
  and paymode_range_type != '弹性'

    ${timeRange} -- 时间范围

-- 说明：以下条件一样适用于退回，因为新增和净增比，就已经含盖了退回的过滤【这个也实际跑过，确实没差异，万分之一不到】
group by year,month,uin,zone_name,instance_model
having sum(case when change_bill_service_core>0 then change_bill_service_core else 0 end)>100
   and sum(case when change_bill_service_core>0 then change_bill_service_core else 0 end)/30>abs(sum(change_bill_service_core))