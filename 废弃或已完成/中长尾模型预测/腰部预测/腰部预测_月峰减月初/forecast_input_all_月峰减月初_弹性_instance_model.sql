-- 弹性的，月峰减月初：zone_name+instance_model的方式
select year,month,max(month_end_date) as stime, 0 as uin,
    region_name,${instanceTypeMergeCaseWhen} as gins_family,
    sum(case when new_core>0 then new_core else 0 end) as new_cores, -- 增量：max-月初，>0部分
    sum(case when ret_core>0 then ret_core else 0 end) as ret_cores --   退回：max-月末，>0部分
from
(
    select year1 as year,month1 as month, max(month_end_date) as month_end_date,
    zone_name,any(region_name) as region_name,instance_type,
    sum(for_new_core) as new_core, sum(for_ret_core) as ret_core
    from
    (
        select
        year1,month1,max(month_end_date1) as month_end_date, zone_name,any(region_name1) as region_name,
        instance_model,any(instance_type1) as instance_type,
        max(cur_bill_service_core) as for_new_core, max(cur_bill_service_core) as for_ret_core
        from
        (
            select stat_time, any(year) as year1, any(month) as month1, max(month_end_date) as month_end_date1,
            zone_name,any(region_name) as region_name1,instance_model,any(instance_type) as instance_type1,
            sum(cur_bill_service_core) as cur_bill_service_core
            from dwd_txy_scale_df
            where stat_time>='${rangeBegin}' and stat_time<='${rangeEnd}'
            and cpu_or_gpu = 'CPU'
            and biz_type = 'cvm'
            and app_role!='LH'
            and instance_type not like 'RS%' and instance_type not like 'RM%'
            and biz_range_type='外部业务'
            and customer_tab_type in ('名单客户')
            and industry_dept!='战略客户部'
            and paymode_range_type='弹性' and paymode='2'

            -- 剔除异常值，中央广播电视台，这个发生在1月份，这个事情影响太大且后续月份没有再出现，因此认为是一次性的，也不需要特别说明
            and not (uin=100010224120 and region_name='北京' and instance_type='S6t' and year=2023 and month=1)

            ${timeRange} -- 时间范围

            group by stat_time,zone_name,instance_model
        )
        group by year1, month1,  zone_name, instance_model

        union all

        select year as year1, month as month1, max(month_end_date) as month_end_date1,
        zone_name,any(region_name) as region_name1,instance_model, any(instance_type) as instance_type1,
        -sum(case when stat_time=month_start_date then cur_bill_service_core else 0 end) as for_new_core,
        -sum(case when stat_time=month_end_date then cur_bill_service_core else 0 end) as for_ret_core
        from dwd_txy_scale_df
        where stat_time>='${rangeBegin}' and stat_time<='${rangeEnd}'
        and cpu_or_gpu = 'CPU'
        and biz_type = 'cvm'
        and app_role!='LH'
        and instance_type not like 'RS%' and instance_type not like 'RM%'
        and customer_tab_type in ('名单客户')
        and biz_range_type='外部业务'
        and industry_dept!='战略客户部'
        and paymode_range_type='弹性' and paymode='2'

        -- 剔除异常值，中央广播电视台，这个发生在1月份，这个事情影响太大且后续月份没有再出现，因此认为是一次性的，也不需要特别说明
        and not (uin=100010224120 and region_name='北京' and instance_type='S6t' and year=2023 and month=1)

        ${timeRange} -- 时间范围

        group by zone_name,instance_model,year, month
    )
    group by zone_name,instance_type,year1,month1
)
group by region_name,year,month,${instanceTypeMergeCaseWhen}
