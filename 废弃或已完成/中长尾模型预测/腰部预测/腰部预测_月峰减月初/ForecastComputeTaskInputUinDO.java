package com.pugwoo.dboperate.archived.腰部预测.腰部预测_月峰减月初;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 全量输入明细表
 */
@Data
@ToString
@Table("forecast_compute_task_input_uin")
public class ForecastComputeTaskInputUinDO {

    @Column(value = "task_id")
    private Integer taskId;

    /** 时间序列日期<br/>Column: [date] */
    @Column(value = "date")
    private LocalDate date;

    /** 值<br/>Column: [value] */
    @Column(value = "value")
    private BigDecimal value;

    @Column(value = "dim1")
    private String dim1;

    @Column(value = "dim2")
    private String dim2;

    @Column(value = "dim3")
    private String dim3;

    @Column(value = "dim4")
    private String dim4;

    @Column(value = "dim5")
    private String dim5;

    @Column(value = "uin")
    private Long uin;

}