package com.pugwoo.dboperate.archived.腰部预测.腰部预测_月峰减月初;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dboperate.common.ForecastCommonService;
import com.pugwoo.dboperate.common.ReadFileUtils;
import com.pugwoo.dboperate.forecast_compute.entity.ForecastComputeTaskDO;
import com.pugwoo.dboperate.forecast_compute.entity.ForecastComputeTaskInputTestDatasetDO;
import com.pugwoo.dboperate.forecast_compute.enums.SerialIntervalEnum;
import com.pugwoo.dboperate.forecast_compute.enums.TaskTypeEnum;
import com.pugwoo.dboperate.forecast_compute.model.CreateTaskDTO;
import com.pugwoo.dboperate.forecast_compute.model.CreateTaskInputDataDTO;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.io.IOUtils;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import com.pugwoo.wooutils.net.Browser;
import com.pugwoo.wooutils.net.HttpResponse;
import com.pugwoo.wooutils.string.StringTools;
import lombok.Data;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;

@SpringBootTest
public class ForecastTest_月峰减月初 {

    @Resource
    private DBHelper ckStdCrpNewIdcDBHelper;
    @Resource
    private DBHelper cloudDemandCommonDevDBHelper;
    @Resource
    private ForecastCommonService forecastCommonService;

    private boolean isCalNewCore = true; // 是否计算新增的准确率

    private boolean isEnableBlackList = true; /*计算准确率时，是否排除掉黑名单机型，目前页面是排除了的*/
    private boolean isEnableInstanceTypeMerge = true; /*是否开启机型收敛，默认开启*/

    private boolean isWithElastic = false; // 是否含弹性
    private boolean isElasticAtInstanceModel = true; // 弹性部分是否以instance_model的方式来

    private boolean isRemoveElasticPoint = false; // 是否移除包年包月中的异常弹性数据

    // 计算准确率时是否移除包年包月中的异常弹性数据【这个保持为false】
    private boolean isCalAccuracyRateWithRemovedElasticPoint = false;

    // ================================= 整合成直接计算多个月的程序

    @Deprecated
    @Test
    public void run() throws Exception {
        cloudDemandCommonDevDBHelper.setSlowSqlWarningValve(100000);
        ckStdCrpNewIdcDBHelper.setSlowSqlWarningValve(100000);

        BigDecimal totalCurCore = getCurCore(new ArrayList<>());

        List<ChangeTopNDTO> topChange = getTopChange();
        // 只要名单客户
        topChange = ListUtils.filter(topChange, o -> "名单客户".equalsIgnoreCase(o.getCustomerTabType()));

        // 从1到20个，逐步剔除，看准确率的变化
        for (int i = 58; i <= 300; i++) {
            List<Long> excludeAppIds = new ArrayList<>();
            if (i > 0) {
                excludeAppIds = ListUtils.transform(topChange.subList(0, i), o -> o.getAppId());
            }

            BigDecimal curCore = getCurCore(excludeAppIds);

            BigDecimal rate1 = calAccuracyRateAvg(1, excludeAppIds);
            BigDecimal rate2 = calAccuracyRateAvg(2, excludeAppIds);
            System.out.println("剔除top" + i + "准确率:" + NumberUtils.avg(ListUtils.of(rate1, rate2), 6) +
                ",范围占比:" +
                    NumberUtils.percent(curCore, totalCurCore, 6) + "%" + ",剔除appId:" + JSON.toJson(excludeAppIds));
        }
    }

    /**
     * 单独计算准确率
     */
    @Test
    public void run2() throws Exception {
        cloudDemandCommonDevDBHelper.setSlowSqlWarningValve(100000);
        ckStdCrpNewIdcDBHelper.setSlowSqlWarningValve(100000);

        BigDecimal rate1 = calAccuracyRateAvg(1, null);
        System.out.println("rate1:" + rate1);
        BigDecimal rate2 = calAccuracyRateAvg(2, null);
        System.out.println("rate2:" + rate2);
//        BigDecimal rate3 = calAccuracyRateAvg(3, null);
//        System.out.println("rate3:" + rate3);

        System.out.println("准确率:" + NumberUtils.avg(ListUtils.of(rate1, rate2), 6));
    }

    public BigDecimal getCurCore(List<Long> excludeAppIds) {
        return ckStdCrpNewIdcDBHelper.getRawOne(BigDecimal.class,
                "select sum(case when biz_range_type='外部业务' then cur_bill_core else cur_service_core end) as cur_core\n" +
                        "from dwd_txy_scale_df\n" +
                        "where stat_time='2023-08-31'\n" +
                        "and paymode_range_type!='弹性'\n" +
                        "  and cpu_or_gpu = 'CPU'\n" +
                        "  and biz_type = 'cvm'\n" +
                        "  and app_role!='LH'\n" +
                        "  and instance_type not like 'RS%'  and instance_type not like 'RM%' and app_id not in (?)", excludeAppIds);
    }


    public List<ChangeTopNDTO> getTopChange() {
        String sql = "select app_id, any(customer_name) as customer_name, any(customer_short_name) as customer_short_name,\n" +
                "       any(customer_tab_type) as customer_tab_type,\n" +
                "       sum(abs(change_from_last_month)) as total_change\n" +
                "    from (\n" +
                "select toYear(stat_time), toMonth(stat_time), app_id, any(customer_name) as customer_name, any(customer_short_name) as customer_short_name,\n" +
                "       any(customer_tab_type) as customer_tab_type,\n" +
                "         sum(case when stat_time=month_end_date then (case when biz_range_type='外部业务' then change_bill_core_from_last_month\n" +
                "           else change_service_core_from_last_month end) else 0 end) as change_from_last_month\n" +
                "from dwd_txy_scale_df\n" +
                "where stat_time between '2023-06-01' and '2023-08-31' and paymode_range_type!='弹性'\n" +
                "  and cpu_or_gpu = 'CPU'\n" +
                "  and biz_type = 'cvm'\n" +
                "  and app_role!='LH'\n" +
                "  and instance_type not like 'RS%' and instance_type not like 'RM%'\n" +
                "group by toYear(stat_time), toMonth(stat_time), app_id )\n" +
                "group by app_id\n" +
                "order by sum(abs(change_from_last_month)) desc\n" +
                "limit 10000";
        return ckStdCrpNewIdcDBHelper.getRaw(ChangeTopNDTO.class, sql);
    }

    private BigDecimal calAccuracyRateAvg(int beforeNMonth, List<Long> excludeAppIds) throws Exception {
        LocalDate start = DateUtils.parseLocalDate("2023-01");
        List<BigDecimal> accuracyRates = ListUtils.newArrayList();

        Map<Integer, LocalDate> taskIds = new LinkedHashMap<>();
        while (start.isBefore(DateUtils.parseLocalDate("2023-11-01"))) { // 最长填到当月的1号
            Integer taskId = createTask("13周中长尾需求预测", start, beforeNMonth, excludeAppIds); // 提前N个月预测
            taskIds.put(taskId, start);
            start = start.plusMonths(1);
        }

        for (Integer taskId : taskIds.keySet()) {
            // 等待任务完成
            while(true) {
                ForecastComputeTaskDO task = cloudDemandCommonDevDBHelper.getOne(ForecastComputeTaskDO.class,
                        "where id=?", taskId);
                if (!"DONE".equals(task.getStatus())) {
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException ignored) {
                    }
                } else {
                    break;
                }
            }

            BigDecimal accuracyRate = calAccuracyRate(taskId, null).getAccuracyRate();
            accuracyRates.add(accuracyRate);
            System.out.println(taskIds.get(taskId) + "准确率:" + accuracyRate + "% taskId:" + taskId);
        }

        // System.out.println("--------------");
        // System.out.println("平均准确率:" + NumberUtils.avg(accuracyRates, 6) + "%");

        return NumberUtils.avg(accuracyRates, 6);
    }

    // =================================

    private Integer createTask(String taskName, LocalDate predictMonth, int beforeNMonth,
                               List<Long> excludeAppIds) throws Exception {
        CreateTaskDTO task = new CreateTaskDTO();

        task.setCreateUser("nickxie");
        task.setTaskName(taskName);
        task.setTaskType(TaskTypeEnum.PREDICT.getCode());
        task.setSerialInterval(SerialIntervalEnum.MONTH.getCode());
        task.setInputDims(2);
        task.setInputDimsName("机型族,地域");
        task.setIsAutoFillData(true);
        task.setPredictIndexStart(1);
        task.setPredictIndexEnd(6); // 预测未来1到6个月

        CreateTaskDTO.Algorithm algorithm = new CreateTaskDTO.Algorithm(
         //       CreateTaskDTO.AlgorithmEnum.MA.getName(), ListUtils.newList(3));
         //       CreateTaskDTO.AlgorithmEnum.MAX.getName(), ListUtils.newList(3));
         //       CreateTaskDTO.AlgorithmEnum.ARIMA.getName(), ListUtils.newList(0,1,6));
                  CreateTaskDTO.AlgorithmEnum.ARIMAX.getName(), ListUtils.newList(0,1,3,0,1,6,0.2));
        task.setAlgorithms(ListUtils.newList(algorithm));


        String sql = CommonSql.get月峰减月初Sql(isCalNewCore);
        String sqlWithUin = CommonSql.get月峰减月初SqlWithUin();

        // 处理是否加上弹性
        if (isWithElastic) {
            String sqlName = isElasticAtInstanceModel ? "forecast_input_all_月峰减月初_弹性_instance_model.sql"
                    : "forecast_input_all_月峰减月初_弹性_instance_type.sql";
            String sqlElastic = ReadFileUtils.read(sqlName);
            sql = sql.replace("${UNION_ELASTIC}", " union all \n" + sqlElastic);
        } else {
            sql = sql.replace("${UNION_ELASTIC}", "");
        }

        // 是否机型收敛
        if (isEnableInstanceTypeMerge) {
            String instanceTypeMergeCaseWhen = forecastCommonService.getInstanceTypeMergeCaseWhen();
            sql = sql.replace("${instanceTypeMergeCaseWhen}", instanceTypeMergeCaseWhen);
            sqlWithUin = sqlWithUin.replace("${instanceTypeMergeCaseWhen}", instanceTypeMergeCaseWhen);
        } else {
            sql = sql.replace("${instanceTypeMergeCaseWhen}", "instance_type");
            sqlWithUin = sqlWithUin.replace("${instanceTypeMergeCaseWhen}", "instance_type");
        }

        task.setInputDataDatasource("std_crp");

        LocalDate inputBefore = predictMonth.minusMonths(beforeNMonth - 1);
        inputBefore = inputBefore.withDayOfMonth(1);

        // 是否剔除弹性异常点，这个放在这里是因为它的输入和测试集sql不同
        String sqlForInput = "";
        String sqlForInputWithUin = "";
        if (isRemoveElasticPoint) {
            String removeElasticPointSql = ReadFileUtils.read("elastic_point.sql");
            removeElasticPointSql = " and (year,month,uin,zone_name,instance_model) global not in (" + removeElasticPointSql + ")";
            sqlForInput = sql.replace("${removeElasticPoint}", removeElasticPointSql);
            sqlForInputWithUin = sqlWithUin.replace("${removeElasticPoint}", removeElasticPointSql);
        } else {
            sqlForInput = sql.replace("${removeElasticPoint}", "");
            sqlForInputWithUin = sqlWithUin.replace("${removeElasticPoint}", "");
        }

        sqlForInput = sqlForInput.replace("${timeRange}",
                "and stat_time < '" + DateUtils.formatDate(inputBefore) +"'"); // 用提前的数据来预测
        task.setInputDataSql(sqlForInput);

        sqlForInputWithUin = sqlForInputWithUin.replace("${timeRange}",
                "and stat_time < '" + DateUtils.formatDate(inputBefore) +"'"); // 用提前的数据来预测

        if (excludeAppIds == null) {  // TODO exclude不要了
            excludeAppIds = new ArrayList<>();
            excludeAppIds.add(-1L); // 添加一个肯定不存在的appId
        }

        // 由于查询量非常大，这里分时间段进行查询，再合并起来
        List<List<String>> queryTimeRange = ListUtils.of(ListUtils.of("2021-01-01", "2021-06-30"),
                ListUtils.of("2021-07-01", "2021-12-31"), ListUtils.of("2022-01-01", "2022-06-30"),
                ListUtils.of("2022-07-01", "2022-12-31"), ListUtils.of("2023-01-01", "2023-06-30"),
                ListUtils.of("2023-07-01", "2024-12-31"));
        List<InputDTO> all = new ArrayList<>();
        List<InputWithUinDTO> allWithUin = new ArrayList<>();
        for (List<String> range : queryTimeRange) {
            String newSql = sqlForInput.replace("${rangeBegin}", range.get(0));
            newSql = newSql.replace("${rangeEnd}", range.get(1));
            all.addAll(ckStdCrpNewIdcDBHelper.getRaw(InputDTO.class, newSql));

            // 查询带uin的明细
            String newSqlWithUin = sqlForInputWithUin.replace("${rangeBegin}", range.get(0));
            newSqlWithUin = newSqlWithUin.replace("${rangeEnd}", range.get(1));
            allWithUin.addAll(ckStdCrpNewIdcDBHelper.getRaw(InputWithUinDTO.class, newSqlWithUin));
        }

        // 预测输入过滤黑名单机型
        if (isEnableBlackList) {
            List<String> blackList = forecastCommonService.getBlacklistInstanceType();
            all = ListUtils.filter(all, o -> !blackList.contains(o.getGinsFamily()));
        }

        task.setInputData(ListUtils.transform(all, o -> {
            CreateTaskInputDataDTO d = new CreateTaskInputDataDTO();
            d.setDate(o.getStatTime());
            d.setValue(o.getCores());
            d.setDim1(o.getGinsFamily());
            d.setDim2(o.getRegionName());
            d.setDim3("");
            d.setDim4("");
            d.setDim5("");
            return d;
        }));

        // 测试集，测试集只留下要测试的数据接口，其它的不要，【所以】这里要测试的是5月，也就是提前1个月做预测

        // 是否剔除弹性异常点，这个放在这里是因为它的输入和测试集sql不同
        String sqlForTestSet = "";
        String sqlForTestSetWithUin = "";
        if (isCalAccuracyRateWithRemovedElasticPoint) {
            String removeElasticPointSql = ReadFileUtils.read("elastic_point.sql");
            removeElasticPointSql = " and (year,month,uin,zone_name,instance_model) global not in (" + removeElasticPointSql + ")";
            sqlForTestSet = sql.replace("${removeElasticPoint}", removeElasticPointSql);
            sqlForTestSetWithUin = sqlWithUin.replace("${removeElasticPoint}", removeElasticPointSql);
        } else {
            sqlForTestSet = sql.replace("${removeElasticPoint}", "");
            sqlForTestSetWithUin = sqlWithUin.replace("${removeElasticPoint}", "");
        }

        String testSql = sqlForTestSet.replace("${timeRange}",
                "and toYYYYMM(stat_time)='" + DateUtils.format(predictMonth, "yyyyMM") +"'");
        task.setTestDatasetSql(testSql);
        String testSqlWithUin = sqlForTestSetWithUin.replace("${timeRange}",
                "and toYYYYMM(stat_time)='" + DateUtils.format(predictMonth, "yyyyMM") +"'");

        // 由于查询量非常大，这里分时间段进行查询，再合并起来
        List<InputDTO> test = new ArrayList<>();
        List<InputWithUinDTO> testWithUin = new ArrayList<>();
        for (List<String> range : queryTimeRange) {
            String newSql = testSql.replace("${rangeBegin}", range.get(0));
            newSql = newSql.replace("${rangeEnd}", range.get(1));
            test.addAll(ckStdCrpNewIdcDBHelper.getRaw(InputDTO.class, newSql));

            String newSqlWithUin = testSqlWithUin.replace("${rangeBegin}", range.get(0));
            newSqlWithUin = newSqlWithUin.replace("${rangeEnd}", range.get(1));
            testWithUin.addAll(ckStdCrpNewIdcDBHelper.getRaw(InputWithUinDTO.class, newSqlWithUin));
        }

        // 预测输入过滤黑名单机型
        if (isEnableBlackList) {
            List<String> blackList = forecastCommonService.getBlacklistInstanceType();
            test = ListUtils.filter(test, o -> !blackList.contains(o.getGinsFamily()));
        }

        task.setTestDataset(ListUtils.transform(test, o -> {
            CreateTaskInputDataDTO d = new CreateTaskInputDataDTO();
            d.setDate(o.getStatTime());
            d.setValue(o.getCores());
            d.setDim1(o.getGinsFamily());
            d.setDim2(o.getRegionName());
            d.setDim3("");
            d.setDim4("");
            d.setDim5("");
            return d;
        }));

        Browser browser = new Browser();
        browser.addRequestHeader("Cookie", IOUtils.readClasspathResourceAsString("/废弃或已完成/db-operate/resources/exp-crp-cookie.txt").trim());

        HttpResponse resp = browser.postJson(
                "https://exp-crp.woa.com/cloud-demand-common/ops/submitForecastTask", task);
        SubmitResult parsed = JSON.parse(resp.getContentString(), SubmitResult.class);
        Integer taskId = parsed.getTaskId();

        // 输入的带uin的批量写入db
        List<ForecastComputeTaskInputUinDO> inputUins = ListUtils.transform(allWithUin, o -> {
            ForecastComputeTaskInputUinDO d = new ForecastComputeTaskInputUinDO();
            d.setDate(o.getStatTime());
            d.setUin(o.getUin());
            d.setTaskId(taskId);
            d.setDim1(o.getGinsFamily());
            d.setDim2(o.getRegionName());
            d.setValue(isCalNewCore ? o.getNewCores() : o.getRetCores());
            return d;
        });
        cloudDemandCommonDevDBHelper.insertBatchWithoutReturnId(inputUins);

        // 插入测试集带uin的
        List<ForecastComputeTaskInputTestDatasetUinDO> testUins = ListUtils.transform(testWithUin, o -> {
            ForecastComputeTaskInputTestDatasetUinDO d = new ForecastComputeTaskInputTestDatasetUinDO();
            d.setDate(o.getStatTime());
            d.setUin(o.getUin());
            d.setTaskId(taskId);
            d.setDim1(o.getGinsFamily());
            d.setDim2(o.getRegionName());
            d.setValue(isCalNewCore ? o.getNewCores() : o.getRetCores());
            return d;
        });
        cloudDemandCommonDevDBHelper.insertBatchWithoutReturnId(testUins);

        return taskId;
    }

    @Test
    public void submitForecast() throws Exception {
        Integer taskId = createTask("13周中长尾需求预测", DateUtils.parseLocalDate("2023-05"),
                1, new ArrayList<>());
        System.out.println("create task:" + taskId);
    }

    @Data
    public static class SubmitResult {
        private String uuid;
        private Integer taskId;
        private Boolean success;
    }

    @Data
    public static class InputDTO {
        @Column("stime")
        private Date statTime;
        @Column("gins_family")
        private String ginsFamily;
        @Column("region_name")
        private String regionName;
        @Column("cores")
        private BigDecimal cores;
    }

    @Data
    public static class InputWithUinDTO {
        @Column("stime")
        private LocalDate statTime;
        @Column("uin")
        private Long uin;
        @Column("gins_family")
        private String ginsFamily;
        @Column("region_name")
        private String regionName;
        @Column("new_cores")
        private BigDecimal newCores;
        @Column("ret_cores")
        private BigDecimal retCores;
    }

    @Data
    public static class CalAccuracyRateResult {
        List<ForecastComputeTaskInputTestDatasetVO> testDataset;
        BigDecimal accuracyRate;
    }

    public CalAccuracyRateResult calAccuracyRate(Integer taskId, String testDatasetExtraWhere) {
        // 1. 先读取测试集
        // 【特别说明】
        // 1) 现在是以真实值作为权重，也就是说，如果有预测，实际结果没有出现，还是认为准的，所以这个计算是按这种算法来简化了
        // 2) 现在是每次只测试一个月，所以直接对结果进行加权，不用分月份
        List<ForecastComputeTaskInputTestDatasetVO> testDataset = cloudDemandCommonDevDBHelper.getAll(
                ForecastComputeTaskInputTestDatasetVO.class,
                "where task_id=? " + (testDatasetExtraWhere == null ? "" : testDatasetExtraWhere), taskId);

        // 2. 再读取预测结果
        List<PredictResultDTO> predictResults = cloudDemandCommonDevDBHelper.getRaw(PredictResultDTO.class,
                "SELECT a.date,a.value,b.dim1,b.dim2,b.dim3,b.dim4,b.dim5\n" +
                        "FROM `forecast_compute_task_run_output` a\n" +
                        "LEFT JOIN `forecast_compute_task_run` b\n" +
                        "ON a.`task_run_id`=b.`id`\n" +
                        "WHERE a.task_id=?", taskId);

        // 3.1 合并数据
        testDataset = ListUtils.merge(testDataset, predictResults,
                o -> o.groupKey(), o -> o.groupKey(),
                (test, predict) -> {
                    if (ListUtils.isNotEmpty(test) && ListUtils.isNotEmpty(predict)) {
                        test.get(0).setPredictValue(predict.get(0).getValue());
                        return test.get(0);
                    } else {
                        return null;
                    }
                });
        testDataset = ListUtils.filter(testDataset, o -> o != null);

        // 3.2 过滤黑名单机型
        if (isEnableBlackList) {
            List<String> blackList = forecastCommonService.getBlacklistInstanceType();
            testDataset = ListUtils.filter(testDataset, o -> !blackList.contains(o.getDim1()));
        }

        // 4. 计算准确率
        ListUtils.forEach(testDataset, o -> {
            BigDecimal min = NumberUtils.min(o.getPredictValue(), o.getValue());
            BigDecimal max = NumberUtils.max(o.getPredictValue(), o.getValue());
            if (max.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal rate = NumberUtils.divide(min.multiply(BigDecimal.valueOf(100)), max, 6);
                o.setAccuracyRate(rate.doubleValue());
            } else {
                BigDecimal rate = BigDecimal.valueOf(100);
                o.setAccuracyRate(rate.doubleValue());
            }
        });

        // 5. 输出准确率，加权【现在是每次只测试一个月，所以直接对结果进行加权，不用分月份】
        BigDecimal total = NumberUtils.sum(testDataset, ForecastComputeTaskInputTestDatasetDO::getValue);
        ListUtils.forEach(testDataset, o -> {
         //   if (total.compareTo(BigDecimal.ZERO) <= 0) {
         //       o.setWeightedAccuracyRate(0d);
         //   } else {
                double rate = NumberUtils.divide(o.getValue(), total, 6)
                        .multiply(BigDecimal.valueOf(o.getAccuracyRate())).doubleValue();
                o.setWeightedAccuracyRate(rate);
          //  }
        });

        CalAccuracyRateResult result = new CalAccuracyRateResult();
        result.setTestDataset(testDataset);
        result.setAccuracyRate(NumberUtils.sum(testDataset, o -> o.getWeightedAccuracyRate()));

        return result;
    }



    /**计算准确率*/
    @Test
    public void calAccuracyRate() throws Exception {
        Integer taskId = 601;
        CalAccuracyRateResult result = calAccuracyRate(taskId, null);
        System.out.println("准确率：" + result.getAccuracyRate());

        System.out.println("===================================");

        List<ForecastComputeTaskInputTestDatasetVO> testDataset = result.getTestDataset();
        System.out.println("dim1,dim2,准确率,真实值,预测值");
        for (ForecastComputeTaskInputTestDatasetVO v : testDataset) {
            System.out.println(v.getDim1() + "," + v.getDim2() + "," + v.getAccuracyRate() + "," + v.getValue() + "," + v.getPredictValue());
        }
    }

    /**
     * 找出影响排序的机型+地域，输入是任务id：taskId
     */
    @Test
    public void findInfluence() {
        cloudDemandCommonDevDBHelper.setSlowSqlWarningValve(1000000);
        Integer taskId = 2536;

        CalAccuracyRateResult base = calAccuracyRate(taskId, null);// 基准的准确率

        for (ForecastComputeTaskInputTestDatasetVO combination : base.getTestDataset()) {
            BigDecimal accuracyRate = calAccuracyRate(taskId,
                    " and !(dim1='" + combination.getDim1() +
                            "' and dim2='" + combination.getDim2() + "')").getAccuracyRate(); // 剔除
            BigDecimal diff =base.getAccuracyRate().subtract(accuracyRate);

            combination.setGlobalAccuracyRate(base.getAccuracyRate().doubleValue());
            combination.setGlobalAccuracyRateIfRemove(accuracyRate.doubleValue());
            combination.setDiffAccuracyRate(diff.doubleValue());
        }

        // 打印出结果
        System.out.println("机型,地域,当前项贡献准确率(正数表示提升，负数表示降低),原始全局准确率,剔除当前项之后准确率,实际值,预测值,当前项准确率");
        for (ForecastComputeTaskInputTestDatasetVO combination : base.getTestDataset()) {
            System.out.println(combination.getDim1() + "," + combination.getDim2() + ","  + combination.getDiffAccuracyRate() + "," + combination.getGlobalAccuracyRate() + "," + combination.getGlobalAccuracyRateIfRemove() +  "," + combination.getValue() + "," + combination.getPredictValue() + "," + combination.getAccuracyRate());
        }
    }

    @Data
    public static class PredictResultDTO {
        /** 时间序列日期<br/>Column: [date] */
        @Column(value = "date")
        private LocalDate date;
        /** 值<br/>Column: [value] */
        @Column(value = "value")
        private BigDecimal value;
        @Column(value = "dim1")
        private String dim1;
        @Column(value = "dim2")
        private String dim2;
        @Column(value = "dim3")
        private String dim3;
        @Column(value = "dim4")
        private String dim4;
        @Column(value = "dim5")
        private String dim5;

        public String groupKey() {
            return StringTools.join("&", getDate(), getDim1(), getDim2(), getDim3(), getDim4(), getDim5());
        }
    }

    @Data
    public static class ForecastComputeTaskInputTestDatasetVO extends  ForecastComputeTaskInputTestDatasetDO {

        /**预测值*/
        private BigDecimal predictValue;

        /**准确率百分比，已经乘以100*/
        private double accuracyRate;

        /**加权的百分比*/
        private double weightedAccuracyRate;


        /**当前测算的整体准确率*/
        private double globalAccuracyRate;
        /**移除了当前组合之后的准确率*/
        private double globalAccuracyRateIfRemove;
        /**差异，这个值为正表示这项预测做得好，为负表示预测做得不好*/
        private double diffAccuracyRate;


        public String groupKey() {
            return StringTools.join("&", getDate(), getDim1(), getDim2(), getDim3(), getDim4(), getDim5());
        }
    }


}
