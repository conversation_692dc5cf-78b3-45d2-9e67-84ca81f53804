package com.pugwoo.dboperate.archived.腰部预测.腰部预测_月峰减月初_剔毛刺;

import com.pugwoo.dboperate.common.ReadFileUtils;

/**
 * 提取一个通用的SQL
 */
public class CommonSql {


    public static String get月峰减月初Sql(boolean isCalNewCore) {
        String sqlWithUin = ReadFileUtils.read("forecast_input_all_月峰减月初_with_uin.sql");

        String sql = ReadFileUtils.read("forecast_input_all_月峰减月初.sql");
        sql = sql.replace("${MONTHLY_WITH_UIN}", sqlWithUin);

        if (isCalNewCore) {
            sql = sql.replace("${CORES}", "sum(new_cores) as cores");
        } else {
            sql = sql.replace("${CORES}", "sum(ret_cores) as cores");
        }

        return sql;
    }

    public static String get月峰减月初SqlWithUin() {
        String sql = ReadFileUtils.read("forecast_input_all_月峰减月初_with_uin.sql");
        return sql;
    }

}
