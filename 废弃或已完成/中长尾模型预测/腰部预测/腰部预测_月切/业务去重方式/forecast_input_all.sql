-- 直接按业务的方式：可用区+机型大类去重

select year,month,max(month_end_date) as stime,
    ${instanceTypeMergeCaseWhen} as gins_family,
    region_name1 as region_name,
    sum(case when change_core>0 then change_core else 0 end) as cores -- 新增
   -- sum(case when change_core<0 then -change_core else 0 end) as cores -- 退回
from
    (
    select year,month,zone_name,any(region_name) as region_name1, any(month_end_date) as month_end_date,
    instance_type,sum(change_bill_service_core) as change_core
    from dwd_txy_scale_df
    where 1=1
    and  zone_name in ('上海一区','上海七区','上海三区','上海二区','上海五区','上海八区','上海六区','上海四区','东京一区','东京二区','北京一区','北京七区','北京三区','北京二区','北京五区','北京八区','北京六区','北京四区','南京一区','南京三区','南京二区','台北一区','圣保罗一区','多伦多一区','天津三区','天津二区','孟买一区','孟买二区','广州七区','广州三区','广州二区','广州五区','广州八区','广州六区','广州四区','弗吉尼亚一区','弗吉尼亚二区','成都一区','成都二区','新加坡一区','新加坡三区','新加坡二区','新加坡四区','曼谷一区','曼谷二区','欧洲东北一区','法兰克福一区','法兰克福三区','法兰克福二区','深圳一区','深圳三区','深圳二区','深圳四区','硅谷一区','硅谷二区','贵阳一区','重庆一区','雅加达一区','雅加达二区','首尔一区','首尔二区','香港一区','香港三区','香港二区')
    and cpu_or_gpu = 'CPU'
    and biz_type = 'cvm'
    and app_role!='LH'
    and instance_type not like 'RS%' and instance_type not like 'RM%'
    and  paymode_range_type !='弹性'
    and customer_tab_type in ('名单客户')
     and biz_range_type='外部业务'
    -- and not (biz_range_type='内部业务' and app_role='EKS')
    and industry_dept!='战略客户部'

    and app_id not in (?)

    -- 剔除2个重大的弹性case【业务去重做法就不需要进行剔除了】
   -- and not ((uin,zone_name,instance_model) in ((2912314241,'广州三区','S5.LARGE8'), (2912314241,'广州三区','S5.MEDIUM4')))
   -- and not ((uin,zone_name,instance_model) in ((2409831278,'广州六区','TS5.4XLARGE64'), (2409831278,'广州七区','TS5.4XLARGE64')))

    ${timeRange} -- 时间范围

    group by year,month,zone_name,instance_type
    )
group by year, month, region_name1
        , ${instanceTypeMergeCaseWhen}