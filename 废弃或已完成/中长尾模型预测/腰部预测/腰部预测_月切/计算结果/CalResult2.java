package com.pugwoo.dboperate.archived.腰部预测.腰部预测_月切.计算结果;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import lombok.Data;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 计算结果，这个比CalResult多的是直接生成结果表，不用再自己手工操作了
 */
@SpringBootTest
public class CalResult2 {

    @Resource
    private DBHelper cloudDemandCommonDevDBHelper;


    @Data
    public static class ResultDTO {
        private BigDecimal removeNewCore;
        private BigDecimal removeRetCore;
        private BigDecimal removeDupNetChange; // 去重后的净变化

        private BigDecimal notRemoteNewCore;
        private BigDecimal notRemoteRetCore;
        private BigDecimal notRemoveDupNetChange; // 不去重后的净变化

        private BigDecimal realNetChange; // 净增变化

        private String whichBetter;
        private BigDecimal howMuchBetter;

        private String removeCufOffRisk;
        private String removeBuyMoreRisk;

        private String notRemoveCufOffRisk;
        private String notRemoveBuyMoreRisk;
    }

    @Data
    public static class PartResultDTO {
        private BigDecimal newCore;
        private BigDecimal retCore;
        private BigDecimal netChange; // 不去重后的净变化
        private BigDecimal realNetChange; // 净增变化
    }

    private int totalCutOffRiskRemoveDupBetterCount = 0; // 供应风险：去重更好的数量
    private int totalCutOffRiskNotRemoveDupBetterCount = 0; // 供应风险：不去重的更好的数量

    private BigDecimal totalCutOffRiskRemoveDupGap = BigDecimal.ZERO; // 去重的供应风险量
    private BigDecimal totalCutOffRiskNotRemoveDupGap = BigDecimal.ZERO; // 不去重的供应风险量

    private int totalBuyMoreRiskRemoveDupBetterCount = 0; // 采购风险：去重更好的数量
    private int totalBuyMoreRiskNotRemoveDupBetterCount = 0; // 采购风险：不去重的更好的数量

    private BigDecimal totalBuyMoreRiskRemoveDupGap = BigDecimal.ZERO; // 去重的采购风险量
    private BigDecimal totalBuyMoreRiskNotRemoveDupGap = BigDecimal.ZERO; // 不去重的采购风险量

    @Test
    public void test1() {
        List<String> regions = ListUtils.newList("北京", "上海", "广州", "南京");
        List<String> instanceTypes = ListUtils.newList("SA3", "SA2", "S5", "S6");

        for (String instanceType : instanceTypes) {
            for (String region : regions) {
                System.out.println("---- " + region + instanceType + " ----");
                testSpecificRegionInstanceType(region, instanceType);
            }
        }

        System.out.println("====================== 汇总 =========================");
        System.out.println("整体供应风险：去重更好的数量：" + totalCutOffRiskRemoveDupBetterCount);
        System.out.println("整体供应风险：不去重更好的数量：" + totalCutOffRiskNotRemoveDupBetterCount);
        System.out.println("整体供应风险：去重的供应风险量：" + totalCutOffRiskRemoveDupGap);
        System.out.println("整体供应风险：不去重的供应风险量：" + totalCutOffRiskNotRemoveDupGap);
        System.out.println("整体库存风险：去重更好的数量：" + totalBuyMoreRiskRemoveDupBetterCount);
        System.out.println("整体库存风险：不去重更好的数量：" + totalBuyMoreRiskNotRemoveDupBetterCount);
        System.out.println("整体库存风险：去重的采购风险量：" + totalBuyMoreRiskRemoveDupGap);
        System.out.println("整体库存风险：不去重的采购风险量：" + totalBuyMoreRiskNotRemoveDupGap);

    }

    private void testSpecificRegionInstanceType(String regionName, String instanceType) {
        List<ResultDTO> result = new ArrayList<>();

        // 去重的
        {
            List<Integer> new1 = ListUtils.newList(23253, 23254, 23255, 23256, 23257, 23258, 23259, 23260, 23261, 23262);
            List<Integer> new2 = ListUtils.newList(23263, 23264, 23265, 23266, 23267, 23268, 23269, 23270, 23271, 23272);
            List<Integer> ret1 = ListUtils.newList(23273, 23274, 23275, 23276, 23277, 23278, 23279, 23280, 23281, 23282);
            List<Integer> ret2 = ListUtils.newList(23283, 23284, 23285, 23286, 23287, 23288, 23289, 23290, 23291, 23292);

            List<PartResultDTO> res = cal(regionName, instanceType, new1, new2, ret1, ret2);
            for (PartResultDTO r : res) {
                ResultDTO tmp = new ResultDTO();
                tmp.setRemoveNewCore(r.getNewCore());
                tmp.setRemoveRetCore(r.getRetCore());
                tmp.setRemoveDupNetChange(r.getNetChange());
                tmp.setRealNetChange(r.getRealNetChange());
                result.add(tmp);
            }
        }

        // 不去重的
        {
            List<Integer> new1 = ListUtils.newList(23293, 23294, 23295, 23296, 23297, 23298, 23299, 23300, 23301, 23302);
            List<Integer> new2 = ListUtils.newList(23303, 23304, 23305, 23306, 23307, 23308, 23309, 23310, 23311, 23312);
            List<Integer> ret1 = ListUtils.newList(23313, 23314, 23315, 23316, 23317, 23318, 23319, 23320, 23321, 23322);
            List<Integer> ret2 = ListUtils.newList(23323, 23324, 23325, 23326, 23327, 23328, 23329, 23330, 23331, 23332);

            List<PartResultDTO> res = cal(regionName, instanceType, new1, new2, ret1, ret2);
            for (int i = 0; i < res.size(); i++) {
                ResultDTO tmp = result.get(i);
                tmp.setNotRemoteNewCore(res.get(i).getNewCore());
                tmp.setNotRemoteRetCore(res.get(i).getRetCore());
                tmp.setNotRemoveDupNetChange(res.get(i).getNetChange());
            }
        }

        // 计算剩余的元素值
        for (ResultDTO r : result) {
            BigDecimal removeDiff = diffAndAbs(r.getRemoveDupNetChange(), r.getRealNetChange());
            BigDecimal notRemoveDiff = diffAndAbs(r.getNotRemoveDupNetChange(), r.getRealNetChange());

            if (removeDiff.doubleValue() - notRemoveDiff.doubleValue() < -0.1) { // 至少0.1核以上，减少因float浮点误差
                r.setWhichBetter("去重");
            } else if (removeDiff.doubleValue() - notRemoveDiff.doubleValue() > 0.1) { // 至少0.1核以上，减少因float浮点误差
                r.setWhichBetter("不去重");
            } else {
                r.setWhichBetter("打平");
            }

            r.setHowMuchBetter(diffAndAbs(r.getRemoveDupNetChange(), r.getNotRemoveDupNetChange()));

            if (r.getRemoveDupNetChange().compareTo(r.getRealNetChange()) >= 0) {
                r.setRemoveCufOffRisk("否");
            } else {
                r.setRemoveCufOffRisk("是");
            }
            if (r.getRemoveDupNetChange().compareTo(r.getRealNetChange()) > 0) {
                r.setRemoveBuyMoreRisk("是");
            } else {
                r.setRemoveBuyMoreRisk("否");
            }

            if (r.getNotRemoveDupNetChange().compareTo(r.getRealNetChange()) >= 0) {
                r.setNotRemoveCufOffRisk("否");
            } else {
                r.setNotRemoveCufOffRisk("是");
            }
            if (r.getNotRemoveDupNetChange().compareTo(r.getRealNetChange()) > 0) {
                r.setNotRemoveBuyMoreRisk("是");
            } else {
                r.setNotRemoveBuyMoreRisk("否");
            }
        }

        // 打印结果
        for (ResultDTO r : result) {
            System.out.println(r.getRemoveNewCore() + "," + r.getRemoveRetCore() + "," + r.getRemoveDupNetChange() + "," +
                    r.getNotRemoteNewCore() + "," + r.getNotRemoteRetCore() + "," + r.getNotRemoveDupNetChange()
                    + "," + r.getRealNetChange() + ","
                    + r.getWhichBetter() + "," + r.getHowMuchBetter() + "," +
                    r.getRemoveCufOffRisk() + "," + r.getNotRemoveCufOffRisk() +
                    "," + r.getRemoveBuyMoreRisk() + "," + r.getNotRemoveBuyMoreRisk());
        }

        System.out.println("=======================================================================");

        // 打印一些汇总信息
        int cutOffRiskRemoveDupBetterCount = 0; // 供应风险：去重更好的数量
        int cutOffRiskNotRemoveDupBetterCount = 0; // 供应风险：不去重的更好的数量

        BigDecimal cutOffRiskRemoveDupGap = BigDecimal.ZERO; // 去重的供应风险量
        BigDecimal cutOffRiskNotRemoveDupGap = BigDecimal.ZERO; // 不去重的供应风险量

        int buyMoreRiskRemoveDupBetterCount = 0; // 购买风险：去重更好的数量
        int buyMoreRiskNotRemoveDupBetterCount = 0; // 购买风险：不去重的更好的数量

        BigDecimal buyMoreRiskRemoveDupGap = BigDecimal.ZERO; // 去重的购买风险量
        BigDecimal buyMoreRiskNotRemoveDupGap = BigDecimal.ZERO; // 不去重的购买风险量

        for (ResultDTO r : result) {
            // 供应风险
            if ("是".equals(r.getRemoveCufOffRisk()) && "否".equals(r.getNotRemoveCufOffRisk())) { // 去重的有风险，不去重没有
                cutOffRiskNotRemoveDupBetterCount++;
                cutOffRiskRemoveDupGap = cutOffRiskRemoveDupGap.add(r.getRealNetChange().subtract(r.getRemoveDupNetChange()));
            } else if ("否".equals(r.getRemoveCufOffRisk()) && "是".equals(r.getNotRemoveCufOffRisk())) { // 去重的没有风险，不去重的有
                cutOffRiskRemoveDupBetterCount++;
                cutOffRiskNotRemoveDupGap = cutOffRiskNotRemoveDupGap.add(r.getRealNetChange().subtract(r.getNotRemoveDupNetChange()));
            } else if ("是".equals(r.getRemoveCufOffRisk()) && "是".equals(r.getNotRemoveCufOffRisk())) { // 都有风险
                if (r.getWhichBetter().equals("去重")) {
                    cutOffRiskRemoveDupBetterCount++;
                } else if (r.getWhichBetter().equals("不去重")){
                    cutOffRiskNotRemoveDupBetterCount++;
                } else {
                    // 打平
                }
                cutOffRiskRemoveDupGap = cutOffRiskRemoveDupGap.add(r.getRealNetChange().subtract(r.getRemoveDupNetChange()));
                cutOffRiskNotRemoveDupGap = cutOffRiskNotRemoveDupGap.add(r.getRealNetChange().subtract(r.getNotRemoveDupNetChange()));
            }

            // 采购太多风险
            if ("是".equals(r.getRemoveBuyMoreRisk()) && "否".equals(r.getNotRemoveBuyMoreRisk())) { // 去重的有风险，不去重没有
                buyMoreRiskNotRemoveDupBetterCount++;
                buyMoreRiskRemoveDupGap = buyMoreRiskRemoveDupGap.add(r.getRemoveDupNetChange().subtract(r.getRealNetChange()));
            } else if ("否".equals(r.getRemoveBuyMoreRisk()) && "是".equals(r.getNotRemoveBuyMoreRisk())) { // 去重的没有风险，不去重的有
                buyMoreRiskRemoveDupBetterCount++;
                buyMoreRiskNotRemoveDupGap = buyMoreRiskNotRemoveDupGap.add(r.getNotRemoveDupNetChange().subtract(r.getRealNetChange()));
            } else if ("是".equals(r.getRemoveBuyMoreRisk()) && "是".equals(r.getNotRemoveBuyMoreRisk())) { // 都有风险
                if (r.getWhichBetter().equals("去重")) {
                    buyMoreRiskRemoveDupBetterCount++;
                } else if (r.getWhichBetter().equals("不去重")){
                    buyMoreRiskNotRemoveDupBetterCount++;
                } else {
                    // 打平
                }
                buyMoreRiskRemoveDupGap = buyMoreRiskRemoveDupGap.add(r.getRemoveDupNetChange().subtract(r.getRealNetChange()));
                buyMoreRiskNotRemoveDupGap = buyMoreRiskNotRemoveDupGap.add(r.getNotRemoveDupNetChange().subtract(r.getRealNetChange()));
            }

        }

        System.out.println("供应风险：去重更好的数量：" + cutOffRiskRemoveDupBetterCount);
        System.out.println("供应风险：不去重更好的数量：" + cutOffRiskNotRemoveDupBetterCount);
        System.out.println("供应风险：去重的供应风险量：" + cutOffRiskRemoveDupGap);
        System.out.println("供应风险：不去重的供应风险量：" + cutOffRiskNotRemoveDupGap);

        System.out.println("采购风险：去重更好的数量：" + buyMoreRiskRemoveDupBetterCount);
        System.out.println("采购风险：不去重更好的数量：" + buyMoreRiskNotRemoveDupBetterCount);
        System.out.println("采购风险：去重的采购风险量：" + buyMoreRiskRemoveDupGap);
        System.out.println("采购风险：不去重的采购风险量：" + buyMoreRiskNotRemoveDupGap);

        totalCutOffRiskRemoveDupBetterCount += cutOffRiskRemoveDupBetterCount;
        totalCutOffRiskNotRemoveDupBetterCount += cutOffRiskNotRemoveDupBetterCount;
        totalCutOffRiskRemoveDupGap = totalCutOffRiskRemoveDupGap.add(cutOffRiskRemoveDupGap);
        totalCutOffRiskNotRemoveDupGap = totalCutOffRiskNotRemoveDupGap.add(cutOffRiskNotRemoveDupGap);

        totalBuyMoreRiskRemoveDupBetterCount += buyMoreRiskRemoveDupBetterCount;
        totalBuyMoreRiskNotRemoveDupBetterCount += buyMoreRiskNotRemoveDupBetterCount;
        totalBuyMoreRiskRemoveDupGap = totalBuyMoreRiskRemoveDupGap.add(buyMoreRiskRemoveDupGap);
        totalBuyMoreRiskNotRemoveDupGap = totalBuyMoreRiskNotRemoveDupGap.add(buyMoreRiskNotRemoveDupGap);

    }

    private static BigDecimal diffAndAbs(BigDecimal a, BigDecimal b) {
        return a.subtract(b).abs();
    }

    private List<PartResultDTO> cal(String regionName, String instanceType,
                                    List<Integer> new1, List<Integer> new2, List<Integer> ret1, List<Integer> ret2) {
        List<PartResultDTO> result = new ArrayList<>();
        for (int i =0; i < new1.size(); i++) {
            int new1Id = new1.get(i);
            int new2Id = new2.get(i);
            int ret1Id = ret1.get(i);
            int ret2Id = ret2.get(i);

            BigDecimal new1Value = cloudDemandCommonDevDBHelper.getRawOne(BigDecimal.class,
                    """
                            SELECT VALUE FROM `forecast_compute_task_run_output`\s
                            WHERE task_run_id IN (
                              SELECT id FROM `forecast_compute_task_run` WHERE task_id=? AND dim1=? AND dim2=?
                            ) AND DATE IN (
                              SELECT DATE FROM `forecast_compute_task_input_test_dataset` WHERE task_id=? AND dim1=? AND dim2=?
                            )
                            """, new1Id, instanceType, regionName, new1Id, instanceType, regionName);
            BigDecimal new2Value = cloudDemandCommonDevDBHelper.getRawOne(BigDecimal.class,
                    """
                            SELECT VALUE FROM `forecast_compute_task_run_output`\s
                            WHERE task_run_id IN (
                              SELECT id FROM `forecast_compute_task_run` WHERE task_id=? AND dim1=? AND dim2=?
                            ) AND DATE IN (
                              SELECT DATE FROM `forecast_compute_task_input_test_dataset` WHERE task_id=? AND dim1=? AND dim2=?
                            )
                            """, new2Id, instanceType, regionName, new2Id, instanceType, regionName);

            BigDecimal ret1Value = cloudDemandCommonDevDBHelper.getRawOne(BigDecimal.class,
                    """
                            SELECT VALUE FROM `forecast_compute_task_run_output`\s
                            WHERE task_run_id IN (
                              SELECT id FROM `forecast_compute_task_run` WHERE task_id=? AND dim1=? AND dim2=?
                            ) AND DATE IN (
                              SELECT DATE FROM `forecast_compute_task_input_test_dataset` WHERE task_id=? AND dim1=? AND dim2=?
                            )
                            """, ret1Id, instanceType, regionName, ret1Id, instanceType, regionName);
            BigDecimal ret2Value = cloudDemandCommonDevDBHelper.getRawOne(BigDecimal.class,
                    """
                            SELECT VALUE FROM `forecast_compute_task_run_output`\s
                            WHERE task_run_id IN (
                              SELECT id FROM `forecast_compute_task_run` WHERE task_id=? AND dim1=? AND dim2=?
                            ) AND DATE IN (
                              SELECT DATE FROM `forecast_compute_task_input_test_dataset` WHERE task_id=? AND dim1=? AND dim2=?
                            )
                            """, ret2Id, instanceType, regionName, ret2Id, instanceType, regionName);

            BigDecimal newValue = NumberUtils.avg(ListUtils.newList(new1Value, new2Value), 6);
            BigDecimal retValue = NumberUtils.avg(ListUtils.newList(ret1Value, ret2Value), 6);

            // 获得实际的执行数
            BigDecimal realNew = cloudDemandCommonDevDBHelper.getRawOne(BigDecimal.class,
                    "SELECT value FROM `forecast_compute_task_input_test_dataset` WHERE task_id=? AND dim1=? AND dim2=?\n",
                    new1Id, instanceType, regionName);
            BigDecimal realRet = cloudDemandCommonDevDBHelper.getRawOne(BigDecimal.class,
                    "SELECT value FROM `forecast_compute_task_input_test_dataset` WHERE task_id=? AND dim1=? AND dim2=?\n",
                    ret1Id, instanceType, regionName);
            BigDecimal realValue = realNew.subtract(realRet);

            PartResultDTO d = new PartResultDTO();
            d.setNewCore(newValue);
            d.setRetCore(retValue);
            d.setNetChange(newValue.subtract(retValue));
            d.setRealNetChange(realValue);

            result.add(d);

            // System.out.println(newValue + "," + retValue + "," + newValue.subtract(retValue) + "," + realValue);
        }

        return result;
    }

}
