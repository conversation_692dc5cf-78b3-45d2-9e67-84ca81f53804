package com.pugwoo.dboperate.archived.腰部预测.腰部预测_月切.计算结果;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

/**
 * 计算结果
 */
@SpringBootTest
public class CalResult {

    @Resource
    private DBHelper cloudDemandCommonDevDBHelper;

    @Test
    public void testSpecificRegionInstanceType() {
        String regionName = "上海";
        String instanceType = "SA3";

        // 去重的
//        List<Integer> new1 = ListUtils.newList(23253, 23254, 23255, 23256, 23257, 23258, 23259, 23260, 23261, 23262);
//        List<Integer> new2 = ListUtils.newList(23263, 23264, 23265, 23266, 23267, 23268, 23269, 23270, 23271, 23272);
//        List<Integer> ret1 = ListUtils.newList(23273, 23274, 23275, 23276, 23277, 23278, 23279, 23280, 23281, 23282);
//        List<Integer> ret2 = ListUtils.newList(23283, 23284, 23285, 23286, 23287, 23288, 23289, 23290, 23291, 23292);

        // 不去重的
        List<Integer> new1 = ListUtils.newList(23293, 23294, 23295, 23296, 23297, 23298, 23299, 23300, 23301, 23302);
        List<Integer> new2 = ListUtils.newList(23303, 23304, 23305, 23306, 23307, 23308, 23309, 23310, 23311, 23312);
        List<Integer> ret1 = ListUtils.newList(23313, 23314, 23315, 23316, 23317, 23318, 23319, 23320, 23321, 23322);
        List<Integer> ret2 = ListUtils.newList(23323, 23324, 23325, 23326, 23327, 23328, 23329, 23330, 23331, 23332);

        for (int i =0; i < new1.size(); i++) {
            int new1Id = new1.get(i);
            int new2Id = new2.get(i);
            int ret1Id = ret1.get(i);
            int ret2Id = ret2.get(i);

            BigDecimal new1Value = cloudDemandCommonDevDBHelper.getRawOne(BigDecimal.class,
                    """
                            SELECT VALUE FROM `forecast_compute_task_run_output`\s
                            WHERE task_run_id IN (
                              SELECT id FROM `forecast_compute_task_run` WHERE task_id=? AND dim1=? AND dim2=?
                            ) AND DATE IN (
                              SELECT DATE FROM `forecast_compute_task_input_test_dataset` WHERE task_id=? AND dim1=? AND dim2=?
                            )
                            """, new1Id, instanceType, regionName, new1Id, instanceType, regionName);
            BigDecimal new2Value = cloudDemandCommonDevDBHelper.getRawOne(BigDecimal.class,
                    """
                            SELECT VALUE FROM `forecast_compute_task_run_output`\s
                            WHERE task_run_id IN (
                              SELECT id FROM `forecast_compute_task_run` WHERE task_id=? AND dim1=? AND dim2=?
                            ) AND DATE IN (
                              SELECT DATE FROM `forecast_compute_task_input_test_dataset` WHERE task_id=? AND dim1=? AND dim2=?
                            )
                            """, new2Id, instanceType, regionName, new2Id, instanceType, regionName);

            BigDecimal ret1Value = cloudDemandCommonDevDBHelper.getRawOne(BigDecimal.class,
                    """
                            SELECT VALUE FROM `forecast_compute_task_run_output`\s
                            WHERE task_run_id IN (
                              SELECT id FROM `forecast_compute_task_run` WHERE task_id=? AND dim1=? AND dim2=?
                            ) AND DATE IN (
                              SELECT DATE FROM `forecast_compute_task_input_test_dataset` WHERE task_id=? AND dim1=? AND dim2=?
                            )
                            """, ret1Id, instanceType, regionName, ret1Id, instanceType, regionName);
            BigDecimal ret2Value = cloudDemandCommonDevDBHelper.getRawOne(BigDecimal.class,
                    """
                            SELECT VALUE FROM `forecast_compute_task_run_output`\s
                            WHERE task_run_id IN (
                              SELECT id FROM `forecast_compute_task_run` WHERE task_id=? AND dim1=? AND dim2=?
                            ) AND DATE IN (
                              SELECT DATE FROM `forecast_compute_task_input_test_dataset` WHERE task_id=? AND dim1=? AND dim2=?
                            )
                            """, ret2Id, instanceType, regionName, ret2Id, instanceType, regionName);

            BigDecimal newValue = NumberUtils.avg(ListUtils.newList(new1Value, new2Value), 6);
            BigDecimal retValue = NumberUtils.avg(ListUtils.newList(ret1Value, ret2Value), 6);

            // 获得实际的执行数
            BigDecimal realNew = cloudDemandCommonDevDBHelper.getRawOne(BigDecimal.class,
                    "SELECT value FROM `forecast_compute_task_input_test_dataset` WHERE task_id=? AND dim1=? AND dim2=?\n",
                    new1Id, instanceType, regionName);
            BigDecimal realRet = cloudDemandCommonDevDBHelper.getRawOne(BigDecimal.class,
                    "SELECT value FROM `forecast_compute_task_input_test_dataset` WHERE task_id=? AND dim1=? AND dim2=?\n",
                    ret1Id, instanceType, regionName);
            BigDecimal realValue = realNew.subtract(realRet);

            System.out.println(newValue + "," + retValue + "," + newValue.subtract(retValue) + "," + realValue);
        }
    }




    @Test
    public void testAll() {
        // 去重的
//        List<Integer> new1 = ListUtils.newList(23253, 23254, 23255, 23256, 23257, 23258, 23259, 23260, 23261, 23262);
//        List<Integer> new2 = ListUtils.newList(23263, 23264, 23265, 23266, 23267, 23268, 23269, 23270, 23271, 23272);
//        List<Integer> ret1 = ListUtils.newList(23273, 23274, 23275, 23276, 23277, 23278, 23279, 23280, 23281, 23282);
//        List<Integer> ret2 = ListUtils.newList(23283, 23284, 23285, 23286, 23287, 23288, 23289, 23290, 23291, 23292);

        // 不去重的
        List<Integer> new1 = ListUtils.newList(23293, 23294, 23295, 23296, 23297, 23298, 23299, 23300, 23301, 23302);
        List<Integer> new2 = ListUtils.newList(23303, 23304, 23305, 23306, 23307, 23308, 23309, 23310, 23311, 23312);
        List<Integer> ret1 = ListUtils.newList(23313, 23314, 23315, 23316, 23317, 23318, 23319, 23320, 23321, 23322);
        List<Integer> ret2 = ListUtils.newList(23323, 23324, 23325, 23326, 23327, 23328, 23329, 23330, 23331, 23332);

        for (int i =0; i < new1.size(); i++) {
            int new1Id = new1.get(i);
            int new2Id = new2.get(i);
            int ret1Id = ret1.get(i);
            int ret2Id = ret2.get(i);

            BigDecimal new1Value = cloudDemandCommonDevDBHelper.getRawOne(BigDecimal.class,
                    """
                            SELECT sum(VALUE) FROM `forecast_compute_task_run_output`\s
                            WHERE task_run_id IN (
                              SELECT id FROM `forecast_compute_task_run` WHERE task_id=? 
                            ) AND DATE IN (
                              SELECT DATE FROM `forecast_compute_task_input_test_dataset` WHERE task_id=?
                            )
                            """, new1Id, new1Id);
            BigDecimal new2Value = cloudDemandCommonDevDBHelper.getRawOne(BigDecimal.class,
                    """
                            SELECT sum(VALUE) FROM `forecast_compute_task_run_output`\s
                            WHERE task_run_id IN (
                              SELECT id FROM `forecast_compute_task_run` WHERE task_id=? 
                            ) AND DATE IN (
                              SELECT DATE FROM `forecast_compute_task_input_test_dataset` WHERE task_id=? 
                            )
                            """, new2Id, new2Id);

            BigDecimal ret1Value = cloudDemandCommonDevDBHelper.getRawOne(BigDecimal.class,
                    """
                            SELECT sum(VALUE) FROM `forecast_compute_task_run_output`\s
                            WHERE task_run_id IN (
                              SELECT id FROM `forecast_compute_task_run` WHERE task_id=? 
                            ) AND DATE IN (
                              SELECT DATE FROM `forecast_compute_task_input_test_dataset` WHERE task_id=? 
                            )
                            """, ret1Id, ret1Id);
            BigDecimal ret2Value = cloudDemandCommonDevDBHelper.getRawOne(BigDecimal.class,
                    """
                            SELECT sum(VALUE) FROM `forecast_compute_task_run_output`\s
                            WHERE task_run_id IN (
                              SELECT id FROM `forecast_compute_task_run` WHERE task_id=? 
                            ) AND DATE IN (
                              SELECT DATE FROM `forecast_compute_task_input_test_dataset` WHERE task_id=?
                            )
                            """, ret2Id, ret2Id);

            BigDecimal newValue = NumberUtils.avg(ListUtils.newList(new1Value, new2Value), 6);
            BigDecimal retValue = NumberUtils.avg(ListUtils.newList(ret1Value, ret2Value), 6);

            // 获得实际的执行数
            BigDecimal realNew = cloudDemandCommonDevDBHelper.getRawOne(BigDecimal.class,
                    "SELECT sum(VALUE) FROM `forecast_compute_task_input_test_dataset` WHERE task_id=?\n",
                    new1Id);
            BigDecimal realRet = cloudDemandCommonDevDBHelper.getRawOne(BigDecimal.class,
                    "SELECT sum(VALUE) FROM `forecast_compute_task_input_test_dataset` WHERE task_id=?\n",
                    ret1Id);
            BigDecimal realValue = realNew.subtract(realRet);

            System.out.println(newValue + "," + retValue + "," + newValue.subtract(retValue) + "," + realValue);

        }
    }
}
