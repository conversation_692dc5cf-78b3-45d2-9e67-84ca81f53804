package com.pugwoo.dboperate.archived.腰部预测.腰部预测_月切.计算结果;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import lombok.Data;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@SpringBootTest
public class CalResultByRegionAndInstanceType {

    @Resource
    private DBHelper cloudDemandCommonDevDBHelper;

    @Data
    public static class RegionInstanceTypeResultDTO {
        private String regionName;
        private String instanceType;

        private BigDecimal removeNewCore;
        private BigDecimal removeRetCore;
        private BigDecimal removeDupNetChange; // 去重后的净变化

        private BigDecimal notRemoteNewCore;
        private BigDecimal notRemoteRetCore;
        private BigDecimal notRemoveDupNetChange; // 不去重后的净变化

        private BigDecimal realNetChange; // 净增变化
    }

    @Data
    public static class SingleResultDTO {
        private BigDecimal newCore;
        private BigDecimal retCore;
        private BigDecimal netChange; // 去重后的净变化
        private BigDecimal realNetChange; // 净增变化
    }

    @Data
    public static class RegionInstanceType {
        @Column("region_name")
        private String regionName;
        @Column("instance_type")
        private String instanceType;
    }

    @Test
    public void test() {
        List<RegionInstanceType> regionInstanceTypes = cloudDemandCommonDevDBHelper.getRaw(RegionInstanceType.class, """
                SELECT dim1 AS instance_type,dim2  AS region_name FROM forecast_compute_task_input_test_dataset WHERE task_id=23302 GROUP BY dim1,dim2
                """);
        System.out.println("地域,机型,去重后新增,去重后退回,去重后净增,不去重新增,不去重退回,不去重净增,实际变化值");
        for (RegionInstanceType ri : regionInstanceTypes) {
            RegionInstanceTypeResultDTO cal = cal(ri.getRegionName(), ri.getInstanceType());
            System.out.println(ri.getRegionName() + "," + ri.getInstanceType() + "," +
                    cal.getRemoveNewCore() + "," + cal.getRemoveRetCore() + "," + cal.getRemoveDupNetChange() + "," +
                    cal.getNotRemoteNewCore() + "," + cal.getNotRemoteRetCore() + "," + cal.getNotRemoveDupNetChange() + "," +
                    cal.getRealNetChange());
        }
    }

    public RegionInstanceTypeResultDTO cal(String regionName, String instanceType) {
        RegionInstanceTypeResultDTO dto = new RegionInstanceTypeResultDTO();
        dto.setRegionName(regionName);
        dto.setInstanceType(instanceType);

        // 去重的
        {
            List<Integer> new1 = ListUtils.newList(23253, 23254, 23255, 23256, 23257, 23258, 23259, 23260, 23261, 23262);
            List<Integer> new2 = ListUtils.newList(23263, 23264, 23265, 23266, 23267, 23268, 23269, 23270, 23271, 23272);
            List<Integer> ret1 = ListUtils.newList(23273, 23274, 23275, 23276, 23277, 23278, 23279, 23280, 23281, 23282);
            List<Integer> ret2 = ListUtils.newList(23283, 23284, 23285, 23286, 23287, 23288, 23289, 23290, 23291, 23292);

            SingleResultDTO cal = cal(regionName, instanceType, new1, new2, ret1, ret2);
            dto.setRemoveNewCore(cal.getNewCore());
            dto.setRemoveRetCore(cal.getRetCore());
            dto.setRemoveDupNetChange(cal.getNetChange());
            dto.setRealNetChange(cal.getRealNetChange());
        }

        // 没去重的
        {
            List<Integer> new1 = ListUtils.newList(23293, 23294, 23295, 23296, 23297, 23298, 23299, 23300, 23301, 23302);
            List<Integer> new2 = ListUtils.newList(23303, 23304, 23305, 23306, 23307, 23308, 23309, 23310, 23311, 23312);
            List<Integer> ret1 = ListUtils.newList(23313, 23314, 23315, 23316, 23317, 23318, 23319, 23320, 23321, 23322);
            List<Integer> ret2 = ListUtils.newList(23323, 23324, 23325, 23326, 23327, 23328, 23329, 23330, 23331, 23332);

            SingleResultDTO cal = cal(regionName, instanceType, new1, new2, ret1, ret2);
            dto.setNotRemoteNewCore(cal.getNewCore());
            dto.setNotRemoteRetCore(cal.getRetCore());
            dto.setNotRemoveDupNetChange(cal.getNetChange());
            // 另外一个一样
        }

        return dto;
    }


    private SingleResultDTO cal(String regionName, String instanceType, List<Integer> new1, List<Integer> new2,
                                            List<Integer> ret1, List<Integer> ret2) {
        List<BigDecimal> newValueList = new ArrayList<>();
        List<BigDecimal> retValueList = new ArrayList<>();
        List<BigDecimal> netChangeList = new ArrayList<>();
        List<BigDecimal> realValueList = new ArrayList<>();

        for (int i =0; i < new1.size(); i++) {
            int new1Id = new1.get(i);
            int new2Id = new2.get(i);
            int ret1Id = ret1.get(i);
            int ret2Id = ret2.get(i);

            BigDecimal new1Value = cloudDemandCommonDevDBHelper.getRawOne(BigDecimal.class,
                    """
                            SELECT VALUE FROM `forecast_compute_task_run_output`\s
                            WHERE task_run_id IN (
                              SELECT id FROM `forecast_compute_task_run` WHERE task_id=? AND dim1=? AND dim2=?
                            ) AND DATE IN (
                              SELECT DATE FROM `forecast_compute_task_input_test_dataset` WHERE task_id=? AND dim1=? AND dim2=?
                            )
                            """, new1Id, instanceType, regionName, new1Id, instanceType, regionName);
            BigDecimal new2Value = cloudDemandCommonDevDBHelper.getRawOne(BigDecimal.class,
                    """
                            SELECT VALUE FROM `forecast_compute_task_run_output`\s
                            WHERE task_run_id IN (
                              SELECT id FROM `forecast_compute_task_run` WHERE task_id=? AND dim1=? AND dim2=?
                            ) AND DATE IN (
                              SELECT DATE FROM `forecast_compute_task_input_test_dataset` WHERE task_id=? AND dim1=? AND dim2=?
                            )
                            """, new2Id, instanceType, regionName, new2Id, instanceType, regionName);

            BigDecimal ret1Value = cloudDemandCommonDevDBHelper.getRawOne(BigDecimal.class,
                    """
                            SELECT VALUE FROM `forecast_compute_task_run_output`\s
                            WHERE task_run_id IN (
                              SELECT id FROM `forecast_compute_task_run` WHERE task_id=? AND dim1=? AND dim2=?
                            ) AND DATE IN (
                              SELECT DATE FROM `forecast_compute_task_input_test_dataset` WHERE task_id=? AND dim1=? AND dim2=?
                            )
                            """, ret1Id, instanceType, regionName, ret1Id, instanceType, regionName);
            BigDecimal ret2Value = cloudDemandCommonDevDBHelper.getRawOne(BigDecimal.class,
                    """
                            SELECT VALUE FROM `forecast_compute_task_run_output`\s
                            WHERE task_run_id IN (
                              SELECT id FROM `forecast_compute_task_run` WHERE task_id=? AND dim1=? AND dim2=?
                            ) AND DATE IN (
                              SELECT DATE FROM `forecast_compute_task_input_test_dataset` WHERE task_id=? AND dim1=? AND dim2=?
                            )
                            """, ret2Id, instanceType, regionName, ret2Id, instanceType, regionName);

            BigDecimal newValue = NumberUtils.avg(ListUtils.newList(new1Value, new2Value), 6);
            BigDecimal retValue = NumberUtils.avg(ListUtils.newList(ret1Value, ret2Value), 6);

            // 获得实际的执行数
            BigDecimal realNew = cloudDemandCommonDevDBHelper.getRawOne(BigDecimal.class,
                    "SELECT value FROM `forecast_compute_task_input_test_dataset` WHERE task_id=? AND dim1=? AND dim2=?\n",
                    new1Id, instanceType, regionName);
            realNew = realNew == null ? BigDecimal.ZERO : realNew;
            BigDecimal realRet = cloudDemandCommonDevDBHelper.getRawOne(BigDecimal.class,
                    "SELECT value FROM `forecast_compute_task_input_test_dataset` WHERE task_id=? AND dim1=? AND dim2=?\n",
                    ret1Id, instanceType, regionName);
            realRet = realRet == null ? BigDecimal.ZERO : realRet;
            BigDecimal realValue = realNew.subtract(realRet);

            // System.out.println(newValue + "," + retValue + "," + newValue.subtract(retValue) + "," + realValue);
            newValueList.add(newValue);
            retValueList.add(retValue);
            netChangeList.add(newValue.subtract(retValue));
            realValueList.add(realValue);
        }

        SingleResultDTO dto = new SingleResultDTO();
        dto.setNewCore(NumberUtils.avg(newValueList, 6));
        dto.setRetCore(NumberUtils.avg(retValueList, 6));
        dto.setNetChange(NumberUtils.avg(netChangeList, 6));
        dto.setRealNetChange(NumberUtils.avg(realValueList, 6));
        return dto;
    }

}
