package com.pugwoo.dboperate.archived.腰部预测.腰部预测_月切.保留客户不去重方式;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dboperate.common.ForecastCommonService;
import com.pugwoo.dboperate.common.ReadFileUtils;
import com.pugwoo.dboperate.forecast_compute.entity.ForecastComputeTaskDO;
import com.pugwoo.dboperate.forecast_compute.entity.ForecastComputeTaskInputTestDatasetDO;
import com.pugwoo.dboperate.forecast_compute.enums.SerialIntervalEnum;
import com.pugwoo.dboperate.forecast_compute.enums.TaskTypeEnum;
import com.pugwoo.dboperate.forecast_compute.model.CreateTaskDTO;
import com.pugwoo.dboperate.forecast_compute.model.CreateTaskInputDataDTO;
import com.pugwoo.dboperate.archived.腰部预测.腰部预测_月峰减月初.ChangeTopNDTO;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.io.IOUtils;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import com.pugwoo.wooutils.net.Browser;
import com.pugwoo.wooutils.net.HttpResponse;
import com.pugwoo.wooutils.string.StringTools;
import lombok.Data;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;

@SpringBootTest
public class ForecastTest_日diff {

    @Resource
    private DBHelper ckStdCrpNewIdcDBHelper;
    @Resource
    private DBHelper cloudDemandCommonDevDBHelper;
    @Resource
    private ForecastCommonService forecastCommonService;

    private boolean isEnableBlackList = true; /*计算准确率时，是否排除掉黑名单机型，目前页面是排除了的*/
    private boolean isEnableInstanceTypeMerge = true; /*是否开启机型收敛，默认开启*/

    // ================================= 整合成直接计算多个月的程序

    @Test
    public void run() throws Exception {
        cloudDemandCommonDevDBHelper.setSlowSqlWarningValve(100000);
        ckStdCrpNewIdcDBHelper.setSlowSqlWarningValve(100000);

        BigDecimal totalCurCore = getCurCore(new ArrayList<>());

        List<ChangeTopNDTO> topChange = getTopChange();
        // 只要名单客户
        topChange = ListUtils.filter(topChange, o -> "名单客户".equalsIgnoreCase(o.getCustomerTabType()));

        // 从1到20个，逐步剔除，看准确率的变化
        for (int i = 58; i <= 300; i++) {
            List<Long> excludeAppIds = new ArrayList<>();
            if (i > 0) {
                excludeAppIds = ListUtils.transform(topChange.subList(0, i), o -> o.getAppId());
            }

            BigDecimal curCore = getCurCore(excludeAppIds);

            BigDecimal rate1 = calAccuracyRateAvg(1, excludeAppIds);
            BigDecimal rate2 = calAccuracyRateAvg(2, excludeAppIds);
            System.out.println("剔除top" + i + "准确率:" + NumberUtils.avg(ListUtils.of(rate1, rate2), 6) +
                ",范围占比:" +
                    NumberUtils.percent(curCore, totalCurCore, 6) + "%" + ",剔除appId:" + JSON.toJson(excludeAppIds));
        }
    }

    /**
     * 单独计算准确率
     */
    @Test
    public void run2() throws Exception {
        cloudDemandCommonDevDBHelper.setSlowSqlWarningValve(100000);
        ckStdCrpNewIdcDBHelper.setSlowSqlWarningValve(100000);

        BigDecimal rate1 = calAccuracyRateAvg(1, null);
        System.out.println("rate1:" + rate1);

        BigDecimal rate2 = calAccuracyRateAvg(2, null);
        System.out.println("rate2:" + rate2);
        System.out.println("准确率:" + NumberUtils.avg(ListUtils.of(rate1, rate2), 6));
    }

    public BigDecimal getCurCore(List<Long> excludeAppIds) {
        return ckStdCrpNewIdcDBHelper.getRawOne(BigDecimal.class,
                "select sum(case when biz_range_type='外部业务' then cur_bill_core else cur_service_core end) as cur_core\n" +
                        "from dwd_txy_scale_df\n" +
                        "where stat_time='2023-08-31'\n" +
                        "and paymode_range_type!='弹性'\n" +
                        "  and cpu_or_gpu = 'CPU'\n" +
                        "  and biz_type = 'cvm'\n" +
                        "  and app_role!='LH'\n" +
                        "  and instance_type not like 'RS%' and instance_type not like 'RM%' and app_id not in (?)", excludeAppIds);
    }


    public List<ChangeTopNDTO> getTopChange() {
        String sql = "select app_id, any(customer_name) as customer_name, any(customer_short_name) as customer_short_name,\n" +
                "       any(customer_tab_type) as customer_tab_type,\n" +
                "       sum(abs(change_from_last_month)) as total_change\n" +
                "    from (\n" +
                "select toYear(stat_time), toMonth(stat_time), app_id, any(customer_name) as customer_name, any(customer_short_name) as customer_short_name,\n" +
                "       any(customer_tab_type) as customer_tab_type,\n" +
                "         sum(case when stat_time=month_end_date then (case when biz_range_type='外部业务' then change_bill_core_from_last_month\n" +
                "           else change_service_core_from_last_month end) else 0 end) as change_from_last_month\n" +
                "from dwd_txy_scale_df\n" +
                "where stat_time between '2023-06-01' and '2023-08-31' and paymode_range_type!='弹性'\n" +
                "  and cpu_or_gpu = 'CPU'\n" +
                "  and biz_type = 'cvm'\n" +
                "  and app_role!='LH'\n" +
                "  and instance_type not like 'RS%' and instance_type not like 'RM%'\n" +
                "group by toYear(stat_time), toMonth(stat_time), app_id )\n" +
                "group by app_id\n" +
                "order by sum(abs(change_from_last_month)) desc\n" +
                "limit 10000";
        return ckStdCrpNewIdcDBHelper.getRaw(ChangeTopNDTO.class, sql);
    }

    private BigDecimal calAccuracyRateAvg(int beforeNMonth, List<Long> excludeAppIds) throws Exception {
        LocalDate start = DateUtils.parseLocalDate("2023-01");
        List<BigDecimal> accuracyRates = ListUtils.newArrayList();

        Map<Integer, LocalDate> taskIds = new LinkedHashMap<>();
        while (start.isBefore(DateUtils.parseLocalDate("2023-11-01"))) {
            Integer taskId = createTask("13周中长尾需求预测", start, beforeNMonth, excludeAppIds); // 提前N个月预测
            taskIds.put(taskId, start);
            start = start.plusMonths(1);
        }

        for (Integer taskId : taskIds.keySet()) {
            // 等待任务完成
            while(true) {
                ForecastComputeTaskDO task = cloudDemandCommonDevDBHelper.getOne(ForecastComputeTaskDO.class,
                        "where id=?", taskId);
                if (!"DONE".equals(task.getStatus())) {
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException ignored) {
                    }
                } else {
                    break;
                }
            }

            BigDecimal accuracyRate = calAccuracyRate(taskId, null).getAccuracyRate();
            accuracyRates.add(accuracyRate);
            System.out.println(taskIds.get(taskId) + "准确率:" + accuracyRate + "% taskId:" + taskId);
        }

        return NumberUtils.avg(accuracyRates, 6);
    }

    // =================================

    private Integer createTask(String taskName, LocalDate predictMonth, int beforeNMonth,
                               List<Long> excludeAppIds) throws Exception {
        CreateTaskDTO task = new CreateTaskDTO();

        task.setCreateUser("nickxie");
        task.setTaskName(taskName);
        task.setTaskType(TaskTypeEnum.PREDICT.getCode());
        task.setSerialInterval(SerialIntervalEnum.MONTH.getCode());
        task.setInputDims(2);
        task.setInputDimsName("机型族,地域");
        task.setIsAutoFillData(true);
        task.setPredictIndexStart(1);
        task.setPredictIndexEnd(6); // 预测未来1到6个月

        CreateTaskDTO.Algorithm algorithm = new CreateTaskDTO.Algorithm(
         //       CreateTaskDTO.AlgorithmEnum.MA.getName(), ListUtils.newList(3));
         //       CreateTaskDTO.AlgorithmEnum.MAX.getName(), ListUtils.newList(3));
         //       CreateTaskDTO.AlgorithmEnum.ARIMA.getName(), ListUtils.newList(0,1,6));
                  CreateTaskDTO.AlgorithmEnum.ARIMAX.getName(), ListUtils.newList(0,1,3,0,1,6,0.2));
        task.setAlgorithms(ListUtils.newList(algorithm));


        String sql = ReadFileUtils.read("forecast_input_all.sql");

        if (isEnableInstanceTypeMerge) {
            String instanceTypeMergeCaseWhen = forecastCommonService.getInstanceTypeMergeCaseWhen();
            sql = sql.replace("${instanceTypeMergeCaseWhen}", instanceTypeMergeCaseWhen);
        } else {
            sql = sql.replace("${instanceTypeMergeCaseWhen}", "instance_type");
        }


        task.setInputDataDatasource("std_crp");

        LocalDate inputBefore = predictMonth.minusMonths(beforeNMonth - 1);
        inputBefore = inputBefore.withDayOfMonth(1);
        String inputSql = sql.replace("${timeRange}", "and stat_time < '" + DateUtils.formatDate(inputBefore) +"'"); // 用提前的数据来预测
        task.setInputDataSql(inputSql);

        if (excludeAppIds == null) {
            excludeAppIds = new ArrayList<>();
            excludeAppIds.add(-1L); // 添加一个肯定不存在的appId
        }
        List<InputDTO> all = ckStdCrpNewIdcDBHelper.getRaw(InputDTO.class, inputSql, excludeAppIds);

        task.setInputData(ListUtils.transform(all, o -> {
            CreateTaskInputDataDTO d = new CreateTaskInputDataDTO();
            d.setDate(o.getStatTime());
            d.setValue(o.getCores());
            d.setDim1(o.getGinsFamily());
            d.setDim2(o.getRegionName());
            d.setDim3("");
            d.setDim4("");
            d.setDim5("");
            return d;
        }));

        // 测试集，测试集只留下要测试的数据接口，其它的不要，【所以】这里要测试的是5月，也就是提前1个月做预测
        String testSql = sql.replace("${timeRange}", "and toYYYYMM(stat_time)='" + DateUtils.format(predictMonth, "yyyyMM") +"'");
        task.setTestDatasetSql(testSql);
        List<InputDTO> test = excludeAppIds == null ? ckStdCrpNewIdcDBHelper.getRaw(InputDTO.class, testSql)
                : ckStdCrpNewIdcDBHelper.getRaw(InputDTO.class, testSql, excludeAppIds);
        task.setTestDataset(ListUtils.transform(test, o -> {
            CreateTaskInputDataDTO d = new CreateTaskInputDataDTO();
            d.setDate(o.getStatTime());
            d.setValue(o.getCores());
            d.setDim1(o.getGinsFamily());
            d.setDim2(o.getRegionName());
            d.setDim3("");
            d.setDim4("");
            d.setDim5("");
            return d;
        }));

        Browser browser = new Browser();
        browser.addRequestHeader("Cookie", IOUtils.readClasspathResourceAsString("/废弃或已完成/db-operate/resources/exp-crp-cookie.txt").trim());

        HttpResponse resp = browser.postJson(
                "https://exp-crp.woa.com/cloud-demand-common/ops/submitForecastTask", task);
        SubmitResult parsed = JSON.parse(resp.getContentString(), SubmitResult.class);
        return parsed.getTaskId();
    }

    @Test
    public void submitForecast() throws Exception {
        Integer taskId = createTask("13周中长尾需求预测", DateUtils.parseLocalDate("2023-05"),
                1, new ArrayList<>());
        System.out.println("create task:" + taskId);
    }

    @Data
    public static class SubmitResult {
        private String uuid;
        private Integer taskId;
        private Boolean success;
    }

    @Data
    public static class InputDTO {
        @Column("stime")
        private Date statTime;
        @Column("gins_family")
        private String ginsFamily;
        @Column("region_name")
        private String regionName;
        @Column("cores")
        private BigDecimal cores;
    }

    @Data
    public static class CalAccuracyRateResult {
        List<ForecastComputeTaskInputTestDatasetVO> testDataset;
        BigDecimal accuracyRate;
    }

    public CalAccuracyRateResult calAccuracyRate(Integer taskId, String testDatasetExtraWhere) {
        // 1. 先读取测试集
        // 【特别说明】
        // 1) 现在是以真实值作为权重，也就是说，如果有预测，实际结果没有出现，还是认为准的，所以这个计算是按这种算法来简化了
        // 2) 现在是每次只测试一个月，所以直接对结果进行加权，不用分月份
        List<ForecastComputeTaskInputTestDatasetVO> testDataset = cloudDemandCommonDevDBHelper.getAll(
                ForecastComputeTaskInputTestDatasetVO.class,
                "where task_id=? " + (testDatasetExtraWhere == null ? "" : testDatasetExtraWhere), taskId);

        // 2. 再读取预测结果
        List<PredictResultDTO> predictResults = cloudDemandCommonDevDBHelper.getRaw(PredictResultDTO.class,
                "SELECT a.date,a.value,b.dim1,b.dim2,b.dim3,b.dim4,b.dim5\n" +
                        "FROM `forecast_compute_task_run_output` a\n" +
                        "LEFT JOIN `forecast_compute_task_run` b\n" +
                        "ON a.`task_run_id`=b.`id`\n" +
                        "WHERE a.task_id=?", taskId);

        // 3.1 合并数据
        testDataset = ListUtils.merge(testDataset, predictResults,
                o -> o.groupKey(), o -> o.groupKey(),
                (test, predict) -> {
                    if (ListUtils.isNotEmpty(test) && ListUtils.isNotEmpty(predict)) {
                        test.get(0).setPredictValue(predict.get(0).getValue());
                        return test.get(0);
                    } else {
                        return null;
                    }
                });
        testDataset = ListUtils.filter(testDataset, o -> o != null);

        // 3.2 过滤黑名单机型
        if (isEnableBlackList) {
            List<String> blackList = forecastCommonService.getBlacklistInstanceType();
            testDataset = ListUtils.filter(testDataset, o -> !blackList.contains(o.getDim1()));
        }

        // 4. 计算准确率
        ListUtils.forEach(testDataset, o -> {
            BigDecimal min = NumberUtils.min(o.getPredictValue(), o.getValue());
            BigDecimal max = NumberUtils.max(o.getPredictValue(), o.getValue());
            if (max.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal rate = NumberUtils.divide(min.multiply(BigDecimal.valueOf(100)), max, 6);
                o.setAccuracyRate(rate.doubleValue());
            } else {
                BigDecimal rate = BigDecimal.valueOf(100);
                o.setAccuracyRate(rate.doubleValue());
            }
        });

        // 5. 输出准确率，加权【现在是每次只测试一个月，所以直接对结果进行加权，不用分月份】
        BigDecimal total = NumberUtils.sum(testDataset, ForecastComputeTaskInputTestDatasetDO::getValue);
        ListUtils.forEach(testDataset, o -> {
            double rate = NumberUtils.divide(o.getValue(), total, 6).multiply(BigDecimal.valueOf(o.getAccuracyRate())).doubleValue();
            o.setWeightedAccuracyRate(rate);
        });

        CalAccuracyRateResult result = new CalAccuracyRateResult();
        result.setTestDataset(testDataset);
        result.setAccuracyRate(NumberUtils.sum(testDataset, o -> o.getWeightedAccuracyRate()));

        return result;
    }



    /**计算准确率*/
    @Test
    public void calAccuracyRate() throws Exception {
        Integer taskId = 601;
        CalAccuracyRateResult result = calAccuracyRate(taskId, null);
        System.out.println("准确率：" + result.getAccuracyRate());

        System.out.println("===================================");

        List<ForecastComputeTaskInputTestDatasetVO> testDataset = result.getTestDataset();
        System.out.println("dim1,dim2,准确率,真实值,预测值");
        for (ForecastComputeTaskInputTestDatasetVO v : testDataset) {
            System.out.println(v.getDim1() + "," + v.getDim2() + "," + v.getAccuracyRate() + "," + v.getValue() + "," + v.getPredictValue());
        }
    }

    /**
     * 找出影响排序的机型+地域，输入是任务id：taskId
     */
    @Test
    public void findInfluence() {
        cloudDemandCommonDevDBHelper.setSlowSqlWarningValve(1000000);
        Integer taskId = 2536;

        CalAccuracyRateResult base = calAccuracyRate(taskId, null);// 基准的准确率

        for (ForecastComputeTaskInputTestDatasetVO combination : base.getTestDataset()) {
            BigDecimal accuracyRate = calAccuracyRate(taskId,
                    " and !(dim1='" + combination.getDim1() +
                            "' and dim2='" + combination.getDim2() + "')").getAccuracyRate(); // 剔除
            BigDecimal diff =base.getAccuracyRate().subtract(accuracyRate);

            combination.setGlobalAccuracyRate(base.getAccuracyRate().doubleValue());
            combination.setGlobalAccuracyRateIfRemove(accuracyRate.doubleValue());
            combination.setDiffAccuracyRate(diff.doubleValue());
        }

        // 打印出结果
        System.out.println("机型,地域,当前项贡献准确率(正数表示提升，负数表示降低),原始全局准确率,剔除当前项之后准确率,实际值,预测值,当前项准确率");
        for (ForecastComputeTaskInputTestDatasetVO combination : base.getTestDataset()) {
            System.out.println(combination.getDim1() + "," + combination.getDim2() + ","  + combination.getDiffAccuracyRate() + "," + combination.getGlobalAccuracyRate() + "," + combination.getGlobalAccuracyRateIfRemove() +  "," + combination.getValue() + "," + combination.getPredictValue() + "," + combination.getAccuracyRate());
        }
    }

    @Data
    public static class PredictResultDTO {
        /** 时间序列日期<br/>Column: [date] */
        @Column(value = "date")
        private LocalDate date;
        /** 值<br/>Column: [value] */
        @Column(value = "value")
        private BigDecimal value;
        @Column(value = "dim1")
        private String dim1;
        @Column(value = "dim2")
        private String dim2;
        @Column(value = "dim3")
        private String dim3;
        @Column(value = "dim4")
        private String dim4;
        @Column(value = "dim5")
        private String dim5;

        public String groupKey() {
            return StringTools.join("&", getDate(), getDim1(), getDim2(), getDim3(), getDim4(), getDim5());
        }
    }

    @Data
    public static class ForecastComputeTaskInputTestDatasetVO extends  ForecastComputeTaskInputTestDatasetDO {

        /**预测值*/
        private BigDecimal predictValue;

        /**准确率百分比，已经乘以100*/
        private double accuracyRate;

        /**加权的百分比*/
        private double weightedAccuracyRate;


        /**当前测算的整体准确率*/
        private double globalAccuracyRate;
        /**移除了当前组合之后的准确率*/
        private double globalAccuracyRateIfRemove;
        /**差异，这个值为正表示这项预测做得好，为负表示预测做得不好*/
        private double diffAccuracyRate;


        public String groupKey() {
            return StringTools.join("&", getDate(), getDim1(), getDim2(), getDim3(), getDim4(), getDim5());
        }
    }


}
