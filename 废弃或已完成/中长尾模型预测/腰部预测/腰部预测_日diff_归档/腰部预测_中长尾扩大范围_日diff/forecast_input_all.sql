-- 不区分地域和可用区
select  toYear(stat_time) as year,
        to<PERSON><PERSON>h(stat_time) as month ,
        max(month_end_date) as stime, -- 注意这里不要取stat_time，因为日期可能不完全
      ${instanceTypeMergeCaseWhen} as gins_family, -- 默认用机型收敛
     region_name as region_name,
   sum(case when change_bill_service_core>0 then change_bill_service_core else 0 end) as cores -- 新增
   -- sum(case when change_bill_service_core<0 then -change_bill_service_core else 0 end) as cores -- 退回 特别说明：如果是剔除的话，elastic_point_for_daily_diff也要切换

from dwd_txy_scale_df
where 1=1
  and  zone_name in ('上海一区','上海七区','上海三区','上海二区','上海五区','上海八区','上海六区','上海四区','东京一区','东京二区','北京一区','北京七区','北京三区','北京二区','北京五区','北京八区','北京六区','北京四区','南京一区','南京三区','南京二区','台北一区','圣保罗一区','多伦多一区','天津三区','天津二区','孟买一区','孟买二区','广州七区','广州三区','广州二区','广州五区','广州八区','广州六区','广州四区','弗吉尼亚一区','弗吉尼亚二区','成都一区','成都二区','新加坡一区','新加坡三区','新加坡二区','新加坡四区','曼谷一区','曼谷二区','欧洲东北一区','法兰克福一区','法兰克福三区','法兰克福二区','深圳一区','深圳三区','深圳二区','深圳四区','硅谷一区','硅谷二区','贵阳一区','重庆一区','雅加达一区','雅加达二区','首尔一区','首尔二区','香港一区','香港三区','香港二区')
  and cpu_or_gpu = 'CPU'
  and biz_type = 'cvm'
  and app_role!='LH'
  and instance_type not like 'RS%' and instance_type not like 'RM%'
  and  paymode_range_type !='弹性'
  and customer_tab_type in ('名单客户')
--  and biz_range_type='外部业务'
  and industry_dept!='战略客户部'

  and app_id not in (?)

  -- and uin not in (2912314241,2409831278)
  -- 剔除2个重大的弹性case
  and not ((uin,zone_name,instance_model) in ((2912314241,'广州三区','S5.LARGE8'), (2912314241,'广州三区','S5.MEDIUM4')))
  and not ((uin,zone_name,instance_model) in ((2409831278,'广州六区','TS5.4XLARGE64'), (2409831278,'广州七区','TS5.4XLARGE64')))

-- 主力机型主力地域
--  and instance_type in ('S5','S6','SA3','SA2') and region_name in ('上海','北京','广州','南京')
-- 主力机型非主力地域
--and instance_type in ('S5','S6','SA3','SA2') and region_name not in ('上海','北京','广州','南京')

    ${timeRange} -- 时间范围
group by toYear(stat_time), toMonth(stat_time)
    , region_name
    , ${instanceTypeMergeCaseWhen}
