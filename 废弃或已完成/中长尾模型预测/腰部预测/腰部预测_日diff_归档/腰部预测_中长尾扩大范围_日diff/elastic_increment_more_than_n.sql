-- 得到客户最细粒度的本月新增量和上月新增量，目的是为了拿到本月和上月的比例N值
with dwd_txy_scale_df_yaobu as (
    select *
    from dwd_txy_scale_df
    where cpu_or_gpu = 'CPU'
      and biz_type = 'cvm'
      and app_role != 'LH'
    and instance_type not like 'RS%'
    and instance_type not like 'RM%'
    and customer_tab_type in ('名单客户')
    and biz_range_type = '外部业务'
    and industry_dept != '战略客户部'
    and paymode_range_type != '弹性'

    and  zone_name in ('上海一区','上海七区','上海三区','上海二区','上海五区','上海八区','上海六区','上海四区','东京一区','东京二区','北京一区','北京七区','北京三区','北京二区','北京五区','北京八区','北京六区','北京四区','南京一区','南京三区','南京二区','台北一区','圣保罗一区','多伦多一区','天津三区','天津二区','孟买一区','孟买二区','广州七区','广州三区','广州二区','广州五区','广州八区','广州六区','广州四区','弗吉尼亚一区','弗吉尼亚二区','成都一区','成都二区','新加坡一区','新加坡三区','新加坡二区','新加坡四区','曼谷一区','曼谷二区','欧洲东北一区','法兰克福一区','法兰克福三区','法兰克福二区','深圳一区','深圳三区','深圳二区','深圳四区','硅谷一区','硅谷二区','贵阳一区','重庆一区','雅加达一区','雅加达二区','首尔一区','首尔二区','香港一区','香港三区','香港二区')

    -- 剔除2个重大的弹性case
    and not ((uin,zone_name,instance_model) in ((2912314241,'广州三区','S5.LARGE8'), (2912314241,'广州三区','S5.MEDIUM4')))
    and not ((uin,zone_name,instance_model) in ((2409831278,'广州六区','TS5.4XLARGE64'), (2409831278,'广州七区','TS5.4XLARGE64')))

    ${timeRange} -- 时间范围 and stat_time between '2021-01-01' and '2023-09-30'

    ),
    dwd_txy_scale_df_yaobu_this_month as (
select year as year1,month as month1,uin,zone_name,instance_model,any(region_name) as region_name, any(instance_type) as instance_type,
    sum(case when change_bill_service_core>0 then change_bill_service_core else 0 end) as new_core
from dwd_txy_scale_df_yaobu
group by year,month,uin,zone_name,instance_model
    ),
    dwd_txy_scale_df_yaobu_last_month as (
select (case when month=12 then year+1 else year end) as year1,(case when month=12 then 1 else month+1 end) as month1, -- 年月加1个月
    uin,zone_name,instance_model,any(region_name) as region_name, any(instance_type) as instance_type,
    sum(case when change_bill_service_core>0 then change_bill_service_core else 0 end) as new_core
from dwd_txy_scale_df_yaobu
group by year,month,uin,zone_name,instance_model
    ),
    dwd_txy_scale_df_yaobu_last_2_month as (
select (case when month=11 then year+1 else year end) as year1,(case when month=11 then 1 else month+2 end) as month1, -- 年月加2个月
    uin,zone_name,instance_model,any(region_name) as region_name, any(instance_type) as instance_type,
    sum(case when change_bill_service_core>0 then change_bill_service_core else 0 end) as new_core
from dwd_txy_scale_df_yaobu
group by year,month,uin,zone_name,instance_model
    ),
    dwd_txy_scale_df_yaobu_last_3_month as (
select (case when month=10 then year+1 else year end) as year1,(case when month=10 then 1 else month+3 end) as month1, -- 年月加3个月
    uin,zone_name,instance_model,any(region_name) as region_name, any(instance_type) as instance_type,
    sum(case when change_bill_service_core>0 then change_bill_service_core else 0 end) as new_core
from dwd_txy_scale_df_yaobu
group by year,month,uin,zone_name,instance_model
    ),
dwd_txy_scale_df_yaobu_this_last_month as (
select a.year1 as year, a.month1 as month,
    a.uin as uin, a.zone_name as zone_name, a.instance_model as instance_model,
    a.region_name as region_name, a.instance_type as instance_type,
    a.new_core as this_month_new_core,
    (case when b.new_core is null then 0 else b.new_core end) as last_month_new_core,
    (case when c.new_core is null then 0 else c.new_core end) as last_2_month_new_core,
    (case when d.new_core is null then 0 else d.new_core end) as last_3_month_new_core
from dwd_txy_scale_df_yaobu_this_month a
    left join dwd_txy_scale_df_yaobu_last_month b
on a.year1=b.year1 and a.month1=b.month1 and a.uin=b.uin and a.zone_name=b.zone_name and a.instance_model=b.instance_model
    left join dwd_txy_scale_df_yaobu_last_2_month c
    on a.year1=c.year1 and a.month1=c.month1 and a.uin=c.uin and a.zone_name=c.zone_name and a.instance_model=c.instance_model
    left join dwd_txy_scale_df_yaobu_last_3_month d
    on a.year1=d.year1 and a.month1=d.month1 and a.uin=d.uin and a.zone_name=d.zone_name and a.instance_model=d.instance_model
    )
select year,month,uin,zone_name,instance_model,region_name,instance_type,
    this_month_new_core,last_month_new_core,last_2_month_new_core,last_3_month_new_core,
    (last_month_new_core+last_2_month_new_core)/2 as avg_last_2_month_new_core,
    (last_month_new_core+last_2_month_new_core+last_3_month_new_core)/3 as avg_last_3_month_new_core
from dwd_txy_scale_df_yaobu_this_last_month
where this_month_new_core>${elasticNewCoreValve} and (last_month_new_core=0 or this_month_new_core/last_month_new_core>${elasticNValve})
