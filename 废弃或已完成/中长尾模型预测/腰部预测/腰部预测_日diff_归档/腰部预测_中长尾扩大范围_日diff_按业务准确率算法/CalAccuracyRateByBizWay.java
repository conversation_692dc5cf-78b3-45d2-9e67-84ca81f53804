package com.pugwoo.dboperate.archived.腰部预测.腰部预测_日diff_归档.腰部预测_中长尾扩大范围_日diff_按业务准确率算法;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dboperate.common.ForecastCommonService;
import com.pugwoo.dboperate.forecast_compute.entity.ForecastComputeTaskInputTestDatasetDO;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import com.pugwoo.wooutils.string.StringTools;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 以业务的方式来计算准确率：可用区+机型大类先净增处理
 */
@Slf4j
@SpringBootTest
public class CalAccuracyRateByBizWay {

    @Resource
    private DBHelper ckStdCrpNewIdcDBHelper;
    @Resource
    private DBHelper cloudDemandCommonDevDBHelper;
    @Resource
    private ForecastCommonService forecastCommonService;

    private boolean isEnableBlackList = true; /*计算准确率时，是否排除掉黑名单机型，目前页面是排除了的*/

    @Test
    public void 测试验证() {
        Map<String, String> zoneToRegionMap = getZoneToRegionMap();

        System.out.println("============================== 提前1期 ==================");
        System.out.println("2023-08-01");
        calAccuracyRate(14533L, 15276L, zoneToRegionMap);
    }

    @Test
    public void test内外部() {
        Map<String, String> zoneToRegionMap = getZoneToRegionMap();

        System.out.println("============================== 提前1期 ==================");
        System.out.println("2023-01-01");
        calAccuracyRate(14523L, 15266L, zoneToRegionMap);
        System.out.println("2023-02-01");
        calAccuracyRate(14525L, 15268L, zoneToRegionMap);
        System.out.println("2023-03-01");
        calAccuracyRate(14527L, 15269L, zoneToRegionMap);
        System.out.println("2023-04-01");
        calAccuracyRate(14528L, 15270L, zoneToRegionMap);
        System.out.println("2023-05-01");
        calAccuracyRate(14529L, 15271L, zoneToRegionMap);
        System.out.println("2023-06-01");
        calAccuracyRate(14531L, 15273L, zoneToRegionMap);
        System.out.println("2023-07-01");
        calAccuracyRate(14532L, 15274L, zoneToRegionMap);
        System.out.println("2023-08-01");
        calAccuracyRate(14533L, 15276L, zoneToRegionMap);

        System.out.println("============================== 提前2期 ==================");
        System.out.println("2023-01-01");
        calAccuracyRate(14540L, 15282L, zoneToRegionMap);
        System.out.println("2023-02-01");
        calAccuracyRate(14541L, 15284L, zoneToRegionMap);
        System.out.println("2023-03-01");
        calAccuracyRate(14542L, 15285L, zoneToRegionMap);
        System.out.println("2023-04-01");
        calAccuracyRate(14543L, 15287L, zoneToRegionMap);
        System.out.println("2023-05-01");
        calAccuracyRate(14544L, 15288L, zoneToRegionMap);
        System.out.println("2023-06-01");
        calAccuracyRate(14546L, 15290L, zoneToRegionMap);
        System.out.println("2023-07-01");
        calAccuracyRate(14547L, 15291L, zoneToRegionMap);
        System.out.println("2023-08-01");
        calAccuracyRate(14548L, 15293L, zoneToRegionMap);
    }

    @Test
    public void test外部() {
        Map<String, String> zoneToRegionMap = getZoneToRegionMap();

        System.out.println("============================== 提前1期 ==================");
        System.out.println("2023-01-01");
        calAccuracyRate(15629L, 15655L, zoneToRegionMap);
        System.out.println("2023-02-01");
        calAccuracyRate(15630L, 15657L, zoneToRegionMap);
        System.out.println("2023-03-01");
        calAccuracyRate(15631L, 15659L, zoneToRegionMap);
        System.out.println("2023-04-01");
        calAccuracyRate(15632L, 15660L, zoneToRegionMap);
        System.out.println("2023-05-01");
        calAccuracyRate(15635L, 15661L, zoneToRegionMap);
        System.out.println("2023-06-01");
        calAccuracyRate(15636L, 15662L, zoneToRegionMap);
        System.out.println("2023-07-01");
        calAccuracyRate(15637L, 15663L, zoneToRegionMap);
        System.out.println("2023-08-01");
        calAccuracyRate(15638L, 15665L, zoneToRegionMap);

        System.out.println("============================== 提前2期 ==================");
        System.out.println("2023-01-01");
        calAccuracyRate(15642L, 15670L, zoneToRegionMap);
        System.out.println("2023-02-01");
        calAccuracyRate(15643L, 15671L, zoneToRegionMap);
        System.out.println("2023-03-01");
        calAccuracyRate(15645L, 15672L, zoneToRegionMap);
        System.out.println("2023-04-01");
        calAccuracyRate(15646L, 15674L, zoneToRegionMap);
        System.out.println("2023-05-01");
        calAccuracyRate(15647L, 15675L, zoneToRegionMap);
        System.out.println("2023-06-01");
        calAccuracyRate(15649L, 15676L, zoneToRegionMap);
        System.out.println("2023-07-01");
        calAccuracyRate(15650L, 15678L, zoneToRegionMap);
        System.out.println("2023-08-01");
        calAccuracyRate(15651L, 15679L, zoneToRegionMap);
    }

    private void calAccuracyRate(Long taskIdNew, Long taskIdRet, Map<String, String> zoneToRegionMap) {
        // 1. 先读取测试集，新增和退回
        List<ForecastComputeTaskInputTestDatasetVO> testDatasetNew = cloudDemandCommonDevDBHelper.getAll(
                ForecastComputeTaskInputTestDatasetVO.class,
                "where task_id=? ", taskIdNew);
        List<ForecastComputeTaskInputTestDatasetVO> testDatasetRet = cloudDemandCommonDevDBHelper.getAll(
                ForecastComputeTaskInputTestDatasetVO.class,
                "where task_id=? ", taskIdRet);

        List<LocalDate> testDate = ListUtils.distinct(ListUtils.transform(testDatasetNew, o -> o.getDate()), o -> o);

        // 2. 再读取预测结果，新增和退回
        String sql = "SELECT a.date,a.value,b.dim1,b.dim2,b.dim3,b.dim4,b.dim5\n" +
                "FROM `forecast_compute_task_run_output` a\n" +
                "LEFT JOIN `forecast_compute_task_run` b\n" +
                "ON a.`task_run_id`=b.`id`\n" +
                "WHERE a.task_id=?";
        List<PredictResultDTO> predictResultsNew = cloudDemandCommonDevDBHelper.getRaw(PredictResultDTO.class,
                sql, taskIdNew);
        predictResultsNew = ListUtils.filter(predictResultsNew, o -> testDate.contains(o.getDate()));

        List<PredictResultDTO> predictResultsRet = cloudDemandCommonDevDBHelper.getRaw(PredictResultDTO.class,
                sql, taskIdRet);
        predictResultsRet = ListUtils.filter(predictResultsRet, o -> testDate.contains(o.getDate()));

        // 3. 以年、月、可用区、机型大类，将新增退回聚合成净增值；注意dim2是可用区
        List<ForecastComputeTaskInputTestDatasetVO> testDatasetNet = ListUtils.merge(testDatasetNew, testDatasetRet,
                o -> StringTools.join("&", DateUtils.formatDate(o.getDate()), o.getDim1(), o.getDim2()),
                o -> StringTools.join("&", DateUtils.formatDate(o.getDate()), o.getDim1(), o.getDim2()),
                (newList, retList) -> {
            if (ListUtils.isNotEmpty(newList) && newList.size() > 1) {
                log.error("新增数据有重复，不应该有重复的:{}", JSON.toJson(newList));
                return null;
            }
            if (ListUtils.isNotEmpty(retList) && retList.size() > 1) {
                log.error("退回数据有重复，不应该有重复的:{}", JSON.toJson(retList));
                return null;
            }

            ForecastComputeTaskInputTestDatasetVO newOne = null;
            if (ListUtils.isEmpty(newList)) {
                newOne = new ForecastComputeTaskInputTestDatasetVO();
                newOne.setDate(retList.get(0).getDate());
                newOne.setDim1(retList.get(0).getDim1());
                newOne.setDim2(retList.get(0).getDim2());
                newOne.setDim3(retList.get(0).getDim3());
                newOne.setDim4(retList.get(0).getDim4());
                newOne.setDim5(retList.get(0).getDim5());
                newOne.setValue(BigDecimal.ZERO);
            } else {
                newOne = newList.get(0);
            }

            ForecastComputeTaskInputTestDatasetVO retOne = null;
            if (ListUtils.isEmpty(retList)) {
                retOne = new ForecastComputeTaskInputTestDatasetVO();
                retOne.setDate(newOne.getDate());
                retOne.setDim1(newOne.getDim1());
                retOne.setDim2(newOne.getDim2());
                retOne.setDim3(newOne.getDim3());
                retOne.setDim4(newOne.getDim4());
                retOne.setDim5(newOne.getDim5());
                retOne.setValue(BigDecimal.ZERO);
            } else {
                retOne = retList.get(0);
            }

            ForecastComputeTaskInputTestDatasetVO vo = new ForecastComputeTaskInputTestDatasetVO();
            vo.setDate(newOne.getDate());
            vo.setDim1(newOne.getDim1());
            vo.setDim2(newOne.getDim2());
            vo.setDim3(newOne.getDim3());
            vo.setDim4(newOne.getDim4());
            vo.setDim5(newOne.getDim5());
            vo.setValue(newOne.getValue().subtract(retOne.getValue()));
            return vo;
        });

//        System.out.println("===================== 验证集，广州S5");
//        for (ForecastComputeTaskInputTestDatasetVO vo : testDatasetNet) {
//            if (Objects.equals(vo.getDim1(), "S5") &&
//                    vo.getDim2().startsWith("广州")) {
//                System.out.println(vo.getDate() + "," + vo.getDim1() + "," +vo.getDim2() + "," + vo.getValue());
//            }
//        }

        // 4. 预测也是做一样的事情，先聚合成净增值
        List<PredictResultDTO> predictResultsNet = ListUtils.merge(predictResultsNew, predictResultsRet,
                o -> StringTools.join("&", DateUtils.formatDate(o.getDate()), o.getDim1(), o.getDim2()),
                o -> StringTools.join("&", DateUtils.formatDate(o.getDate()), o.getDim1(), o.getDim2()),
                (newList, retList) -> {
                    if (ListUtils.isNotEmpty(newList) && newList.size() > 1) {
                        log.error("新增数据有重复，不应该有重复的:{}", JSON.toJson(newList));
                        return null;
                    }
                    if (ListUtils.isNotEmpty(retList) && retList.size() > 1) {
                        log.error("退回数据有重复，不应该有重复的:{}", JSON.toJson(retList));
                        return null;
                    }

                    PredictResultDTO newOne = null;
                    if (ListUtils.isEmpty(newList)) {
                        newOne = new PredictResultDTO();
                        newOne.setDate(retList.get(0).getDate());
                        newOne.setDim1(retList.get(0).getDim1());
                        newOne.setDim2(retList.get(0).getDim2());
                        newOne.setDim3(retList.get(0).getDim3());
                        newOne.setDim4(retList.get(0).getDim4());
                        newOne.setDim5(retList.get(0).getDim5());
                        newOne.setValue(BigDecimal.ZERO);
                    } else {
                        newOne = newList.get(0);
                    }

                    PredictResultDTO retOne = null;
                    if (ListUtils.isEmpty(retList)) {
                        retOne = new PredictResultDTO();
                        retOne.setDate(newOne.getDate());
                        retOne.setDim1(newOne.getDim1());
                        retOne.setDim2(newOne.getDim2());
                        retOne.setDim3(newOne.getDim3());
                        retOne.setDim4(newOne.getDim4());
                        retOne.setDim5(newOne.getDim5());
                        retOne.setValue(BigDecimal.ZERO);
                    } else {
                        retOne = retList.get(0);
                    }

                    PredictResultDTO vo = new PredictResultDTO();
                    vo.setDate(newOne.getDate());
                    vo.setDim1(newOne.getDim1());
                    vo.setDim2(newOne.getDim2());
                    vo.setDim3(newOne.getDim3());
                    vo.setDim4(newOne.getDim4());
                    vo.setDim5(newOne.getDim5());
                    vo.setValue(newOne.getValue().subtract(retOne.getValue()));
                    return vo;
                });

//        System.out.println("===================== 预测集，广州S5");
//        for (PredictResultDTO vo : predictResultsNet) {
//            if (Objects.equals(vo.getDim1(), "S5") &&
//                    vo.getDim2().startsWith("广州")) {
//                System.out.println(vo.getDate() + "," + vo.getDim1() + "," +vo.getDim2() + "," + vo.getValue());
//            }
//        }


        // 5. 按可用区 -> 地域的方式，将净增值分为新增和退回两个序列，执行和预测都这么处理
        List<ForecastComputeTaskInputTestDatasetVO> testDatasetByRegionNew = new ArrayList<>();
        List<ForecastComputeTaskInputTestDatasetVO> testDatasetByRegionRet = new ArrayList<>();

        for (ForecastComputeTaskInputTestDatasetVO td : testDatasetNet) {
            LocalDate date = td.getDate();
            String instanceType = td.getDim1();
            String zoneName = td.getDim2();
            String regionName = zoneToRegionMap.get(zoneName);
            if (regionName == null) {
                log.error("可用区没有对应的地域，可用区：{}", zoneName);
                regionName = zoneName;
            }
            if (td.getValue().compareTo(BigDecimal.ZERO) > 0) {
                // 新增队列
                String finalRegionName = regionName;
                List<ForecastComputeTaskInputTestDatasetVO> filter = ListUtils.filter(testDatasetByRegionNew, o ->
                        Objects.equals(o.getDate(), date) && Objects.equals(o.getDim1(), instanceType) && Objects.equals(o.getDim2(), finalRegionName));
                if (filter.isEmpty()) {
                    ForecastComputeTaskInputTestDatasetVO v = new ForecastComputeTaskInputTestDatasetVO();
                    v.setDate(date);
                    v.setDim1(instanceType);
                    v.setDim2(regionName);
                    v.setDim3("");
                    v.setDim4("");
                    v.setDim5("");
                    v.setValue(td.getValue());
                    testDatasetByRegionNew.add(v);
                } else {
                    filter.get(0).setValue(filter.get(0).getValue().add(td.getValue()));
                }
            } else if (td.getValue().compareTo(BigDecimal.ZERO) < 0) {
                // 退回队列
                String finalRegionName1 = regionName;
                List<ForecastComputeTaskInputTestDatasetVO> filter = ListUtils.filter(testDatasetByRegionRet, o ->
                        Objects.equals(o.getDate(), date) && Objects.equals(o.getDim1(), instanceType) && Objects.equals(o.getDim2(), finalRegionName1));
                if (filter.isEmpty()) {
                    ForecastComputeTaskInputTestDatasetVO v = new ForecastComputeTaskInputTestDatasetVO();
                    v.setDate(date);
                    v.setDim1(instanceType);
                    v.setDim2(regionName);
                    v.setDim3("");
                    v.setDim4("");
                    v.setDim5("");
                    v.setValue(BigDecimal.ZERO.subtract(td.getValue()));
                    testDatasetByRegionRet.add(v);
                } else {
                    filter.get(0).setValue(filter.get(0).getValue().add(BigDecimal.ZERO.subtract(td.getValue())));
                }
            }
        }

        List<PredictResultDTO> predictResultsByRegionNew = new ArrayList<>();
        List<PredictResultDTO> predictResultsByRegionRet = new ArrayList<>();

        for (PredictResultDTO pd : predictResultsNet) {
            LocalDate date = pd.getDate();
            String instanceType = pd.getDim1();
            String zoneName = pd.getDim2();
            String regionName = zoneToRegionMap.get(zoneName);
            if (regionName == null) {
                log.error("可用区没有对应的地域，可用区：{}", zoneName);
                regionName = zoneName;
            }
            if (pd.getValue().compareTo(BigDecimal.ZERO) > 0) {
                // 新增队列
                String finalRegionName = regionName;
                List<PredictResultDTO> filter = ListUtils.filter(predictResultsByRegionNew, o ->
                        Objects.equals(o.getDate(), date) && Objects.equals(o.getDim1(), instanceType) && Objects.equals(o.getDim2(), finalRegionName));
                if (filter.isEmpty()) {
                    PredictResultDTO v = new PredictResultDTO();
                    v.setDate(date);
                    v.setDim1(instanceType);
                    v.setDim2(regionName);
                    v.setDim3("");
                    v.setDim4("");
                    v.setDim5("");
                    v.setValue(pd.getValue());
                    predictResultsByRegionNew.add(v);
                } else {
                    filter.get(0).setValue(filter.get(0).getValue().add(pd.getValue()));
                }
            } else if (pd.getValue().compareTo(BigDecimal.ZERO) < 0) {
                // 退回队列
                String finalRegionName1 = regionName;
                List<PredictResultDTO> filter = ListUtils.filter(predictResultsByRegionRet, o ->
                        Objects.equals(o.getDate(), date) && Objects.equals(o.getDim1(), instanceType) && Objects.equals(o.getDim2(), finalRegionName1));
                if (filter.isEmpty()) {
                    PredictResultDTO v = new PredictResultDTO();
                    v.setDate(date);
                    v.setDim1(instanceType);
                    v.setDim2(regionName);
                    v.setDim3("");
                    v.setDim4("");
                    v.setDim5("");
                    v.setValue(BigDecimal.ZERO.subtract(pd.getValue()));
                    predictResultsByRegionRet.add(v);
                } else {
                    filter.get(0).setValue(filter.get(0).getValue().add(BigDecimal.ZERO.subtract(pd.getValue())));
                }
            }
        }

//        System.out.println("===================== 验证集，广州S5，新增");
//        for (ForecastComputeTaskInputTestDatasetVO v : testDatasetByRegionNew) {
//            if (Objects.equals(v.getDim1(), "S5") &&
//                    v.getDim2().startsWith("广州")) {
//                System.out.println(v.getDate() + "," + v.getDim1() + "," +v.getDim2() + "," + v.getValue());
//            }
//        }
//        System.out.println("===================== 验证集，广州S5，退回");
//        for (ForecastComputeTaskInputTestDatasetVO v : testDatasetByRegionRet) {
//            if (Objects.equals(v.getDim1(), "S5") &&
//                    v.getDim2().startsWith("广州")) {
//                System.out.println(v.getDate() + "," + v.getDim1() + "," +v.getDim2() + "," + v.getValue());
//            }
//        }
//
//        System.out.println("===================== 预测集，广州S5，新增");
//        for (PredictResultDTO v : predictResultsByRegionNew) {
//            if (Objects.equals(v.getDim1(), "S5") &&
//                    v.getDim2().startsWith("广州")) {
//                System.out.println(v.getDate() + "," + v.getDim1() + "," +v.getDim2() + "," + v.getValue());
//            }
//        }
//        System.out.println("===================== 预测集，广州S5，退回");
//        for (PredictResultDTO v : predictResultsByRegionRet) {
//            if (Objects.equals(v.getDim1(), "S5") &&
//                    v.getDim2().startsWith("广州")) {
//                System.out.println(v.getDate() + "," + v.getDim1() + "," +v.getDim2() + "," + v.getValue());
//            }
//        }


        // 6. 过滤黑名单机型
        if (isEnableBlackList) {
            List<String> blackList = forecastCommonService.getBlacklistInstanceType();
            testDatasetByRegionNew = ListUtils.filter(testDatasetByRegionNew, o -> !blackList.contains(o.getDim1()));
            testDatasetByRegionRet = ListUtils.filter(testDatasetByRegionRet, o -> !blackList.contains(o.getDim1()));
        }

        // 7. 合并数据
        testDatasetByRegionNew = ListUtils.merge(testDatasetByRegionNew, predictResultsByRegionNew,
                o -> o.groupKey(), o -> o.groupKey(),
                (test, predict) -> {
                    if (ListUtils.isNotEmpty(test) && ListUtils.isNotEmpty(predict)) {
                        test.get(0).setPredictValue(predict.get(0).getValue());
                        return test.get(0);
                    } else {
                        return null;
                    }
                });
        testDatasetByRegionNew = ListUtils.filter(testDatasetByRegionNew, o -> o != null);

        testDatasetByRegionRet = ListUtils.merge(testDatasetByRegionRet, predictResultsByRegionRet,
                o -> o.groupKey(), o -> o.groupKey(),
                (test, predict) -> {
                    if (ListUtils.isNotEmpty(test) && ListUtils.isNotEmpty(predict)) {
                        test.get(0).setPredictValue(predict.get(0).getValue());
                        return test.get(0);
                    } else {
                        return null;
                    }
                });
        testDatasetByRegionRet = ListUtils.filter(testDatasetByRegionRet, o -> o != null);

        // 8. 安装新增退回分开算准确率
        ListUtils.forEach(testDatasetByRegionNew, o -> {
            BigDecimal min = NumberUtils.min(o.getPredictValue(), o.getValue());
            BigDecimal max = NumberUtils.max(o.getPredictValue(), o.getValue());
            if (max.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal rate = NumberUtils.divide(min.multiply(BigDecimal.valueOf(100)), max, 6);
                o.setAccuracyRate(rate.doubleValue());
            } else {
                BigDecimal rate = BigDecimal.valueOf(100);
                o.setAccuracyRate(rate.doubleValue());
            }
        });

        ListUtils.forEach(testDatasetByRegionRet, o -> {
            BigDecimal min = NumberUtils.min(o.getPredictValue(), o.getValue());
            BigDecimal max = NumberUtils.max(o.getPredictValue(), o.getValue());
            if (max.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal rate = NumberUtils.divide(min.multiply(BigDecimal.valueOf(100)), max, 6);
                o.setAccuracyRate(rate.doubleValue());
            } else {
                BigDecimal rate = BigDecimal.valueOf(100);
                o.setAccuracyRate(rate.doubleValue());
            }
        });

//        // 打印广州S5的准确率
//        System.out.println("===================== 广州S5，新增准确率");
//        for (ForecastComputeTaskInputTestDatasetVO v : testDatasetByRegionNew) {
//            if (Objects.equals(v.getDim1(), "S5") &&
//                    v.getDim2().startsWith("广州")) {
//                System.out.println(v.getDate() + "," + v.getDim1() + "," +v.getDim2() + "," + v.getAccuracyRate());
//            }
//        }
//        System.out.println("===================== 广州S5，退回准确率");
//        for (ForecastComputeTaskInputTestDatasetVO v : testDatasetByRegionRet) {
//            if (Objects.equals(v.getDim1(), "S5") &&
//                    v.getDim2().startsWith("广州")) {
//                System.out.println(v.getDate() + "," + v.getDim1() + "," +v.getDim2() + "," + v.getAccuracyRate());
//            }
//        }

        // 9. 加权汇总成整体的新增准确率、退回准确率
        BigDecimal newTotal = NumberUtils.sum(testDatasetByRegionNew, ForecastComputeTaskInputTestDatasetDO::getValue);
        ListUtils.forEach(testDatasetByRegionNew, o -> {
            double rate = NumberUtils.divide(o.getValue(), newTotal, 6).multiply(BigDecimal.valueOf(o.getAccuracyRate())).doubleValue();
            o.setWeightedAccuracyRate(rate);
        });

        BigDecimal retTotal = NumberUtils.sum(testDatasetByRegionRet, ForecastComputeTaskInputTestDatasetDO::getValue);
        ListUtils.forEach(testDatasetByRegionRet, o -> {
            double rate = NumberUtils.divide(o.getValue(), retTotal, 6).multiply(BigDecimal.valueOf(o.getAccuracyRate())).doubleValue();
            o.setWeightedAccuracyRate(rate);
        });

        // 10. 加成最终准确率
        BigDecimal accuracyNew = NumberUtils.sum(testDatasetByRegionNew, o -> o.getWeightedAccuracyRate());
        BigDecimal accuracyRet = NumberUtils.sum(testDatasetByRegionRet, o -> o.getWeightedAccuracyRate());

        System.out.println("新增准确率：" + accuracyNew + "，退回准确率：" + accuracyRet);
    }


    private Map<String, String> getZoneToRegionMap() {
        List<ZoneAndRegion> list = ckStdCrpNewIdcDBHelper.getRaw(ZoneAndRegion.class,
                "select distinct zone_name, region_name\n" +
                        "from dwd_txy_scale_df\n" +
                        "where stat_time between '2023-01-01' and '2023-09-30'\n" +
                        " and cpu_or_gpu = 'CPU'\n" +
                        " and biz_type = 'cvm'\n" +
                        " and app_role != 'LH'\n" +
                        " and instance_type not like 'RS%'\n" +
                        " and instance_type not like 'RM%'\n" +
                        " and customer_tab_type in ('名单客户')\n" +
                        " and industry_dept != '战略客户部'\n" +
                        " and paymode_range_type != '弹性'");
        return ListUtils.toMap(list, o -> o.getZoneName(), o -> o.getRegionName());
    }

    @Data
    public static class ZoneAndRegion {
        @Column("zone_name")
        private String zoneName;
        @Column("region_name")
        private String regionName;
    }

    // TODO 等上面统一口径的算完之后，再按梦颖的需求532/执行的方式再算一遍吧，一步一步来

    @Data
    public static class ForecastComputeTaskInputTestDatasetVO extends ForecastComputeTaskInputTestDatasetDO {
        /**预测值*/
        private BigDecimal predictValue;

        /**准确率百分比，已经乘以100*/
        private double accuracyRate;

        /**加权的百分比*/
        private double weightedAccuracyRate;

        /**当前测算的整体准确率*/
        private double globalAccuracyRate;
        /**移除了当前组合之后的准确率*/
        private double globalAccuracyRateIfRemove;
        /**差异，这个值为正表示这项预测做得好，为负表示预测做得不好*/
        private double diffAccuracyRate;

        public String groupKey() {
            return StringTools.join("&", getDate(), getDim1(), getDim2(), getDim3(), getDim4(), getDim5());
        }
    }

    @Data
    public static class PredictResultDTO {
        /** 时间序列日期<br/>Column: [date] */
        @Column(value = "date")
        private LocalDate date;
        /** 值<br/>Column: [value] */
        @Column(value = "value")
        private BigDecimal value;
        @Column(value = "dim1")
        private String dim1;
        @Column(value = "dim2")
        private String dim2;
        @Column(value = "dim3")
        private String dim3;
        @Column(value = "dim4")
        private String dim4;
        @Column(value = "dim5")
        private String dim5;

        public String groupKey() {
            return StringTools.join("&", getDate(), getDim1(), getDim2(), getDim3(), getDim4(), getDim5());
        }

    }

}
