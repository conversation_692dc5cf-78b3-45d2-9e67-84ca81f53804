select region_name,instance_type,
       sum(case when demand_type in ('NEW','ELASTIC') then total_core else 0 end) as new_core,
       sum(case when demand_type in ('RETURN') then total_core else 0 end) as ret_core
from dwd_crp_ppl_item_cf
where product='CVM&CBS'

-- 排除掉内部业务和战略客户部
and industry_dept not in ('运营管理部','云智研发中心','云运营管理部','云产品二部','云技术运营服务部','云产品一部','区域解决方案平台部','技术运营部','云架构平台部','云鼎实验室','产业生态合作部','PCG技术与内容平台','技术架构部','安全产品三部','企业微信应用部','IEG','自研上云','内部业务','内部业务部')

    ${EXCLUDE_ZHANLUE}

    ${WITH_COMD_INTERVENE}


and year=:year and month=:month
group by region_name,instance_type