package com.pugwoo.dboperate.archived.中长尾方式算PPL准确率;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dboperate.common.ReadFileUtils;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import lombok.Data;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用中长尾的方式来计算头部ppl的准确率
 */
@SpringBootTest
public class CalForecastRateByLongTailMethod {

    @Resource
    private DBHelper ckStdCrpNewIdcDBHelper;

    @Data
    public static class AmountDTO {
        @Column("region_name")
        private String regionName;
        @Column("instance_type")
        private String instanceType;
        @Column("new_core")
        private BigDecimal newCore;
        @Column("ret_core")
        private BigDecimal retCore;
    }

    private static final boolean isExcludeZhanlue = true; // 是否排除战略客户部

    private static final boolean isWithComd = true; // 是否包含云运管干预

    @Test
    public void cal() {
        System.out.println("【范围】" + (isExcludeZhanlue ? "腰部" : "头部+腰部") + "," + (isWithComd ? "包含云运管干预" : "不包含云运管干预"));

        ckStdCrpNewIdcDBHelper.setSlowSqlWarningValve(1000000);

        String beginMonth = "2023-01";
        String endMonth = "2023-09";

        while(beginMonth.compareTo(endMonth) <= 0) {
            doCal(beginMonth);

            LocalDate b = DateUtils.parseLocalDate(beginMonth).plusMonths(1);
            beginMonth = DateUtils.format(b, "yyyy-MM");
        }
    }

    private void doCal(String month) {
        Map<String, Object> sqlParam = new HashMap<>();
        sqlParam.put("monthBegin", DateUtils.getFirstDayOfMonth(DateUtils.parse(month)));
        sqlParam.put("monthEnd", DateUtils.getLastDayOfMonth(DateUtils.parse(month)));
        sqlParam.put("year", DateUtils.getYear(DateUtils.parse(month)));
        sqlParam.put("month", DateUtils.getMonth(DateUtils.parse(month)));

        // 1. 查出ppl新增和弹性类型，CVM的，按地域、机型大类聚合（这里先拿最新数据就好）
        String pplSql = ReadFileUtils.read("ppl.sql");
        if (isExcludeZhanlue) {
            pplSql = pplSql.replace("${EXCLUDE_ZHANLUE}", "and industry_dept!='战略客户部'");
        } else{
            pplSql = pplSql.replace("${EXCLUDE_ZHANLUE}", "");
        }
        if (isWithComd) {
            pplSql = pplSql.replace("${WITH_COMD_INTERVENE}", "and ((source = 'IMPORT' and is_comd = 0) or source = 'COMD_INTERVENE')");
        } else {
            pplSql = pplSql.replace("${WITH_COMD_INTERVENE}", "and source='IMPORT'");
        }

        List<AmountDTO> ppl = ckStdCrpNewIdcDBHelper.getRaw(AmountDTO.class, pplSql, sqlParam);

        // 2. 查询头部/报备的需求量，按天/uin/可用区/机型规格 diff出新增退回，按地域、机型大类聚合
        String executeSql = ReadFileUtils.read(/*"execute_peak_sub_begin.sql"*/ "execute_daily_diff.sql" ); // 另外一种是execute_daily_diff.sql
        if (isExcludeZhanlue) {
            executeSql = executeSql.replace("${EXCLUDE_ZHANLUE}", "and industry_dept!='战略客户部'");
        } else{
            executeSql = executeSql.replace("${EXCLUDE_ZHANLUE}", "");
        }
        List<AmountDTO> execute = ckStdCrpNewIdcDBHelper.getRaw(AmountDTO.class, executeSql, sqlParam);

        // 3. 计算准确率，每个地域、机型大类，计算新增/退回的准确率，然后再按执行数加权
        System.out.println("月份:" + month);
        calRate(ppl, execute);
    }

    private void calRate(List<AmountDTO> ppl, List<AmountDTO> execute) {
        {
            int totalPpl = NumberUtils.sum(ppl, o -> o.getNewCore()).intValue();
            int total = NumberUtils.sum(execute, o -> o.getNewCore()).intValue();
            System.out.println("新增总需求：" + totalPpl + ", 新增执行数：" + total);

            BigDecimal rate = BigDecimal.ZERO;
            for (AmountDTO a : execute) {
                List<AmountDTO> p = ListUtils.filter(
                        ppl, o -> o.getRegionName().equals(a.getRegionName()) && o.getInstanceType().equals(a.getInstanceType()));
                if (p.isEmpty()) { // 没有需求，那准确率就是0，那么就不用加权了
                    continue;
                }

                int executeCore = a.getNewCore().intValue();
                int pplCore = p.get(0).getNewCore().intValue();
                int min = Math.min(executeCore, pplCore);
                int max = Math.max(executeCore, pplCore);

                if (max == 0) {
                    continue; // max为0，说明执行数权重为0，也不用加了
                } else {
                    rate = rate.add(BigDecimal.valueOf(min).divide(BigDecimal.valueOf(max), 6, BigDecimal.ROUND_HALF_UP)
                            .multiply(BigDecimal.valueOf(executeCore).divide(BigDecimal.valueOf(total), 6, BigDecimal.ROUND_HALF_UP))
                            .multiply(BigDecimal.valueOf(100)));
                }
            }

            System.out.println("新增准确率：" + rate + "%");
        }


        // 计算退回的
        {
            int totalPpl = NumberUtils.sum(ppl, o -> o.getRetCore()).intValue();
            int total = NumberUtils.sum(execute, o -> o.getRetCore()).intValue();
            System.out.println("退回总需求：" + totalPpl + ", 退回执行数：" + total);

            BigDecimal rate = BigDecimal.ZERO;
            for (AmountDTO a : execute) {
                List<AmountDTO> p = ListUtils.filter(
                        ppl, o -> o.getRegionName().equals(a.getRegionName()) && o.getInstanceType().equals(a.getInstanceType()));
                if (p.isEmpty()) { // 没有需求，那准确率就是0，那么就不用加权了
                    continue;
                }

                int executeCore = a.getRetCore().intValue();
                int pplCore = p.get(0).getRetCore().intValue();
                int min = Math.min(executeCore, pplCore);
                int max = Math.max(executeCore, pplCore);

                if (max == 0) {
                    continue; // max为0，说明执行数权重为0，也不用加了
                } else {
                    rate = rate.add(BigDecimal.valueOf(min).divide(BigDecimal.valueOf(max), 6, BigDecimal.ROUND_HALF_UP)
                            .multiply(BigDecimal.valueOf(executeCore).divide(BigDecimal.valueOf(total), 6, BigDecimal.ROUND_HALF_UP))
                            .multiply(BigDecimal.valueOf(100)));
                }
            }

            System.out.println("退回准确率：" + rate + "%");
        }

    }

}
