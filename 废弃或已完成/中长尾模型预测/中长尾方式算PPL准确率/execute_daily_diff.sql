select region_name,instance_type,
       cast(sum(case when change_core>0 then change_core else 0 end) as Int32) as new_core,
       cast(sum(case when change_core<0 then -change_core else 0 end) as Int32) as ret_core
from (
         select region_name, instance_type,
                (case when biz_range_type='外部业务' then change_bill_core else change_service_core end) change_core
         from dwd_txy_scale_df
         where stat_time between :monthBegin and :monthEnd
           and paymode_range_type!='弹性'
           and cpu_or_gpu = 'CPU'
           and biz_type = 'cvm'
           and app_role!='LH'
           and instance_type not like 'RS%' and instance_type not like 'RM%'
           and customer_tab_type in ('名单客户') and biz_range_type='外部业务'
           ${EXCLUDE_ZHANLUE}
     )
group by region_name,instance_type