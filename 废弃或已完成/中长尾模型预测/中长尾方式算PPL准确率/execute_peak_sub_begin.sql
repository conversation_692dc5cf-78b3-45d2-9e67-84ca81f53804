select region_name,instance_type,
       sum(case when new_core>0 then new_core else 0 end) as new_core,
     0 as ret_core -- TODO 暂不支持退回
from
    (
        select uin,zone_name,any(region_name) as region_name,instance_model, any(instance_type1) as instance_type, sum(core) as new_core
        from
            (
            select uin,zone_name,any(region_name) as region_name,instance_model, any(instance_type) as instance_type1,max(cur_bill_core) as core
            from dwd_txy_scale_df
            where stat_time between :monthBegin and :monthEnd
            and paymode_range_type!='弹性'
            and cpu_or_gpu = 'CPU'
            and biz_type = 'cvm'
            and app_role!='LH'
            and instance_type not like 'RS%' and instance_type not like 'RM%'
            and customer_tab_type in ('名单客户') and biz_range_type='外部业务'
            ${EXCLUDE_ZHANLUE}
            group by uin,zone_name,instance_model

            union all

            select uin,zone_name,region_name,instance_model,instance_type as instance_type1,-cur_bill_core as core
            from dwd_txy_scale_df
            where stat_time = :monthBegin
            and paymode_range_type!='弹性'
            and cpu_or_gpu = 'CPU'
            and biz_type = 'cvm'
            and app_role!='LH'
            and instance_type not like 'RS%' and instance_type not like 'RM%'
            and customer_tab_type in ('名单客户') and biz_range_type='外部业务'
            ${EXCLUDE_ZHANLUE}
            )
        group by uin,zone_name,instance_model
    )
group by region_name,instance_type