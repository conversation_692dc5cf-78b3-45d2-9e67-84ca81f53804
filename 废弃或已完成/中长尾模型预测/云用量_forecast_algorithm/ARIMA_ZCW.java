package com.pugwoo.dboperate.archived.云用量_forecast_algorithm;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.io.IOUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.net.Browser;
import com.pugwoo.wooutils.net.HttpResponse;
import com.pugwoo.wooutils.string.StringTools;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import jakarta.annotation.Resource;
import java.io.IOException;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 中长尾用arima算法的测算
 */
@SpringBootTest
public class ARIMA_ZCW {

    @Resource
    private DBHelper ckCloudDemandDevDBHelper;

    /**
     * 测试新增 - 中长尾 - 机型
     */
    @Test
    public void increaseZCWByInsfamily() throws Exception {
        // 1. 查询到数据
        List<DataDimInsfamilyDTO> data = ckCloudDemandDevDBHelper.getRaw(DataDimInsfamilyDTO.class,
                IOUtils.readClasspathResourceAsString("/废弃或已完成/db-operate/resources/cloud_forecast_sql/increase_zcw_dim_insfamily.sql"));

        // 2. 按机型和可用区分组
        Map<String, List<DataDimInsfamilyDTO>> groups =
                ListUtils.groupBy(data, o -> o.getGinsfamily());

        // 3. 对于每一个分组，计算准确率并输出
        System.out.println("date,机型,实际值,预测值,准确率");

        for (Map.Entry<String, List<DataDimInsfamilyDTO>> group : groups.entrySet()) {
            // 4. 对于开始到2021-10-31的数据，逐个测算2022-01-31的预测
            String ginsfamily = group.getValue().get(0).getGinsfamily();

            String date = "2021-10-31";
            while(true) {
                String source = getSourceUntil(group.getValue(), date);
                if (StringTools.isNotBlank(source)) {
                    // 3. 输出结果列：date,机型,可用区,实际,预测,准确率
                    Double predict = getPredictN3(source, 1, 1, 1);
                    Double realNum = getRealNum(data, getNext3MonthEndDate(date), ginsfamily);
                    System.out.println(getNext3MonthEndDate(date) + ","
                            + ginsfamily + ","
                            + realNum + ","
                            + predict + ","
                            + minMax(realNum, predict));
                }

                date = getNextMonthEndDate(date);
                if (date.equals("2022-11-30")) { // 到11月就不用了
                    break;
                }
            }
        }
    }

    /**
     * 测试新增 - 中长尾 - 机型/可用区
     */
    @Test
    public void increaseZCWByInsfamilyZone() throws Exception {
        // 1. 查询到数据
        List<DataDimInsfamilyZoneDTO> data = ckCloudDemandDevDBHelper.getRaw(DataDimInsfamilyZoneDTO.class,
                IOUtils.readClasspathResourceAsString("/废弃或已完成/db-operate/resources/cloud_forecast_sql/increase_zcw_dim_insfamily_zone.sql"));

        // 2. 按机型和可用区分组
        Map<String, List<DataDimInsfamilyZoneDTO>> groups =
                ListUtils.groupBy(data, o -> o.getGinsfamily() + "&" + o.getZone());

        // 3. 对于每一个分组，计算准确率并输出
        System.out.println("date,机型,可用区,实际值,预测值,准确率");

        for (Map.Entry<String, List<DataDimInsfamilyZoneDTO>> group : groups.entrySet()) {
            // 4. 对于开始到2021-10-31的数据，逐个测算2022-01-31的预测
            String ginsfamily = group.getValue().get(0).getGinsfamily();
            String zone = group.getValue().get(0).getZone();

            String date = "2021-10-31";
            while(true) {
                String source = getSourceUntil(group.getValue(), date);
                if (StringTools.isNotBlank(source)) {
                    // 3. 输出结果列：date,机型,可用区,实际,预测,准确率
                    Double predict = getPredictN3(source, 3, 1, 0);
                    Double realNum = getRealNum(data, getNext3MonthEndDate(date), ginsfamily, zone);
                    System.out.println(getNext3MonthEndDate(date) + ","
                            + ginsfamily + "," + zone + ","
                            + realNum + ","
                            + predict + ","
                            + minMax(realNum, predict));
                }

                date = getNextMonthEndDate(date);
                if (date.equals("2022-11-30")) { // 到11月就不用了
                    break;
                }
            }
        }
    }

    /**
     * 测算新增 - 中长尾 - 没有维度细分
     */
    @Test
    public void increaseZCWNoDim() throws Exception {
        // 1. 查询到数据
        List<DataDimZeroDTO> data = ckCloudDemandDevDBHelper.getRaw(DataDimZeroDTO.class,
                IOUtils.readClasspathResourceAsString("/废弃或已完成/db-operate/resources/cloud_forecast_sql/increase_zcw_no_dim.sql"));

        // 2. 对于开始到2021-10-31的数据，逐个测算2022-01-31的预测
        System.out.println("date,实际值,预测值,准确率");

        String date = "2021-10-31";
        while(true) {
            String source = getSourceUntil(data, date);
            if (StringTools.isNotBlank(source)) {
                // 3. 输出结果列：date,实际,预测,准确率
                Double predict = getPredictN3(source, 1, 1, 1);
                Double realNum = getRealNum(data, getNext3MonthEndDate(date));
                System.out.println(getNext3MonthEndDate(date) + ","
                        + realNum + ","
                        + predict + ","
                        + minMax(realNum, predict));
            }

            date = getNextMonthEndDate(date);
            if (date.equals("2022-11-30")) { // 到11月就不用了
                break;
            }
        }
    }

    private double minMax(double a, double b) {
        double max = Math.max(a, b);
        if (max == 0) {
            return 1;
        }
        return Math.min(a, b) / max;
    }

    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class PredictResultDTO {
        private String date;
        private Double num;
    }

    private static Double getPredictN3(String source, int p, int d, int q) throws IOException {
        Browser browser = new Browser();
        HttpResponse resp = browser.post("http://127.0.0.1:8383/?n=3&p=" + p + "&d=" + d + "&q=" + q,
                source.getBytes());
        String respBody = resp.getContentString();

        // 解析出第三行的值
        String[] strings = StringTools.splitLines(respBody);
        if (strings.length >= 3) {
            String[] split = strings[2].split("\\s+");
            return Math.max(Double.parseDouble(split[1]), 0d);
        } else {
            // 取source最后一行，目前发现当只有2个的时候，无法预测
            String[] strs = StringTools.splitLines(source);
            String[] split = strs[strs.length - 1].split(",");
            return Double.valueOf(split[1]);
        }
    }

    private String getNextMonthEndDate(String date) {
        Date d = DateUtils.parse(date);
        Date firstDay = DateUtils.parse(DateUtils.format(d, "yyyy-MM"));
        return DateUtils.formatDate(
                DateUtils.addTime(
                        DateUtils.addTime(firstDay, Calendar.MONTH, 2),
                        Calendar.DATE, -1));
    }

    private String getNext3MonthEndDate(String date) {
        Date d = DateUtils.parse(date);
        Date firstDay = DateUtils.parse(DateUtils.format(d, "yyyy-MM"));
        return DateUtils.formatDate(
                DateUtils.addTime(
                        DateUtils.addTime(firstDay, Calendar.MONTH, 4),
                        Calendar.DATE, -1));
    }

    private Double getRealNum(List<DataDimZeroDTO> data, String date) {
        for (DataDimZeroDTO d : data) {
            if (d.getDate().compareTo(DateUtils.parse(date)) == 0) {
                return d.getNum();
            }
        }
        return 0d;
    }

    private Double getRealNum(List<DataDimInsfamilyZoneDTO> data, String date, String ginstfamily, String zone) {
        for (DataDimInsfamilyZoneDTO d : data) {
            if (d.getDate().compareTo(DateUtils.parse(date)) == 0
                && ginstfamily.equals(d.getGinsfamily()) && zone.equals(d.getZone())) {
                return d.getNum();
            }
        }
        return 0d;
    }

    private Double getRealNum(List<DataDimInsfamilyDTO> data, String date, String ginstfamily) {
        for (DataDimInsfamilyDTO d : data) {
            if (d.getDate().compareTo(DateUtils.parse(date)) == 0
                    && ginstfamily.equals(d.getGinsfamily())) {
                return d.getNum();
            }
        }
        return 0d;
    }

    /**
     * @return 返回null表示没有数据了
     */
    private String getSourceUntil(List<? extends DataDimZeroDTO> data, String untilDate) {
        StringBuilder sb = new StringBuilder();
        sb.append("Date,Value\n");
        boolean isHaveData = false;
        for (DataDimZeroDTO d : data) {
            if (!d.getDate().after(DateUtils.parse(untilDate))) {
                sb.append(DateUtils.formatDate(d.getDate())).append(",").append(d.getNum()).append("\n");
                isHaveData = true;
            }
        }
        return isHaveData ? sb.toString() : null;
    }

}
