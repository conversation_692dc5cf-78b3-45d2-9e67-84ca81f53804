-- 这是另外一种写法，但是这种写法在头部名单发生变化时，会受到影响，因为本月和上月的名单不同的
-- 因为有这个因素，所以先不用了

select cast(sum(case when month_change_core>0 then month_change_core else 0 end) as Int32) as new_core,
       cast(sum(case when month_change_core<0 then -month_change_core else 0 end) as Int32) as ret_core
from (
         select zone_name, instance_type, sum(cur_core) as month_change_core
         from
             (select zone_name,instance_type,
                     sum(case when biz_range_type='外部业务' then cur_bill_core else cur_service_core end) as cur_core
              from dwd_txy_scale_df
              where stat_time='${lastDayOfMonth}'
                and paymode != '5' -- 不要竞价计费的
               and cpu_or_gpu = 'CPU'
               and biz_type = 'cvm'
               and app_role!='LH'
               and instance_type not like 'RS%' and instance_type not like 'RM%'
       -- 额外的查询条件
                  ${extraWhere}
              group by zone_name,instance_type

              union all

              select zone_name,instance_type,
                  -sum(case when biz_range_type='外部业务' then cur_bill_core else cur_service_core end) as cur_core
              from dwd_txy_scale_df
              where stat_time='${lastDayOfLastMonth}'
                and paymode != '5' -- 不要竞价计费的
                and cpu_or_gpu = 'CPU'
                and biz_type = 'cvm'
                and app_role!='LH'
                and instance_type not like 'RS%' and instance_type not like 'RM%'
                  -- 额外的查询条件
                  ${extraWhere}
              group by zone_name,instance_type)
         group by zone_name, instance_type
     )

