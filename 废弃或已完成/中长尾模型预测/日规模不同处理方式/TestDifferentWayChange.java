package com.pugwoo.dboperate.archived.日规模不同处理方式;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dboperate.common.ReadFileUtils;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.string.StringTools;
import lombok.Data;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;

/**
 * 不同的日规模表的处理方式
 */
@SpringBootTest
public class TestDifferentWayChange {

    @Resource
    private DBHelper ckStdCrpNewIdcDBHelper;

    /**额外的条件写这里，方便查询一些特定的场景*/
    private static final String extraWhereCondition = ""; // "" and industry_dept='智慧行业七部' and instance_type='S5' ";

    @Data
    public static class AmountDTO {
        /**新增量*/
        @Column("new_core")
        private BigDecimal newCore;
        /**退回量*/
        @Column("ret_core")
        private BigDecimal retCore;
    }

    @Test
    public void cal() {
        ckStdCrpNewIdcDBHelper.setSlowSqlWarningValve(120000);

        String begin = "2023-01";

        while(begin.compareTo("2023-09") <= 0) {
            cal(1, begin, null, ListUtils.of("名单客户"));

            LocalDate beginDate = DateUtils.parseLocalDate(begin);
            beginDate = beginDate.plusMonths(1);
            begin = DateUtils.format(beginDate, "yyyy-MM");
        }
    }

    private void cal(int billType, String month, List<Long> uins, List<String> customerTabType) {
        System.out.print("==============================================【");
        if (StringTools.isNotBlank(extraWhereCondition)) {
            System.out.print(extraWhereCondition);
        }
        System.out.print(" 月份:" + month);
        if (billType == 1) {
            System.out.println("，包年包月】");
        } else if (billType == 2) {
            System.out.println("，按量计费】");
        } else {
            System.out.println("，全部】");
        }

        // List<String> customerTabType = ListUtils.of("中长尾客户","报备客户");

        String name = name(customerTabType, uins);

        // ----------------------中长尾每天diff-----------------------------
        {
            System.out.println("【每天diff，uin+可用区+机型规格】");

            AmountDTO result = getDailyDiff(billType, month, uins, customerTabType, "");
            System.out.println(name + "，内外部，新增: " + result.getNewCore() + ", 退回:" + result.getRetCore());

            result = getDailyDiff(billType, month, uins, customerTabType, "外部业务");
            System.out.println(name + "，外部，新增: " + result.getNewCore() + ", 退回:" + result.getRetCore());

            result = getDailyDiff(billType, month, uins, customerTabType, "内部业务");
            System.out.println(name + "，内部，新增: " + result.getNewCore() + ", 退回:" + result.getRetCore());
        }

        // ---------------------月峰减月初------------------------
        {
            System.out.println("【月峰减月初，uin+可用区+机型规格】");

            AmountDTO monthPeakSubMonthBegin = getMonthPeakSubMonthBegin(billType, month, uins, customerTabType, "");
            System.out.println(name + "，内外部，新增：" + monthPeakSubMonthBegin.getNewCore() + ",退回：" + monthPeakSubMonthBegin.getRetCore());

            monthPeakSubMonthBegin = getMonthPeakSubMonthBegin(billType, month, uins, customerTabType, "外部业务");
            System.out.println(name + "，外部，新增：" + monthPeakSubMonthBegin.getNewCore() + ",退回：" + monthPeakSubMonthBegin.getRetCore());

            monthPeakSubMonthBegin = getMonthPeakSubMonthBegin(billType, month, uins, customerTabType, "内部业务");
            System.out.println(name + "，内部，新增：" + monthPeakSubMonthBegin.getNewCore() + ",退回：" + monthPeakSubMonthBegin.getRetCore());
        }

        // ---------------------- 月末减月初，uin不去重版本
        {
            System.out.println("【月末减月初，uin+可用区+机型规格】");
            AmountDTO result = monthEndSubMonthBeginByUinZoneInstanceModel(billType, month, uins, customerTabType, "");
            System.out.println(name + "，内外部，新增: " + result.getNewCore() + ", 退回:" + result.getRetCore());

            result = monthEndSubMonthBeginByUinZoneInstanceModel(billType, month, uins, customerTabType, "外部业务");
            System.out.println(name + "，外部，新增: " + result.getNewCore() + ", 退回:" + result.getRetCore());

            result = monthEndSubMonthBeginByUinZoneInstanceModel(billType, month, uins, customerTabType, "内部业务");
            System.out.println(name + "，内部，新增: " + result.getNewCore() + ", 退回:" + result.getRetCore());
        }

        // ---------------------- 月末减月初，uin不去重版本
        {
            System.out.println("【月末减月初，uin+可用区+机型大类】");
            AmountDTO result = monthEndSubMonthBeginByUinZoneInstanceType(billType, month, uins, customerTabType, "");
            System.out.println(name + "，内外部，新增: " + result.getNewCore() + ", 退回:" + result.getRetCore());

            result = monthEndSubMonthBeginByUinZoneInstanceType(billType, month, uins, customerTabType, "外部业务");
            System.out.println(name + "，外部，新增: " + result.getNewCore() + ", 退回:" + result.getRetCore());

            result = monthEndSubMonthBeginByUinZoneInstanceType(billType, month, uins, customerTabType, "内部业务");
            System.out.println(name + "，内部，新增: " + result.getNewCore() + ", 退回:" + result.getRetCore());
        }

        // ---------------------- 月末减月初，业务去重版 ---------------------
        {
            System.out.println("【月末减月初(去重版)，可用区+机型大类】");
            AmountDTO result = lostUinMergeMonthEndSubMonthBegin(billType, month, uins, customerTabType, "");
            System.out.println(name + "，内外部，新增: " + result.getNewCore() + ", 退回:" + result.getRetCore());

            result = lostUinMergeMonthEndSubMonthBegin(billType, month, uins, customerTabType, "外部业务");
            System.out.println(name + "，外部，新增: " + result.getNewCore() + ", 退回:" + result.getRetCore());

            result = lostUinMergeMonthEndSubMonthBegin(billType, month, uins, customerTabType, "内部业务");
            System.out.println(name + "，内部，新增: " + result.getNewCore() + ", 退回:" + result.getRetCore());
        }

        // ---------------------- 月均版，业务去重版
        {
            System.out.println("【月均版(去重版)，可用区+机型大类】");
            AmountDTO result = lostUinMergeMonthAvgChange(billType, month, uins, customerTabType, "");
            System.out.println(name + "，内外部，新增: " + result.getNewCore() + ", 退回:" + result.getRetCore());

            result = lostUinMergeMonthAvgChange(billType, month, uins, customerTabType, "外部业务");
            System.out.println(name + "，外部，新增: " + result.getNewCore() + ", 退回:" + result.getRetCore());

            result = lostUinMergeMonthAvgChange(billType, month, uins, customerTabType, "内部业务");
            System.out.println(name + "，内部，新增: " + result.getNewCore() + ", 退回:" + result.getRetCore());
        }

    }

    /**月均变化，抹除uin按可用区机型大类抵消版本*/
    private AmountDTO lostUinMergeMonthAvgChange(int billType, String month, List<Long> uins, List<String> customerTabType,
                                                 String bizRangeType) {
        return cal("lost_uin_merge_month_avg_change.sql", billType, month, uins, customerTabType, bizRangeType);
    }

    /**月末减月初，抹除uin按可用区机型大类抵消版本*/
    private AmountDTO lostUinMergeMonthEndSubMonthBegin(int billType, String month, List<Long> uins, List<String> customerTabType,
                                                        String bizRangeType) {
        return cal("lost_uin_merge_month_end_sub_month_begin.sql", billType, month, uins, customerTabType, bizRangeType);
    }

    private AmountDTO monthEndSubMonthBeginByUinZoneInstanceModel(int billType, String month, List<Long> uins, List<String> customerTabType,
                                                                 String bizRangeType) {
        return cal("month_end_sub_month_begin_by_uin_zone_instance_model.sql", billType, month, uins, customerTabType, bizRangeType);
    }

    private AmountDTO monthEndSubMonthBeginByUinZoneInstanceType(int billType, String month, List<Long> uins, List<String> customerTabType,
                                                                 String bizRangeType) {
        return cal("month_end_sub_month_begin_by_uin_zone_instance_type.sql", billType, month, uins, customerTabType, bizRangeType);
    }

    private AmountDTO cal(String sqlName, int billType, String month, List<Long> uins, List<String> customerTabType,
                          String bizRangeType) {
        StringBuilder extraWhere = makeWhere(billType, uins, customerTabType, bizRangeType);

        Date month1 = DateUtils.parse(month);
        LocalDate firstDayOfMonth = DateUtils.getFirstDayOfMonth(month1);
        LocalDate lastDayOfMonth = DateUtils.getLastDayOfMonth(month1);
        LocalDate firstDayOfLastMonth = firstDayOfMonth.minusMonths(1);
        LocalDate lastDayOfLastMonth = firstDayOfMonth.minusDays(1);

        String sql = ReadFileUtils.read(sqlName);
        sql = sql.replace("${extraWhere}", extraWhere.toString());
        sql = sql.replace("${firstDayOfMonth}", DateUtils.format(firstDayOfMonth));
        sql = sql.replace("${lastDayOfMonth}", DateUtils.format(lastDayOfMonth));
        sql = sql.replace("${firstDayOfLastMonth}", DateUtils.format(firstDayOfLastMonth));
        sql = sql.replace("${lastDayOfLastMonth}", DateUtils.format(lastDayOfLastMonth));

        return ckStdCrpNewIdcDBHelper.getRawOne(AmountDTO.class, sql);
    }

    /**
     * 【中长尾每天diff版本，新增】
     * @param billType 必填，0全部，1包年包月，2按量计费
     * @param month 必填
     * @param uins 选填，有则查询
     * @param customerTabType 选填，有则查询，枚举值：名单客户 报备客户 中长尾客户
     * @param bizRangeType 选填，有则查询，枚举值：外部业务、内部业务
     */
    private AmountDTO getDailyDiff(int billType, String month, List<Long> uins, List<String> customerTabType,
                                       String bizRangeType) {
        StringBuilder extraWhere = makeWhere(billType, uins, customerTabType, bizRangeType);

        Date month1 = DateUtils.parse(month);
        LocalDate firstDayOfMonth = DateUtils.getFirstDayOfMonth(month1);
        LocalDate lastDayOfMonth = DateUtils.getLastDayOfMonth(month1);

        String sql = ReadFileUtils.read("month_daily_diff.sql");
        sql = sql.replace("${extraWhere}", extraWhere.toString());
        sql = sql.replace("${firstDayOfMonth}", DateUtils.format(firstDayOfMonth));
        sql = sql.replace("${lastDayOfMonth}", DateUtils.format(lastDayOfMonth));

        return ckStdCrpNewIdcDBHelper.getRawOne(AmountDTO.class, sql);
    }

    /**
     * 【月峰减月初】
     * 查询指定月份，指定uin的增量，如果没有指定uin，则查全部
     * @param billType 必填，0全部，1包年包月，2按量计费
     * @param month 必填
     * @param uins 选填，有则查询
     * @param customerTabType 选填，有则查询，枚举值：名单客户 报备客户 中长尾客户
     * @param bizRangeType 选填，有则查询，枚举值：外部业务、内部业务
     */
    private AmountDTO getMonthPeakSubMonthBegin(int billType, String month, List<Long> uins, List<String> customerTabType,
                                                 String bizRangeType) {
        StringBuilder extraWhere = makeWhere(billType, uins, customerTabType, bizRangeType);

        Date month1 = DateUtils.parse(month);
        LocalDate firstDayOfMonth = DateUtils.getFirstDayOfMonth(month1);
        LocalDate lastDayOfMonth = DateUtils.getLastDayOfMonth(month1);

        String sql = ReadFileUtils.read("month_peak_sub_month_begin.sql");
        sql = sql.replace("${extraWhere}", extraWhere.toString());
        sql = sql.replace("${firstDayOfMonth}", DateUtils.format(firstDayOfMonth));
        sql = sql.replace("${lastDayOfMonth}", DateUtils.format(lastDayOfMonth));

        return ckStdCrpNewIdcDBHelper.getRawOne(AmountDTO.class, sql);
    }

    private StringBuilder makeWhere(int billType, List<Long> uins, List<String> customerTabType,
                                    String bizRangeType) {
        StringBuilder extraWhere = new StringBuilder(" and 1=1");

        if (billType == 1) {
            extraWhere.append(" and paymode_range_type!='弹性'");
        } else if (billType == 2) {
            extraWhere.append(" and paymode_range_type='弹性'");
        }

        if (ListUtils.isNotEmpty(uins)) {
            extraWhere.append(" and uin in (").append(StringTools.join(",", uins)).append(")");
        }
        if (ListUtils.isNotEmpty(customerTabType)) {
            extraWhere.append(" and customer_tab_type in (")
                    .append(String.join(",", ListUtils.transform(customerTabType, o -> "'" + o + "'"))).append(")");
        }
        if (StringTools.isNotBlank(bizRangeType)) {
            extraWhere.append(" and biz_range_type='").append(bizRangeType).append("'");
        }

        if (StringTools.isNotBlank(extraWhereCondition)) {
            extraWhere.append(" ").append(extraWhereCondition);
        }

        return extraWhere;
    }


    /**转换成打印的客户名称*/
    private String name(List<String> customerTabType, List<Long> uins) {
        StringBuilder sb = new StringBuilder();
        if (ListUtils.isNotEmpty(uins)) {
            sb.append("uin:").append(String.join(",", ListUtils.transform(uins, String::valueOf)));
        } else {
            if (ListUtils.isEmpty(customerTabType)) {
                sb.append("全部客户");
            } else {
                sb.append(String.join(",", customerTabType));
            }
        }
        return sb.toString();
    }
}
