select cast(sum(case when change_core>0 then change_core else 0 end) as Int32) as new_core,
       cast(sum(case when change_core<0 then -change_core else 0 end) as Int32) as ret_core
from (
  select (case when biz_range_type='外部业务' then change_bill_core else change_service_core end) change_core
  from dwd_txy_scale_df
  where stat_time between '${firstDayOfMonth}' and '${lastDayOfMonth}'
  -- and paymode_range_type!='弹性'
  and paymode != '5' -- 不要竞价计费的
  and cpu_or_gpu = 'CPU'
  and biz_type = 'cvm'
  and app_role!='LH'
  and instance_type not like 'RS%' and instance_type not like 'RM%'
  --      and uin in (342151535)
      -- 额外的查询条件
      ${extraWhere}
 )