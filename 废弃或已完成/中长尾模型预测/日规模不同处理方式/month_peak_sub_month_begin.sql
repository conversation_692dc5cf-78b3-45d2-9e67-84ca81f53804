select -- region_name,instance_type,
       sum(case when new_core>0 then new_core else 0 end) as new_core, -- 新增量
       sum(case when ret_core<0 then -ret_core else 0 end) as ret_core -- 退回量，处理成正数
from
    (
        select uin,zone_name,any(region_name1) as region_name,instance_model,
            any(instance_type1) as instance_type, sum(for_new_core) as new_core, sum(for_ret_core) as ret_core
        from
            (
            select uin,zone_name,any(region_name) as region_name1 ,instance_model, any(instance_type) as instance_type1,
                   max(cur_bill_service_core) as for_new_core,
                   -max(cur_bill_service_core) as for_ret_core
            from dwd_txy_scale_df
            where stat_time between '${firstDayOfMonth}' and '${lastDayOfMonth}'
            --and paymode_range_type!='弹性'
            and paymode != '5' -- 不要竞价计费的
            and cpu_or_gpu = 'CPU'
            and biz_type = 'cvm'
            and app_role!='LH'
            and instance_type not like 'RS%' and instance_type not like 'RM%'
            --and customer_tab_type in ('名单客户','报备客户')
            -- 额外的查询条件
            ${extraWhere}
            group by uin,zone_name,instance_model

            union all

            select uin,zone_name,region_name as region_name1,instance_model,instance_type as instance_type1,
                   -(case when stat_time='${firstDayOfMonth}' then cur_bill_service_core else 0 end) as for_new_core,
                   (case when stat_time='${lastDayOfMonth}' then cur_bill_service_core else 0 end) as for_ret_core
            from dwd_txy_scale_df
            where stat_time in ('${firstDayOfMonth}','${lastDayOfMonth}')
            --and paymode_range_type!='弹性'
            and paymode != '5' -- 不要竞价计费的
            and cpu_or_gpu = 'CPU'
            and biz_type = 'cvm'
            and app_role!='LH'
            and instance_type not like 'RS%' and instance_type not like 'RM%'
            --and customer_tab_type in ('名单客户','报备客户')
            -- 额外的查询条件
            ${extraWhere}
            )
        group by uin,zone_name,instance_model
    )
-- group by region_name,instance_type