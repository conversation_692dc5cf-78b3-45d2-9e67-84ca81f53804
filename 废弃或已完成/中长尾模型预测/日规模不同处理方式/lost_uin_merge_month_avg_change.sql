select cast(sum(case when avg_change_core>0 then avg_change_core else 0 end) as Int32) as new_core,
       cast(sum(case when avg_change_core<0 then -avg_change_core else 0 end) as Int32) as ret_core
from
    (
        select zone_name,instance_type,sum(day_avg_cur_core) as avg_change_core
        from
            (
                select zone_name,instance_type,sum(case when biz_range_type='外部业务' then cur_bill_core else cur_service_core end)/31 as day_avg_cur_core
                from dwd_txy_scale_df
                where stat_time between '${firstDayOfMonth}' and '${lastDayOfMonth}'
           --       and paymode_range_type!='弹性'
          and paymode != '5' -- 不要竞价计费的
          and cpu_or_gpu = 'CPU'
          and biz_type = 'cvm'
          and app_role!='LH'
          and instance_type not like 'RS%' and instance_type not like 'RM%'

                       -- 额外的查询条件
                    ${extraWhere}

         -- and uin in (342151535)
                group by zone_name, instance_type

                union all

                select zone_name,instance_type,-sum(case when biz_range_type='外部业务' then cur_bill_core else cur_service_core end)/31 as day_avg_cur_core
                from dwd_txy_scale_df
                where stat_time between '${firstDayOfLastMonth}' and '${lastDayOfLastMonth}'
               --   and paymode_range_type!='弹性'
                  and paymode != '5' -- 不要竞价计费的
                  and cpu_or_gpu = 'CPU'
                  and biz_type = 'cvm'
                  and app_role!='LH'
                  and instance_type not like 'RS%' and instance_type not like 'RM%'

                    -- 额外的查询条件
                    ${extraWhere}

              --    and uin in (342151535)
                group by zone_name, instance_type
            )
        group by zone_name,instance_type
    )