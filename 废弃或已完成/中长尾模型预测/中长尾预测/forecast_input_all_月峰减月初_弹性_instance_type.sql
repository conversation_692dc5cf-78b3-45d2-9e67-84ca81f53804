-- 弹性的，如果峰值按instance_type来
select year,month,max(month_end_date) as stime,
    region_name,${instanceTypeMergeCaseWhen} as gins_family,
    sum(case when new_core>0 then new_core else 0 end) as cores--, -- 增量：max-月初，>0部分
   -- sum(case when ret_core>0 then ret_core else 0 end) as ret_cores --   退回：max-月末，>0部分
from
(
    select year1 as year,month1 as month, max(month_end_date) as month_end_date,
    zone_name,any(region_name) as region_name,instance_type,
    sum(for_new_core) as new_core, sum(for_ret_core) as ret_core
    from
    (
        select
        year1,month1,max(month_end_date1) as month_end_date, zone_name,any(region_name1) as region_name,
        instance_type,
        toDecimal64(avg(cur_bill_service_core1),6) as for_new_core, toDecimal64(avg(cur_bill_service_core1),6) as for_ret_core
        from
        (
            select stat_time, any(year) as year1, any(month) as month1, max(month_end_date) as month_end_date1,
            zone_name,any(region_name) as region_name1,instance_type,
            sum(cur_bill_service_core) as cur_bill_service_core1,
            row_number() over (partition by year1,month1,zone_name,instance_type order by sum(cur_bill_service_core) desc) as rank
            from dwd_txy_scale_df
            where stat_time>='${rangeBegin}' and stat_time<='${rangeEnd}'
            and cpu_or_gpu = 'CPU'
            and biz_type = 'cvm'
            and app_role!='LH'
            and instance_type not like 'RS%' and instance_type not like 'RM%'
            and customer_tab_type in ('中长尾客户','报备客户')
            and paymode_range_type='弹性' and paymode='2'

            ${timeRange} -- 时间范围

            group by stat_time,zone_name,instance_type
        ) where rank=1
        group by year1, month1,  zone_name, instance_type

        union all

        select year as year1, month as month1, max(month_end_date) as month_end_date1,
        zone_name,any(region_name) as region_name1,instance_type,
        -sum(case when stat_time=month_start_date then cur_bill_service_core else 0 end) as for_new_core,
        -sum(case when stat_time=month_end_date then cur_bill_service_core else 0 end) as for_ret_core
        from dwd_txy_scale_df
        where stat_time>='${rangeBegin}' and stat_time<='${rangeEnd}'
        and cpu_or_gpu = 'CPU'
        and biz_type = 'cvm'
        and app_role!='LH'
        and instance_type not like 'RS%' and instance_type not like 'RM%'
        and customer_tab_type in ('中长尾客户','报备客户')
        and paymode_range_type='弹性' and paymode='2'

        ${timeRange} -- 时间范围

        group by zone_name,instance_type,year, month
    )
    group by zone_name,instance_type,year1,month1
    )
group by region_name,year,month,${instanceTypeMergeCaseWhen}
