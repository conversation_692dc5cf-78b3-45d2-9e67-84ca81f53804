select year,month,stime,region_name,gins_family,sum(cores) as cores
from
(
-- 新增，月峰减月初，中长尾
select year,month,max(month_end_date1) as stime,
    region_name,${instanceTypeMergeCaseWhen} as gins_family,
    sum(new_core) as cores -- 增量
from (
    select year,month, max(month_end_date1) as month_end_date1,
    uin,zone_name,any(region_name) as region_name,instance_model, any(instance_type1) as instance_type, sum(core) as new_core
    from
    (
    select year, month, max(month_end_date) as month_end_date1,
    uin,zone_name,any(region_name) as region_name,instance_model, any(instance_type) as instance_type1,max(cur_bill_service_core) as core
    from dwd_txy_scale_df
    where stat_time>='${rangeBegin}' and stat_time<='${rangeEnd}'
    and paymode_range_type!='弹性'
    and cpu_or_gpu = 'CPU'
    and biz_type = 'cvm'
    and app_role!='LH'
    and instance_type not like 'RS%' and instance_type not like 'RM%'
    and customer_tab_type in ('中长尾客户','报备客户')
    and zone_name in ('上海一区','上海七区','上海三区','上海二区','上海五区','上海八区','上海六区','上海四区','东京一区','东京二区','北京一区','北京七区','北京三区','北京二区','北京五区','北京八区','北京六区','北京四区','南京一区','南京三区','南京二区','台北一区','圣保罗一区','多伦多一区','天津三区','天津二区','孟买一区','孟买二区','广州七区','广州三区','广州二区','广州五区','广州八区','广州六区','广州四区','弗吉尼亚一区','弗吉尼亚二区','成都一区','成都二区','新加坡一区','新加坡三区','新加坡二区','新加坡四区','曼谷一区','曼谷二区','欧洲东北一区','法兰克福一区','法兰克福三区','法兰克福二区','深圳一区','深圳三区','深圳二区','深圳四区','硅谷一区','硅谷二区','贵阳一区','重庆一区','雅加达一区','雅加达二区','首尔一区','首尔二区','香港一区','香港三区','香港二区')

    ${timeRange} -- 时间范围

    group by uin,zone_name,instance_model,year, month

    union all

    select year, month, max(month_end_date) as month_end_date1,
    uin,zone_name,any(region_name),instance_model,any(instance_type) as instance_type1,
    sum(case when stat_time=month_start_date then -cur_bill_service_core else 0 end) as core
    from dwd_txy_scale_df
    where stat_time>='${rangeBegin}' and stat_time<='${rangeEnd}'
    and paymode_range_type!='弹性'
    and cpu_or_gpu = 'CPU'
    and biz_type = 'cvm'
    and app_role!='LH'
    and instance_type not like 'RS%' and instance_type not like 'RM%'
    and customer_tab_type in ('中长尾客户','报备客户')
    and zone_name in ('上海一区','上海七区','上海三区','上海二区','上海五区','上海八区','上海六区','上海四区','东京一区','东京二区','北京一区','北京七区','北京三区','北京二区','北京五区','北京八区','北京六区','北京四区','南京一区','南京三区','南京二区','台北一区','圣保罗一区','多伦多一区','天津三区','天津二区','孟买一区','孟买二区','广州七区','广州三区','广州二区','广州五区','广州八区','广州六区','广州四区','弗吉尼亚一区','弗吉尼亚二区','成都一区','成都二区','新加坡一区','新加坡三区','新加坡二区','新加坡四区','曼谷一区','曼谷二区','欧洲东北一区','法兰克福一区','法兰克福三区','法兰克福二区','深圳一区','深圳三区','深圳二区','深圳四区','硅谷一区','硅谷二区','贵阳一区','重庆一区','雅加达一区','雅加达二区','首尔一区','首尔二区','香港一区','香港三区','香港二区')

    ${timeRange} -- 时间范围

    group by uin,zone_name,instance_model,year, month
    )
    group by uin,zone_name,instance_model,year,month
    )
group by region_name,year,month
        ,${instanceTypeMergeCaseWhen}

------------------------------ 以上是月峰减月初的包年包月
union all

------------------------------ 以下是月峰减月初的弹性
select year,month,max(month_end_date) as stime,
    region_name,${instanceTypeMergeCaseWhen} as gins_family,
    sum(case when new_core>0 then new_core else 0 end) as cores--, -- 增量：max-月初，>0部分
-- sum(case when ret_core>0 then ret_core else 0 end) as ret_cores --   退回：max-月末，>0部分
    from
    (
    select year1 as year,month1 as month, max(month_end_date) as month_end_date,
    zone_name,any(region_name) as region_name,instance_type,
    sum(for_new_core) as new_core, sum(for_ret_core) as ret_core
    from
    (
    select
    year1,month1,max(month_end_date1) as month_end_date, zone_name,any(region_name1) as region_name,
    instance_type,
    toDecimal64(avg(cur_bill_service_core1),6) as for_new_core, toDecimal64(avg(cur_bill_service_core1),6) as for_ret_core
    from
    (
    select stat_time, any(year) as year1, any(month) as month1, max(month_end_date) as month_end_date1,
    zone_name,any(region_name) as region_name1,instance_type,
    sum(cur_bill_service_core) as cur_bill_service_core1,
    row_number() over (partition by year1,month1,zone_name,instance_type order by sum(cur_bill_service_core) desc) as rank
    from dwd_txy_scale_df
    where stat_time>='${rangeBegin}' and stat_time<='${rangeEnd}'
    and cpu_or_gpu = 'CPU'
    and biz_type = 'cvm'
    and app_role!='LH'
    and instance_type not like 'RS%' and instance_type not like 'RM%'
    and customer_tab_type in ('中长尾客户','报备客户')
    and paymode_range_type='弹性' and paymode='2'

    ${timeRange} -- 时间范围

    group by stat_time,zone_name,instance_type
    ) where rank=1
    group by year1, month1,  zone_name, instance_type

    union all

    select year as year1, month as month1, max(month_end_date) as month_end_date1,
    zone_name,any(region_name) as region_name1,instance_type,
    -sum(case when stat_time=month_start_date then cur_bill_service_core else 0 end) as for_new_core,
    -sum(case when stat_time=month_end_date then cur_bill_service_core else 0 end) as for_ret_core
    from dwd_txy_scale_df
    where stat_time>='${rangeBegin}' and stat_time<='${rangeEnd}'
    and cpu_or_gpu = 'CPU'
    and biz_type = 'cvm'
    and app_role!='LH'
    and instance_type not like 'RS%' and instance_type not like 'RM%'
    and customer_tab_type in ('中长尾客户','报备客户')
    and paymode_range_type='弹性' and paymode='2'

    ${timeRange} -- 时间范围

    group by zone_name,instance_type,year, month
    )
    group by zone_name,instance_type,year1,month1
    )
    group by region_name,year,month,${instanceTypeMergeCaseWhen}



) group by year,month,stime,region_name,gins_family



