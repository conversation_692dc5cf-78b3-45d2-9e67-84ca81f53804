-- 不区分地域和可用区
select  toYear(stat_time) as year,
        toMonth(stat_time) as month ,
        max(month_end_date) as stime, -- 注意这里不要取stat_time，因为日期可能不完全
        -- 默认用机型收敛
        ${instanceTypeMergeCaseWhen} as gins_family,
 --   'GROUP_REGION_NAME' as region_name,
     region_name as region_name,

  sum (case when lt_change_cores>0 then lt_change_cores else 0 end) as cores -- 新增
 -- sum (lt_ret_cores) as cores -- 退回
 -- sum (lt_cur_cores) as cores -- 存量

from -- dwd_txy_scale_df_view_longtail_monthly_subscription -- 包年包月
-- from dwd_txy_scale_df_view_longtail_elastic_subscription -- 弹性
-- from dwd_txy_scale_df_view_longtail -- 包年包月+弹性
(
     select stat_time, zone_name, any(region_name) as region_name, instance_model, any(instance_type) as instance_type,
            max(month_end_date) as month_end_date,
            sum(lt_new_cores-lt_ret_cores) as lt_change_cores
     from dwd_txy_scale_df_view_longtail_monthly_subscription
     where 1=1
    ${timeRange} -- 时间范围
     group by stat_time, zone_name, instance_model
    )

where 1=1

-- and biz_range_type = '外部业务'

group by toYear(stat_time), toMonth(stat_time)
        , region_name
-- 默认用机型收敛
        , ${instanceTypeMergeCaseWhen}
