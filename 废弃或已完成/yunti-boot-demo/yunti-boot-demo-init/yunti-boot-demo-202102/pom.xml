<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="http://maven.apache.org/POM/4.0.0"
        xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>yunti.apps</groupId>
    <artifactId>yunti-boot-demo-202102</artifactId>
    <version>1.0.0-SNAPSHOT</version>

    <parent>
        <artifactId>yunti-spring-boot-parent</artifactId>
        <groupId>yunti</groupId>
        <version>2.0.2-SNAPSHOT</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>

    <dependencies>
        <dependency>
            <artifactId>yunti-spring-boot-core</artifactId>
            <groupId>yunti</groupId>
        </dependency>

        <dependency>
            <artifactId>yunti-spring-boot-dao</artifactId>
            <groupId>yunti</groupId>
        </dependency>

        <!--
        <dependency>
          <groupId>yunti</groupId>
          <artifactId>yunti-spring-boot-clojure</artifactId>
        </dependency>
        -->

        <dependency>
            <artifactId>yunti-spring-boot-zeebe</artifactId>
            <groupId>yunti</groupId>
        </dependency>

        <dependency>
            <artifactId>yunti-spring-boot-oss</artifactId>
            <groupId>yunti</groupId>
        </dependency>

        <dependency>
            <artifactId>spring-boot-devtools</artifactId>
            <groupId>org.springframework.boot</groupId>
            <scope>runtime</scope>
        </dependency>

        <dependency>
            <artifactId>spring-boot-starter-test</artifactId>
            <groupId>org.springframework.boot</groupId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <groupId>org.springframework.boot</groupId>
            <optional>true</optional>
        </dependency>

        <dependency>
            <artifactId>spring-boot-starter-jooq</artifactId>
            <groupId>org.springframework.boot</groupId>
        </dependency>

        <dependency>
            <artifactId>yunti-jooq-entities</artifactId>
            <groupId>yunti</groupId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <artifactId>yunti-teg-starter</artifactId>
            <groupId>yunti</groupId>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <groupId>org.springframework.boot</groupId>
            </plugin>
            <plugin>
                <artifactId>maven-source-plugin</artifactId>
                <configuration>
                    <skipSource>true</skipSource>
                </configuration>
                <groupId>org.apache.maven.plugins</groupId>
            </plugin>
        </plugins>
    </build>

</project>
