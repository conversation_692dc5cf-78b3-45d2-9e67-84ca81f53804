package demo;

import com.google.common.collect.ImmutableMap;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import yunti.boot.client.JsonrpcClient;

import java.util.UUID;

@SpringBootTest
public class TestAuthApi {

    JsonrpcClient jsonrpcClient;

    @Autowired
    public void setJsonrpcClient(JsonrpcClient jsonrpcClient) {
        this.jsonrpcClient = jsonrpcClient;
    }

    // 这个会报错，因为没有cookie上下文
    @Test
    public void test() {
        Object resp = jsonrpcClient.jsonrpc("queryExtra")
                .uri("http://devapps.erp.oa.com/jsonrpc")
                .uriPath("auth")
                .id(UUID.randomUUID().toString())
                .params(null, "yunti-cmdb-cdb-test", "query-instances")
                .postForObject(Object.class);

        System.out.println(resp);
    }

    @Test
    public void testApiKey() {
        Object resp = jsonrpcClient.jsonrpc("queryExtra")
                .uri("http://devapps.erp.oa.com/jsonrpc")
                .uriPath("auth")
                .uriQuery("api_key", "no")
                .id(UUID.randomUUID().toString())
                .params(ImmutableMap.of("principal", "nickxie", "ns", "user")
                        , "yunti-cmdb-cdb-test", "query-instances") // 这个map等于是直接查询指定员工的权限
                .noInjectServletRequest() // 不要带上http请求体上下文
                .postForObject(Object.class);

        System.out.println(resp);
    }


}
