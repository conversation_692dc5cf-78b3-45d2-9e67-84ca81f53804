package demo;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.annotation.RequestMapping;
import yunti.boot.client.JsonrpcClient;
import yunti.boot.security.CurrentUser;
import yunti.boot.security.TofUser;
import yunti.boot.web.jsonrpc.JsonrpcController;
import yunti.boot.web.jsonrpc.JsonrpcParam;

import javax.validation.constraints.NotBlank;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@JsonrpcController("/api/cdb")
@Slf4j
public class MyRpc implements ApplicationContextAware {

    JsonrpcClient jsonrpcClient;

    @Autowired
    public void setJsonrpcClient(JsonrpcClient jsonrpcClient) {
        this.jsonrpcClient = jsonrpcClient;
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        context = applicationContext;
    }

    private ApplicationContext context;

    /**
     * 可以查询到：
     * 1、用户有没有查询实例的权限
     * 2、用户对各类信息的查询权限颗粒度如何
     * 3、*代表该类信息的颗粒度为满，否则结果数组里有什么返回字段则有对显示的字段有权限
     */
    @RequestMapping
    @SuppressWarnings("unchecked")
    public Object testAuth(@CurrentUser TofUser tofUser, @JsonrpcParam AuthParams params, BindingResult bindingResult) {
        // 这是检查入参有没有错误
        if (bindingResult.hasErrors()) {
            StringBuilder sb = new StringBuilder();
            List<FieldError> errorList = bindingResult.getFieldErrors();
            for (FieldError item : errorList) {
                sb.append(item.getDefaultMessage());
                sb.append(";");
            }
            return sb.toString();
        }
        // 这是对权限系统发起鉴权请求
        String domain = params.getDomain();
        String action = params.getAction();
        // 用我们的框架工具JsonrpcClient发起网络请求
        Object resp = jsonrpcClient.jsonrpc("queryExtra")
                .uri("http://devapps.erp.oa.com/jsonrpc")
                .uriPath("auth")
                .id(UUID.randomUUID().toString())
                .params(null, domain, action)
                .postForObject(Object.class);
        // 获得权限系统的结果
        String username = tofUser.getUsername();
        log.info("员工[{}]对CDB的查询实例权限的返回结果为: {} {}", username, resp.getClass(), resp);
        if (resp instanceof Boolean) {
            // 如果员工没有配置这个权限或不存在这个权限，会返回false
            log.info("无权限，返回值为false");
            return ImmutableMap.of("productInfoFields", ImmutableList.of(),
                    "netInfoFields", ImmutableList.of(),
                    "billingInfoFields", ImmutableList.of());
        } else if (resp instanceof Map) {
            // 配置了权限，则一定会匹配你配置在权限系统上的结构体
            Map<String, Object> authResult = (Map<String, Object>) resp;
            Map<String, List<String>> extra = ((List<Map<String, List<String>>>) authResult.get("extra")).get(0);
            List<String> productInfoFields = extra.get("product-info");
            List<String> netInfoFields = extra.get("net-info");
            List<String> billingInfoFields = extra.get("billing-info");
            log.info("有查询权限，现在看看每个分类下的权限如何");
            log.info("产品信息有权限的字段: {}", productInfoFields);
            log.info("网络信息有权限的字段: {}", netInfoFields);
            log.info("计费信息有权限的字段: {}", billingInfoFields);
            return ImmutableMap.of("productInfoFields", productInfoFields,
                    "netInfoFields", netInfoFields,
                    "billingInfoFields", billingInfoFields);
        }
        throw new RuntimeException("系统级别的异常");
    }

    @Data
    static class AuthParams {

        @NotBlank(message = "action不能为空")
        private String action;
        @NotBlank(message = "domain不能为空")
        private String domain;
    }

}
