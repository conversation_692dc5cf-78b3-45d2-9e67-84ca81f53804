package yuntibootdemo202106;

import yunti.boot.zeebe.ZeebeOperations;
import yunti.boot.zeebe.annotation.ZeebeWorker;
import yunti.boot.zeebe.annotation.ZeebeDeployment;

import io.zeebe.client.api.response.ActivatedJob;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;
import lombok.Data;


/**
 * ZeebeWorkers在此定义.
 */
@Component
@ConditionalOnProperty(value = "yunti.zeebe.enabled")
// @ZeebeDeployment(classPathResources = {})
@Slf4j
public class MyZeebe {

    @Autowired
    ZeebeOperations zeebe;

    @ZeebeWorker(type = "yunti-boot-demo-202106:foobar", timeout = "5m", enabled = true)
    public void foobarWorker(ActivatedJob job, FoobarReq req) throws Exception {
        // req.getAssetIds()
        // zeebe.publishMessage(...).join();
        // zeebe.completeJob(...).join();
    }

    @Data
    static class FoobarReq {
        String[] assetIds;
    }
}
