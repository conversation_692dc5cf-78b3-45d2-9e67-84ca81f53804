package yuntibootdemo202106;

import lombok.extern.slf4j.Slf4j;
import lombok.Data;
import lombok.Builder;

import javax.validation.constraints.NotNull;

import org.springframework.web.bind.annotation.RequestMapping;
import yunti.boot.security.CurrentUser;
import yunti.boot.security.TofUser;
import yunti.boot.web.jsonrpc.JsonrpcController;

import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.info.Info;

@JsonrpcController
@Slf4j
@OpenAPIDefinition(
        info = @Info(title = "yunti-boot-demo-202106 API接口",
                     version = "1.0.0")
)
public class MyRpc {

    @Operation(
            summary = "测试用API",
            description = "这是一个测试用的接口，返回当前登录用户"
    )
    @RequestMapping
    public TestApiResp testApi(
            @CurrentUser TofUser user
    ) {
        log.info("testApi called {}", user.getUsername());
        return TestApiResp.builder()
                .username(user.getUsername())
                .build();
    }



    @Data
    @Builder
    public static class TestApiResp {

        @NotNull
        String username;

    }
}
