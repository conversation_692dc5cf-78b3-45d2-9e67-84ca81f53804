<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <groupId>yunti.apps</groupId>
  <artifactId>yunti-boot-demo-database</artifactId>
  <version>1.0.0-SNAPSHOT</version>

  <parent>
    <groupId>yunti</groupId>
    <artifactId>yunti-spring-boot-parent</artifactId>
    <version>2.0.3-SNAPSHOT</version>
    <relativePath/> <!-- lookup parent from repository -->
  </parent>

  <dependencies>
    <dependency>
      <groupId>yunti</groupId>
      <artifactId>yunti-spring-boot-core</artifactId>
    </dependency>

    <dependency>
      <groupId>yunti</groupId>
      <artifactId>yunti-spring-boot-dao</artifactId>
    </dependency>

    <!--
    <dependency>
      <groupId>yunti</groupId>
      <artifactId>yunti-spring-boot-clojure</artifactId>
    </dependency>
    -->

    <dependency>
      <groupId>yunti</groupId>
      <artifactId>yunti-spring-boot-zeebe</artifactId>
    </dependency>

    <dependency>
      <groupId>yunti</groupId>
      <artifactId>yunti-spring-boot-oss</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-devtools</artifactId>
      <scope>runtime</scope>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>yunti</groupId>
      <artifactId>yunti-teg-starter</artifactId>
    </dependency>

  </dependencies>


  <build>
    <plugins>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
      </plugin>
    </plugins>

  </build>

</project>
