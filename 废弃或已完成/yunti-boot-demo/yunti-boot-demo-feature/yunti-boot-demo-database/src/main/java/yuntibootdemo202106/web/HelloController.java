package yuntibootdemo202106.web;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;
import yunti.boot.data.Database;

import javax.sql.DataSource;

@RestController
public class HelloController {

    @Autowired @Database("yunti")
    private JdbcTemplate yuntiJdbcTemplate;

    @Autowired @Database("demand")
    private JdbcTemplate demandJdbcTemplate;

    @Autowired @Database("yunti")
    private DataSource yuntiDataSource;

    @Autowired @Database("demand")
    private DataSource demandDataSource;

    @GetMapping("/hello")
    public String hello() {

        System.out.println("yuntiJdbcTemplate:" + yuntiJdbcTemplate);
        System.out.println("demandJdbcTemplate:" + demandJdbcTemplate);

        System.out.println("yuntiDataSource:" + yuntiDataSource);
        System.out.println("demandDataSource:" + demandDataSource);

        return "hello";
    }

}
