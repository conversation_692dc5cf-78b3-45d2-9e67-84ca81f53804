yunti:
  app-name: "yunti-boot-demo-202106"
  jsonrpc-url: "http://devapps.erp.oa.com/jsonrpc/"
  web:
    port: 3000
    context-path: "/"
  jdbc:
    yunti:
      enabled: true
      url: "************************************?${mysql.default-url-args}"
      username: test
      password: test_test
      validation-query: select 1
    demand:
      enabled: true
      url: "*********************************************?${mysql.default-url-args}"
      username: test
      password: test_test
      validation-query: select 1

management:
  endpoints:
    web:
      exposure:
        include: "*"
  endpoint:
    health:
      show-details: always
  metrics:
    tags:
      app: "${spring.application.name}"
