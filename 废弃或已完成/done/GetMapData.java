package com.nickxie.others.done;

import com.fasterxml.jackson.databind.JsonNode;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.net.Browser;
import com.pugwoo.wooutils.net.HttpResponse;
import com.pugwoo.wooutils.string.StringTools;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.FileOutputStream;
import java.util.List;

/**
 * 这个是泽虹拉取地图数据的小任务
 */
public class GetMapData {

    @Data @AllArgsConstructor
    private static class Node {
        private Integer code;
        private String level;
    }

    public static void main(String[] args) throws Exception {

        Browser browser = new Browser();

        HttpResponse resp = browser.get("https://geo.datav.aliyun.com/areas_v2/bound/all.json");

        List<JsonNode> list = JSON.parseArray(resp.getContentString());
        List<Node> nodes = ListUtils.transform(list, o -> {
            Node node = new Node(o.get("adcode").asInt(), o.get("level").asText());
            if (node.getCode() == null || StringTools.isBlank(node.getLevel())) {
                System.err.println("节点数据有异常：" + JSON.toJson(o));
            }
            return node;
        });

        FileOutputStream out = new FileOutputStream("d:/mapdata/all.json");
        browser.get("https://geo.datav.aliyun.com/areas_v2/bound/all.json", out);

        out = new FileOutputStream("d:/mapdata/infos.json");
        browser.get("https://geo.datav.aliyun.com/areas_v2/bound/infos.json", out);

        for (Node node : nodes) {
            String code = node.getCode().toString();
            String fileName, url;
            if ("district".equals(node.getLevel())) {
                fileName = "d:/mapdata/" + code + ".json";
                url = "https://geo.datav.aliyun.com/areas_v2/bound/" + code + ".json";
            } else {
                fileName = "d:/mapdata/" + code + "_full.json";
                url = "https://geo.datav.aliyun.com/areas_v2/bound/" + code + "_full.json";
            }
            try {
                out = new FileOutputStream(fileName);
                resp = browser.get(url, out);
                if (resp.getResponseCode() != 200) {
                    System.err.println("get code fail, code=" + code + ",http status=" + resp.getResponseCode());
                }
            } catch (Exception e) {
                System.err.println("get code fail, code=" + code + ",exception:" + e.getMessage());
            }
        }

    }

}
