package com.pugwoo.dboperate.archived.stock_health;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Slf4j
@SpringBootTest
public class CalculateDiffService {

    @Resource
    private DBHelper yuntiIdcDBHelper;
    @Resource
    private DBHelper rrpIdcDBHelper;

    @Data
    public static class SaleVO {
        @Column("stat_time")
        private Date statTime;
        @Column("device_type")
        private String deviceType;
        @Column("instance_type")
        private String instanceType;
        @Column("zone_name")
        private String zoneName;
        @Column("region_name")
        private String regionName;
        @Column("cores")
        private BigDecimal cores;
    }

    /**
     * 计算diff
     */
    @Test
    public void calDiff() {
        // 1. 根据物理机型->cvm实例大类，构造出case when
        List<CloudDemandCsigDeviceExtendInfoDO> all =
                yuntiIdcDBHelper.getAll(CloudDemandCsigDeviceExtendInfoDO.class);
        StringBuilder caseWhen = new StringBuilder();
        caseWhen.append("(case ");
        for (CloudDemandCsigDeviceExtendInfoDO info : all) {
            caseWhen.append(" when device_type='").append(info.getDeviceType())
                    .append("'").append(" then '").append(info.getInstanceType()).append("' ");
        }
        caseWhen.append(" else '未分类' end)");

        // 2. 整合SQL，用于查询每一天的数据
        StringBuilder sql = new StringBuilder();
        sql.append("select stat_time,device_type,");
        sql.append(caseWhen).append(" as instance_type,");
        sql.append("zone_name,region_name,sum(cores) as cores ");
        sql.append(" FROM report_plan_detail ");
        sql.append("WHERE compute_type = 'CPU'\n" +
                "\t AND product_type = 'CVM' and category='售卖规模' and stat_time=? ");
        sql.append(" group by stat_time,device_type,instance_type,zone_name,region_name");

        // 3. 查询出需要diff的每一天
        List<String> statTimes = rrpIdcDBHelper.getRaw(String.class,
                "select distinct stat_time from report_plan_detail order by stat_time");

        // 4. 对于每一天diff出结果的值，并入到数据库
        List<SaleVO> last;
        List<SaleVO> current = null;
        for (String statTime : statTimes) {
            System.out.println("begin date:" + statTime);

            // 0. 把current赋值给last，并查出新的值赋值给current
            last = current;
            current = rrpIdcDBHelper.getRaw(SaleVO.class, sql.toString(), statTime);

            // 1. 如果只有current有值，那current直接存入db
            if (last == null && current != null) {
                Map<String, List<SaleVO>> currentMap = ListUtils.toMapList(current, o -> key(o), o -> o);
                List<ReportPlanDetailCvmSaleDiffDO> result = ListUtils.transform(currentMap.values(), o -> trans(o));
                rrpIdcDBHelper.insertBatchWithoutReturnId(result);
            } else if (last != null && current != null) {
                // 2. 如果last和current都有值，那么current比对last出diff，并把current存入db
                Map<String, List<SaleVO>> lastMap = ListUtils.toMapList(last, o -> key(o), o -> o);
                Map<String, List<SaleVO>> currentMap = ListUtils.toMapList(current, o -> key(o), o -> o);

                List<ReportPlanDetailCvmSaleDiffDO> result = new ArrayList<>();

                // 2.1 current有
                for (Map.Entry<String, List<SaleVO>> e : currentMap.entrySet()) {
                    List<SaleVO> lastOne = lastMap.get(e.getKey());
                    ReportPlanDetailCvmSaleDiffDO diff = trans(e.getValue());
                    if (lastOne == null) {
                        diff.setDiffCores(NumberUtils.sum(e.getValue(), o -> o.getCores()));
                    } else {
                        diff.setDiffCores(NumberUtils.sum(e.getValue(), o -> o.getCores())
                                .subtract(NumberUtils.sum(lastOne, o -> o.getCores())));
                    }
                    result.add(diff);
                }

                // 2.2 last有而current没有的，补充一条，这里不要看日期
                for (Map.Entry<String, List<SaleVO>> e : lastMap.entrySet()) {
                    if (currentMap.containsKey(e.getKey())) {
                        continue;
                    }
                    ReportPlanDetailCvmSaleDiffDO diff = trans(e.getValue());

                    Date sT = DateUtils.parse(statTime);
                    diff.setStatTime(sT); // 刷新成当前的日期

                    Calendar calendar = Calendar.getInstance();
                    calendar.setFirstDayOfWeek(Calendar.MONDAY);
                    assert sT != null;
                    calendar.setTime(sT);
                    diff.setYear(calendar.get(Calendar.YEAR));
                    diff.setWeek(calendar.get(Calendar.WEEK_OF_YEAR));
                    diff.setDayOfWeek(getDayOfWeek(sT));
                    calendar.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
                    diff.setWeekStartDate(calendar.getTime());

                    diff.setDiffCores(BigDecimal.ZERO.subtract(diff.getCores()));
                    diff.setCores(BigDecimal.ZERO); // 补的一条，清0
                    result.add(diff);
                }

                rrpIdcDBHelper.insertBatchWithoutReturnId(result);
            } else {
                // 3. 如果都没有值则不处理
            }
        }
    }

    /**
     * 计算累计数据和peak峰值
     */
    @Test
    public void calAccAndPeak() {
        // 太慢或mysql不支持，还是得单独来了
        // 全量是46万，所以这里直接全量来算了
        List<ReportPlanDetailCvmSaleDiffDO> all = rrpIdcDBHelper.getAll(ReportPlanDetailCvmSaleDiffDO.class,
                " order by device_type,instance_type,zone_name,region_name,stat_time");

        String lastDeviceType = "";
        String lastInstanceType = "";
        String lastZoneName = "";
        String lastRegionName = "";
        Integer lastYear = -1;
        Integer lastWeek = -1;

        List<ReportPlanDetailCvmSaleDiffDO> currentWeek = new ArrayList<>();
        for (ReportPlanDetailCvmSaleDiffDO d : all) {
            if (isStartNew(d, lastDeviceType, lastInstanceType, lastZoneName, lastRegionName, lastYear, lastWeek)) {
                // 更新current week并存入数据库
                if (!currentWeek.isEmpty()) {
                    BigDecimal peak = NumberUtils.max(currentWeek, o -> o.getWeeklyAccDiff());
                    peak = NumberUtils.max(peak, BigDecimal.ZERO);
                    BigDecimal finalPeak = peak;
                    ListUtils.forEach(currentWeek, o -> o.setWeeklyPeekAccDiff(finalPeak));
                    rrpIdcDBHelper.update(currentWeek);
                    log.info("update deviceType:{},instanceType:{},zone:{},region:{},year:{},week:{} done",
                            lastDeviceType, lastInstanceType, lastZoneName, lastRegionName, lastYear, lastWeek);
                    currentWeek = new ArrayList<>();
                }
            }

            currentWeek.add(d);
            d.setWeeklyAccDiff(NumberUtils.sum(currentWeek, o -> o.getDiffCores())); // acc包含它自己

            lastDeviceType = d.getDeviceType();
            lastInstanceType = d.getInstanceType();
            lastZoneName = d.getZoneName();
            lastRegionName = d.getRegionName();
            lastYear = d.getYear();
            lastWeek = d.getWeek();
        }

        if (!currentWeek.isEmpty()) { // 最后的一份再保存
            BigDecimal peak = NumberUtils.max(currentWeek, o -> o.getWeeklyAccDiff());
            peak = NumberUtils.max(peak, BigDecimal.ZERO);
            BigDecimal finalPeak = peak;
            ListUtils.forEach(currentWeek, o -> o.setWeeklyPeekAccDiff(finalPeak));
            rrpIdcDBHelper.update(currentWeek);
            log.info("update deviceType:{},instanceType:{},zone:{},region:{},year:{},week:{} done",
                    lastDeviceType, lastInstanceType, lastZoneName, lastRegionName, lastYear, lastWeek);
        }
    }

    private boolean isStartNew(ReportPlanDetailCvmSaleDiffDO d, String lastDeviceType, String lastInstanceType,
                               String lastZoneName, String lastRegionName, Integer lastYear, Integer lastWeek) {
        if (lastYear == -1) {
            return true;
        }
        return !(lastDeviceType.equals(d.getDeviceType()) && lastInstanceType.equals(d.getInstanceType())
            && lastZoneName.equals(d.getZoneName()) && lastRegionName.equals(d.getRegionName())
            && lastYear.equals(d.getYear()) && lastWeek.equals(d.getWeek()));
    }

    // 这里不能加入日期
    private String key(SaleVO d) {
        return String.join("&", d.getDeviceType(), d.getInstanceType(),
                d.getZoneName(), d.getRegionName());
    }

    private ReportPlanDetailCvmSaleDiffDO trans(List<SaleVO> list) {
        SaleVO v = list.get(0);

        ReportPlanDetailCvmSaleDiffDO reportPlanDetailCvmSaleDiffDO = new ReportPlanDetailCvmSaleDiffDO();
        reportPlanDetailCvmSaleDiffDO.setStatTime(v.getStatTime());
        reportPlanDetailCvmSaleDiffDO.setDeviceType(v.getDeviceType());
        reportPlanDetailCvmSaleDiffDO.setInstanceType(v.getInstanceType());
        reportPlanDetailCvmSaleDiffDO.setZoneName(v.getZoneName());
        reportPlanDetailCvmSaleDiffDO.setRegionName(v.getRegionName());
        reportPlanDetailCvmSaleDiffDO.setCores(NumberUtils.sum(list, o -> o.getCores()));
        reportPlanDetailCvmSaleDiffDO.setDiffCores(BigDecimal.ZERO);

        Calendar calendar = Calendar.getInstance();
        calendar.setFirstDayOfWeek(Calendar.MONDAY);
        calendar.setTime(v.getStatTime());
        reportPlanDetailCvmSaleDiffDO.setYear(calendar.get(Calendar.YEAR));
        reportPlanDetailCvmSaleDiffDO.setWeek(calendar.get(Calendar.WEEK_OF_YEAR));
        reportPlanDetailCvmSaleDiffDO.setDayOfWeek(getDayOfWeek(v.getStatTime()));
        calendar.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
        reportPlanDetailCvmSaleDiffDO.setWeekStartDate(calendar.getTime());

//        reportPlanDetailCvmSaleDiffDO.setWeeklyAccDiff(); // 这个待第二步再计算
//        reportPlanDetailCvmSaleDiffDO.setWeeklyPeekAccDiff();
        return reportPlanDetailCvmSaleDiffDO;
    }

    private static int getDayOfWeek(Date statTime) {
        return DateUtils.toLocalDate(statTime)
                .getDayOfWeek()
                .getValue();
    }

}
