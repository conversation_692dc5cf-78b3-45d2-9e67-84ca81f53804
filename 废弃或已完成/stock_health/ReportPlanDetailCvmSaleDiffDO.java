package com.pugwoo.dboperate.archived.stock_health;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 进销存的售卖diff数据
 */
@Data
@ToString
@Table("report_plan_detail_cvm_sale_diff")
public class ReportPlanDetailCvmSaleDiffDO {

    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    /** 统计日期<br/>Column: [stat_time] */
    @Column(value = "stat_time")
    private Date statTime;

    /** 设备类型<br/>Column: [device_type] */
    @Column(value = "device_type")
    private String deviceType;

    /** cvm实例类型<br/>Column: [instance_type] */
    @Column(value = "instance_type")
    private String instanceType;

    /** 可用区<br/>Column: [zone_name] */
    @Column(value = "zone_name")
    private String zoneName;

    /** 地域<br/>Column: [region_name] */
    @Column(value = "region_name")
    private String regionName;

    /** 核心数<br/>Column: [cores] */
    @Column(value = "cores")
    private BigDecimal cores;

    /** 和上一天变化核心数<br/>Column: [diff_cores] */
    @Column(value = "diff_cores")
    private BigDecimal diffCores;

    /** 年份<br/>Column: [year] */
    @Column(value = "year")
    private Integer year;

    /** 属于第几周<br/>Column: [week] */
    @Column(value = "week")
    private Integer week;

    /** 该周的第几天<br/>Column: [day_of_week] */
    @Column(value = "day_of_week")
    private Integer dayOfWeek;

    /** 周起始日期<br/>Column: [week_start_date] */
    @Column(value = "week_start_date")
    private Date weekStartDate;

    /** 周累计变化<br/>Column: [weekly_acc_diff] */
    @Column(value = "weekly_acc_diff")
    private BigDecimal weeklyAccDiff;

    /** 周累计变化峰值<br/>Column: [weekly_peek_acc_diff] */
    @Column(value = "weekly_peek_acc_diff")
    private BigDecimal weeklyPeekAccDiff;

}