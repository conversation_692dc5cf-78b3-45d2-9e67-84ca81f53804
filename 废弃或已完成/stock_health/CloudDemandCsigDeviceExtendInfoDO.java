package com.pugwoo.dboperate.archived.stock_health;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * csig机型扩展信息表
 */
@Data
@ToString
@Table("cloud_demand_csig_device_extend_info")
public class CloudDemandCsigDeviceExtendInfoDO {

    /** id<br/>Column: [id] */
    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    /** 删除标记<br/>Column: [deleted] */
    @Column(value = "deleted", softDelete = {"0", "1"})
    private Boolean deleted;

    /** 创建时间<br/>Column: [create_time] */
    @Column(value = "create_time")
    private Date createTime;

    /** 更新时间<br/>Column: [update_time] */
    @Column(value = "update_time")
    private Date updateTime;

    /** 设备类型<br/>Column: [device_type] */
    @Column(value = "device_type")
    private String deviceType;

    /** 主实例规格<br/>Column: [main_instance_type] */
    @Column(value = "main_instance_type")
    private String mainInstanceType;

    /** 可生产实例类型<br/>Column: [instance_type] */
    @Column(value = "instance_type")
    private String instanceType;

    /** 可售卖核心数(核)<br/>Column: [sale_core] */
    @Column(value = "sale_core")
    private BigDecimal saleCore;

}