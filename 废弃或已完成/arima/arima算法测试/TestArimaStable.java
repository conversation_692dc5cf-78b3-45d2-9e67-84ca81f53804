package com.pugwoo.dboperate.archived.arima算法测试;

import com.pugwoo.wooutils.io.IOUtils;
import com.pugwoo.wooutils.net.Browser;
import com.pugwoo.wooutils.net.HttpResponse;

/**
 * 实测结果：ARIMA大概有3%的概率，出现错误的结果
 */
public class TestArimaStable {

    public static void main(String[] args) {
        int successCount = 0;
        int failCount = 0;

        for(int i = 0; i < 10000; i++) {
            try {
                boolean result = test();
                if (result) {
                    successCount++;
                } else {
                    failCount++;
                }
            } catch (Exception e) {
                failCount++;
            }
            if (i % 100 == 0) {
                System.out.println("successCount: " + successCount);
                System.out.println("failCount: " + failCount);
            }
        }

        System.out.println("final:");
        System.out.println("successCount: " + successCount);
        System.out.println("failCount: " + failCount);
    }

    public static boolean test() throws Exception {

        Browser browser = new Browser();
        browser.addRequestHeader("Cookie", IOUtils.readClasspathResourceAsString("/废弃或已完成/db-operate/resources/exp-crp-cookie.txt"));

        HttpResponse resp = browser.post("https://exp-crp.woa.com/cloud-demand-arima/?n=6&p=0&d=1&q=6",
                ("Date,Value\n" +
                "2021-01-31,44754.610422\n" +
                "2021-02-28,31167.928455\n" +
                "2021-03-31,20517.551923\n" +
                "2021-04-30,17558.298689\n" +
                "2021-05-31,3119.687373\n" +
                "2021-06-30,3426.010459\n" +
                "2021-07-31,6219.224276\n" +
                "2021-08-31,18311.927846\n" +
                "2021-09-30,18013.639477\n" +
                "2021-10-31,14444.705790\n" +
                "2021-11-30,19371.382131\n" +
                "2021-12-31,15363.064661\n" +
                "2022-01-31,10240.046601\n" +
                "2022-02-28,11446.307139\n" +
                "2022-03-31,26358.503514\n" +
                "2022-04-30,6924.731797\n" +
                "2022-05-31,4153.398410\n" +
                "2022-06-30,3656.620191\n" +
                "2022-07-31,3223.838444\n" +
                "2022-08-31,4219.361467\n" +
                "2022-09-30,2970.664110\n" +
                "2022-10-31,3594.627226\n" +
                "2022-11-30,2385.312117\n" +
                "2022-12-31,1111.539509\n" +
                "2023-01-31,878.285331\n" +
                "2023-02-28,2084.899323").getBytes());

        String result = "2023-03-31   -10163.007974\n" +
                "2023-04-30    -3053.832292\n" +
                "2023-05-31     4410.010842\n" +
                "2023-06-30    19074.546513\n" +
                "2023-07-31    37300.371576\n" +
                "2023-08-31    43879.770359\n" +
                "Freq: M, Name: predicted_mean, dtype: float64";

        boolean success = resp.getContentString().equals(result);
        return success;
    }

}
