package com.tencent.cmdboper.common.cmdbclient;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * CMDB查询参数。<br>
 *
 * 这里并非实际发送给cmdb的结构，而是封装后调用方实际关心的参数。
 */
@Data
public class CmdbQueryDTO {

    /**查询对象，必填*/
    private String schemeId;

    /**参数列，必填*/
    private List<String> resultColumn;

    /**offset偏移位置，从0开始*/
    private int startIndex;

    /**每页个数*/
    private int pageSize = 10;

    /**是否传回总数*/
    private boolean returnTotalRows;

    private String orderBy = "";

    /**表达searchCondition中的关系*/
    private String conditionLogical = "";

    /**查询条件，多个条件是AND逻辑关系*/
    private Map<String, Object> searchCondition;

    /**自定义查询语句，【当该字段有值时，searchCondition和conditionLogical无效】*/
    private String customCondition;

}
