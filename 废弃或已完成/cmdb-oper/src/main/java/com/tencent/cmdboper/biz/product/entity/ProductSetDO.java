package com.tencent.cmdboper.biz.product.entity;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import com.tencent.cmdboper.common.utils.GBK;
import lombok.Data;

/**
 * 产品集
 */
@Data
@Table("bas_dis_product_set")
public class ProductSetDO {

    /**产品集ID*/
    @Column(value = "ProductSetId", isKey = true, isAutoIncrement = true)
    private Long id;

    /**产品集名称*/
    @Column("ProductSetName")
    private byte[] name;

    /**是否启用*/
    @Column("EnableFlag")
    private Boolean enableFlag;

    /**排序*/
    @Column("OrderNo")
    private Integer orderNo;

    public String getNameGBK() {
        return GBK.fromLatin1Utf8(name);
    }
}
