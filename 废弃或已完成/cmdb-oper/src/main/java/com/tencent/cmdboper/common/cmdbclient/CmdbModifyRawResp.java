package com.tencent.cmdboper.common.cmdbclient;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * cmdb 修改操作的原生返回
 */
@Data
public class CmdbModifyRawResp {

    @Data
    public static class Header {
        private Integer errorCode;
        private String logId;
        private Integer returnCode;
        private Integer returnRows;
        private Integer errorRows;
        private String errorInfo;
        private Long totalRows;
    }

    @Data
    public static class Result {
        /**错误码，0是成功*/
        private Integer errorCode;
        private Boolean status;
        /**当插入时，此值是插入返回的主键，目前都是数字类型*/
        private Long key;
        /**错误信息*/
        private String message;
    }

    @Data
    public static class DataSet {
        /**结果的key是action名称*/
        private List<Map<String, Result>> result;
        private Header header;
    }

    private DataSet dataSet;

}
