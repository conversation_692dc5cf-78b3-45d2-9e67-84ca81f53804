package com.tencent.cmdboper.common.utils;

import com.pugwoo.wooutils.json.JSON;
import lombok.extern.slf4j.Slf4j;

import java.io.UnsupportedEncodingException;

@Slf4j
public class GBK {

    public static String to(byte[] bytes) {
        if(bytes == null) {
            return null;
        }
        try {
            return new String(bytes, "gbk");
        } catch (UnsupportedEncodingException e) {
            log.error("gbk byte[]->string fail, bytes:{}", JSON.toJson(bytes), e);
            return null;
        }
    }

    public static String fromLatin1Utf8(byte[] bytes) {
        if(bytes == null) {
            return null;
        }
        try {
            return GBK.to(UTF8.to(bytes).getBytes("latin1"));
        } catch (UnsupportedEncodingException e) {
            log.error("gbk latin1 utf8 byte[]->string fail, bytes:{}", JSON.toJson(bytes), e);
            return null;
        }
    }

    public static byte[] to(String str) {
        if(str == null) {
            return null;
        }
        try {
            return str.getBytes("gbk");
        } catch (UnsupportedEncodingException e) {
            log.error("gbk string->byte[] fail, string:{}", str, e);
            return null;
        }
    }

}
