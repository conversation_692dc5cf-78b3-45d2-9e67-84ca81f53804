package com.tencent.cmdboper.dict.devicestatus.controller;

import com.pugwoo.admin.bean.WebJsonBean;
import com.pugwoo.admin.utils.PageUtils;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.model.PageData;
import com.tencent.cmdboper.dict.devicestatus.vo.DeviceStatusVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Map;

@Controller
public class DeviceStatusDictController {

    @Autowired
    private DBHelper dbHelper;

    @GetMapping("/dict_device_status")
    public String deviceStatus() {
        return "dict/device_status";
    }

    @ResponseBody
    @GetMapping("/get_device_status_dict")
    public WebJsonBean getDeviceStatus(int page, int pageSize, Integer configItemId) {
        PageData<DeviceStatusVO> pageData;
        if(configItemId == null) {
            pageData = dbHelper.getPage(DeviceStatusVO.class, page, pageSize);
        } else {
            pageData = dbHelper.getPage(DeviceStatusVO.class, page, pageSize,
                    "where ConfigItemId=?", configItemId);
        }

        Map<String, Object> result = PageUtils.trans(pageData);
        return WebJsonBean.ok(result);
    }

}
