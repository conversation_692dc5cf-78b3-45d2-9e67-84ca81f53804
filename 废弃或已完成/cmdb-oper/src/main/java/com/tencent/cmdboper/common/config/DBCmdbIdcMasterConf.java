package com.tencent.cmdboper.common.config;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.impl.SpringJdbcDBHelper;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;

@Configuration
public class DBCmdbIdcMasterConf {

    @Bean(name = "cmdbIdcMasterDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.cmdb-idc-master")
    public DataSource dataSource() {
        return DataSourceBuilder.create().build();
    }

    @Bean("cmdbIdcMasterJdbcTemplate")
    public JdbcTemplate jdbcTemplate(@Qualifier("cmdbIdcMasterDataSource") DataSource dataSource) {
        return new JdbcTemplate(dataSource);
    }

    @Bean("cmdbIdcMasterNamedParameterJdbcTemplate")
    public NamedParameterJdbcTemplate namedParameterJdbcTemplate(
            @Qualifier("cmdbIdcMasterDataSource") DataSource dataSource) {
        return new NamedParameterJdbcTemplate(dataSource);
    }

    @Bean(name = "cmdbIdcMasterTransactionManager")
    public DataSourceTransactionManager transactionManager(
            @Qualifier("cmdbIdcMasterDataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    @Bean("cmdbIdcMasterDBHelper")
    public DBHelper dbHelper(@Qualifier("cmdbIdcMasterJdbcTemplate") JdbcTemplate jdbcTemplate,
            @Qualifier("cmdbIdcMasterNamedParameterJdbcTemplate") NamedParameterJdbcTemplate namedParameterJdbcTemplate) {

        SpringJdbcDBHelper springJdbcDBHelper = new SpringJdbcDBHelper();

        springJdbcDBHelper.setJdbcTemplate(jdbcTemplate);
        springJdbcDBHelper.setNamedParameterJdbcTemplate(namedParameterJdbcTemplate);

        return springJdbcDBHelper;
    }

}
