package com.tencent.cmdboper.biz.product.entity;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import com.tencent.cmdboper.common.utils.GBK;
import lombok.Data;

@Data
@Table("bas_dis_product")
public class ProductDO {

    @Column(value = "ProductId", isKey = true, isAutoIncrement = true)
    private Long id;

    /**产品名称*/
    @Column("ProductName")
    private byte[] productName;

    /**业务部门ID，关联的是bas_dis_dept的deptid*/
    @Column("BusinessDeptId")
    private Long businessDeptId;

    /**运维部门ID，关联的是t_dep的id*/
    @Column("OperateDeptId")
    private Long operateDeptId;

    /**产品集ID，关联的是bas_dis_product的id*/
    @Column("ProductSetId")
    private Long productSetId;

    /**规划产品ID，关联的是bas_dis_plan_product*/
    @Column("PlanProductId")
    private Long planProductId;

    /**有没有启用，提供给用户选择时，只返回启用的*/
    @Column("EnableFlag")
    private Boolean enableFlag;

    // 还有4个Is开头的字段，看起来不太重要，暂不管

    public String getProductNameGBK() {
        // 这张表特殊，字段是utf8编码，存入的是字符gbk编码的latin1二进制
        return GBK.fromLatin1Utf8(productName);
    }

}
