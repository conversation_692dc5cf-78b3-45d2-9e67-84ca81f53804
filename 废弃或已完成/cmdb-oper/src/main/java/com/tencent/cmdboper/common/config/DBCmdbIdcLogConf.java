package com.tencent.cmdboper.common.config;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.impl.SpringJdbcDBHelper;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;

import javax.sql.DataSource;

@Configuration
public class DBCmdbIdcLogConf {

    @Bean(name = "cmdbIdcLogDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.cmdb-idc-log")
    public DataSource dataSource() {
        return DataSourceBuilder.create().build();
    }

    @Bean("cmdbIdcLogJdbcTemplate")
    public JdbcTemplate jdbcTemplate(@Qualifier("cmdbIdcLogDataSource") DataSource dataSource) {
        return new JdbcTemplate(dataSource);
    }

    @Bean("cmdbIdcLogNamedParameterJdbcTemplate")
    public NamedParameterJdbcTemplate namedParameterJdbcTemplate(
            @Qualifier("cmdbIdcLogDataSource") DataSource dataSource) {
        return new NamedParameterJdbcTemplate(dataSource);
    }

    @Bean("cmdbIdcLogDBHelper")
    public DBHelper dbHelper(@Qualifier("cmdbIdcLogJdbcTemplate") JdbcTemplate jdbcTemplate,
                             @Qualifier("cmdbIdcLogNamedParameterJdbcTemplate") NamedParameterJdbcTemplate namedParameterJdbcTemplate) {

        SpringJdbcDBHelper springJdbcDBHelper = new SpringJdbcDBHelper();

        springJdbcDBHelper.setJdbcTemplate(jdbcTemplate);
        springJdbcDBHelper.setNamedParameterJdbcTemplate(namedParameterJdbcTemplate);

        return springJdbcDBHelper;
    }

}
