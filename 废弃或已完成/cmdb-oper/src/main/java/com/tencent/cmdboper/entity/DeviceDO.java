package com.tencent.cmdboper.entity;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;

import java.sql.Timestamp;
import java.util.Date;

@Data
@Table("t_device")
public class DeviceDO {

    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Integer id;

    @Column(value = "deviceid")
    private String deviceid;

    @Column(value = "assetid")
    private String assetid;

    @Column(value = "name")
    private String name;

    @Column(value = "inputtime")
    private Date inputtime;

    @Column(value = "operator")
    private String operator;

    @Column(value = "SN")
    private String sN;

    @Column(value = "isdel")
    private String isdel;

    @Column(value = "isspecial")
    private String isspecial;

    @Column(value = "dep_id")
    private Integer depId;

    @Column(value = "devicetype_id")
    private Integer devicetypeId;

    @Column(value = "group_id")
    private Integer groupId;

    @Column(value = "status_id")
    private Integer statusId;

    @Column(value = "memo")
    private String memo;

    @Column(value = "hardmemo")
    private String hardmemo;

    @Column(value = "os_id")
    private Integer osId;

    @Column(value = "firstusetime")
    private Date firstusetime;

    @Column(value = "raidid")
    private Integer raidid;

    @Column(value = "buggetprice")
    private Float buggetprice;

    @Column(value = "cost")
    private Float cost;

    @Column(value = "alarmlevel_id")
    private Integer alarmlevelId;

    @Column(value = "bakoperator")
    private String bakoperator;

    @Column(value = "device_reduce")
    private Date deviceReduce;

    @Column(value = "faultstat")
    private Integer faultstat;

    @Column(value = "backup")
    private String backup;

    @Column(value = "InstallAgent")
    private Boolean installAgent;

    @Column(value = "borrowendtime")
    private Date borrowendtime;

    @Column(value = "deviceclass")
    private String deviceclass;

    @Column(value = "ServerTypeId")
    private Integer serverTypeId;

    @Column(value = "cicode")
    private String cicode;

    @Column(value = "assetdep_id")
    private Integer assetdepId;

    @Column(value = "OutBandProtocol")
    private String outBandProtocol;

    @Column(value = "ProtocolVersion")
    private String protocolVersion;

    @Column(value = "ProtocolUserName")
    private String protocolUserName;

    @Column(value = "ProtocolUserPassword")
    private String protocolUserPassword;

    @Column(value = "ImportantLevelId")
    private Integer importantLevelId;

    @Column(value = "adslastupdatetime")
    private Date adslastupdatetime;

    @Column(value = "adsfieldoperatetypeid")
    private Integer adsfieldoperatetypeid;

    @Column(value = "LastUpdate")
    private Timestamp lastUpdate;

    @Column(value = "CheckByADS")
    private Boolean checkByADS;

    @Column(value = "CheckDateTime")
    private Date checkDateTime;

    /** ºÏÍ¬ºÅ<br/>Column: [CompactNum] */
    @Column(value = "CompactNum")
    private String compactNum;

    /** ·þÎñÀàÐÍ<br/>Column: [ServiceType] */
    @Column(value = "ServiceType")
    private Integer serviceType;

    /** ·þÎñµ½ÆÚÊ±¼ä<br/>Column: [pacttime] */
    @Column(value = "pacttime")
    private Date pacttime;

    @Column(value = "ClassifyLevel")
    private Integer classifyLevel;

    @Column(value = "uuid")
    private String uuid;

    /** °æ±¾ºÅ»úÐÍID,¶ÔÓ¦server_typeclass_relation±íid<br/>Column: [ModelVersionId] */
    @Column(value = "ModelVersionId")
    private Integer modelVersionId;

    /** Êý¾ÝÐ¹ÃÜ±£»¤£¬0 ²»ÐèÒª 1 ÐèÒª<br/>Column: [DataLeakProtect] */
    @Column(value = "DataLeakProtect")
    private Boolean dataLeakProtect;

    /** EPOç‰©å“ID<br/>Column: [GoodsId] */
    @Column(value = "GoodsId")
    private Integer goodsId;

    @Column(value = "VarChar01")
    private String varChar01;

    @Column(value = "VarChar02")
    private String varChar02;

    @Column(value = "VarChar03")
    private String varChar03;

    @Column(value = "VarChar04")
    private String varChar04;

    @Column(value = "Int02")
    private Integer int02;

    @Column(value = "Int03")
    private Integer int03;

    @Column(value = "Int04")
    private Integer int04;

    /** SvrMaintMode<br/>Column: [SvrMaintMode] */
    @Column(value = "SvrMaintMode")
    private String svrMaintMode;

    /** 客户属性<br/>Column: [ClientAttr] */
    @Column(value = "ClientAttr")
    private String clientAttr;

    /** 客户名称<br/>Column: [ClientName] */
    @Column(value = "ClientName")
    private String clientName;

}
