package com.tencent.cmdboper.biz.product.vo;

import com.pugwoo.dbhelper.annotation.RelatedColumn;
import com.tencent.cmdboper.biz.department.entity.BasDisDepartmentDO;
import com.tencent.cmdboper.biz.department.entity.DepartmentDO;
import com.tencent.cmdboper.biz.product.entity.PlanProductDO;
import com.tencent.cmdboper.biz.product.entity.ProductDO;
import com.tencent.cmdboper.biz.product.entity.ProductSetDO;
import lombok.Data;

@Data
public class ProductVO extends ProductDO {

    @RelatedColumn(localColumn = "BusinessDeptId", remoteColumn = "DeptId")
    private BasDisDepartmentDO businessDepartment;

    @RelatedColumn(localColumn = "OperateDeptId", remoteColumn = "id")
    private DepartmentDO operateDepartment;

    @RelatedColumn(localColumn = "ProductSetId", remoteColumn = "ProductSetId")
    private ProductSetDO productSet;

    @RelatedColumn(localColumn = "PlanProductId", remoteColumn = "PlanProductId")
    private PlanProductDO planProduct;

}
