package com.tencent.cmdboper.common.utils;

import com.pugwoo.wooutils.json.JSON;
import lombok.extern.slf4j.Slf4j;

import java.io.UnsupportedEncodingException;

@Slf4j
public class UTF8 {

    public static String to(byte[] bytes) {
        if(bytes == null) {
            return null;
        }
        try {
            return new String(bytes, "utf-8");
        } catch (UnsupportedEncodingException e) {
            log.error("utf-8 byte[]->string fail, bytes:{}", JSON.toJson(bytes), e);
            return null;
        }
    }


    public static byte[] to(String str) {
        if(str == null) {
            return null;
        }
        try {
            return str.getBytes("utf-8");
        } catch (UnsupportedEncodingException e) {
            log.error("utf-8 string->byte[] fail, string:{}", str, e);
            return null;
        }
    }

}
