package com.tencent.cmdboper.biz.department.entity;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import com.tencent.cmdboper.common.utils.GBK;
import lombok.Data;

/**
 * 部门表（无层级）
 */
@Data
@Table("t_dep")
public class DepartmentDO {

    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    /**部门名称*/
    @Column("name")
    private byte[] name;

    /**部门负责人*/
    @Column("operator")
    private String operator;

    /**运维总监*/
    @Column("principal")
    private String principal;

    /**备注*/
    @Column("memo")
    private byte[] memo;

    public String getNameGBK() {
        return GBK.to(name);
    }

    public String getMemoGBK() {
        return GBK.to(memo);
    }

}
