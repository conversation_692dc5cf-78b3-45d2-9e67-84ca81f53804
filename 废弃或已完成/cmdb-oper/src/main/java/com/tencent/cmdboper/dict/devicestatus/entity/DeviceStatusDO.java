package com.tencent.cmdboper.dict.devicestatus.entity;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import com.tencent.cmdboper.common.utils.GBK;
import lombok.Data;

/**
 * 服务器、网络设备等的状态枚举表
 */
@Data
@Table("equipment_status")
public class DeviceStatusDO {

    @Column(value = "Id", isKey = true, isAutoIncrement = true)
    private Long id;

    /**状态名称*/
    @Column("Name")
    private byte[] name;

    /**备注*/
    @Column("Memo")
    private byte[] memo;

    /**设备类型id -> BaseSetDO t_baseset*/
    @Column("ConfigItemId")
    private Long configItemId;

    public String getNameGBK() {
        return GBK.to(name);
    }

    public String getMemoGBK() {
        return GBK.to(memo);
    }

}
