package com.tencent.cmdboper.common.entity;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;

import java.util.Date;

@Data
@Table("table_count")
public class TableCountDO {

    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    /** 软删除<br/>Column: [deleted] */
    @Column(value = "deleted", softDelete = {"0", "1"})
    private Boolean deleted;

    @Column(value = "create_time", setTimeWhenInsert = true)
    private Date createTime;

    @Column(value = "update_time", setTimeWhenInsert = true, setTimeWhenUpdate = true)
    private Date updateTime;

    /** 表名<br/>Column: [table_name] */
    @Column(value = "table_name")
    private String tableName;

    /** 统计日期<br/>Column: [date] */
    @Column(value = "date")
    private String date;

    /** 表条数<br/>Column: [count] */
    @Column(value = "count")
    private Long count;

}
