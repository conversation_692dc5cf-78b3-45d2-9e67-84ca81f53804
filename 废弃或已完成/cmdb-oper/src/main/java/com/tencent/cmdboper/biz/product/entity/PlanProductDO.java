package com.tencent.cmdboper.biz.product.entity;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import com.tencent.cmdboper.common.utils.GBK;
import lombok.Data;

/**
 * 规划产品表
 */
@Data
@Table("bas_dis_plan_product")
public class PlanProductDO {

    /*规划产品ID*/
    @Column(value = "PlanProductId", isKey = true, isAutoIncrement = true)
    private Long id;

    /**规划产品名称*/
    @Column("PlanProductName")
    private byte[] name;

    /**规划产品类型*/
    @Column("Type")
    private byte[] type;

    // 还有其它deptId、ProductSetId等几个属性，先不管

    public String getNameGBK() {
        return GBK.fromLatin1Utf8(name);
    }

    public String getTypeGBK() {
        return GBK.fromLatin1Utf8(type);
    }

}
