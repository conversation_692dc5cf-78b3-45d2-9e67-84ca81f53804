package com.tencent.cmdboper.common.cmdbclient;

import lombok.Data;

import java.util.List;

/**
 * 请求CMDB的返回结果
 */
@Data
public class CmdbQueryResp<T> {

    /**返回码，0为成功，非0为失败*/
    private int returnCode;

    /**错误码*/
    private int errorCode;

    /**具体错误信息*/
    private String errorInfo;

    /**当前返回的条数*/
    private int returnRows;

    /**总条数，-1表示未知*/
    private long totalRows;

    private String logId;

    /**数据*/
    private List<T> data;

    public boolean isSuccess() {
        return returnCode == 0 && errorCode == 0;
    }

}
