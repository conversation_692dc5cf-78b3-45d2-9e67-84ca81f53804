package com.tencent.cmdboper.biz.product.controller;

import com.pugwoo.admin.bean.WebJsonBean;
import com.pugwoo.admin.utils.PageUtils;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.model.PageData;
import com.tencent.cmdboper.biz.product.vo.ProductVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Map;

@Controller
public class ProductController {

    @Autowired
    private DBHelper dbHelper;

    @GetMapping("/product")
    public String department() {
        return "biz/product";
    }

    @ResponseBody
    @GetMapping("/get_product")
    public WebJsonBean getDepartment(int page, int pageSize) {
        PageData<ProductVO> pageData;

        pageData = dbHelper.getPage(ProductVO.class, page, pageSize, "order by ProductId desc");

        Map<String, Object> result = PageUtils.trans(pageData);
        return WebJsonBean.ok(result);
    }

}
