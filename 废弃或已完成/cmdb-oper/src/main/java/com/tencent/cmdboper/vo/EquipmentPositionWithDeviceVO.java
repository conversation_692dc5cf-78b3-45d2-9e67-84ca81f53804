package com.tencent.cmdboper.vo;

import com.pugwoo.dbhelper.annotation.RelatedColumn;
import com.tencent.cmdboper.entity.DeviceDO;
import com.tencent.cmdboper.entity.EquipmentPositionDO;
import lombok.Data;

import java.util.List;

@Data
public class EquipmentPositionWithDeviceVO extends EquipmentPositionDO {

    @RelatedColumn(localColumn = "id", remoteColumn = "ParentId",
            extraWhere = "where id!=ParentId and flag=0 AND configItemId=2 ")
    private List<EquipmentPositionWithDeviceVO> children;

    @RelatedColumn(localColumn = "EquipmentId", remoteColumn = "id",
            extraWhere = "where isdel=0")
    private DeviceDO deviceDO;

}
