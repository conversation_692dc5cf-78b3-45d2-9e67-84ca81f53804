package com.tencent.cmdboper.vo;

import com.pugwoo.dbhelper.annotation.RelatedColumn;
import com.tencent.cmdboper.entity.EquipmentPositionDO;
import lombok.Data;

import java.util.List;

@Data
public class EquipmentPositionVO extends EquipmentPositionDO {

    @RelatedColumn(localColumn = "id", remoteColumn = "ParentId",
            extraWhere = "where id!=ParentId and flag=0 AND configItemId=2 ")
    private List<EquipmentPositionVO> children;

}
