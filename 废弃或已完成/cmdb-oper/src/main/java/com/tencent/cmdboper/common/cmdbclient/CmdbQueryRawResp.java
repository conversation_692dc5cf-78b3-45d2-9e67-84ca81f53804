package com.tencent.cmdboper.common.cmdbclient;

import lombok.Data;

import java.util.List;

/**
 * CMDB的原生返回，为了方便json解析，内部用
 */
@Data
public class CmdbQueryRawResp {

    @Data
    public static class FieldDef {
        private String id;
        private String name;
        private String dataType;
    }

    @Data
    public static class Header {
        private Integer errorCode;
        private String logId;
        private Integer returnCode;
        private Integer returnRows;
        private String version;
        private String errorInfo;
        private Long totalRows;
    }

    @Data
    public static class DataSet {
        private List<FieldDef> fieldDef; // TODO 当有异常时，cmdb返回的这个字段是{}
        private List<List<Object>> data; // TODO 当有异常时，cmdb返回的这个字段是{}
        private Header header;
    }

    private DataSet dataSet;

}
