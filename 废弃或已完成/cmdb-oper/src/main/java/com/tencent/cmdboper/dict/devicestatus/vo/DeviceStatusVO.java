package com.tencent.cmdboper.dict.devicestatus.vo;

import com.pugwoo.dbhelper.annotation.RelatedColumn;
import com.tencent.cmdboper.dict.devicestatus.entity.BaseSetDO;
import com.tencent.cmdboper.dict.devicestatus.entity.DeviceStatusDO;
import lombok.Data;

@Data
public class DeviceStatusVO extends DeviceStatusDO {

    @RelatedColumn(localColumn = "ConfigItemId", remoteColumn = "id")
    private BaseSetDO baseSetDO;

    public String getConfigItemName() {
        return baseSetDO == null ? "" :
                baseSetDO.getEngName() + "(" + baseSetDO.getChineseNameGBK() + ")";
    }

}
