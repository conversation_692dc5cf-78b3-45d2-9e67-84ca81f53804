package com.tencent.cmdboper.common.cmdbclient;

import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.net.Browser;
import com.pugwoo.wooutils.net.HttpResponse;
import com.pugwoo.wooutils.string.StringTools;
import lombok.Builder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * CMDB客户端
 */
@Slf4j
@Builder
public class CmdbClient {

    /** CMDB 系统ID */
    private final String systemId;
    /** CMDB 场景ID */
    private final String sceneId;
    /** CMDB 读接口 */
    private final String readUrl;
    /** CMDB 写接口 */
    private final String writeUrl;

    /**
     * 向CMDB发送查询请求
     * @param queryDTO 查询条件
     * @param dataClazz 直接转换成指定类，建议为结果创建DTO类
     * @return null if fail，非null也需要判断返回码
     */
    public <T> CmdbQueryResp<T> query(CmdbQueryDTO queryDTO, Class<T> dataClazz) throws Exception {
        String request = toRequestJson(queryDTO);

        try {
            Browser browser = new Browser();
            HttpResponse httpresp = browser.post(readUrl, request.getBytes());

            // 这里gson默认会把整数解析为double，所以用jackson了
            CmdbQueryRawResp rawResp = JSON.parse(httpresp.getContentString(), CmdbQueryRawResp.class);
            CmdbQueryResp resp = transRead(rawResp, dataClazz);
            if (resp == null) {
                log.error("request cmdb fail, parse result fail, request:{}, return:{}",
                        request, JSON.toJson(httpresp));
            }
            return resp;
        } catch (Exception e) {
            log.error("request cmdb fail, request:{}", request, e);
            return null;
        }
    }

    /**
     * 向CMDB发送修改请求
     * @param modifyDTO 修改请求体
     * @return null if fail，非null也需要判断返回码
     */
    public CmdbModifyResp modify(CmdbModifyDTO modifyDTO) {
        String request = toRequestJson(modifyDTO);

        try {
            Browser browser = new Browser();
            HttpResponse httpresp = browser.post(writeUrl, request.getBytes());

            // 这里gson默认会把整数解析为double，所以用jackson了
            CmdbModifyRawResp rawResp = JSON.parse(httpresp.getContentString(), CmdbModifyRawResp.class);
            CmdbModifyResp resp = transWrite(modifyDTO.getAction(), rawResp);
            if (resp == null) {
                log.error("request cmdb fail, parse result fail, request:{}, return:{}",
                        request, JSON.toJson(httpresp));
            }
            return resp;
        } catch (Exception e) {
            log.error("request cmdb fail, request:{}", request, e);
            return null;
        }
    }

    /**
     * 将cmdb原生返回的对象转换成封装的返回体
     * @param rawResp
     * @param dataClazz 支持直接转换成指定类
     * @return
     */
    private <T> CmdbQueryResp<T> transRead(CmdbQueryRawResp rawResp, Class<T> dataClazz) {
        if (rawResp == null || rawResp.getDataSet() == null) {
            return null;
        }
        CmdbQueryRawResp.DataSet dataSet = rawResp.getDataSet();
        if (dataSet.getHeader() == null) {
            return null;
        }
        CmdbQueryRawResp.Header header = dataSet.getHeader();

        CmdbQueryResp<T> resp = new CmdbQueryResp<T>();
        resp.setReturnCode(header.getReturnCode() == null ? -999999 : header.getReturnCode());
        resp.setErrorCode(header.getErrorCode() == null ? -999999 : header.getErrorCode());
        resp.setErrorInfo(header.getErrorInfo());
        resp.setTotalRows(header.getTotalRows() == null ? -1L : header.getTotalRows());
        resp.setLogId(header.getLogId());
        resp.setReturnRows(header.getReturnRows() == null ? -1 : header.getReturnRows());

        // 转换data
        List<T> data = new ArrayList<>();
        resp.setData(data);
        List<CmdbQueryRawResp.FieldDef> fieldDef = dataSet.getFieldDef();
        List<List<Object>> dataList = dataSet.getData();
        if (fieldDef == null || dataList == null) {
            return resp;
        }

        for (List<Object> obj : dataList) {
            if (obj == null) {
                continue; // 脏数据不处理
            }

            // 检查数据，定义行应该和dataList每个元素长度一致，否则认为数据有问题，整个解析按报错处理
            if (obj.size() != fieldDef.size()) {
                log.error("data element size not match fieldDef size, data:{}, def:{}",
                        JSON.toJson(obj), JSON.toJson(fieldDef));
                return null;
            }

            // 转换
            Map<String, Object> map = new HashMap<>();
            for (int i = 0; i < fieldDef.size(); i++) {
                CmdbQueryRawResp.FieldDef field = fieldDef.get(i);
                map.put(field.getId(), obj.get(i));
            }

            T t = JSON.parse(JSON.toJson(map), dataClazz);
            data.add(t);
        }

        return resp;
    }

    /**
     * 转换成封装后的结果
     * @param action 请求方法
     * @param rawResp 修改请求的原生返回
     * @return
     */
    private CmdbModifyResp transWrite(String action, CmdbModifyRawResp rawResp) {
        if (rawResp == null || rawResp.getDataSet() == null) {
            return null;
        }

        CmdbModifyRawResp.DataSet dataSet = rawResp.getDataSet();
        if (dataSet.getHeader() == null || dataSet.getResult() == null) {
            return null;
        }

        CmdbModifyRawResp.Header header = dataSet.getHeader();
        CmdbModifyResp resp = new CmdbModifyResp();

        resp.setReturnCode(header.getReturnCode());
        resp.setErrorCode(header.getErrorCode());
        resp.setErrorInfo(header.getErrorInfo());
        resp.setLogId(header.getLogId());
        resp.setAffectedRows(header.getReturnRows());

        List<Map<String, CmdbModifyRawResp.Result>> results = dataSet.getResult();
        if (results.size() == 0 || results.get(0).get(action) == null) {
            return resp;
        }
        CmdbModifyRawResp.Result result = results.get(0).get(action);

        resp.setErrorCode(result.getErrorCode());
        if (StringTools.isNotBlank(result.getMessage())) {
            resp.setErrorInfo(result.getMessage());
        }
        resp.setInsertedId(result.getKey());

        return resp;
    }

    /**
     * 转换成实际请求的参数
     * @param queryDTO
     * @return
     */
    private String toRequestJson(CmdbQueryDTO queryDTO) {

        Map<String, Object> request = new HashMap<>();
        Map<String, Object> params = new HashMap<>();
        request.put("params", params);
        Map<String, Object> content = new HashMap<>();
        params.put("content", content);

        content.put("schemeId", queryDTO.getSchemeId());
        content.put("type", "Json");
        content.put("version", "1.0");
        content.put("dataFormat", "list");
        Map<String, Object> requestInfo = new HashMap<>();
        content.put("requestInfo", requestInfo);

        requestInfo.put("systemId", systemId);
        requestInfo.put("sceneId", sceneId);
        requestInfo.put("requestModule", "");

        requestInfo.put("operator", "nickxie");

        Map<String, Object> resultColumn = new HashMap<>();
        content.put("resultColumn", resultColumn);
        if (queryDTO.getResultColumn() != null) {
            for (String column : queryDTO.getResultColumn()) {
                resultColumn.put(column, "");
            }
        }

        Map<String, Object> pagingInfo = new HashMap<>();
        content.put("pagingInfo", pagingInfo);
        pagingInfo.put("startIndex", queryDTO.getStartIndex());
        pagingInfo.put("pageSize", queryDTO.getPageSize());
        pagingInfo.put("returnTotalRows", queryDTO.isReturnTotalRows() ? "1" : "0");

        content.put("orderBy", queryDTO.getOrderBy());

        if (StringUtils.isNotBlank(queryDTO.getCustomCondition())) {
            content.put("conditionLogical", "");
            content.put("searchCondition", new HashMap<>());
            content.put("customCondi", queryDTO.getCustomCondition());
        } else {
            content.put("conditionLogical", queryDTO.getConditionLogical());
            Map<String, Object> searchCondition;
            if (queryDTO.getSearchCondition() != null) {
                searchCondition = queryDTO.getSearchCondition();
            } else {
                searchCondition = new HashMap<>();
            }
            content.put("searchCondition", searchCondition);
        }

        return JSON.toJson(request);
    }

    /** 转换成实际请求的参数*/
    private String toRequestJson(CmdbModifyDTO modifyDTO) {

        Map<String, Object> request = new HashMap<>();
        Map<String, Object> params = new HashMap<>();
        request.put("params", params);
        Map<String, Object> content = new HashMap<>();
        params.put("content", content);

        content.put("schemeId", modifyDTO.getSchemeId());
        content.put("type", "Json");
        content.put("version", "1.0");

        Map<String, Object> requestInfo = new HashMap<>();
        content.put("requestInfo", requestInfo);

        requestInfo.put("systemId", systemId);
        requestInfo.put("sceneId", sceneId);
        requestInfo.put("requestModule", "");
        requestInfo.put("operator", "nickxie");

        List<Map<String, Object>> actions = new ArrayList<>();
        content.put("actions", actions);

        Map<String, Object> action = new HashMap<>();
        actions.add(action);

        Map<String, Object> actionData = new HashMap<>();
        action.put(modifyDTO.getAction(), actionData);

        actionData.put("reason", modifyDTO.getReason());
        if (modifyDTO.getData() != null) { // cmdb api对值为null的判断不严，所以这里统一处理null为空字符串
            for (Map.Entry<String, Object> e : modifyDTO.getData().entrySet()) {
                if (e.getValue() == null) {
                    e.setValue("");
                }
            }
        }
        actionData.put("data", modifyDTO.getData() == null ? new HashMap<>() : modifyDTO.getData());
        actionData.put("condition", modifyDTO.getCondition() == null ? new HashMap<>() : modifyDTO.getCondition());

        return JSON.toJson(request);
    }

}
