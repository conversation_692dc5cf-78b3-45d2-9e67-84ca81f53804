package com.tencent.cmdboper.common.config;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.impl.SpringJdbcDBHelper;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;

import javax.sql.DataSource;

@Configuration
public class DBCmdbIdcSlaveConf {

    @Bean(name = "cmdbIdcSlaveDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.cmdb-idc-slave")
    public DataSource dataSource() {
        return DataSourceBuilder.create().build();
    }

    @Bean("cmdbIdcSlaveJdbcTemplate")
    public JdbcTemplate jdbcTemplate(@Qualifier("cmdbIdcSlaveDataSource") DataSource dataSource) {
        return new JdbcTemplate(dataSource);
    }

    @Bean("cmdbIdcSlaveNamedParameterJdbcTemplate")
    public NamedParameterJdbcTemplate namedParameterJdbcTemplate(
            @Qualifier("cmdbIdcSlaveDataSource") DataSource dataSource) {
        return new NamedParameterJdbcTemplate(dataSource);
    }

    /**
     * 生产环境的从库设置为主dbHelper
     */
    @Primary
    @Bean("cmdbIdcSlaveDBHelper")
    public DBHelper dbHelper(@Qualifier("cmdbIdcSlaveJdbcTemplate") JdbcTemplate jdbcTemplate,
            @Qualifier("cmdbIdcSlaveNamedParameterJdbcTemplate") NamedParameterJdbcTemplate namedParameterJdbcTemplate) {

        SpringJdbcDBHelper springJdbcDBHelper = new SpringJdbcDBHelper();

        springJdbcDBHelper.setJdbcTemplate(jdbcTemplate);
        springJdbcDBHelper.setNamedParameterJdbcTemplate(namedParameterJdbcTemplate);

        return springJdbcDBHelper;
    }

}
