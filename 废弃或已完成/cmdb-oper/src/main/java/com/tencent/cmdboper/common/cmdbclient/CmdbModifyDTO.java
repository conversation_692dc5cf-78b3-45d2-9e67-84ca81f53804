package com.tencent.cmdboper.common.cmdbclient;

import lombok.Data;

import java.util.Map;

/**
 * CMDB修改参数 <br>
 *
 * 这里并非实际发送给cmdb的结构，而是封装后调用方实际关心的参数。
 */
@Data
public class CmdbModifyDTO {

    /**操作对象，必填*/
    private String schemeId;

    /**操作方法，必填*/
    private String action;

    /**操作原因，必填*/
    private String reason;

    /**操作数据，可选*/
    private Map<String, Object> data;

    /**操作记录的条件，当修改和删除时，一般必须有值*/
    private Map<String, Object> condition;

}
