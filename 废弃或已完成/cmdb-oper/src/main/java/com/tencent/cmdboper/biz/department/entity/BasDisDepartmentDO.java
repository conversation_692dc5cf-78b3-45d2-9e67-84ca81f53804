package com.tencent.cmdboper.biz.department.entity;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import com.tencent.cmdboper.common.utils.GBK;
import lombok.Data;

/**
 * 产品信息中的业务部门
 */
@Data
@Table("bas_dis_dept")
public class BasDisDepartmentDO {

    /*业务部门ID*/
    @Column(value = "DeptId", isKey = true, isAutoIncrement = true)
    private Long id;

    /**部门名称*/
    @Column("DeptName")
    private byte[] name;

    // 还有其它很多属性，BizGroupId等

    public String getNameGBK() {
        return GBK.fromLatin1Utf8(name);
    }

}
