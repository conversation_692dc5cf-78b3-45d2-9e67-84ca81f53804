package com.tencent.cmdboper.entity;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;

import java.io.UnsupportedEncodingException;

@Data
@Table("business")
public class BusinessDO {

    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    @Column(value = "Uid")
    private String uid;

    @Column(value = "ParentUid")
    private String parentUid;

    @Column("ChineseName")
    private byte[] chineseName;

    public String getChineseNameGbk() {
        if(chineseName == null) {
            return null;
        }
        try {
            return new String(chineseName, "gbk");
        } catch (UnsupportedEncodingException e) {
            return null;
        }
    }

    public void setChineseName(String chineseName) {
        if(chineseName == null) {
            return;
        }
        try {
            this.chineseName = chineseName.getBytes("gbk");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
    }

}
