package com.tencent.cmdboper.dict.devicestatus.entity;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import com.tencent.cmdboper.common.utils.GBK;
import lombok.Data;

/**
 * 这个是configItemId的枚举字典
 */
@Data
@Table("t_baseset")
public class BaseSetDO {

    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    /**英文名称*/
    @Column("ename")
    private String engName;

    /**中文名称*/
    @Column("cname")
    private byte[] chineseName;

    @Column("ConfigItemTableName")
    private String configItemTableName;

    @Column("CICodePrefix")
    private String ciCodePrefix;

    public String getChineseNameGBK() {
        return GBK.to(chineseName);
    }

}
