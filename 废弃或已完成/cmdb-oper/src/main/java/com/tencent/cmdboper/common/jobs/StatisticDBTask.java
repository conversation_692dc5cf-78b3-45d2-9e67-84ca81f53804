package com.tencent.cmdboper.common.jobs;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.lang.DateUtils;
import com.tencent.cmdboper.common.entity.TableCountDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class StatisticDBTask {

    @Autowired @Qualifier("cmdbIdcSlaveJdbcTemplate")
    private JdbcTemplate cmdbIdcSlaveJdbcTemplate;

    @Autowired @Qualifier("adminDBHelper")
    private DBHelper adminDBHelper;

    @Scheduled(cron = "0 0 3 * * *")
    public void countTable() {
        log.info("countTable task start.");

        List<String> tables = cmdbIdcSlaveJdbcTemplate.queryForList("show tables", String.class);

        log.info("db table count:{}", tables.size());

        for(String table : tables) {
            Long count = cmdbIdcSlaveJdbcTemplate.queryForObject(
                    "select count(*) from " + table, Long.class);
            log.info("table:{} count:{}", table, count);

            TableCountDO tableCountDO = new TableCountDO();
            tableCountDO.setTableName(table);
            tableCountDO.setDate(DateUtils.formatDate(new Date()));
            tableCountDO.setCount(count);

            adminDBHelper.insert(tableCountDO);
        }

        log.info("countTable task end.");
    }

}
