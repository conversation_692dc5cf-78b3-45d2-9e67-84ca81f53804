package com.tencent.cmdboper.biz.department.controller;

import com.pugwoo.admin.bean.WebJsonBean;
import com.pugwoo.admin.utils.PageUtils;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.model.PageData;
import com.tencent.cmdboper.biz.department.entity.DepartmentDO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Map;

@Controller
public class DepartmentController {

    @Autowired
    private DBHelper dbHelper;

    @GetMapping("/department")
    public String department() {
        return "biz/department";
    }

    @ResponseBody
    @GetMapping("/get_deparment")
    public WebJsonBean getDepartment(int page, int pageSize) {
        PageData<DepartmentDO> pageData;

        pageData = dbHelper.getPage(DepartmentDO.class, page, pageSize);

        Map<String, Object> result = PageUtils.trans(pageData);
        return WebJsonBean.ok(result);
    }

}
