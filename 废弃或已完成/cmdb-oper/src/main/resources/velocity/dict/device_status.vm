#set($page_title='服务器状态枚举')
<style>
</style>

<div id="app" v-cloak>
    <el-form :inline="true" @keyup.native.enter="getData">
##        <el-form-item label="Id">
##            <el-input v-model="queryForm.Id" placeholder="仅示例，后台未实现"></el-input>
##        </el-form-item>
        <el-form-item>
##            <el-button type="primary" @click="(queryForm.page=1) && getData()">查询</el-button>
            <el-button @click="filterConfigItem()">全部</el-button>
            <el-button type="primary" @click="filterConfigItem(2)">只看服务器</el-button>
            <el-button type="primary" @click="filterConfigItem(5)">只看网络设备</el-button>
            <el-button type="primary" @click="filterConfigItem(6)">只看网络专线</el-button>
        </el-form-item>
    </el-form>

    <el-table :data="tableData" border stripe v-loading.body="tableLoading">
        <el-table-column prop="id" label="状态Id"></el-table-column>
        <el-table-column prop="nameGBK" label="状态名称"></el-table-column>
        <el-table-column prop="memoGBK" label="状态备注"></el-table-column>
        <el-table-column prop="configItemId" label="ConfigItemId"></el-table-column>
        <el-table-column prop="configItemName" label="ConfigItemName"></el-table-column>
    </el-table>

    <el-pagination style="float:right" @current-change="pageChange" :current-page="queryForm.page"
                   :total="total" :page-size="queryForm.pageSize" layout="total, prev, pager, next, jumper" background>
    </el-pagination>

</div>

<script>
    var defaultQueryForm = {page: 1, pageSize: 1000}
    var vm = new Vue({
        el: '#app',
        data: {
            queryForm: Utils.copy(defaultQueryForm),
            rules: {},
            total: 0, tableData: [], tableLoading: false
        },
        created: function() {
            this.getData()
        },
        methods: {
            getData: function() {
                var that = this
                that.tableLoading = true
                Resource.get("${_contextPath_}/get_device_status_dict", this.queryForm, function(resp){
                    that.tableData = resp.data.data
                    that.total = resp.data.total
                    that.tableLoading = false
                })
            },
            pageChange: function(page) {
                this.queryForm.page = page
                this.getData()
            },
            resetQuery: function() {
                this.queryForm = Utils.copy(defaultQueryForm)
            },
            filterConfigItem: function(configItemId) {
                this.queryForm.configItemId = configItemId
                this.queryForm.page = 1
                this.getData()
            }
        }
    })
</script>