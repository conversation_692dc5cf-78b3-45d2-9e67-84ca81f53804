nickxie-ip: ***********

spring:
  redis:
    host: ************
    port: 6380
    password: dev@123456
    database: 4
  datasource:
    admin:
      jdbc-url: jdbc:mysql://${nickxie-ip}:13300/cmdb_oper?useSSL=false&serverTimezone=GMT%2B8&useUnicode=true&characterEncoding=utf8
      username: mysql
      password: mysql888
    cmdb-idc-slave:
      jdbc-url: jdbc:mysql://${nickxie-ip}:13301/config15?useSSL=false&serverTimezone=GMT%2B8&useUnicode=true&characterEncoding=latin1
      username: mysql
      password: mysql888
    cmdb-idc-master:
      jdbc-url: jdbc:mysql://${nickxie-ip}:13303/config15?useSSL=false&serverTimezone=GMT%2B8&useUnicode=true&characterEncoding=latin1
      username: mysql
      password: mysql888
    cmdb-idc-log:
      jdbc-url: ********************************************************************************************************************************************************
      username: mysql
      password: mysql888