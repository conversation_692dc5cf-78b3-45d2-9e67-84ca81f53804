spring:
  redis:
    host: ************
    port: 6380
    password: dev@123456
    database: 3
  datasource:
    admin:
      jdbc-url: ******************************************************************************************************************
      username: mysql
      password: mysql888
    cmdb-idc-slave:
      jdbc-url: *******************************************************************************************************************
      username: mysql
      password: mysql888
    cmdb-idc-master:
      jdbc-url: *******************************************************************************************************************************************************
      username: mysql
      password: mysql888
    cmdb-idc-log:
      jdbc-url: ********************************************************************************************************************************************************
      username: mysql
      password: mysql888
