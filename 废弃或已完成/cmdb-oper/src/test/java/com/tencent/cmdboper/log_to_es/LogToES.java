package com.tencent.cmdboper.log_to_es;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.lang.DateUtils;
import org.apache.http.HttpHost;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.xcontent.XContentType;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Date;
import java.util.List;

@SpringBootTest
public class LogToES {

    @Autowired
    @Qualifier("cmdbIdcLogDBHelper")
    private DBHelper dbHelper;

    private static String esServerIp = "************";
    private static Integer esServerPort = 30092;

    private static RestHighLevelClient client = null;

    @Test
    public void test() throws Exception {
        int pageSize = 10000;
        Long lastId = null;
        while(true) {
            List<LogServerDO> list = null;
            if (lastId == null) { // 时间正序查询
                list = dbHelper.getAll(LogServerDO.class, "order by Id limit " + pageSize);
            } else {
                list = dbHelper.getAll(LogServerDO.class, "where Id>? order by Id limit " + pageSize,
                        lastId);
            }

            write("server_log_2021", list);

            if (list.isEmpty()) {
                break;
            }
            lastId = list.get(list.size() - 1).getId();
            System.out.println(DateUtils.format(new Date()) + ", at id:" + lastId);
        }
        System.out.println("done");
    }

    private static void write(String indexName, List<LogServerDO> list) throws Exception {
        final RestHighLevelClient client = getClient(esServerIp, esServerPort); // 线程安全的
        BulkRequest bulk = new BulkRequest(); //创建BulkRequest

        for(LogServerDO logDO : list) {
            IndexRequest request = new IndexRequest(indexName); // 参数是index名称
            request.type("_doc"); // type以后会废弃，都写死为_doc
            request.id(String.valueOf(logDO.getId())); // 文档id，docId
            request.source(JSON.toJson(logDO), XContentType.JSON);

            bulk.add(request);
        }

        client.bulk(bulk, RequestOptions.DEFAULT);
    }

    private static RestHighLevelClient getClient(String ip, int port) {
        if (client != null) {
            return client;
        }
        client = new RestHighLevelClient(
                RestClient.builder(new HttpHost(ip, port, "http")));
        return client;
    }

}
