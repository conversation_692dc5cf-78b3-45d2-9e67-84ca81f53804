package com.tencent.cmdboper;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.model.PageData;
import com.pugwoo.wooutils.json.JSON;
import com.tencent.cmdboper.entity.BusinessDO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
class CmdbOperApplicationTests {

	@Autowired
	private DBHelper dbHelper;

	@Test
	void contextLoads() throws Exception {

		System.out.println(dbHelper);

		PageData<BusinessDO> page = dbHelper.getPage(BusinessDO.class, 1, 10,
				"where id=?", 1325932);

		System.out.println(JSON.toJson(page));

		page = dbHelper.getPage(BusinessDO.class, 1, 10,
				"where chineseName=?", "用户基础[交易记录]".getBytes("gbk"));

		System.out.println(JSON.toJson(page));

	}

}
