package com.tencent.cmdboper.cmdb20210506;

import com.pugwoo.wooutils.collect.ListUtils;
import com.tencent.cmdboper.export_table.QuerySettingModel;
import com.tencent.cmdboper.utils.CmdbUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class GenerateLeftColumn {

    public static void main(String[] args) throws Exception {

        // 查询出cmdb中文名对应的英文变量名称
        Map<String, String> columnCN2EN = new HashMap<>();
        QuerySettingModel.SearchScheme server = CmdbUtils.getSchema("Server");
        for (QuerySettingModel.ResultColumn column : server.getResultColumn()) {
 //           System.out.println(column);
            columnCN2EN.put(column.getName(), column.getId());
        }


        List<String> exist = ListUtils.newArrayList("SvrAssetId","ZoneId","EqsName","SvrSn","IdcName","SvrOwnerAssetId","ModId","AmlId","SvrIsSpecial","SvrTypeId","EqsId","RegionName","HealthStatus","serverProcessId","ImlId","SvrTypeName","serverRack","SfwName","GroupId","serverIPEnabled","outerNetIdcId","serverIdcsId","DeptId","linkNetdeviceId","serverWanIP","SfwId","SvrName","idcCityId","SvrClassifyLevel","idcParentName","outerNetIdc","SzoneName","serverProcess","serverProduct","innerSwitchPort","SvrBakOperator","SvrIp","SvrFirstUseTime","SvrInputTime","BsiUid","PosId","ProductSetId","IdcOperator","DeptName","ProductId","BsiId","RckName","SvrOperator","outerSwitchPort","idcParentId","SvrDeviceClassName","serverLanIP","SfwNameVersion","RaidId","StrVersion","serverPort","v_childId","serverDeviceId","TdtName","SvrId","outerSwitchPort_rt","BsiName","serverAllIP2","ZoneName","allBusiness","SvrDeviceClass","RaidName","serverLogicDomain","v_childAssetId","ClassifyLevelName","svrLogicDomainId","BsiPath","BusinessDeptId","PosCode","SvrHardMemo","SvrChangeTime","innerNetIdc","ProductSetName","serverBusi1","serverAllIPv6","StrMemo","BusinessDeptName","serverBusi2","innerSwitchPort_rt","SzoneId","TdtId","serverProductCode","ImlName","EqsId","v_parentId","ModName","IopName","AmlName","IdcId","BusiDeptName","innerNetIdcId","RegionId","SfwVersion","idcCity","GroupName","StrTdtName","serverAllIP_string","serverAllIP","serverAllIP_cache","idcOperationId","RckId","ModCluster","serverIdcsName","serverBusi1Id","serverBusi2Id","Dept_Business","allBusiness_cache","StrId","StrTdtId","SvrCompactNum","SvrPactTime","serverBorrowEndTime","serverInstallAgent","ClientAttr","ClientName","domainName","serverDisabledIP","adsValidate","ADSCheckDateTime","serverHarddisk","serverRaidInfo","IdcBackupFax","StrDiskType2","StrDiskVolume2","StrDiskNumber2","StrDiskFirmware2","StrRaidPowerModel","StrMiniOsScore","StrDiskModel2","StrDiskMemo2","StrGpuModel","StrGpuNumber","StrGpuFirmware","StrGpuMemo","StrPowerCordNumber","StrPowerCordType","StrGuideType","StrDiskSize2","LockId","LockType","LockStartTime","LockEndTime","LockSystemId","LockOperator","LockKey","LockReason","SvrItCloud","BiosId","SvrRedoTime","SvrSuggestTime","StrBiosType","CloudNode","vip");

        for (Map.Entry<String, String> entry : columnCN2EN.entrySet()) {
            if (exist.contains(entry.getValue())) {
                continue;
            }

            System.out.println("\"服务器\",\"" + entry.getKey() + "\",\"" + entry.getValue() + "\"");
        }


    }



}
