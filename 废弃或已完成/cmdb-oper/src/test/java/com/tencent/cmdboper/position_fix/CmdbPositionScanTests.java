package com.tencent.cmdboper.position_fix;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.tencent.cmdboper.vo.EquipmentPositionVO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * 遍历全表，找出有问题的机位
 */
@SpringBootTest
public class CmdbPositionScanTests {

    @Autowired
    private DBHelper dbHelper;

    @Test
    public void test() {

        // 每次遍历10000台
        Long lastId = null;
        while(true) {
            List<EquipmentPositionVO> all = null;
            if (lastId == null) {
                all = dbHelper.getAll(EquipmentPositionVO.class,
                        "WHERE flag=0 AND configItemId=2 AND PositionId>0 AND id=ParentId "
                                + " ORDER BY id DESC LIMIT 1000");
            } else {
                all = dbHelper.getAll(EquipmentPositionVO.class,
                        "WHERE flag=0 AND configItemId=2 AND PositionId>0 AND id=ParentId and id<?"
                                + " ORDER BY id DESC LIMIT 1000", lastId);
            }
            if (all == null || all.isEmpty()) {
                break;
            }
            lastId = all.get(all.size() - 1).getId();

            handle(all);

            //System.out.println("handled id:" + lastId + "-" + all.get(0).getId());
        }

    }

    private void handle(List<EquipmentPositionVO> all) {
        for (EquipmentPositionVO vo : all) {
            List<Long> allPositionId = getAllPositionId(vo);
            Set<Long> ids = ListUtils.toSet(allPositionId, o -> o);
            if (ids.size() > 1) {
                System.err.println("Found: id=" + vo.getId()); // 红色打印出来
            }
        }
    }

    private List<Long> getAllPositionId(EquipmentPositionVO all) {
        List<Long> result = new ArrayList<>();
        if (all.getChildren() != null) {
            for (EquipmentPositionVO vo : all.getChildren()) {
                result.addAll(getAllPositionId(vo));
            }
        }

        result.add(all.getPositionId());
        return result;
    }

}
