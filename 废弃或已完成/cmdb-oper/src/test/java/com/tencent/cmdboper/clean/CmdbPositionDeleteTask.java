package com.tencent.cmdboper.clean;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.task.ExecuteThem;
import com.tencent.cmdboper.entity.EquipmentPositionDO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Date;
import java.util.List;

/**
 * 清理cmdb服务器机位关系里面的脏数据
 */
@SpringBootTest
public class CmdbPositionDeleteTask {

    @Autowired @Qualifier("cmdbIdcMasterDBHelper")
    private DBHelper dbHelper;
    @Autowired
    private CleanService cleanService;

    @Test
    public void clean() {
        long total = 0;
        while(true) {
            String sql = "SELECT a.* FROM equipment_position a LEFT JOIN t_device b" +
                    " ON a.`EquipmentId`=b.`id` AND a.`ConfigItemId`=2" +
                    " WHERE a.`ConfigItemId`=2 AND b.id IS NULL" +
                    " ORDER BY a.`id` LIMIT 5000";

            List<EquipmentPositionDO> list = dbHelper.getRaw(EquipmentPositionDO.class, sql);

            // 多线程执行
            ExecuteThem executeThem = new ExecuteThem(20);

            for (EquipmentPositionDO one : list) {
                executeThem.add(new Runnable() {
                    @Override
                    public void run() {
                        cleanService.migrateOne(dbHelper, one);
                    }
                });
            }

            executeThem.waitAllTerminate();

            total += 5000;
            System.out.println(DateUtils.format(new Date()) + " clean total:" + total);
        }

    }

}
