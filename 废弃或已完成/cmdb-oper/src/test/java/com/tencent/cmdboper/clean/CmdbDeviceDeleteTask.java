package com.tencent.cmdboper.clean;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.task.ExecuteThem;
import com.tencent.cmdboper.entity.DeviceDO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Calendar;
import java.util.Date;
import java.util.List;

@SpringBootTest
public class CmdbDeviceDeleteTask {

    @Autowired
    @Qualifier("cmdbIdcMasterDBHelper")
    private DBHelper dbHelper;
    @Autowired
    private CleanService cleanService;

    @Test
    public void clean() {

        Date now = new Date();
        Date daysBefore = DateUtils.addTime(now, Calendar.DATE, -30);

        long total = 0;
        while(true) {
            String sql = "select * from t_device where isdel=1 and LastUpdate<? limit 5000";
            List<DeviceDO> list = dbHelper.getRaw(DeviceDO.class, sql, daysBefore);

            // 多线程执行
            ExecuteThem executeThem = new ExecuteThem(30);

            for (DeviceDO one : list) {
                executeThem.add(new Runnable() {
                    @Override
                    public void run() {
                        cleanService.migrateDevice(dbHelper, one);
                    }
                });
            }

            executeThem.waitAllTerminate();

            total += 5000;
            System.out.println(DateUtils.format(new Date()) + " clean total:" + total);
        }

    }


}
