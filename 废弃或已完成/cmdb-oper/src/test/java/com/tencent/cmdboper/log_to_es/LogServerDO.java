package com.tencent.cmdboper.log_to_es;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.Getter;

import java.io.UnsupportedEncodingException;

@Data
@Table("log_server_2021")
public class LogServerDO {

    @Column(value = "Id", isKey = true, isAutoIncrement = true)
    private Long id;

    @Column(value = "Source")
    private String source;

    @Getter(onMethod = @__( @JsonIgnore))
    @Column(value = "Reason")
    private byte[] _reason;

    public String getReason() {
        if(_reason == null) {
            return null;
        }
        try {
            return new String(_reason, "gbk");
        } catch (UnsupportedEncodingException e) {
            return null;
        }
    }

    @Column(value = "Operator")
    private String operator;

    @Column(value = "AccessLogId")
    private String accessLogId;

    @Column(value = "OperateType")
    private String operateType;

    @Column(value = "LogTime")
    private Long logTime;

    @Column(value = "HostID", isKey = true)
    private Integer hostID;

    @Column(value = "HostTypeId")
    private Boolean hostTypeId;

    @Column(value = "KeyDesc")
    private String keyDesc;

    @Getter(onMethod = @__( @JsonIgnore))
    @Column(value = "Content")
    private byte[] _content;

    public String getContent() {
        if(_content == null) {
            return null;
        }
        try {
            return new String(_content, "gbk");
        } catch (UnsupportedEncodingException e) {
            return null;
        }
    }

    /** �~D¬�~H�~O�~Nª�~Wª»»0£¬�~Wª»»ºó�~Nª1<br/>Column: [Converted] */
    @Column(value = "Converted")
    private Boolean converted;

    @Getter(onMethod = @__( @JsonIgnore))
    @Column(value = "ConvertContent")
    private byte[] _convertContent;

    public String getConvertContent() {
        if(_convertContent == null) {
            return null;
        }
        try {
            return new String(_convertContent, "gbk");
        } catch (UnsupportedEncodingException e) {
            return null;
        }
    }

    @Column(value = "CITypeId")
    private Integer cITypeId;

    @Column(value = "CIId")
    private Integer cIId;



}
