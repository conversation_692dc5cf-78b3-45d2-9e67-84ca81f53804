package com.tencent.cmdboper.position_fix;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.io.IOUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import com.pugwoo.wooutils.string.StringTools;
import com.tencent.cmdboper.vo.EquipmentPositionWithDeviceVO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.InputStream;
import java.util.List;
import java.util.Objects;

/**
 * 导出有问题的机位信息，给业务方确认
 */
@SpringBootTest
public class CmdbPositionExportTests {

    @Autowired
    private DBHelper dbHelper;

    @Test
    public void export() throws Exception {
        InputStream in = this.getClass().getResourceAsStream("/pos-not-match-equipment_position-ids.txt");
        String content = IOUtils.readAllAndClose(in, "utf-8");

        String[] lines = StringTools.splitLines(content);
        for (String line : lines) {
            if (StringTools.isBlank(line)) {
                continue;
            }

            Long id = NumberUtils.parseLong(line);
            if (id == null) {
                continue;
            }

            EquipmentPositionWithDeviceVO one = dbHelper.getOne(EquipmentPositionWithDeviceVO.class,
                    "where id=?", id);
            if (one == null) {
                System.err.println("id:" + id + " not exist.");
            }

            // 检查一下有没有4层的
            List<EquipmentPositionWithDeviceVO> children = one.getChildren();
            if (children != null && !children.isEmpty()) {
                for (EquipmentPositionWithDeviceVO child : children) {
                    List<EquipmentPositionWithDeviceVO> children2 = child.getChildren();
                    if (children2 != null && !children2.isEmpty()) {
                        for (EquipmentPositionWithDeviceVO child2 : children2) {
                            if (!Objects.equals(one.getPositionId(), child.getPositionId())
                                || !Objects.equals(child.getPositionId(), child2.getPositionId())) {
                                System.out.println(one.getDeviceDO().getAssetid() + "," + one.getPositionId()
                                        + "," + child.getDeviceDO().getAssetid() + "," + child.getPositionId()
                                        + "," + child2.getDeviceDO().getAssetid() + "," + child2.getPositionId());
                            }
                        }
                    } else {
                        if (!Objects.equals(one.getPositionId(), child.getPositionId())) {
                            System.out.println("" + one.getDeviceDO().getAssetid() + "," + one.getPositionId()
                                + "," + child.getDeviceDO().getAssetid() + "," + child.getPositionId());
                        }
                    }
                }
            } else {
                System.err.println("id:" + id + "'s children is null/empty");
            }


        }
    }

}
