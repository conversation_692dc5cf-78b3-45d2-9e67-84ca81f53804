package com.tencent.cmdboper.scan;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;

@Data
@Table("log_server_2020")
public class LogServerDO {

    @Column(value = "Id", isKey = true, isAutoIncrement = true)
    private Long id;

    @Column(value = "Source")
    private String source;

    @Column(value = "Operator")
    private String operator;

    @Column(value = "OperateType")
    private String operateType;

    @Column(value = "LogTime")
    private Long logTime;

    @Column(value = "HostID", isKey = true)
    private Integer hostID;

    @Column(value = "HostTypeId")
    private Boolean hostTypeId;

    @Column(value = "KeyDesc")
    private String keyDesc;

    @Column(value = "Content")
    private String content;

    @Column(value = "CITypeId")
    private Integer cITypeId;

    @Column(value = "CIId")
    private Integer cIId;

}
