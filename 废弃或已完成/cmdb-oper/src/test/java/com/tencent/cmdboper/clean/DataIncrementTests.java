package com.tencent.cmdboper.clean;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import com.tencent.cmdboper.common.entity.TableCountDO;
import lombok.Data;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

@SpringBootTest
public class DataIncrementTests {

    @Autowired
    @Qualifier("adminDBHelper")
    private DBHelper adminDBHelper;

    @Data
    private static class StatDTO {
        private String tableName;
        private Long increment;
    }

    /**
     * 统计数据增量比较大的表
     */
    @Test
    public void stat() {

        List<TableCountDO> tables = adminDBHelper.getAll(TableCountDO.class, "group by table_name");

        // 最近2个月增量
        Date today = DateUtils.parse(DateUtils.formatDate(new Date()));
        Date twoMonthBefore = DateUtils.addTime(today, Calendar.MONTH, -2);


        List<StatDTO> result = new ArrayList<>();

        for (TableCountDO table : tables) {
            TableCountDO t1 = adminDBHelper.getOne(TableCountDO.class, "where table_name=? and date=?",
                    table.getTableName(), today);
            TableCountDO t2 = adminDBHelper.getOne(TableCountDO.class, "where table_name=? and date=?",
                    table.getTableName(), twoMonthBefore);

            if (t2 != null && t1 != null) {
                StatDTO statDTO = new StatDTO();
                result.add(statDTO);
                statDTO.setTableName(table.getTableName());
                statDTO.setIncrement(t1.getCount() - t2.getCount());
            }
        }

        ListUtils.sortDescNullLast(result, o -> o.getIncrement());

        for (StatDTO statDTO : result) {
            System.out.println(statDTO.getTableName() + ":" + statDTO.getIncrement());
        }
    }

}
