package com.tencent.cmdboper.export_table;

import com.pugwoo.wooutils.json.JSON;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.jdbc.core.JdbcTemplate;

import java.io.BufferedReader;
import java.io.FileWriter;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@SpringBootTest
public class ExportSchemaAndTable {

    @Autowired @Qualifier("cmdbIdcSlaveJdbcTemplate")
    private JdbcTemplate jdbcTemplate;

    @Test
    public void export() throws Exception {
        exportTableDataToCsv();
    }





    private QuerySettingModel parseQuerySetting() throws Exception {
        InputStream in = ExportSchemaAndTable.class.getClass().getResourceAsStream("/querysetting.py");

        // 去掉 # 开头的列
        BufferedReader br = new BufferedReader(new InputStreamReader(in, "UTF-8"));
        String line;
        StringBuilder sb = new StringBuilder();
        while((line = br.readLine()) != null) {
            if (line.trim().startsWith("#")) {
                continue;
            }
            sb.append(line).append("\n");
        }

        String json = sb.toString();
        json = json.replace("True", "true");
        json = json.replace("False", "false");
        json = json.replace(":u\"", ":\"");
        json = json.replace(": u\"", ":\"");
        json = json.replace("'''", "\"");

        QuerySettingModel querySettingModel = JSON.parse(json, QuerySettingModel.class);
        return querySettingModel;
    }

    private List<String> getTables() throws Exception {
        List<String> tableNames = new ArrayList<>();

        Connection conn = jdbcTemplate.getDataSource().getConnection();
        DatabaseMetaData metaData = conn.getMetaData();
        ResultSet rs = metaData.getTables(null, null,
                null, new String[]{"TABLE"});
        while(rs.next()) {
            tableNames.add(rs.getString("TABLE_NAME"));
        }

        conn.close();

        return tableNames;
    }

    // 导出表列到csv文件
    private void exportTableColumnToCsv() throws Exception {
        FileWriter fileWriter = new FileWriter("d:/table_columns.csv");
        fileWriter.write("表名,列名,说明\n");

        Connection conn = jdbcTemplate.getDataSource().getConnection();
        DatabaseMetaData metaData = conn.getMetaData();

        List<String> tables = getTables();
        for (String table : tables) {
            ResultSet columns = metaData.getColumns(null,null, table, null);
            while(columns.next()) {
                String columnName = columns.getString("COLUMN_NAME");
                String remarks = columns.getString("REMARKS");
                String remarks2 = new String(remarks.getBytes("latin1"), "gbk");

                // System.out.println(table + "." + columnName + ":" + remarks);

                fileWriter.write(table);
                fileWriter.write(",");
                fileWriter.write(columnName);
                fileWriter.write(",");
                fileWriter.write(remarks2);
                fileWriter.write("\n");
            }
        }

        fileWriter.close();
    }

    // 导出资产 -> 表的关系 到 csv文件
    private void exportAssetToTableToCsv() throws Exception {
        FileWriter fileWriter = new FileWriter("d:/asset_to_tables.csv");
        fileWriter.write("资产,相关表\n");

        QuerySettingModel querySettingModel = parseQuerySetting();
        List<QuerySettingModel.SearchScheme> schemes = querySettingModel.getSearchScheme();

        for (QuerySettingModel.SearchScheme scheme : schemes) {
            fileWriter.write(scheme.getName());
            List<QuerySettingModel.Join> joins = scheme.getJoin();

            fileWriter.write(",");

            boolean isFirst = true;
            for (QuerySettingModel.Join join : joins) {
                if (!isFirst) {
                    fileWriter.write(";");
                }
                isFirst = false;

                String joinTable = join.getJoinTable();
                String[] strs = joinTable.trim().split(" ");
                joinTable = strs[0].trim();

                fileWriter.write(joinTable);
            }

            fileWriter.write("\n");
        }

        fileWriter.close();
    }

    // 导出表的前10条记录
    private void exportTableDataToCsv() throws Exception {
        FileWriter fileWriter = new FileWriter("d:/table_data.csv");

        Connection conn = jdbcTemplate.getDataSource().getConnection();
        DatabaseMetaData metaData = conn.getMetaData();

        List<String> tables = getTables();

        for (String table : tables) {
            fileWriter.write("表名:");
            fileWriter.write(table);
            fileWriter.write("\n");

            ResultSet columns = metaData.getColumns(null,null, table, null);
            List<String> columnNames = new ArrayList<>();
            boolean isFirst = true;
            while(columns.next()) {
                String columnName = columns.getString("COLUMN_NAME");
                columnNames.add(columnName);

                if (!isFirst) {
                    fileWriter.write(",");
                }
                fileWriter.write(columnName);

                isFirst = false;
            }
            fileWriter.write("\n");

            // 查出前10条记录，这里要处理好乱码问题
            StringBuilder sql = new StringBuilder("SELECT ");
            for (int i = 0; i < columnNames.size(); i++) {
                if (i > 0) {
                    sql.append(",");
                }
                String columnName = columnNames.get(i).trim();
                sql.append("CONVERT(CAST(CONVERT(`" + columnName
                        + "` USING latin1) AS BINARY) USING gbk) AS `" + columnName + "`");
            }
            sql.append(" FROM ").append(table).append(" limit 10");

            List<Map<String, Object>> datas = jdbcTemplate.queryForList(sql.toString());
            for (Map<String, Object> data : datas) {
                for (int i = 0; i < columnNames.size(); i++) {
                    Object value = data.get(columnNames.get(i));
                    String v = value == null ? "" : value.toString();
                    v = v.replace("\n", "").replace("\r", "");
                    fileWriter.write(v);
                    if (i > 0) {
                        fileWriter.write(",");
                    }
                }
                fileWriter.write("\n");
            }

            fileWriter.write("\n---------------------------------------------------------------\n");
        }
    }
}
