package com.tencent.cmdboper.cmdb20210506;

import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.io.IOUtils;
import com.pugwoo.wooutils.string.StringTools;
import com.tencent.cmdboper.export_table.QuerySettingModel;
import com.tencent.cmdboper.utils.CmdbUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.File;
import java.io.FileInputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class GenerateColumn2 {

    @AllArgsConstructor
    @EqualsAndHashCode
    @Data
    public static class SchemaColumn {
        private String schema;
        private String column;
    }

    private static String getEnName(String schemaCNName, String columnName) throws Exception {
        List<QuerySettingModel.SearchScheme> schemas = CmdbUtils.getSchemaByCNName(schemaCNName);
        for (QuerySettingModel.SearchScheme schema : schemas) {
            for (QuerySettingModel.ResultColumn column : schema.getResultColumn()) {
                if (column.getName().equals(columnName)) {
                    return column.getId();
                }
            }
        }

        return null;
    }

    @AllArgsConstructor
    @Data
    public static class SchemaStatistic {
        private int used; // 业务使用
        private int unknown;  // 未知
    }

    public static void main(String[] args) throws Exception {

        Map<SchemaColumn, Set<String>> columnToSystem = new HashMap<>();
        Map<String, SchemaStatistic> schemaStatistic = new HashMap<>();

        List<String> exclude = ListUtils.newArrayList("服务器", "进程端口", "业务",
                "物理机房", "机房管理单元", "机架", "机位列表");

        // 处理已知的系统的列，我还是用程序来弄吧
        String body = IOUtils.readAllAndClose(new FileInputStream(new File("d:/cmdbcolumn.csv")), "gbk");
        String[] lines = StringTools.splitLines(body);
        for (int i = 1; i < lines.length; i++) { // 去掉标题
            if (StringTools.isBlank(lines[i])) {
                continue;
            }

            String strs[] = lines[i].split(",");
            if (strs.length < 3) {
                System.err.println("########### 字段解析异常 #####################" + lines[i]);
            }

            // 排除掉已经整理的字段
            if (exclude.contains(strs[1])) {
                continue;
            }
            SchemaColumn column = new SchemaColumn(strs[1], strs[2]);

            if (columnToSystem.containsKey(column)) {
                columnToSystem.get(column).add(strs[0]);
            } else {
                columnToSystem.put(column, new HashSet<>());
                columnToSystem.get(column).add(strs[0]);
            }

        }

        List<Map.Entry<SchemaColumn, Set<String>>> list = new ArrayList<>();
        for (Map.Entry<SchemaColumn, Set<String>> entry : columnToSystem.entrySet()) {
            list.add(entry);
        }

        ListUtils.sortAscNullLast(list, o -> o.getKey().getSchema());

        Set<String> allUsedSchema = new HashSet<>(); // 所有用到的schema
        allUsedSchema.addAll(exclude);
        for (Map.Entry<SchemaColumn, Set<String>> entry : list) {
            allUsedSchema.add(entry.getKey().getSchema());
        }

        String lastSchema = "";
        Set<String> usedColumns = new HashSet<>();

        // list里面的是业务在用的
        for (Map.Entry<SchemaColumn, Set<String>> entry : list) {

            if (lastSchema.length() == 0) {
                lastSchema = entry.getKey().getSchema();
            }
            usedColumns.add(entry.getKey().getColumn());

            // 将相同的业务中没有用到的字段列举出来
            if (!lastSchema.equals(entry.getKey().getSchema())) { // schema发生变化
                List<QuerySettingModel.ResultColumn> unusedColumnByCNName = CmdbUtils.getUnusedColumnByCNName(lastSchema, usedColumns);

                for (QuerySettingModel.ResultColumn column : unusedColumnByCNName) {
                    System.out.println("\"" + lastSchema + "\"," +
                            "\"" + column.getName() + "\",\""
                            + getEnName(lastSchema, column.getName())
                            + "\",\"未知\",\"\"");
                }

                // 统计一下已使用和未使用的量
                schemaStatistic.put(lastSchema, new SchemaStatistic(usedColumns.size(), unusedColumnByCNName.size()));

                // reset
                lastSchema = entry.getKey().getSchema();
                usedColumns = new HashSet<>();
            }

            System.out.println("\"" + entry.getKey().getSchema() + "\"," +
                    "\"" + entry.getKey().getColumn() + "\",\""
                    + getEnName(entry.getKey().getSchema(), entry.getKey().getColumn())
                    + "\",\"业务在用\",\"" + StringTools.join(setToList(entry.getValue()),",") + "\"");

        }

        // 跳出来后再对最后的一次schema进行处理
        {
            List<QuerySettingModel.ResultColumn> unusedColumnByCNName = CmdbUtils.getUnusedColumnByCNName(lastSchema, usedColumns);

            for (QuerySettingModel.ResultColumn column : unusedColumnByCNName) {
                System.out.println("\"" + lastSchema + "\"," +
                        "\"" + column.getName() + "\",\""
                        + getEnName(lastSchema, column.getName())
                        + "\",\"未知\",\"\"");
            }
            // reset
            lastSchema = "";
            usedColumns = new HashSet<>();
        }

        // 最后再把所有剩下的schema打印出来，这些是都是未知的
        List<QuerySettingModel.SearchScheme> schemas = CmdbUtils.getSchemas();
        for (QuerySettingModel.SearchScheme schema : schemas) {
            // 如果已经存在过了，就不再打印了
            if (allUsedSchema.contains(schema.getName())) {
                continue;
            }

            // 打印出来并统计上
            List<QuerySettingModel.ResultColumn> columns = schema.getResultColumn();
            for (QuerySettingModel.ResultColumn column : columns) {
                System.out.println("\"" + schema.getName() + "\"," +
                        "\"" + column.getName() + "\",\""
                        + column.getId()
                        + "\",\"未知\",\"\"");
            }

            schemaStatistic.put(schema.getName(), new SchemaStatistic(0, columns.size()));
        }

        for (Map.Entry<String, SchemaStatistic> entry : schemaStatistic.entrySet()) {
            System.out.println(entry.getKey() + "," + entry.getValue().getUsed()
                    + "," + entry.getValue().getUnknown()
                    + "," + (entry.getValue().getUsed()+entry.getValue().getUnknown())
                    + ",\"" + percent(entry.getValue().getUsed(), entry.getValue().getUnknown())+ "\"");
        }
    }

    private static List<String> setToList(Set<String> list) {
        return ListUtils.newArrayList(list.toArray(new String[0]));
    }

    private static String percent(int used, int unknown) {
        if (used == 0) {
            return "0%";
        }
        if (unknown == 0) {
            return "0%";
        }
        return ((int)(used*100.0/(used+unknown))) + "%";
    }
}
