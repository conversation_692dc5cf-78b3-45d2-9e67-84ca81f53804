package com.tencent.cmdboper.cmdb20210506;

import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.io.IOUtils;
import com.pugwoo.wooutils.string.StringTools;
import com.tencent.cmdboper.export_table.QuerySettingModel;
import com.tencent.cmdboper.utils.CmdbUtils;

import java.io.File;
import java.io.FileInputStream;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class GenerateColumn {

    public static void main(String[] args) throws Exception {

        // 查询出cmdb中文名对应的英文变量名称
        Map<String, String> columnCN2EN = new HashMap<>();
        QuerySettingModel.SearchScheme server = CmdbUtils.getSchema("Server");
        for (QuerySettingModel.ResultColumn column : server.getResultColumn()) {
 //           System.out.println(column);
            columnCN2EN.put(column.getName(), column.getId());
        }


        Map<String, Set<String>> columnToSystem = new HashMap<>();

        // 处理已知的系统的列，我还是用程序来弄吧
        String body = IOUtils.readAllAndClose(new FileInputStream(new File("d:/cmdbcolumn.csv")), "gbk");
        String[] lines = StringTools.splitLines(body);
        for (int i = 1; i < lines.length; i++) { // 去掉标题
            if (StringTools.isBlank(lines[i])) {
                continue;
            }

            String strs[] = lines[i].split(",");
            if (strs.length < 3) {
                System.err.println("########### 字段解析异常 #####################" + lines[i]);
            }
            if ("服务器".equals(strs[1])) {
                if (columnToSystem.containsKey(strs[2])) {
                    columnToSystem.get(strs[2]).add(strs[0]);
                } else {
                    columnToSystem.put(strs[2], new HashSet<>());
                    columnToSystem.get(strs[2]).add(strs[0]);
                }
            }
        }

        for (Map.Entry<String, Set<String>> entry : columnToSystem.entrySet()) {
            System.out.println("\"" + entry.getKey() + "\",\"" + columnCN2EN.get(entry.getKey())
                    + "\",\"" + StringTools.join(setToList(entry.getValue()),",") + "\"");
        }
    }

    private static List<String> setToList(Set<String> list) {
        return ListUtils.newArrayList(list.toArray(new String[0]));
    }

}
