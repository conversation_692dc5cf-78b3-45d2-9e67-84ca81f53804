package com.tencent.cmdboper.scan;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.lang.DateUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Date;
import java.util.List;

@SpringBootTest
public class SearchAssetId {

    @Autowired @Qualifier("cmdbIdcLogDBHelper")
    private DBHelper dbHelper;

    @Test
    public void test() throws Exception {

        List<String> assetIds = ListUtils.newArrayList("TC200527030520");

        Long lastId = null;
        while(true) {
            List<LogServerDO> list = null;
            if (lastId == null) { // 时间逆序查询
                list = dbHelper.getAll(LogServerDO.class, "order by Id desc limit 100000");
            } else {
                list = dbHelper.getAll(LogServerDO.class, "where Id<? order by Id desc limit 100000",
                        lastId);
            }

            for (LogServerDO logServerDO : list) {
                for (String assetId : assetIds) {
                    if (logServerDO.getContent() != null && logServerDO.getContent().contains(assetId)) {
                        System.out.println("====FOUND====" + JSON.toJson(logServerDO));
                    }
                }
            }

            if (list.isEmpty()) {
                break;
            }
            lastId = list.get(list.size() - 1).getId();
            System.out.println(DateUtils.format(new Date()) + ", searched at id:" + lastId);
        }

    }

}
