package com.tencent.cmdboper.clean;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.impl.SpringJdbcDBHelper;
import com.tencent.cmdboper.entity.DeviceDO;
import com.tencent.cmdboper.entity.EquipmentPositionDO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class CleanService {

    /**抽取出来是为了事务*/
    @Transactional("cmdbIdcMasterTransactionManager")
    public void migrateOne(DBHelper dbHelper, EquipmentPositionDO ep) {
        dbHelper.setTableName(EquipmentPositionDO.class, "equipment_position_del");
        dbHelper.insert(ep);
        dbHelper.setTableName(EquipmentPositionDO.class, "equipment_position");

        dbHelper.delete(EquipmentPositionDO.class, "where id=?", ep.getId());
    }

    /**抽取出来是为了事务*/
    @Transactional("cmdbIdcMasterTransactionManager")
    public void migrateDevice(DBHelper dbHelper, DeviceDO deviceDO) {
        // 不要检查外键
        ((SpringJdbcDBHelper) dbHelper).getJdbcTemplate().execute("SET FOREIGN_KEY_CHECKS=0");

        dbHelper.setTableName(DeviceDO.class, "t_device_del");
        dbHelper.insert(deviceDO);
        dbHelper.setTableName(DeviceDO.class, "t_device");

        dbHelper.delete(DeviceDO.class, "where id=?", deviceDO.getId());
    }
}
