package com.tencent.cmdboper.biz2neo4j;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.MapUtils;
import com.tencent.cmdboper.entity.BusinessDO;
import org.junit.jupiter.api.Test;
import org.neo4j.driver.AuthTokens;
import org.neo4j.driver.Driver;
import org.neo4j.driver.GraphDatabase;
import org.neo4j.driver.Result;
import org.neo4j.driver.Session;
import org.neo4j.driver.Transaction;
import org.neo4j.driver.TransactionWork;
import org.neo4j.driver.Value;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;
import java.util.Map;

@SpringBootTest
public class BizToNeo4j {

    @Autowired
    @Qualifier("cmdbIdcSlaveDBHelper")
    private DBHelper dbHelper;

    private Driver driver;

    @Test
    public void test() {
        // 遍历完全部的数据
        long start = System.currentTimeMillis();
        List<BusinessVO> list = dbHelper.getAll(BusinessVO.class, "where flag=1 and depth=1");
        long end = System.currentTimeMillis();

        // 遍历一棵树并存入到neo4j
        for (BusinessVO businessVO : list) {
            exec("CREATE (b:Business {id:$id, uid: $uid, parentUid: $parentUid, name: $chineseName, depth:1}) return b",
                    MapUtils.of("id", businessVO.getId(),
                            "uid", businessVO.getUid(),
                            "parentUid", businessVO.getParentUid(),
                            "chineseName", businessVO.getChineseNameGbk()));

            List<BusinessEndVO> children = businessVO.getChildren();
            for (BusinessEndVO endVO : children) {
                exec("CREATE (b:Business {id:$id, uid: $uid, parentUid: $parentUid, name: $chineseName, depth:2}) return b",
                        MapUtils.of("id", endVO.getId(),
                                "uid", endVO.getUid(),
                                "parentUid", endVO.getParentUid(),
                                "chineseName", endVO.getChineseNameGbk()));

                exec("match (n: Business{uid:$uid}),(m:Business{uid:$childrenUid}) create (n)-[r:Has]->(m) return r",
                        MapUtils.of("uid", businessVO.getUid(), "childrenUid", endVO.getUid())
                );

                List<BusinessDO> children2 = endVO.getChildren();
                for (BusinessDO biz3 : children2) {
                    exec("CREATE (b:Business {id:$id, uid: $uid, parentUid: $parentUid, name: $chineseName, depth:3}) return b",
                            MapUtils.of("id", biz3.getId(),
                                    "uid", biz3.getUid(),
                                    "parentUid", biz3.getParentUid(),
                                    "chineseName", biz3.getChineseNameGbk()));

                    exec("match (n: Business{uid:$uid}),(m:Business{uid:$childrenUid}) create (n)-[r:Has]->(m) return r",
                            MapUtils.of("uid", endVO.getUid(), "childrenUid", biz3.getUid())
                    );
                }
            }
        }

        System.out.println("cost:" + (end - start) + "ms");
    }

    private void exec(String sql, Map<String,Object> parameters) {
        Driver driver =getDriver();
        try (Session session = driver.session())
        {
            // neo4j支持事务，所有操作要么一次完成，要么都不执行
            session.writeTransaction(new TransactionWork<Value>() {
                @Override
                public Value execute(Transaction tx) {
                    Result result = tx.run(sql, parameters );
                    return result.single().get(0);
                }
            } );
        }
    }

    private Driver getDriver() {
        if (driver != null) {
            return driver;
        }
        String uri = "bolt://9.135.116.79:7687";
        String username = "neo4j";
        String password = "password";

        driver = GraphDatabase.driver(uri, AuthTokens.basic(username, password));
        return driver;
    }
}
