package com.tencent.cmdboper.scan_cmdb_not_use_column;

import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.lang.NumberUtils;
import com.pugwoo.wooutils.net.Browser;
import com.pugwoo.wooutils.net.HttpResponse;
import com.pugwoo.wooutils.string.StringTools;
import com.tencent.cmdboper.common.cmdbclient.CmdbQueryRawResp;
import com.tencent.cmdboper.export_table.QuerySettingModel;
import com.tencent.cmdboper.utils.CmdbUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.BufferedReader;
import java.io.FileReader;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Slf4j
@SpringBootTest
public class ScanCmdbNotUseColumn {

    public static void main(String[] args) throws Exception {

        List<String> skip = ListUtils.newArrayList("SvrCompactNum","SvrPactTime","serverBorrowEndTime","serverInstallAgent","ClientAttr","ClientName","domainName","SvrId","serverCICode","SvrAssetId","serverDeviceId","SvrName","SvrOperator","SvrBakOperator","SvrBackUp","SvrSn","SvrIsSpecial","SvrMemo","SvrDeviceClass","SvrDeviceClassName","SvrHardMemo","SvrInputTime","SvrFirstUseTime","SvrServiceType","SvrTypeId","safeFlag","SvrTypeName","SvrClassifyLevel","ClassifyLevelName","DeptId","DeptName","GroupId","GroupName","TdtId","TdtName","serverOutOfBand","serverHeight","SvrElectricity","EqsId","EqsName","RaidId","RaidName","AmlId","AmlName","AmlNameMemo","ImlId","SvrMaintMode","ImlName","SvrIp","serverAllIP_string","serverIPEnabled","SfwId","SfwNameVersion","serverProcessId","serverProcess","serverAllIP","serverAllIP2","serverAllIP_cache","serverAllIPv6");

        QuerySettingModel querySettingModel = CmdbUtils.parseQuerySetting();

        // System.out.println(querySettingModel);

        List<QuerySettingModel.SearchScheme> searchScheme = querySettingModel.getSearchScheme();
        QuerySettingModel.SearchScheme serverSchema = null;
        for (QuerySettingModel.SearchScheme schema : searchScheme) {
            if ("Server".equals(schema.getId())) {
                serverSchema = schema;
            }
        }
        if (serverSchema == null) {
            System.out.println("服务器Schema配置不存在");
            return;
        }

        // 加载全部的服务器ID，一共有300多万
        List<List<Long>> deviceId = loadDeviceId();

        List<String> notUsedColumn = new ArrayList<>();

        List<QuerySettingModel.ResultColumn> resultColumn = serverSchema.getResultColumn();
        for (QuerySettingModel.ResultColumn column : resultColumn) {
            if (skip.contains(column.getId())) { // 已经算过的
                continue;
            }
            //System.out.println("current column:" + column.getId());

            // 按id逐个排查
            int total = 0;
            Set<String> currentValues = new HashSet<>();
            for (List<Long> ids : deviceId) {
                System.out.println("current process, column:" + column.getId() + ",at " + total);
                total += ids.size();

                String idsStr = StringTools.join(ids, ";");

                String body = "{\"params\":{\"content\":{\n" +
                        "\"schemeId\":\"Server\",\"type\":\"Json\",\"version\":\"1.0\",\"dataFormat\":\"list\",\n" +
                        "\"requestInfo\":{\"systemId\":\"200911151\",\"sceneId\":\"1\",\"requestModule\":\"\",\"operator\":\"\"},\n" +
                        "\"resultColumn\":{ \"" + column.getId() + "\":\"\"},\n" +
                        "\"pagingInfo\":{\"startIndex\":\"0\",\"pageSize\":\"20\",\"returnTotalRows\":\"0\"},\n" +
                        "\"orderBy\":\"\",\n" +
                        "\"conditionLogical\":\"\",\n" +
                        "\"searchCondition\":{ \"SvrId\":\"" + idsStr + "\"}}}}";

                Browser browser = new Browser();
                browser.disableGzip();
                HttpResponse httpresp = browser.post("http://api.cmdb.oa.com/api/query/get", body.getBytes());

                CmdbQueryRawResp rawResp = JSON.parse(httpresp.getContentString(), CmdbQueryRawResp.class);

                // System.out.println(rawResp);

                int index = -1;
                List<CmdbQueryRawResp.FieldDef> fieldDef = rawResp.getDataSet().getFieldDef();
                for (int i = 0; i < fieldDef.size(); i++) {
                    CmdbQueryRawResp.FieldDef def = fieldDef.get(i);
                    if (column.getId().equals(def.getId())) {
                        index = i;
                    }
                }

                if (index < 0) {
                    System.err.println("index <0");
                }

                List<List<Object>> datas = rawResp.getDataSet().getData();
                for (List<Object> data : datas) {
                    Object obj = data.get(index);
                    if (obj == null) {
                        continue;
                    }
                    String objStr = obj.toString().trim();
                    if (StringTools.isBlank(objStr)) {
                        continue;
                    }

                    currentValues.add(objStr);

                    if (currentValues.size() > 1) {
                        break;
                    }
                }

                if (currentValues.size() > 1) {
                    break;
                }
            }

            System.out.println("column:" + column.getId() + ",values:" + currentValues.size());

        }

    }

    // 按10个一份来读取
    private static List<List<Long>> loadDeviceId() throws Exception {

        List<List<Long>> result = new ArrayList<>();

        List<Long> ids = new ArrayList<>();

        BufferedReader br = null;
        try {
            br = new BufferedReader(new FileReader("d:/serverid.csv"));
            String line = null;
            while((line = br.readLine()) != null) {
                Long aLong = NumberUtils.parseLong(line);
                if (aLong != null) {
                    ids.add(aLong);
                }

                if (ids.size() == 100000) {
                    result.add(ids);
                    ids = new ArrayList<>();
                }
            }

            if (!ids.isEmpty()) {
                result.add(ids);
            }
        } catch (Exception e) {
            log.error("read file fail", e);
        }

        return result;
    }


}
