package com.pugwoo.dboperate.archived.mck.product_inventory;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import jakarta.annotation.Resource;
import java.util.List;

@SpringBootTest
public class AppidIndustryTests {

    @Resource
    private DBHelper ckCloudDemandNewIdcDBHelper;
    @Resource
    private DBHelper cloudDemandDevDBHelper;

    @Test
    public void writeAppid() {
       // cloudDemandDevDBHelper.executeRaw("truncate table tmp_appid_industry");
        List<Long> appid = ckCloudDemandNewIdcDBHelper.getRaw(Long.class, "select distinct appid from daily_ka_appid_zoneid_ginstype");

        List<Long> exist = cloudDemandDevDBHelper.getRaw(Long.class, "select distinct appid from tmp_appid_industry");

        List<Long> toInsert = ListUtils.subtract(appid, exist);

        List<TmpAppidIndustryDO> result = ListUtils.transform(toInsert, o -> {
            TmpAppidIndustryDO d = new TmpAppidIndustryDO();
            d.setAppid(o);
            d.setIndustry("");
            d.setWarZone("");
            d.setIndustryDept("");
            return d;
        });
        cloudDemandDevDBHelper.insertBatchWithoutReturnId(result);
    }

}
