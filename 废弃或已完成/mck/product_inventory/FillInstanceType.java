package com.pugwoo.dboperate.archived.mck.product_inventory;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.string.StringTools;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import jakarta.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@SpringBootTest
public class FillInstanceType {

    @Resource
    private DBHelper mckIdcDBHelper;
    @Resource
    private DBHelper jxcTxyIdcDBHelper;

    @Test
    public void fill() {
        long start = System.currentTimeMillis();

        Map<String, String> infoByDeviceType = getInfoByDeviceType();

        List<ProductInventoryDO> all =
                mckIdcDBHelper.getAll(ProductInventoryDO.class, "where device_type!=''");

        // 每1000个进行一次操作，这里不要事务，不然70万行会有大事务
        List<List<ProductInventoryDO>> lists = ListUtils.groupByNum(all, 1000);
        int total = 0;
        for (List<ProductInventoryDO> list : lists) {
            ListUtils.forEach(list, o -> {
                String instanceFamily = infoByDeviceType.get(o.getDeviceType());
                if (instanceFamily == null) {
                    instanceFamily = "";
                }
                mckIdcDBHelper.updateCustom(o, "device_type_to_instance_family=?", instanceFamily);
            });
            total += list.size();
            System.out.println("total done:" + total);
        }

        long end = System.currentTimeMillis();
        System.out.println("cost:" + (end - start) + "ms");
        System.out.println(all.size());
    }

    public Map<String, String> getInfoByDeviceType() {
        List<StaticCvmtypeDO> all = jxcTxyIdcDBHelper.getAll(StaticCvmtypeDO.class);
        if (ListUtils.isEmpty(all)) {
            return new HashMap<>();
        }
        Map<String, String> cvm2GinsFamily = ListUtils.toMap(all, o -> o.getCvmtype(), o -> o.getGinsfamily());

        //  取字典表中stdType和ginsFamily均不为空的记录
        List<StaticCvmtypeDO> filtered = all.stream().filter(o -> StringTools.isNotBlank(o.getStdtype())
                && StringTools.isNotBlank(o.getGinsfamily())).collect(Collectors.toList());

        //  stdType ： cvmType 为一对多
        Map<String, String> std2GinsFamily = ListUtils.toMap(filtered, o -> o.getStdtype(), o -> o.getGinsfamily());

        //  stdTypeMap遍历
        //  若发现cvmTypeMap中的k存在，但v为空 或 k不存在时，将它put到cvmTypeMap中
        for (Map.Entry<String, String> entry : std2GinsFamily.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();
            String cvmTypeValue = cvm2GinsFamily.get(key);
            if (StringTools.isBlank(cvmTypeValue)) {
                cvm2GinsFamily.put(key, value);
            }
        }
        return cvm2GinsFamily;
    }
}
