package com.pugwoo.dboperate.archived.mck.product_inventory;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDate;

/**
 * 产品库存
 */
@Data
@ToString
@Table("product_inventory3")
public class ProductInventoryDO {

    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    /** 数据日期<br/>Column: [stat_time] */
    @Column(value = "stat_time")
    private LocalDate statTime;

    /** 产品类型<br/>Column: [product_type] */
    @Column(value = "product_type")
    private String productType;

    /** 产品子类型<br/>Column: [sub_product_type] */
    @Column(value = "sub_product_type", insertValueScript = "''")
    private String subProductType;

    /** 物理机机型<br/>Column: [device_type] */
    @Column(value = "device_type", insertValueScript = "''")
    private String deviceType;

    @Column(value = "device_type_to_instance_family", insertValueScript = "''")
    private String deviceTypeToInstanceFamily;

    /** 虚拟机机型<br/>Column: [instance_model] */
    @Column(value = "instance_model", insertValueScript = "''")
    private String instanceModel;

    /** 区域<br/>Column: [area_name] */
    @Column(value = "area_name", insertValueScript = "''")
    private String areaName;

    /** 地域<br/>Column: [region_name] */
    @Column(value = "region_name", insertValueScript = "''")
    private String regionName;

    /** 可用区<br/>Column: [zone_name] */
    @Column(value = "zone_name", insertValueScript = "''")
    private String zoneName;

    /** 是否新机型<br/>Column: [new_type] */
    @Column(value = "new_type", insertValueScript = "''")
    private String newType;

    /** 是否通用机型<br/>Column: [general_type] */
    @Column(value = "general_type", insertValueScript = "''")
    private String generalType;

    /** 总库存核心数<br/>Column: [total_core] */
    @Column(value = "total_core")
    private Integer totalCore;

    /** 已使用核心数<br/>Column: [used_core] */
    @Column(value = "used_core")
    private Integer usedCore;

//    /** 使用客户<br/>Column: [used_customer] */
//    @Column(value = "used_customer", insertValueScript = "''")
//    private String usedCustomer;

    /** appid<br/>Column: [app_id] */
    @Column(value = "app_id", insertValueScript = "''")
    private String appId;

    /** 客户行业<br/>Column: [customer_industry] */
    @Column(value = "customer_industry", insertValueScript = "''")
    private String customerIndustry;

    /** 战区<br/>Column: [war_zone] */
    @Column(value = "war_zone", insertValueScript = "''")
    private String warZone;

    /** 未使用的核心数<br/>Column: [unused_core] */
    @Column(value = "unused_core")
    private Integer unusedCore;

    /** 未使用库存类型<br/>Column: [unused_type] */
    @Column(value = "unused_type", insertValueScript = "''")
    private String unusedType;

    /** 库存子类型<br/>Column: [inventory_sub_type] */
    @Column(value = "inventory_sub_type")
    private String inventorySubType;

}