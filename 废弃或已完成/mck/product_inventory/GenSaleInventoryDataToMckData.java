package com.pugwoo.dboperate.archived.mck.product_inventory;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import jakarta.annotation.Resource;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 生成云产品的库存给麦肯锡
 */
@SpringBootTest
public class GenSaleInventoryDataToMckData {

    @Resource
    private DBHelper rrpIdcDBHelper;
    @Resource
    private DBHelper mckIdcDBHelper;
    @Resource
    private DBHelper jxcTxyIdcDBHelper;
    @Resource
    private DBHelper ckCloudDemandNewIdcDBHelper;
    @Resource
    private DBHelper cloudDemandDevDBHelper;

    @Test
    public void test1() {
        String statTime = "2023-07-26";
        genCVMSale(statTime);
        genCVMInventory(statTime);
    }

//    @Rollback(false)
//    @Transactional("mckIdcTransactionManager")
    @Test
    public void gen() {
        String statTime = "2023-04-08";
        while(true) {
            genCVMSale(statTime);
            genCVMInventory(statTime);

            statTime = DateUtils.formatDate(DateUtils.addTime(DateUtils.parse(statTime), Calendar.DATE, 1));
            if (statTime.equals(DateUtils.formatDate(new Date()))) {
                break;
            }
        }
    }

    private void genCVMSale(String statTime) {
        // 1. 主表
        List<CVMSaleVO> all = ckCloudDemandNewIdcDBHelper.getAll(CVMSaleVO.class, "", statTime);
        // 2. 转换和映射（如果是工具化的话，这里还需要要一个行的context）
        Map<String, Boolean> newGinsFamily = getNewGinsFamily();
        Map<Long, String> appidIndustry = getAppidIndustry();
        Map<Long, String> appidWarZone = getAppidWarZone();
        List<ProductInventoryDO> result = ListUtils.transform(all, o -> {
            ProductInventoryDO d = new ProductInventoryDO();
            d.setStatTime(o.getStattime());
            d.setProductType("CVM");
            d.setSubProductType(o.getGinsfamilyName());
            d.setInstanceModel(o.getGinstype());
            d.setTotalCore(o.getTotalCore());
            d.setUsedCore(o.getTotalCore());
            d.setUnusedCore(0);
        //    d.setUsedCustomer(StringTools.isEmpty(o.getSname()) ? String.valueOf(o.getAppid()) : o.getSname());
            d.setAppId(o.getAppid() == null ? "" : o.getAppid().toString());
            d.setUnusedType(o.getGinsCategory());
            d.setAreaName(o.getAreaName());
            d.setRegionName(o.getRegionName());
            d.setZoneName(o.getZoneName());

            d.setNewType(String.valueOf(newGinsFamily.getOrDefault(o.getGinsfamily(), false)));
            d.setCustomerIndustry(appidIndustry.getOrDefault(o.getAppid(), ""));
            d.setWarZone(appidWarZone.getOrDefault(o.getAppid(), ""));
            return d;
        });

        // 3. 写入
        mckIdcDBHelper.delete(ProductInventoryDO.class, "where stat_time=? and unused_core=0 and product_type='CVM'", statTime);
        mckIdcDBHelper.insertBatchWithoutReturnId(result);
    }

    /**CVM库存*/
    private void genCVMInventory(String statTime) {
        /**
         * 1. 主表
         * OpType: MAIN_TABLE
         * Expression: SQL
         * VO: CVMInventoryVO
         * Args: statTime 统计日期
         */
        List<CVMInventoryVO> all = rrpIdcDBHelper.getAll(CVMInventoryVO.class, "", statTime);

        Map<String, String> ginsfamilyToName = getGinsfamilyToName();
        /**
         * 2. 转换
         * OpType: TRANSFORM
         * mapping: |
         *    stat_time -> stat_time
         *    product_type -> product_type
         *    device_type -> device_type
         *    inventoryType -> unused_type
         *    inventoryCores -> unused_core
         *    0 -> used_core
         *    inventoryCores -> total_core
         */
        List<ProductInventoryDO> result = ListUtils.transform(all, o -> {
            ProductInventoryDO d = new ProductInventoryDO();
            d.setStatTime(o.getStatTime());
            d.setProductType(o.getProductType());
            d.setDeviceType(o.getDeviceType());
            d.setAreaName(o.getAreaName());
            d.setRegionName(o.getRegionName());
            d.setZoneName(o.getZoneName());
            d.setUnusedType(o.getInventoryType());
            d.setInventorySubType(o.getInventorySubType());
            d.setSubProductType(ginsfamilyToName.getOrDefault(o.getInstanceType(), o.getInstanceType()));
            d.setUsedCore(0);
            d.setUnusedCore(o.getInventoryCores());
            d.setTotalCore(o.getInventoryCores());
            return d;
        });

        /**
         * 3. 映射并填充
         * OpType: FILL
         * Dict: newDeviceType
         * DefaultValue: false
         * SourceField: deviceType
         * TargetField: new_type
         */
        Map<String, Boolean> newDeviceType = getNewDeviceType();
        ListUtils.forEach(result, o -> o.setNewType(
                String.valueOf(newDeviceType.getOrDefault(o.getDeviceType(), false))));

        /**
         * 4. 输出
         * OpType: OUTPUT
         * WriteType: replace
         * Range: where stat_time=? and used_core=0
         */
        mckIdcDBHelper.delete(ProductInventoryDO.class, "where stat_time=? and used_core=0 and product_type='CVM'", statTime);
        mckIdcDBHelper.insertBatchWithoutReturnId(result);
    }

    /**
     * 这个扮演的是：机型是否是新(好料)机型的映射关系
     */
    private Map<String, Boolean> getNewDeviceType() {
        List<String> hosttype = jxcTxyIdcDBHelper.getRaw(String.class, "SELECT hosttype FROM `static_stock_principal_hosttype`");
        return ListUtils.toMap(hosttype, o -> o, o -> true);
    }

    private Map<String, Boolean> getNewGinsFamily() {
        List<String> ginsfamily = jxcTxyIdcDBHelper.getRaw(String.class, " SELECT ginsfamily FROM `static_principal_ginsfamily`");
        return ListUtils.toMap(ginsfamily, o -> o, o -> true);
    }

    private Map<Long, String> getAppidIndustry() {
        List<TmpAppidIndustryDO> all = cloudDemandDevDBHelper.getAll(TmpAppidIndustryDO.class);
        return ListUtils.toMap(all, o -> o.getAppid(), o -> o.getIndustry());
    }

    private Map<Long, String> getAppidWarZone() {
        List<TmpAppidIndustryDO> all = cloudDemandDevDBHelper.getAll(TmpAppidIndustryDO.class);
        return ListUtils.toMap(all, o -> o.getAppid(), o -> o.getWarZone());
    }

    private Map<String, String> getGinsfamilyToName() {
        List<Map> all = ckCloudDemandNewIdcDBHelper.getRaw(Map.class, "select distinct ginsfamily,ginsfamily_name from end_to_end_cvm_sale_all_appid_zone_ginstype");
        return ListUtils.toMap(all, o -> (String) o.get("ginsfamily"), o -> (String) o.get("ginsfamily_name"));
    }
}
