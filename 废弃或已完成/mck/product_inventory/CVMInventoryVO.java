package com.pugwoo.dboperate.archived.mck.product_inventory;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;

import java.time.LocalDate;

@Table(value = "", virtualTablePath = "/废弃或已完成/db-operate/resources/mck_sql/cvm_inventory.sql")
@Data
public class CVMInventoryVO {

    @Column(value = "stat_time")
    private LocalDate statTime;

    @Column(value = "product_type")
    private String productType;

    @Column(value = "device_type")
    private String deviceType;

    @Column(value = "instance_type")
    private String instanceType;

    @Column(value = "inventoryType")
    private String inventoryType;

    @Column(value = "inventorySubType")
    private String inventorySubType;

    @Column(value = "area_name")
    private String areaName;

    @Column(value = "region_name")
    private String regionName;

    @Column(value = "zone_name")
    private String zoneName;

    @Column(value = "inventoryCores")
    private Integer inventoryCores;

}
