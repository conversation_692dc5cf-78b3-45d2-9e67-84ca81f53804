package com.pugwoo.dboperate.archived.mck.product_inventory;

import lombok.Data;
import lombok.ToString;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;

@Data
@ToString
@Table("tmp_appid_industry")
public class TmpAppidIndustryDO {

    /** appid<br/>Column: [appid] */
    @Column(value = "appid", isKey = true)
    private Long appid;

    /** 行业<br/>Column: [industry] */
    @Column(value = "industry")
    private String industry;

    /** 战区<br/>Column: [war_zone] */
    @Column(value = "war_zone")
    private String warZone;

    @Column("industry_dept")
    private String industryDept;

}