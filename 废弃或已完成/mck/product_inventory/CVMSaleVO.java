package com.pugwoo.dboperate.archived.mck.product_inventory;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;

import java.time.LocalDate;

@Table(value = "", virtualTablePath = "/废弃或已完成/db-operate/resources/mck_sql/cvm_sale.sql")
@Data
public class CVMSaleVO {

    @Column(value = "stattime")
    private LocalDate stattime;

    @Column(value = "sname")
    private String sname;

    @Column(value = "appid")
    private Long appid;

    @Column(value = "ginsfamily_name")
    private String ginsfamilyName;

    @Column(value = "ginsfamily")
    private String ginsfamily;

    @Column(value = "ginstype")
    private String ginstype;

    @Column(value = "gins_category")
    private String ginsCategory;

    @Column(value = "area_name")
    private String areaName;

    @Column(value = "region_name")
    private String regionName;

    @Column(value = "zone_name")
    private String zoneName;

    @Column(value = "totalCore")
    private Integer totalCore;

}
