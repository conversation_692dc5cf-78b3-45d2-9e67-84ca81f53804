package com.pugwoo.dboperate.archived.mck.云_用量;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDate;
import java.util.Date;

@Data
@ToString
@Table("industry_report_appid_info")
public class IndustryReportAppidInfoDO {

    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    @Column(value = "create_time")
    private Date createTime;

    @Column(value = "update_time")
    private Date updateTime;

    /** 数据日期，目前每月一份，约定1号<br/>Column: [stat_time] */
    @Column(value = "stat_time")
    private LocalDate statTime;

    @Column(value = "appid")
    private Long appid;

    /** 行业部门<br/>Column: [industry_dept] */
    @Column(value = "industry_dept")
    private String industryDept;

    /** 客户uin<br/>Column: [uin] */
    @Column(value = "uin")
    private Long uin;

    /** 客户名称<br/>Column: [customer_name] */
    @Column(value = "customer_name")
    private String customerName;

    /** 客户简称<br/>Column: [customer_short_name] */
    @Column(value = "customer_short_name")
    private String customerShortName;

    /** 客户类型，0个人1企业<br/>Column: [customer_type] */
    @Column(value = "customer_type")
    private Integer customerType;

    /** 主销售名<br/>Column: [business_manager] */
    @Column(value = "business_manager")
    private String businessManager;

    /** 主销售名的组织架构<br/>Column: [business_manager_oa_dept] */
    @Column(value = "business_manager_oa_dept")
    private String businessManagerOaDept;

    /** 主销售名的组织架构path<br/>Column: [business_manager_oa_path] */
    @Column(value = "business_manager_oa_path")
    private String businessManagerOaPath;

}