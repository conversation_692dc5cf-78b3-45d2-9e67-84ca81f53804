package com.pugwoo.dboperate.archived.mck.云_用量;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.Map;

@SpringBootTest
public class DiffYongliang {

    @Resource
    private DBHelper ckCloudDemandDevDBHelper;

    @Test
    public void test() {
//        List<String> dateList = ckCloudDemandDevDBHelper.getRaw(String.class,
//                "select distinct `期初时间`\n" +
//                        "from cloud_demand.yongliang_2022_month\n" +
//                        " where `期初时间`<='2021-12-31'" + // 新增了一批
//                        "order by `期初时间`");

        List<String> dateList = ListUtils.newList("2023-01-31", "2023-02-28");

        List<Yongliang2022MonthDO> last;
        List<Yongliang2022MonthDO> current = null;
        for (String date : dateList) {
            System.out.println("begin date:" + date);

            // 0. 把current赋值给last，并查出新的值赋值给current
            last = current;
            current = ckCloudDemandDevDBHelper.getAll(Yongliang2022MonthDO.class,
                    "where `期初时间`=?", date);

            // 1. 如果只有current有值，那current直接存入db
            if (last == null && current != null) {
                // 这里因为是存量数据，所以不处理
             //   List<Yongliang2022MonthWithDiffDO> result = ListUtils.transform(current, o -> trans(o));
             //   ckCloudDemandDevDBHelper.insertBatchWithoutReturnId(result);
            } else if (last != null && current != null) {
                // 2. 如果last和current都有值，那么current比对last出diff，并把current存入db
                Map<String, Yongliang2022MonthDO> lastMap = ListUtils.toMap(last,
                        o -> key(o), o -> o);
                Map<String, Yongliang2022MonthDO> currentMap = ListUtils.toMap(current,
                        o -> key(o), o -> o);

                List<Yongliang2022MonthWithDiffDO> result = new ArrayList<>();

                // 2.1 current有
                for (Map.Entry<String, Yongliang2022MonthDO> e : currentMap.entrySet()) {
                    Yongliang2022MonthDO lastOne = lastMap.get(e.getKey());
                    Yongliang2022MonthWithDiffDO diff = trans(e.getValue());
                    if (lastOne == null) {
                        diff.setDiff期初服务用量(e.getValue().get期初服务用量());
                        diff.setDiff期初计费用量(e.getValue().get期初计费用量());
                    } else {
                        diff.setDiff期初服务用量(e.getValue().get期初服务用量() - lastOne.get期初服务用量());
                        diff.setDiff期初计费用量(e.getValue().get期初计费用量() - lastOne.get期初计费用量());
                    }
                    result.add(diff);
                }

                // 2.2 last有而current没有的，补充一条，这里不要看日期
                for (Map.Entry<String, Yongliang2022MonthDO> e : lastMap.entrySet()) {
                    if (currentMap.containsKey(e.getKey())) {
                        continue;
                    }
                    Yongliang2022MonthWithDiffDO diff = trans(e.getValue());
                    diff.set期初时间(date);
                    diff.set期末时间(DateUtils.formatDate(
                            DateUtils.addTime(DateUtils.parse(date), Calendar.DATE, 1)));
                    diff.setDiff期初服务用量(0d - diff.get期初服务用量());
                    diff.setDiff期初计费用量(0d - diff.get期初计费用量());
                    diff.set期初服务用量(0d);
                    diff.set期初计费用量(0d);
                    result.add(diff);
                }

                ckCloudDemandDevDBHelper.insertBatchWithoutReturnId(result);

            } else {
                // 3. 如果都没有值则不处理
            }
        }
    }

    // 这里不能加入日期
    private String key(Yongliang2022MonthDO d) {
        return String.join("&", d.getAppid(), d.getGinstype(),
                d.get可用区(), d.getApprole(), d.getCpuOrGpu(), d.getBiztype());
    }

    private Yongliang2022MonthWithDiffDO trans(Yongliang2022MonthDO d) {
        Yongliang2022MonthWithDiffDO yongliang2022MonthWithDiffDO = new Yongliang2022MonthWithDiffDO();
        yongliang2022MonthWithDiffDO.setAppid(d.getAppid());
        yongliang2022MonthWithDiffDO.setUin(d.getUin());
        yongliang2022MonthWithDiffDO.set用户名称(d.get用户名称());
        yongliang2022MonthWithDiffDO.set用户简称(d.get用户简称());
        yongliang2022MonthWithDiffDO.set组织架构(d.get组织架构());
        yongliang2022MonthWithDiffDO.set收入行业(d.get收入行业());
        yongliang2022MonthWithDiffDO.set收入来源名称(d.get收入来源名称());
        yongliang2022MonthWithDiffDO.set期初时间(d.get期初时间());
        yongliang2022MonthWithDiffDO.set期末时间(d.get期末时间());
        yongliang2022MonthWithDiffDO.setApprole(d.getApprole());
        yongliang2022MonthWithDiffDO.set海关关境(d.get海关关境());
        yongliang2022MonthWithDiffDO.setCpuOrGpu(d.getCpuOrGpu());
        yongliang2022MonthWithDiffDO.setGinstype(d.getGinstype());
        yongliang2022MonthWithDiffDO.setGinsfamily(d.getGinsfamily());
        yongliang2022MonthWithDiffDO.setBiztype(d.getBiztype());
        yongliang2022MonthWithDiffDO.set可用区(d.get可用区());
        yongliang2022MonthWithDiffDO.set地域(d.get地域());
        yongliang2022MonthWithDiffDO.set期初计费用量(d.get期初计费用量());
        yongliang2022MonthWithDiffDO.set期初服务用量(d.get期初服务用量());
        yongliang2022MonthWithDiffDO.setDiff期初计费用量(0d);
        yongliang2022MonthWithDiffDO.setDiff期初服务用量(0d);
        return yongliang2022MonthWithDiffDO;
    }
}
