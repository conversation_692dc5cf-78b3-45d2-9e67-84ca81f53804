package com.pugwoo.dboperate.archived.mck.云_用量;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@Table("yongliang_2022_month")
public class Yongliang2022MonthDO {

    @Column(value = "appid")
    private String appid;

    @Column(value = "uin")
    private String uin;

    @Column(value = "用户名称")
    private String 用户名称;

    @Column(value = "用户简称")
    private String 用户简称;

    @Column(value = "组织架构")
    private String 组织架构;

    @Column(value = "收入行业")
    private String 收入行业;

    @Column(value = "收入来源名称")
    private String 收入来源名称;

    @Column(value = "期初时间")
    private String 期初时间;

    @Column(value = "期末时间")
    private String 期末时间;

    @Column(value = "approle")
    private String approle;

    @Column(value = "海关关境")
    private String 海关关境;

    @Column(value = "cpu_or_gpu")
    private String cpuOrGpu;

    @Column(value = "ginstype")
    private String ginstype;

    @Column(value = "ginsfamily")
    private String ginsfamily;

    @Column(value = "biztype")
    private String biztype;

    @Column(value = "可用区")
    private String 可用区;

    @Column(value = "地域")
    private String 地域;

    @Column(value = "期初计费用量")
    private Double 期初计费用量;

    @Column(value = "期初服务用量")
    private Double 期初服务用量;

}