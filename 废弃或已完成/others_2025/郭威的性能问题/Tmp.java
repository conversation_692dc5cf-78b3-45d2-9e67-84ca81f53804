package com.pugwoo.dboperate.archived.郭威的性能问题;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.thread.ThreadPoolUtils;
import jakarta.annotation.Resource;
import lombok.Data;
import lombok.ToString;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ThreadPoolExecutor;

@SpringBootTest
public class Tmp {

    @Resource
    private DBHelper ckCloudDemandNewIdcDBHelper;

    @Data
    @ToString
    public static class DwsInventoryHealthWeeklyScaleDfVO {

        @Column(value = "stat_time")
        private String statTime;

        @Column(value = "holiday_week_start_date")
        private String holidayWeekStartDate;

        @Column(value = "week_index")
        private Integer weekIndex;

        @Column(value = "holiday_week_end_date")
        private String holidayWeekEndDate;

        @Column(value = "product_type")
        private String productType;

        @Column(value = "instance_type")
        private String instanceType;

        @Column(value = "customhouse_title")
        private String customhouseTitle;

        @Column(value = "area_name")
        private String areaName;

        @Column(value = "region_name")
        private String regionName;

        @Column(value = "zone_name")
        private String zoneName;

        @Column(value = "sum_week_peak_logic_num")
        private BigDecimal sumWeekPeakLogicNum;

    }

    @Test
    public void test() throws Exception {
        for (int i = 0; i < 100; i++) {
            System.out.println("第" + i + "次");
            doRun();
        }
    }

    private void doRun() throws InterruptedException {
        ThreadPoolExecutor pool = ThreadPoolUtils.createThreadPool(13, 0, 13, "test");
        List<Integer> weekIndex = new ArrayList<>();
        for (int i = -13; i <= -1; i++) {
            weekIndex.add(i);
        }
        List<List<Integer>> partition = ListUtils.partition(weekIndex, 100); // 先不分week_index了，效果不明显

        for (final List<Integer> p : partition) {
            pool.submit(() -> {
                long start = System.currentTimeMillis();
                List<DwsInventoryHealthWeeklyScaleDfVO> list = ckCloudDemandNewIdcDBHelper.getRaw(DwsInventoryHealthWeeklyScaleDfVO.class,
                        """
        SELECT `stat_time`,
               `holiday_week_start_date`,
               `week_index`,
               `holiday_week_end_date`,
               `product_type`,
               `instance_type`,
               `customhouse_title`,
               `area_name`,
               `region_name`,
               `zone_name`,
               (COALESCE(sum(week_peak_logic_num), 0)) AS `sum_week_peak_logic_num`
        FROM cloud_demand.`dws_inventory_health_weekly_scale_df` t
        WHERE stat_time = '2024-06-18'
          AND week_index in (?)
          and product_type = 'CVM'
          AND customer_custom_group = 'ALL'
          AND exclude_uin_list = '(空值)'
        GROUP BY stat_time, holiday_week_start_date, week_index, holiday_week_end_date, product_type, instance_type,
                 customhouse_title, area_name, region_name, zone_name
               --   settings min_bytes_to_use_direct_io=1
                                """, p);
                long end = System.currentTimeMillis();
                System.out.println("cost:" + (end - start) + "ms" + ",list size:" + list.size());
            });
        }
        pool.shutdown();
        pool.awaitTermination(1, java.util.concurrent.TimeUnit.HOURS);
    }

}
