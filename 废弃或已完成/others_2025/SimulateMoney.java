package com.pugwoo.dboperate.archived.others;

/**
 * 75岁一次性投入3,085,000，当年起每一年拿12万，剩余的钱我都是做一年期的定期1.45%，第2年我的定期本金加利息里面再拿去12万，剩下的再做一年定期1.45%，假设定期利息一直都不变，到第105岁我还剩多少本金，本金可以为负数
 */
public class SimulateMoney {

    public static void main(String[] args) {

        int beginAge = 75;
        int endAge = 105;
        double beginMoney = 3085000;
        double everyYearMoney = 120000;
        double interestRate = 1.45 / 100; // 1.45%

        double curMoney = beginMoney;

        int curAge = beginAge;
        while(curAge < endAge) {

            // 每年年初，拿出每年要拿的钱
            curMoney = curMoney - everyYearMoney;
            curMoney = curMoney + curMoney * interestRate; // 剩余的钱做定期

            System.out.println("第" + curAge + "岁年末，剩余本金：" + curMoney);

            curAge++;
        }


    }

}
