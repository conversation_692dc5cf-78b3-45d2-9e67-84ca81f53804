-- cvm新增执行量sql，按独立个体划分

select uin,instance_type,sum(change_service_core_from_last_month) as new_core
from dwd_txy_scale_df
where stat_time=:statTime

  -- 【只看国内】
  and customhouse_title='境内'


  and cpu_or_gpu = 'CPU' and biz_type = 'cvm'
  and app_role != 'LH'
  and instance_type not like 'RS%' and instance_type not like 'RM%'
  and paymode_range_type in ('包年包月','弹性') and paymode!='5'
  and biz_range_type in ('外部业务')
  and zone_name in ('上海一区','上海七区','上海三区','上海二区','上海五区','上海八区','上海六区','上海四区','东京一区','东京二区','北京一区','北京七区','北京三区','北京二区','北京五区','北京八区','北京六区','北京四区','南京一区','南京三区','南京二区','台北一区','圣保罗一区','多伦多一区','天津三区','天津二区','孟买一区','孟买二区','广州七区','广州三区','广州二区','广州五区','广州八区','广州六区','广州四区','弗吉尼亚一区','弗吉尼亚二区','成都一区','成都二区','新加坡一区','新加坡三区','新加坡二区','新加坡四区','曼谷一区','曼谷二区','欧洲东北一区','法兰克福一区','法兰克福三区','法兰克福二区','深圳一区','深圳三区','深圳二区','深圳四区','硅谷一区','硅谷二区','贵阳一区','重庆一区','雅加达一区','雅加达二区','首尔一区','首尔二区','香港一区','香港三区','香港二区')
  and instance_type not in ('BC1','VSCVMMS','I6t','IA3se','SH2','BF1','BS1','C2','CN2','D1','DR1','DS2','DSW13','DSW14','FX4','HC20','HI1','HI20','HM1','HM20','HS10','HS1A','HS20','I1','I2','IT2','M1','M2','S1','S2','S2ne','SA1','SH1','SHARED','SK1','SN2','SPECIAL','SR1','TS2','TS3','VSCCS','VSCNAS','VSVPN','S5nt','VSVPNDPDK','SA1','SK1','SH1','SR1','OCA2','OC1','HS51','S3','S2','S1','TS3','TS2','HS30','HS20','HS10','S2ne','HS31','SN3ne','S4','TS4','M3','M2','M1','HM20','TM3','M4','C5','C3','C2','HC20','TC3','TC3ne','C4','C4ne','TC4','C4t','TC4t','IT5','IT5c','I3','I2','I1','HI20','IT3','IT3c','ITA3','CN3','CN2','TCN3','TCN3ne','D2','D1','D3','M3','M2','M1','HM20','TM3','M4') -- 黑名单机型

  and app_id not in (1258344706, 1251316161) -- 内部的2个appid，王丽丽给的，目前固定的，排除掉的
group by uin,instance_type
having sum(change_service_core_from_last_month)>0