package com.pugwoo.dboperate.archived.可视化8大产品;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dboperate.common.ReadFileUtils;
import com.pugwoo.wooutils.collect.ListUtils;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用于生成3d的数据
 */
@SpringBootTest
public class GenCvm3DData {

    @Resource
    private DBHelper ckStdCrpNewIdcDBHelper;

    @Test
    public void test() {
        List<String> months = ListUtils.of("2024-01-31",
                "2024-02-28", "2024-03-31",
                "2024-04-30", "2024-05-31",
                "2024-06-30", "2024-07-31",
                "2024-08-31", "2024-09-30",
                "2024-10-31", "2024-11-30",
                "2024-12-31", "2025-01-24");

        for (int monthIndex = 0; monthIndex < months.size(); monthIndex++) {
            Map<String, Object> params = new HashMap<>();
            params.put("statTime", months.get(monthIndex));

            String sql = ReadFileUtils.read("cvm_new_execute.sql");

            List<NewCoreUnitDTO> list = ckStdCrpNewIdcDBHelper.getRaw(NewCoreUnitDTO.class, sql, params);

            // 对结果进行聚合，阈值1024，要区分机型，用户则进行聚合
            BigDecimal valve = new BigDecimal(1024);

            List<NewCoreUnitDTO> result = new ArrayList<>();

            Map<String, List<NewCoreUnitDTO>> map = ListUtils.toMapList(list, o -> o.getInstanceType(), o -> o);
            for (Map.Entry<String, List<NewCoreUnitDTO>> e : map.entrySet()) {
                list = e.getValue();
                ListUtils.sortAscNullLast(list, o -> o.getNewCore());

                NewCoreUnitDTO tmp = null;
                for (NewCoreUnitDTO o : list) {
                    if (tmp == null) {
                        tmp = o;
                    }
                    if (tmp.getNewCore().compareTo(valve) >= 0) {
                        result.add(tmp);
                        tmp = null;
                    } else {
                        if (o.getNewCore().compareTo(valve) >= 0) {
                            result.add(o);
                        } else {
                            tmp.setNewCore(tmp.getNewCore().add(o.getNewCore()));
                        }
                    }
                }
                if (tmp != null) {
                    result.add(tmp);
                }
            }

            list = result;
            ListUtils.sortDescNullLast(list, o -> o.getNewCore());

            for (int i = 0; i < list.size(); i++) {
                System.out.print("[" + i + "," + monthIndex + "," + list.get(i).getNewCore().intValue() + "],");
            }
        }
    }

}
