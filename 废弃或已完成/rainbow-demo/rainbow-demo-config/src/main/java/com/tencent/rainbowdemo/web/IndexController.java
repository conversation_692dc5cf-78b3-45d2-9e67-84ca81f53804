package com.tencent.rainbowdemo.web;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class IndexController {

    @Value("${devloper}") // 这个配置项来自于七彩石，然后七彩石修改之后，可以直接在这里看到新的值
    private String devloper;

    @GetMapping("/")
    public String index() {
        return "结果:" + devloper;
    }

}
