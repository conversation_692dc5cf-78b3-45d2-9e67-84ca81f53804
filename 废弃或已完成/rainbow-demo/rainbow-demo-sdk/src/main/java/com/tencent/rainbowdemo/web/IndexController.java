package com.tencent.rainbowdemo.web;

import com.tencent.rainbow.RainbowSdkApplication;
import com.tencent.rainbow.base.callback.ListenCallback;
import com.tencent.rainbow.base.config.FileGroup;
import com.tencent.rainbow.base.config.KvGroup;
import com.tencent.rainbow.base.config.RainbowGroup;
import com.tencent.rainbow.base.config.TableGroup;
import com.tencent.rainbow.base.entity.RainbowChangeInfo;
import com.tencent.rainbow.base.entity.RainbowInfo;
import com.tencent.rainbow.base.enums.GroupType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
public class IndexController {

    @Autowired
    private RainbowSdkApplication rainbowSdkApplication;

    @PostConstruct
    private void init () {

        // 还可以监听值的变化，这里要自己实现，有些地方要自己处理
        String appId = "477bf456-28f6-46cb-a50d-4b6f5a732b5a";
        String groupName = "yunti_requirement_info.dev";
        String envName = "Default";
        rainbowSdkApplication.openCallBack(appId, groupName, envName, new ListenCallback() {
            @Override
            public void callback(RainbowChangeInfo change) {
                //发生配置变更时执行的代码
            }
        });

    }

    @GetMapping("/")
    public String index() {

        RainbowInfo rainbowInfo = new RainbowInfo();
        rainbowInfo.setAppId("477bf456-28f6-46cb-a50d-4b6f5a732b5a");
        rainbowInfo.setGroupName("yunti_requirement_info.dev"); // 这个才是groupName，带上.dev，也有点反人类
        rainbowInfo.setEnvName("Default"); // 这个有点反人类，居然在URL中拿到的

        //获取配置
        RainbowGroup group = rainbowSdkApplication.getGroup(rainbowInfo);

        // 使用配置，根据不同类型

        //获取配置类型
        GroupType groupType = group.getGroupType();
        //根据配置类型进行强转后获取配置信息
        switch (groupType) {
            case KV: {
                KvGroup kvGroup = (KvGroup) group;
                Map<String, KvGroup.KvDataValue> data = kvGroup.getData();
                //使用配置，请debug到这里看数据

                break;
            }
            case TABLE: {
                TableGroup tableGroup = (TableGroup) group;
                List<HashMap<String, String>> data = tableGroup.getData();
                //使用配置，请debug到这里看数据

                break;
            }
            case FILE: {
                FileGroup fileGroup = (FileGroup) group;
                List<FileGroup.FileData> data = fileGroup.getData();
                //使用配置，请debug到这里看数据

                break;
            }
            default:
        }

        return "ok，请debug到对应看数据";
    }


}
