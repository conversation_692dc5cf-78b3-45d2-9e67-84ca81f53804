package com.pugwoo.dboperate.腰部预测_中长尾扩大范围_月峰减月初;

import com.pugwoo.wooutils.io.IOUtils;
import com.pugwoo.wooutils.string.RegexUtils;
import com.pugwoo.wooutils.string.StringTools;

import java.io.FileInputStream;

public class MakeTable {

    public static void main(String[] args) throws Exception {
        String body = IOUtils.readAll(new FileInputStream("d:/a.txt"), "utf-8");

        System.out.println("topN,准确率,范围占比");

        String[] strings = StringTools.splitLines(body);
        for (String line : strings) {
            String n = RegexUtils.getFirstMatchStr(line, "top(\\d+)");
            String accuracy = RegexUtils.getFirstMatchStr(line, "准确率:(\\d+.\\d+)");
            String range = RegexUtils.getFirstMatchStr(line, "范围占比:(\\d+.\\d+)");

            System.out.println(n + "," + accuracy + "," + range);
        }
    }
}
