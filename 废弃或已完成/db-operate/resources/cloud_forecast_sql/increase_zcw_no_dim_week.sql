-- 中长尾 - 新增需求 - 没有维度
select `期初时间` as date,
     sum(abs(`期末计费用量`-`期初计费用量`)) as num
from yongliang_2022_week
where uin GLOBAL not in (select uin
    from (select uin, sum(`期初计费用量`) as num
    from yongliang_2022_month
    where cpu_or_gpu = 'CPU'
  and biztype = 'cvm'
  and approle not in ('EKS', 'EMR', 'CDH', 'LH')
  and `期初时间` = '2023-01-26'
    group by uin) t
    where num > 1000)
  and cpu_or_gpu = 'CPU'
  and biztype = 'cvm'
  and approle not in ('EKS', 'EMR', 'CDH', 'LH')
  and `海关关境`='境内'
  -- and ginsfamily in ('S5','S6','SA2','SA3') -- 只看4个主流机型
  and (`期末计费用量`-`期初计费用量`)>0 -- 只要新增的
group by date
order by date