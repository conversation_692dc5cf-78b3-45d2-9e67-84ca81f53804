-- 峰值
SELECT stat_time,instance_type,region_name,SUM(peak_core) AS peak_core
from ppl_forecast_peak_zone_instance_type_mf
where 1=1 and top_n=?
--      and region_name in ('上海','北京','广州','南京')
--      and instance_type in ('S5','S6','SA2','SA3')
        ${timeRange} -- 时间范围
GROUP BY stat_time,instance_type,region_name


-- 新增diff(保留uin粒度)
-- SELECT stat_time,instance_type,region_name,SUM(abs(diff_core)) AS peak_core
-- FROM ppl_forecast_peak_diff_mf
-- WHERE diff_core>0 and top_n=?
-- --     and region_name in ('上海','北京','广州','南京')
-- --     and instance_type in ('S5','S6','SA2','SA3')
--     ${timeRange} -- 时间范围
-- GROUP BY stat_time,instance_type,region_name

-- 净增
-- SELECT stat_time,instance_type,region_name,(case when SUM(diff_core) < 0 then 0 else sum(diff_core) end) AS peak_core
-- FROM ppl_forecast_peak_diff_mf
-- WHERE 1=1 and top_n=5
--     ${timeRange} -- 时间范围
-- GROUP BY stat_time,instance_type,region_name

-- 按可用区+机型大类区分新增、退回两个序列，这里是新增序列
-- SELECT stat_time,region_name,instance_type,SUM(abs(diff_core)) as peak_core
-- FROM
--     (SELECT stat_time,zone_name,region_name,instance_type,SUM(diff_core) AS diff_core
--      FROM ppl_forecast_peak_diff_mf
--      where top_n=5
--      GROUP BY stat_time,zone_name,instance_type
--     ) t
-- WHERE diff_core<0
--               ${timeRange} -- 时间范围
-- GROUP BY stat_time,region_name,instance_type
