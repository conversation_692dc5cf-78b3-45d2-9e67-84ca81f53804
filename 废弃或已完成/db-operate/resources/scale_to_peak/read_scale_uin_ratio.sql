select app_id,any(uin) as uin,any(region_name) as region_name,
    sum(
    case when biz_range_type='外部业务' then
    (case when cur_bill_core>0 then ceil(cur_bill_core/(instance_model_cpu/CAST(100 AS Decimal(10, 2))))*(instance_model_cpu/CAST(100 AS Decimal(10, 2))) else 0 end)
    else (case when cur_service_core>0 then ceil(cur_service_core/(instance_model_cpu/CAST(100 AS Decimal(10, 2))))*(instance_model_cpu/CAST(100 AS Decimal(10, 2))) else 0 end) end
    ) as scale_core
from dwd_txy_scale_df
where stat_time = ? and product='CVM' and app_role!='LH' and paymode='2'
  and customer_tab_type in ('中长尾客户','报备客户')
  and instance_type not like 'RS%' and instance_type not like 'RM%'
  and zone_name in ('上海一区','上海七区','上海三区','上海二区','上海五区','上海八区','上海六区','上海四区','东京一区','东京二区','北京一区','北京七区','北京三区','北京二区','北京五区','北京八区','北京六区','北京四区','南京一区','南京三区','南京二区','台北一区','圣保罗一区','多伦多一区','天津三区','天津二区','孟买一区','孟买二区','广州七区','广州三区','广州二区','广州五区','广州八区','广州六区','广州四区','弗吉尼亚一区','弗吉尼亚二区','成都一区','成都二区','新加坡一区','新加坡三区','新加坡二区','新加坡四区','曼谷一区','曼谷二区','欧洲东北一区','法兰克福一区','法兰克福三区','法兰克福二区','深圳一区','深圳三区','深圳二区','深圳四区','硅谷一区','硅谷二区','贵阳一区','重庆一区','雅加达一区','雅加达二区','首尔一区','首尔二区','香港一区','香港三区','香港二区')
  and zone_name=? and instance_type=?
group by app_id