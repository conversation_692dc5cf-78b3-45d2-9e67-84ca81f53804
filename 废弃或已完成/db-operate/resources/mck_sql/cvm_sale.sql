select stattime,sname,appid,ginsfamily,ginsfamily_name,ginstype,gins_category,area_name,region_name,zone_name,
       round(sum(cur_timecpu)) as totalCore
from
    (
        SELECT end_to_end_cvm_sale_ka_appid_zone_ginstype.stattime,
               end_to_end_cvm_sale_ka_appid_zone_ginstype.customer_category,
               end_to_end_cvm_sale_ka_appid_zone_ginstype.customer_category_new,
               end_to_end_cvm_sale_ka_appid_zone_ginstype.appid_type,
               end_to_end_cvm_sale_ka_appid_zone_ginstype.appid,
               end_to_end_cvm_sale_ka_appid_zone_ginstype.bg_name,
               end_to_end_cvm_sale_ka_appid_zone_ginstype.is_inner,
               end_to_end_cvm_sale_ka_appid_zone_ginstype.sname,
               end_to_end_cvm_sale_ka_appid_zone_ginstype.customhouse_title,
               end_to_end_cvm_sale_ka_appid_zone_ginstype.region_name,
               end_to_end_cvm_sale_ka_appid_zone_ginstype.area_name,
               end_to_end_cvm_sale_ka_appid_zone_ginstype.zone_name,
               end_to_end_cvm_sale_ka_appid_zone_ginstype.zone_id,
               end_to_end_cvm_sale_ka_appid_zone_ginstype.ginsfamily,
               end_to_end_cvm_sale_ka_appid_zone_ginstype.ginsfamily_name,
               end_to_end_cvm_sale_ka_appid_zone_ginstype.ginstype,
               end_to_end_cvm_sale_ka_appid_zone_ginstype.device_type,
               end_to_end_cvm_sale_ka_appid_zone_ginstype.gins_category,
               end_to_end_cvm_sale_ka_appid_zone_ginstype.cpu_count,
               end_to_end_cvm_sale_ka_appid_zone_ginstype.is_good_ginstype,
               end_to_end_cvm_sale_ka_appid_zone_ginstype.cpu_category,
               end_to_end_cvm_sale_ka_appid_zone_ginstype.buy_timecpu,
               end_to_end_cvm_sale_ka_appid_zone_ginstype.rtn_timecpu,
               end_to_end_cvm_sale_ka_appid_zone_ginstype.timecpu_diff,
               end_to_end_cvm_sale_ka_appid_zone_ginstype.buy_billcpu,
               end_to_end_cvm_sale_ka_appid_zone_ginstype.rtn_billcpu,
               end_to_end_cvm_sale_ka_appid_zone_ginstype.billcpu_diff,
               end_to_end_cvm_sale_ka_appid_zone_ginstype.cur_billcpu,
               end_to_end_cvm_sale_ka_appid_zone_ginstype.cur_timecpu
        FROM cloud_demand.end_to_end_cvm_sale_ka_appid_zone_ginstype
        UNION ALL
        SELECT end_to_end_cvm_sale_longtail_zone_ginstype.stattime,
               end_to_end_cvm_sale_longtail_zone_ginstype.customer_category,
               end_to_end_cvm_sale_longtail_zone_ginstype.customer_category_new,
               end_to_end_cvm_sale_longtail_zone_ginstype.appid_type,
               0 as appid,
               end_to_end_cvm_sale_longtail_zone_ginstype.bg_name,
               end_to_end_cvm_sale_longtail_zone_ginstype.is_inner,
               '中长尾' as sname,
               end_to_end_cvm_sale_longtail_zone_ginstype.customhouse_title,
               end_to_end_cvm_sale_longtail_zone_ginstype.region_name,
               end_to_end_cvm_sale_longtail_zone_ginstype.area_name,
               end_to_end_cvm_sale_longtail_zone_ginstype.zone_name,
               end_to_end_cvm_sale_longtail_zone_ginstype.zone_id,
               end_to_end_cvm_sale_longtail_zone_ginstype.ginsfamily,
               end_to_end_cvm_sale_longtail_zone_ginstype.ginsfamily_name,
               end_to_end_cvm_sale_longtail_zone_ginstype.ginstype,
               end_to_end_cvm_sale_longtail_zone_ginstype.device_type,
               end_to_end_cvm_sale_longtail_zone_ginstype.gins_category,
               end_to_end_cvm_sale_longtail_zone_ginstype.cpu_count,
               end_to_end_cvm_sale_longtail_zone_ginstype.is_good_ginstype,
               end_to_end_cvm_sale_longtail_zone_ginstype.cpu_category,
               end_to_end_cvm_sale_longtail_zone_ginstype.buy_timecpu,
               end_to_end_cvm_sale_longtail_zone_ginstype.rtn_timecpu,
               end_to_end_cvm_sale_longtail_zone_ginstype.timecpu_diff,
               end_to_end_cvm_sale_longtail_zone_ginstype.buy_billcpu,
               end_to_end_cvm_sale_longtail_zone_ginstype.rtn_billcpu,
               end_to_end_cvm_sale_longtail_zone_ginstype.billcpu_diff,
               end_to_end_cvm_sale_longtail_zone_ginstype.cur_billcpu,
               end_to_end_cvm_sale_longtail_zone_ginstype.cur_timecpu
        FROM cloud_demand.end_to_end_cvm_sale_longtail_zone_ginstype
    )
where stattime=? and cur_timecpu>0
group by stattime,sname,appid,ginsfamily,ginsfamily_name,ginstype,gins_category,area_name,region_name,zone_name
