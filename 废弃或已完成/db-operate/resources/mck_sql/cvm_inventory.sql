SELECT stat_time,'CVM' AS product_type,device_type,instance_type,
       (CASE WHEN indicator_code='d1' THEN '线上好料'
             WHEN indicator_code='d2' THEN '线上差料'
             WHEN indicator_code='d3' THEN '线上呆料'
             WHEN indicator_code IN ('e4','e8') THEN '线下库存'
             when indicator_code IN ('e1','e3', 'e5') THEN '其他设备'
             END) AS inventoryType,
       (case when indicator_code='e4' then '流转'
             when indicator_code='e8' then '搬迁中'
             when indicator_code='e1' then '支撑'
             when indicator_code='e3' then '备机'
             when indicator_code='e5' then '故障'
             WHEN indicator_code='d1' THEN '线上好料'
             WHEN indicator_code='d2' THEN '线上差料'
             WHEN indicator_code='d3' THEN '线上呆料'
           else ''
           end) as inventorySubType,
       area_name,
       region_name,
       zone_name,
       SUM(cores) AS inventoryCores
FROM report_plan_detail
WHERE stat_time=? AND product_type='CVM' AND compute_type='CPU'
      AND indicator_code IN ('d1','d2','d3','e4','e8')
      AND cores>0
GROUP BY stat_time,device_type,instance_type,inventoryType,inventorySubType,area_name,region_name,zone_name