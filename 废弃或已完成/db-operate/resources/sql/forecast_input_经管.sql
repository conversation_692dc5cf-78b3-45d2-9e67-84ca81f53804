-- 不区分地域和可用区
select  toYear(stat_time) as year,
        toMonth(stat_time) as month ,
        max(month_end_date) as stime, -- 注意这里不要取stat_time，因为日期可能不完全

 --   'GROUP_INSTANCE_TYPE' as gins_family,
--     (case when instance_type in ('S5','TS5','S4','TS4','S5se','HS50') then '机型大类1'
--       when instance_type in ('SA2','MA2') then '机型大类2'
--       when instance_type in ('SA3','MA3') then '机型大类3'
--       when instance_type in ('M5','HM50','TM5') then '机型大类4'
--       when instance_type in ('IT3','IT5') then '机型大类5'
--       when instance_type in ('C4','C4ne','TC4') then '机型大类6'
--       when instance_type in ('C3','TC3') then '机型大类7'
--       else instance_type end) as gins_family,
     instance_type as gins_family,
 --   'GROUP_REGION_NAME' as region_name,
     region_name as region_name,

 -- sum (lt_new_cores) as cores -- 新增
 sum (lt_ret_cores) as cores -- 退回
 -- sum (lt_cur_cores) as cores -- 存量

from dwd_txy_scale_df_view_forecast
where 1=1
  and uin in (select uin from std_crp.dwd_txy_appid_info_cf where channel_mark=0)
    -- and biz_range_type = '外部业务'
  and paymode_range_type !='弹性'
  ${timeRange} -- 时间范围
group by toYear(stat_time), toMonth(stat_time)
  , region_name
--   , (case when instance_type in ('S5','TS5','S4','TS4','S5se','HS50') then '机型大类1'
--     when instance_type in ('SA2','MA2') then '机型大类2'
--     when instance_type in ('SA3','MA3') then '机型大类3'
--     when instance_type in ('M5','HM50','TM5') then '机型大类4'
--     when instance_type in ('IT3','IT5') then '机型大类5'
--     when instance_type in ('C4','C4ne','TC4') then '机型大类6'
--     when instance_type in ('C3','TC3') then '机型大类7'
--     else instance_type end)
  , instance_type


