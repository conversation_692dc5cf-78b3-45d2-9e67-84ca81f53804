-- 不区分地域和可用区
select  toYear(stat_time) as year,
        toMonth(stat_time) as month ,
        max(month_end_date) as stime, -- 注意这里不要取stat_time，因为日期可能不完全

 --   'GROUP_INSTANCE_TYPE' as gins_family,
--      (case when instance_type in ('S5','TS5','S4','TS4','S5se','HS50') then '机型大类1'
--        when instance_type in ('SA2','MA2') then '机型大类2'
--        when instance_type in ('SA3','MA3') then '机型大类3'
--        when instance_type in ('M5','HM50','TM5') then '机型大类4'
--        when instance_type in ('IT3','IT5') then '机型大类5'
--        when instance_type in ('C4','C4ne','TC4') then '机型大类6'
--        when instance_type in ('C3','TC3') then '机型大类7'
--        else instance_type end)
--     (case
--     when instance_type in ('SA3','MA3') then 'SA3组'
--     when instance_type in ('M6','M6ce') then 'M6组'
--     when instance_type in ('M6p','M6mp') then 'M6p组'
--     when instance_type in ('S5','TS5','S5se','HS50') then 'S5组'
--     when instance_type in ('SA2','MA2','HSA20') then 'SA2组'
--     when instance_type in ('M5','TM5','HM50') then 'M5组'
--     when instance_type in ('S4','TS4') then 'S4组'
--     when instance_type in ('C4','C4ne','TC4') then 'C4组'
--     when instance_type in ('S3','S2','S1','TS3','TS2','HS30','HS20','HS10','S2ne') then 'S3组'
--     when instance_type in ('M3','M2','M1','HM20') then 'M3组'
--     when instance_type in ('C3','C2','HC20','TC3','TC3ne') then 'C3组'
--     when instance_type in ('I3','I2','I1','HI20') then 'I3组'
--     when instance_type in ('CN3','CN2') then 'CN3组'
--     when instance_type in ('D2','D1') then 'D2组'
--         else instance_type end)
--         as gins_family,
    instance_type as gins_family,
 --   'GROUP_REGION_NAME' as region_name,
     region_name as region_name,

  sum (lt_new_cores) as cores -- 新增
 -- sum (lt_ret_cores) as cores -- 退回
 -- sum (lt_cur_cores) as cores -- 存量

--from dwd_txy_scale_df_view_longtail_monthly_subscription -- 包年包月
 from dwd_txy_scale_df_view_longtail_lh -- 弹性
-- from dwd_txy_scale_df_view_longtail -- 包年包月+弹性
where 1=1
-- 主力机型主力地域
--  and instance_type in ('S5','S6','SA3','SA2') and region_name in ('上海','北京','广州','南京')
-- 主力机型非主力地域
--and instance_type in ('S5','S6','SA3','SA2') and region_name not in ('上海','北京','广州','南京')

-- 剔除掉不要的机型，只留下要的
--   and instance_type in ('SA3','MA3','M6','M6ce','M6p','M6mp','S5','TS5','S5se','HS50','SA2','MA2','HSA20','M5','TM5','HM50','S4','TS4','C4','C4ne','TC4','S3','S2','S1','TS3','TS2','HS30','HS20','HS10','S2ne','M3','M2','M1','HM20','C3','C2','HC20','TC3','TC3ne','I3','I2','I1','HI20','CN3','CN2','D2','D1')

-- and biz_range_type = '外部业务'
    ${timeRange} -- 时间范围
group by toYear(stat_time), toMonth(stat_time)
        , region_name
--    , (case when instance_type in ('S5','TS5','S4','TS4','S5se','HS50') then '机型大类1'
--      when instance_type in ('SA2','MA2') then '机型大类2'
--      when instance_type in ('SA3','MA3') then '机型大类3'
--      when instance_type in ('M5','HM50','TM5') then '机型大类4'
--      when instance_type in ('IT3','IT5') then '机型大类5'
--      when instance_type in ('C4','C4ne','TC4') then '机型大类6'
--      when instance_type in ('C3','TC3') then '机型大类7'
--      else instance_type end)
--         , (case
--     when instance_type in ('SA3','MA3') then 'SA3组'
--     when instance_type in ('M6','M6ce') then 'M6组'
--     when instance_type in ('M6p','M6mp') then 'M6p组'
--     when instance_type in ('S5','TS5','S5se','HS50') then 'S5组'
--     when instance_type in ('SA2','MA2','HSA20') then 'SA2组'
--     when instance_type in ('M5','TM5','HM50') then 'M5组'
--     when instance_type in ('S4','TS4') then 'S4组'
--     when instance_type in ('C4','C4ne','TC4') then 'C4组'
--     when instance_type in ('S3','S2','S1','TS3','TS2','HS30','HS20','HS10','S2ne') then 'S3组'
--     when instance_type in ('M3','M2','M1','HM20') then 'M3组'
--     when instance_type in ('C3','C2','HC20','TC3','TC3ne') then 'C3组'
--     when instance_type in ('I3','I2','I1','HI20') then 'I3组'
--     when instance_type in ('CN3','CN2') then 'CN3组'
--     when instance_type in ('D2','D1') then 'D2组'
--     else instance_type end)
 , instance_type


