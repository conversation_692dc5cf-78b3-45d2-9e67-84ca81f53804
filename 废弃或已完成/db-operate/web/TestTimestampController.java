package com.pugwoo.dboperate.web;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class TestTimestampController {

    @Autowired
    @Qualifier("cmdbLabJdbcTemplate")
    private JdbcTemplate jdbcTemplate;

    /**
     * 实验结论：NOW()的值是当时执行SQL的值。
     *       另外，不用NOW() 而用数据库的 CURRENT_TIMESTAMP也是一样的效果的，和NOW()一样
     * @return
     */
    @Transactional
    @GetMapping("/timestamp")
    public String timestamp() {
        jdbcTemplate.execute("INSERT INTO modify_timestamp2(hostTypeId,updatetime) VALUES(3,NOW())");
        //if (true) {
        //    throw new RuntimeException("xx");
        //}
        try {
            Thread.sleep(30000);
        } catch (InterruptedException e) {
            //e.printStackTrace();
        }

        jdbcTemplate.execute("INSERT INTO modify_timestamp2(hostTypeId,updatetime) VALUES(4,NOW())");
        return "done";
    }

}
