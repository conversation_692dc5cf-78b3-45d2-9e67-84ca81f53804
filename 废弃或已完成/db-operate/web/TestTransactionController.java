package com.pugwoo.dboperate.web;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class TestTransactionController {

    @Autowired @Qualifier("cmdbLabJdbcTemplate")
    private JdbcTemplate jdbcTemplate;

    @Transactional // 如果不指定名称，那就需要有个Primary的DataSourceTransactionManager
    @GetMapping("/hello")
    public String hello() {

        jdbcTemplate.execute("insert into tmp_student(name) values('nick')");

        if (true) {
            throw new RuntimeException("xxx");
        }

        return "hello world";
    }

}
