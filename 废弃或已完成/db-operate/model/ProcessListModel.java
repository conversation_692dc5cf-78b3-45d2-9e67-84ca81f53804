package com.pugwoo.dboperate.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class ProcessListModel {

    @JsonProperty("Id")
    private Long id;

    @JsonProperty("User")
    private String user;

    @JsonProperty("Host")
    private String host;

    private String db;

    @JsonProperty("Command")
    private String command;

    /**已执行的时间，单位秒*/
    @JsonProperty("Time")
    private Integer time;

    @JsonProperty("State")
    private String state;

    @JsonProperty("Info")
    private String info;

}
