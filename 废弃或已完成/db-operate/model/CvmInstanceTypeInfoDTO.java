package com.pugwoo.dboperate.model;

import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;

/**
 * cvm机型规格信息
 */
@Data
public class CvmInstanceTypeInfoDTO {

    /**cvm机型规格*/
    @Column("CvmInstanceModel")
    private String cvmInstanceModel;
    /**cvm机型大类*/
    @Column("CvmInstanceClass")
    private String cvmInstanceClass;
    /**该机型的cpu数*/
    @Column("CpuAmount")
    private Integer cpuAmount;
    /**该机型的内存数*/
    @Column("RamAmount")
    private Integer ramAmount;
    /**该机型的替换机型大类*/
    @Column("CanReplaceInstanceClass")
    private String canReplaceInstanceClass;
    /**该机型对应的母机*/
    @Column("HostDeviceClass")
    private String hostDeviceClass;
    /**一台母机可以生产多少个这样的cvm，这个数是大于等于1的整数*/
    @Column("HostProduceNum")
    private Integer hostProduceNum;

}
