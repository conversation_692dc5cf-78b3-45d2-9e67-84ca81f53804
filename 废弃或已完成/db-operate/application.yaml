mysql-default-args: useSSL=false&serverTimezone=GMT%2B8&useUnicode=true&characterEncoding=utf-8&zeroDateTimeBehavior=convertToNull&rewriteBatchedStatements=TRUE&useCursorFetch=true

spring:
  datasource:
    cmdb-lab: # devCloud自己搭建的cmdb config15，存生产的数据
      jdbc-url: ********************************************************************************************************************************************************
      username: mysql
      password: mysql888
      driver-class-name: com.mysql.cj.jdbc.Driver
    idcrm-idc: # 生产环境的数全通数据库，这个也是ERP的生产数据库
      jdbc-url: *********************************************************************************************************************************************************
      username: resdb
      password: resdb_resdb
      driver-class-name: com.mysql.cj.jdbc.Driver
    matrix-idc: # cloud-matrix生产环境
      jdbc-url: ********************************************?${mysql-default-args}
      username: resdb
      password: resdb_resdb
      driver-class-name: com.mysql.cj.jdbc.Driver
    clickhouse-lab:
      jdbc-url: *******************************************
      username: root
      password: a123456
      driver-class-name: ru.yandex.clickhouse.ClickHouseDriver
      #
    clickhouse-yunti-dev-cloud-demand:
      jdbc-url: ************************************************
      username: test
      password: app@ERP2018
      driver-class-name: ru.yandex.clickhouse.ClickHouseDriver
      #driver-class-name: com.clickhouse.jdbc.ClickHouseDriver
    clickhouse-yunti-idc:
      jdbc-url: ***********************************
      username: default
      password: app@ERP2018
      driver-class-name: ru.yandex.clickhouse.ClickHouseDriver
      #
    ck-cloud-demand-idc:
      jdbc-url: ***********************************/cloud_demand
      username: default
      password: app@ERP2018
      driver-class-name: ru.yandex.clickhouse.ClickHouseDriver
    ck-std-crp-idc:
      jdbc-url: ***********************************/std_crp
      username: default
      password: app@ERP2018
      driver-class-name: ru.yandex.clickhouse.ClickHouseDriver
    cmdb-refactor: # cmdb的dev重构信息库
      jdbc-url: **********************************************************************************************************************************************************
      username: mysql
      password: mysql888
      driver-class-name: com.mysql.cj.jdbc.Driver
    yunti-idc: # 生产环境的云梯数据库（主库）
      jdbc-url: ****************************************************************************************************************************************************
      username: yunti_apply
      password: yunti_apply
      driver-class-name: com.mysql.cj.jdbc.Driver
    demand-idc: # 生产环境的cvm需求预测
      jdbc-url: **********************************************************************************************************************************************************
      username: root
      password: app@ERP2018
      driver-class-name: com.mysql.cj.jdbc.Driver
    obs-idc: # obs的生产数据库
      jdbc-url: ***********************************************************************************************************************************************************
      username: obsdb
      password: obsdb
      driver-class-name: com.mysql.cj.jdbc.Driver
    shuttle-idc: # 资源中台shuttle的生产数据库
      jdbc-url: *********************************************?${mysql-default-args}
      username: shuttle
      password: "TscM2019@#cloud"
      driver-class-name: com.mysql.cj.jdbc.Driver
    rrp-idc: # 资源中台rrp的生产数据库
      jdbc-url: ******************************************?${mysql-default-args}
      username: cloud_demand
      password: z8Z_4ke5bAs_gzS
      driver-class-name: com.mysql.cj.jdbc.Driver
    jxc-txy-idc: # 进销存（腾讯云）数据库
      jdbc-url: *********************************?${mysql-default-args}
      username: galaxyro
      password: OQ@boVA2^YcD!4
      driver-class-name: com.mysql.cj.jdbc.Driver
    mck-idc: #麦肯锡要的数据
      jdbc-url: ********************************?${mysql-default-args}
      username: root
      password: app@ERP2018
      driver-class-name: com.mysql.cj.jdbc.Driver
    cloud-demand-dev:
      jdbc-url: *********************************************?${mysql-default-args}
      username: test
      password: test_test
      driver-class-name: com.mysql.cj.jdbc.Driver
    cloud-demand-idc:
      jdbc-url: *******************************************?${mysql-default-args}
      username: yunti_apply
      password: yunti_apply
      driver-class-name: com.mysql.cj.jdbc.Driver
    cloud-demand-common-dev:
      jdbc-url: *********************************************_common?${mysql-default-args}
      username: test
      password: test_test
      driver-class-name: com.mysql.cj.jdbc.Driver
    cloud-demand-common-idc:
      jdbc-url: **************************************************?${mysql-default-args}
      username: yunti_apply
      password: yunti_apply
      driver-class-name: com.mysql.cj.jdbc.Driver