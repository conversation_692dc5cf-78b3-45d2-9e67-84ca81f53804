package com.pugwoo.dboperate.config;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.impl.SpringJdbcDBHelper;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.core.JdbcTemplate;

import javax.sql.DataSource;

/**
 * 资源中台的数据库配置
 */
@Configuration
public class MatrixDatabaseConfiguration {

    // matrix idc

    @Bean(name = "matrixIdcDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.matrix-idc")
    public DataSource dataSource() {
        return DataSourceBuilder.create().build();
    }

    @Bean("matrixIdcDBHelper")
    public DBHelper dbHelper(@Qualifier("matrixIdcDataSource") DataSource dataSource) {
        return new SpringJdbcDBHelper(new JdbcTemplate(dataSource));
    }

}
