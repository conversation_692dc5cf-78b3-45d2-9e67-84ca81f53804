package com.pugwoo.dboperate.config;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.impl.SpringJdbcDBHelper;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;

/**
 * 生产环境的数全通数据库
 */
@Configuration
public class IdcrmIdcConfiguration {

    @Bean(name = "idcrmIdcDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.idcrm-idc")
    public DataSource dataSource() {
        return DataSourceBuilder.create().build();
    }

    @Bean("idcrmIdcJdbcTemplate")
    public JdbcTemplate jdbcTemplate(@Qualifier("idcrmIdcDataSource") DataSource dataSource) {
        return new JdbcTemplate(dataSource);
    }

    @Bean("idcrmIdcNamedParameterJdbcTemplate")
    public NamedParameterJdbcTemplate namedParameterJdbcTemplate(
            @Qualifier("idcrmIdcDataSource") DataSource dataSource) {
        return new NamedParameterJdbcTemplate(dataSource);
    }

    @Bean(name = "idcrmIdcTransactionManager")
    public DataSourceTransactionManager transactionManager(
            @Qualifier("idcrmIdcDataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    @Bean("idcrmIdcDBHelper")
    public DBHelper dbHelper(@Qualifier("idcrmIdcJdbcTemplate") JdbcTemplate jdbcTemplate,
                             @Qualifier("idcrmIdcNamedParameterJdbcTemplate") NamedParameterJdbcTemplate
                                     namedParameterJdbcTemplate) {

        SpringJdbcDBHelper springJdbcDBHelper = new SpringJdbcDBHelper();

        springJdbcDBHelper.setJdbcTemplate(jdbcTemplate);
        springJdbcDBHelper.setNamedParameterJdbcTemplate(namedParameterJdbcTemplate);

        return springJdbcDBHelper;
    }

}
