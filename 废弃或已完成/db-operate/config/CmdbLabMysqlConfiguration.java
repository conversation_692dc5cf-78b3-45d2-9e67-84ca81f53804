package com.pugwoo.dboperate.config;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;

/**
 * 我自己的devCloud搭建的mysql，导入生产的cmdb config15数据库的数据
 */
@Configuration
public class CmdbLabMysqlConfiguration {

    @Bean(name = "cmdbLabDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.cmdb-lab")
    public DataSource dataSource() {
        return DataSourceBuilder.create().build();
    }

    @Bean("cmdbLabJdbcTemplate")
    public JdbcTemplate jdbcTemplate(@Qualifier("cmdbLabDataSource") DataSource dataSource) {
        return new JdbcTemplate(dataSource);
    }

    @Bean("cmdbLabNamedParameterJdbcTemplate")
    public NamedParameterJdbcTemplate namedParameterJdbcTemplate(
            @Qualifier("cmdbLabDataSource") DataSource dataSource) {
        return new NamedParameterJdbcTemplate(dataSource);
    }

    @Primary
    @Bean(name = "cmdbLabTransactionManager")
    public DataSourceTransactionManager transactionManager(
            @Qualifier("cmdbLabDataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

}
