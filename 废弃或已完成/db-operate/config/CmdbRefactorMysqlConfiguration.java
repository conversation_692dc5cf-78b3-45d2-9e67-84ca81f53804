package com.pugwoo.dboperate.config;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.impl.SpringJdbcDBHelper;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;

/**
 * 我自己的devCloud搭建的mysql，导入生产的cmdb config15数据库的数据
 */
@Configuration
public class CmdbRefactorMysqlConfiguration {

    @Bean(name = "cmdbRefactorDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.cmdb-refactor")
    public DataSource dataSource() {
        return DataSourceBuilder.create().build();
    }

    @Bean("cmdbRefactorJdbcTemplate")
    public JdbcTemplate jdbcTemplate(@Qualifier("cmdbRefactorDataSource") DataSource dataSource) {
        return new JdbcTemplate(dataSource);
    }

    @Bean("cmdbRefactorNamedParameterJdbcTemplate")
    public NamedParameterJdbcTemplate namedParameterJdbcTemplate(
            @Qualifier("cmdbRefactorDataSource") DataSource dataSource) {
        return new NamedParameterJdbcTemplate(dataSource);
    }

    @Bean(name = "cmdbRefactorTransactionManager")
    public DataSourceTransactionManager transactionManager(
            @Qualifier("cmdbRefactorDataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    @Bean("cmdbRefactorDBHelper")
    public DBHelper dbHelper(@Qualifier("cmdbRefactorJdbcTemplate") JdbcTemplate jdbcTemplate,
                             @Qualifier("cmdbRefactorNamedParameterJdbcTemplate") NamedParameterJdbcTemplate namedParameterJdbcTemplate) {

        SpringJdbcDBHelper springJdbcDBHelper = new SpringJdbcDBHelper();

        springJdbcDBHelper.setJdbcTemplate(jdbcTemplate);
        springJdbcDBHelper.setNamedParameterJdbcTemplate(namedParameterJdbcTemplate);

        return springJdbcDBHelper;
    }

}
