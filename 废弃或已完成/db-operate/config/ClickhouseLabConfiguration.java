package com.pugwoo.dboperate.config;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.impl.SpringJdbcDBHelper;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.core.JdbcTemplate;

import javax.sql.DataSource;

/**
 * clickhouse 自己搭的实验环境
 */
@Configuration
public class ClickhouseLabConfiguration {

    @Bean(name = "clickhouseLabDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.clickhouse-lab")
    public DataSource dataSource() {
        return DataSourceBuilder.create().build();
    }

    @Bean("clickhouseLabJdbcTemplate")
    public JdbcTemplate jdbcTemplate(@Qualifier("clickhouseLabDataSource") DataSource dataSource) {
        return new JdbcTemplate(dataSource);
    }

    @Bean("clickhouseLabDBHelper")
    public DBHelper clickhouseLabDBHelper(@Qualifier("clickhouseLabJdbcTemplate") JdbcTemplate jdbcTemplate) {
        return new SpringJdbcDBHelper(jdbcTemplate);
    }

}
