package com.pugwoo.dboperate.yunti;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dboperate.entity.demand.YuntiDemandCvmItemRecordDO;
import com.pugwoo.dboperate.service.YuntiDemandCsigReturnDataFixService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import jakarta.annotation.Resource;
import java.util.List;

@Slf4j
@SpringBootTest
public class YuntiDemandCsigReturnDataFix {

    @Resource
    private DBHelper demandIdcDBHelper;
    @Resource
    private YuntiDemandCsigReturnDataFixService service;

    @Test
    public void findAndFix() {
        String sql = "SELECT demand_id,operate_batch_no\n" +
                "FROM yunti_demand_cvm_item_record \n" +
                "WHERE demand_id IN (\n" +
                "    SELECT id FROM `yunti_demand_cvm_item`\n" +
                "    WHERE deleted=0 AND resource_pool_type=1 AND instance_model!=''\n" +
                "    AND id IN (\n" +
                "        SELECT demand_id FROM yunti_demand_cvm_item_record WHERE source_type='APPLY_COST_RETURN' AND change_cvm_amount=0  AND change_all_disk_amount=0\n" +
                "    )\n" +
                ") \n" +
                "AND order_id LIKE 'YT%'\n" +
                "GROUP BY demand_id,operate_batch_no\n" +
                "HAVING SUM(CASE WHEN source_type='APPLY_COST_RETURN' THEN 1 ELSE 0 END)>0\n" +
                "       AND SUM(CASE WHEN source_type='APPLY_COST_RETURN' AND change_cvm_amount!=0 THEN 1 ELSE 0 END)=0\n" +
                "       AND SUM(CASE WHEN source_type='APPLY_COST' AND change_cvm_amount<0 THEN 1 ELSE 0 END) > 0\n" +
                "\n" +
                "ORDER BY demand_id\n" +
                "\n";

        List<YuntiDemandCvmItemRecordDO> records = demandIdcDBHelper.getRaw(YuntiDemandCvmItemRecordDO.class,
                sql);

        // records = records.subList(0, 5); // 测试，只处理第一个，TODO 后续移除

        for (YuntiDemandCvmItemRecordDO record : records) {
            service.fix(record);
        }

    }

}
