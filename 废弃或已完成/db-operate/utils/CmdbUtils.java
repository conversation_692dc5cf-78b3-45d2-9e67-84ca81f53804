package com.pugwoo.dboperate.utils;

import com.pugwoo.wooutils.json.JSON;
import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

@Slf4j
public class CmdbUtils {

    public static QuerySettingModel parseQuerySetting() throws Exception {
        InputStream in = CmdbUtils.class.getClass().getResourceAsStream("/querysetting.py");

        // 去掉 # 开头的列
        BufferedReader br = new BufferedReader(new InputStreamReader(in, "UTF-8"));
        String line;
        StringBuilder sb = new StringBuilder();
        while((line = br.readLine()) != null) {
            if (line.trim().startsWith("#")) {
                continue;
            }
            sb.append(line).append("\n");
        }

        String json = sb.toString();
        json = json.replace("True", "true");
        json = json.replace("False", "false");
        json = json.replace(":u\"", ":\"");
        json = json.replace(": u\"", ":\"");
        json = json.replace("'''", "\"");

        QuerySettingModel querySettingModel = JSON.parse(json, QuerySettingModel.class);
        return querySettingModel;
    }

    // 获得所有的schema
    public static List<QuerySettingModel.SearchScheme> getSchemas() throws Exception {
        QuerySettingModel querySettingModel = CmdbUtils.parseQuerySetting();
        return querySettingModel.getSearchScheme();
    }

    /**
     * 获得指定的schema，如果不存在返回null
     * @param schemaName
     * @return
     * @throws Exception
     */
    public static QuerySettingModel.SearchScheme getSchema(String schemaName) throws Exception {
        QuerySettingModel querySettingModel = CmdbUtils.parseQuerySetting();

        List<QuerySettingModel.SearchScheme> searchScheme = querySettingModel.getSearchScheme();
        QuerySettingModel.SearchScheme matched = null;
        for (QuerySettingModel.SearchScheme schema : searchScheme) {
            if (schemaName.equals(schema.getId())) {
                matched = schema;
            }
        }

        return matched;
    }

    // 因为查询的Schema的名称是中文，所以返回的结果可能有多个的
    public static List<QuerySettingModel.SearchScheme> getSchemaByCNName(String schemaCNName) throws Exception {
        QuerySettingModel querySettingModel = CmdbUtils.parseQuerySetting();

        List<QuerySettingModel.SearchScheme> result = new ArrayList<>();

        List<QuerySettingModel.SearchScheme> searchScheme = querySettingModel.getSearchScheme();
        for (QuerySettingModel.SearchScheme schema : searchScheme) {
            if (schemaCNName.equals(schema.getName())) {
                result.add(schema);
            }
        }

        return result;
    }

    public static List<QuerySettingModel.ResultColumn> getUnusedColumnByCNName(
            String schemaCNName, Set<String> usedColumns) throws Exception {
        QuerySettingModel querySettingModel = CmdbUtils.parseQuerySetting();

        List<QuerySettingModel.ResultColumn> result = new ArrayList<>();

        List<QuerySettingModel.SearchScheme> searchScheme = querySettingModel.getSearchScheme();
        for (QuerySettingModel.SearchScheme schema : searchScheme) {
            if (schemaCNName.equals(schema.getName())) {
                for (QuerySettingModel.ResultColumn column : schema.getResultColumn()) {
                    if (!usedColumns.contains(column.getName())) {
                        result.add(column);
                    }
                }
            }
        }

        return result;
    }

}
