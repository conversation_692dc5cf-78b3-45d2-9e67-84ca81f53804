package com.pugwoo.dboperate.utils;

import lombok.Data;

import java.util.List;

@Data
public class QuerySettingModel {

    @Data
    public static class SearchScheme {
        private String id;
        private String name;
        private String primaryKey;
        private String primaryKeyAlias;
        private String defaultWhereSql;
        private List<ResultColumn> resultColumn;
        private List<SearchCondition> searchCondition;
        private List<Join> join;
    }

    @Data
    public static class ResultColumn {
        private String id; // 英文名
        private String alias;
        private String name; // 中文名
        private String tableField; // 表名.字段名
        private String dataType; // 数据类型
    }

    @Data
    private static class SearchCondition {
        private String id;
        private String name;
        private String type;
        private String tableField; // 表名.字段名
        private String joinId;
    }

    @Data
    public static class Join {
        private String id;
        private String joinTable; // 关联的表
        private String primaryTable; // 如果主表是yes
        private String joinFormat; // left是左关联
        private String joinCondition; // join的关联条件
    }

    private List<SearchScheme> searchScheme;

}
