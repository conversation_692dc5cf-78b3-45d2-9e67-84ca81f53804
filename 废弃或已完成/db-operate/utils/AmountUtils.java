package com.pugwoo.dboperate.utils;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 数量相关
 */
public class AmountUtils {

    /**
     * 向上取整，例如：
     * 1）输入5，输出5
     * 2）输入5.5，输出6
     * 3）输出-5.5，输出-6
     */
    public static int ceiling(BigDecimal num) {
        return num.setScale(0, RoundingMode.UP).intValue();
    }

    /**
     * x除以y之后，结果向负方向取整。
     * 1）x=5,y=3，输出1
     * 2）x=-5，y=3，输出-2
     */
    public static int ceilingNegativeDiv(int x, int y) {
        if (x == 0 || y == 0) {
            return 0;
        }
        int mod = x % y;
        if (mod == 0) {
            return x / y;
        } else {
            if (x * y > 0) {
                return x / y;
            } else {
                return x / y - 1;
            }
        }
    }

}
