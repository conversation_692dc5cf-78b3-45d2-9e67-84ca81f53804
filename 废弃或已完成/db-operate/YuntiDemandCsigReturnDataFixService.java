package com.pugwoo.dboperate.service;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dboperate.entity.demand.YuntiDemandCvmItemRecordDO;
import com.pugwoo.wooutils.collect.ListUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

@Slf4j
@Service
public class YuntiDemandCsigReturnDataFixService {

    @Resource
    private DBHelper demandIdcDBHelper;

    @Transactional("demandIdcTransactionManager")
    public void fix(YuntiDemandCvmItemRecordDO record) {
        List<YuntiDemandCvmItemRecordDO> all = demandIdcDBHelper.getAll(YuntiDemandCvmItemRecordDO.class,
                "where operate_batch_no=? AND demand_id=? order by id",
                record.getOperateBatchNo(), record.getDemandId());
        List<YuntiDemandCvmItemRecordDO> less0Cost = ListUtils.filter(all, o -> "APPLY_COST".equals(o.getSourceType())
                && o.getChangeCvmAmount().compareTo(BigDecimal.ZERO) < 0);
        for (YuntiDemandCvmItemRecordDO cost : less0Cost) {
            if (cost.getDesc().contains("hasReturnApplied")) {
                log.info("record id:{} demand_id:{} has handled", cost.getId(), cost.getDemandId());
                continue;
            }

            demandIdcDBHelper.executeRaw("update yunti_demand_cvm_item set "
                            + "applied_cvm_amount=applied_cvm_amount-?,"
                            + "applied_core_amount=applied_core_amount-?,"
                            + "applied_ram_amount=applied_ram_amount-?"
                            + " where id=?",
                    BigDecimal.ZERO.subtract(cost.getChangeCvmAmount()),
                    -cost.getChangeCoreAmount(), -cost.getChangeRamAmount(),
                    cost.getDemandId());
            demandIdcDBHelper.executeRaw("update yunti_demand_cvm_item_snapshot set "
                            + "applied_cvm_amount=applied_cvm_amount-?,"
                            + "applied_core_amount=applied_core_amount-?,"
                            + "applied_ram_amount=applied_ram_amount-?"
                            + " where id=? and snapshot_time>?",
                    BigDecimal.ZERO.subtract(cost.getChangeCvmAmount()),
                    -cost.getChangeCoreAmount(), -cost.getChangeRamAmount(),
                    cost.getDemandId(), cost.getCreateTime());
            cost.setDesc("hasReturnApplied");
            demandIdcDBHelper.update(cost);

            log.info("record id:{} demand_id:{} success", cost.getId(), cost.getDemandId());
        }
    }

}
