package com.pugwoo.dboperate.entity.obs;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

@Data
@ToString
@Table("obs_budget_roll_adjust_cvm_append_device")
public class ObsBudgetRollAdjustCvmAppendDeviceDO {

    /** 记录ID<br/>Column: [Id] */
    @Column(value = "Id", isKey = true, isAutoIncrement = true)
    private Integer id;

    /** 转换源类型<br/>Column: [FromType] */
    @Column(value = "FromType")
    private String fromType;

    /** 转换目标类型<br/>Column: [ToType] */
    @Column(value = "ToType")
    private String toType;

    /** 转换比例<br/>Column: [TransferRatio] */
    @Column(value = "TransferRatio")
    private BigDecimal transferRatio;

    /** 转换类型：1.追加CVM后，自动追加设备<br/>Column: [TransferType] */
    @Column(value = "TransferType")
    private String transferType;

    /** 是否有效<br/>Column: [EnableFlag] */
    @Column(value = "EnableFlag")
    private Boolean enableFlag;

}