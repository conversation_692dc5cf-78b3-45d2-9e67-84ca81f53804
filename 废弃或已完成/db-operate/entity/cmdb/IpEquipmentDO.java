package com.pugwoo.dboperate.entity.cmdb;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;

@Data
@Table("ip_equipment")
public class IpEquipmentDO {

    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Integer id;

    @Column(value = "IpId")
    private Integer ipId;

    @Column(value = "IpAddress")
    private String ipAddress;

    @Column(value = "IpInt")
    private Long ipInt;

    @Column(value = "ConfigItemId")
    private Integer configItemId;

    @Column(value = "Enabled")
    private Boolean enabled;

    @Column(value = "EquipmentId")
    private Integer equipmentId;

    @Column(value = "IsOtherIP")
    private Integer isOtherIP;

    @Column(value = "flag")
    private Integer flag;

    /** 0±íÊ¾¹ÜÀíip£¬1±íÊ¾ÆäËüip(Ô­ÆäËüipÈÔ´æ·ÅÔÚÕâ¸ö×Ö¶Î),2±íÊ¾loopbackip,3±íÊ¾Íø¹Øip,4±íÊ¾¶Ë¿Ú»¥Áªip,5±íÊ¾vlan-ip<br/>Column: [IpType] */
    @Column(value = "IpType")
    private Boolean ipType;

    /** ·Ö¸øÍøÂçÉè±¸¶Ë¿ÚµÄip,¼´¶Ë¿Ú»¥Áªip¡£µ±iptypeÎª4Ê±£¬¶ÔÓ¦netdeviceportÖÐid£»Îª5Ê±¶ÔÓ¦netdevicevlanÖÐid<br/>Column: [TypeId] */
    @Column(value = "TypeId")
    private Integer typeId;

    @Column(value = "Domain")
    private String domain;

    @Column(value = "Active")
    private Boolean active;

    @Column(value = "SegmentId")
    private Integer segmentId;


}
