package com.pugwoo.dboperate.entity.cmdb;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;

@Data
@Table("cmdb_query_setting")
public class CmdbQuerySettingDO {

    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Integer id;

    /** 逻辑表英文名称<br/>Column: [schema_id] */
    @Column(value = "schema_id")
    private String schemaId;

    /** 逻辑表中文名称<br/>Column: [schema_name] */
    @Column(value = "schema_name")
    private String schemaName;

    /** 字段英文名称<br/>Column: [column_id] */
    @Column(value = "column_id")
    private String columnId;

    /** 字段中文名称<br/>Column: [column_name] */
    @Column(value = "column_name")
    private String columnName;

}
