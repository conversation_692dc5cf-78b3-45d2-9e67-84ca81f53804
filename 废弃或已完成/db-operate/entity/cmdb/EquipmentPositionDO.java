package com.pugwoo.dboperate.entity.cmdb;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;

import java.util.Date;

/**
 * 服务器-机位关系表
 */
@Data
@Table("equipment_position")
public class EquipmentPositionDO {

    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    @Column(value = "IdcId")
    private Long idcId;

    @Column(value = "ShelfId")
    private Long shelfId;

    @Column(value = "PositionId")
    private Long positionId;

    @Column(value = "EquipmentId")
    private Long equipmentId;

    @Column(value = "ConfigItemId")
    private Integer configItemId;

    @Column(value = "flag")
    private Integer flag;

    @Column(value = "cicode")
    private String cicode;

    @Column(value = "InputTime")
    private Date inputTime;

    @Column(value = "ParentId")
    private Long parentId;

}
