package com.pugwoo.dboperate.entity.shuttle;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

/**
 * 自研上云业务模块表
 */
@Data
@ToString
@Table("business_type")
public class BusinessTypeDO {

    @Column(value = "plan_product", isKey = true)
    private String planProduct;

    @Column(value = "business1")
    private String business1;

}