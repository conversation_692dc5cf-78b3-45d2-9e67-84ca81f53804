package com.pugwoo.dboperate.entity.shuttle;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.sql.Timestamp;
import java.util.Date;

/**
 * 资源中台采购单
 */
@Data
@ToString
@Table("device_apply")
public class DeviceApplyDO {

    @Column(value = "id", isKey = true)
    private String id;

    @Column(value = "setId")
    private String setId;

    @Column(value = "creator")
    private String creator;

    @Column(value = "dept")
    private String dept;

    @Column(value = "business1")
    private String business1;

    @Column(value = "business1Id")
    private Integer business1Id;

    @Column(value = "itemId")
    private String itemId;

    @Column(value = "priority")
    private Integer priority;

    @Column(value = "resourceType")
    private Integer resourceType;

    @Column(value = "module_id")
    private String moduleId;

    @Column(value = "module_name")
    private String moduleName;

    @Column(value = "zone_id")
    private Integer zoneId;

    @Column(value = "zone")
    private String zone;

    @Column(value = "showLogicDomain")
    private String showLogicDomain;

    @Column(value = "serviceLevel")
    private String serviceLevel;

    @Column(value = "business2")
    private String business2;

    @Column(value = "business2Id")
    private Integer business2Id;

    @Column(value = "business3")
    private String business3;

    @Column(value = "business3Id")
    private Integer business3Id;

    @Column(value = "plan_type")
    private Integer planType;

    @Column(value = "plan_type_name")
    private String planTypeName;

    @Column(value = "plan_dept_id")
    private Integer planDeptId;

    @Column(value = "plan_dept")
    private String planDept;

    @Column(value = "plan_product_id")
    private Integer planProductId;

    @Column(value = "plan_product")
    private String planProduct;

    @Column(value = "deviceType")
    private String deviceType;

    @Column(value = "num")
    private Integer num;

    @Column(value = "hasOs")
    private Integer hasOs;

    @Column(value = "osName")
    private String osName;

    @Column(value = "hasPartition")
    private Integer hasPartition;

    @Column(value = "isForVm")
    private Integer isForVm;

    @Column(value = "hasDiskEnclosure")
    private Integer hasDiskEnclosure;

    @Column(value = "raid")
    private String raid;

    @Column(value = "innerIPNum")
    private Integer innerIPNum;

    @Column(value = "lanRate")
    private String lanRate;

    @Column(value = "outerIPNum")
    private String outerIPNum;

    @Column(value = "wanRate")
    private String wanRate;

    @Column(value = "wanAccessAbility")
    private Integer wanAccessAbility;

    @Column(value = "wanAccessOperator")
    private String wanAccessOperator;

    @Column(value = "expectUseTime")
    private Date expectUseTime;

    @Column(value = "latestUseTime")
    private Date latestUseTime;

    @Column(value = "status")
    private Integer status;

    @Column(value = "substatus")
    private String substatus;

    @Column(value = "quotaId", isKey = true)
    private String quotaId;

    @Column(value = "authId")
    private String authId;

    @Column(value = "createTime")
    private Date createTime;

    @Column(value = "auditTime")
    private Date auditTime;

    @Column(value = "auditor")
    private String auditor;

    @Column(value = "region")
    private String region;

    @Column(value = "city")
    private String city;

    @Column(value = "promiseDeliveryTime")
    private Date promiseDeliveryTime;

    @Column(value = "applyNum")
    private Integer applyNum;

    @Column(value = "usedNum")
    private Integer usedNum;

    @Column(value = "holdNum")
    private Integer holdNum;

    @Column(value = "totalNum")
    private Integer totalNum;

    @Column(value = "waitdeliverNum")
    private Integer waitdeliverNum;

    @Column(value = "waitInitialNum")
    private Integer waitInitialNum;

    @Column(value = "ResAuther")
    private String resAuther;

    @Column(value = "ResAuthTime")
    private Date resAuthTime;

    @Column(value = "PlanAuther")
    private String planAuther;

    @Column(value = "PlanAuthTime")
    private Date planAuthTime;

    @Column(value = "productId")
    private Integer productId;

    @Column(value = "ProductAuther")
    private String productAuther;

    @Column(value = "ProductAuthTime")
    private Date productAuthTime;

    @Column(value = "DeptAuther")
    private String deptAuther;

    @Column(value = "DeptAuthTime")
    private Date deptAuthTime;

    @Column(value = "bios_set_id")
    private Integer biosSetId;

    @Column(value = "bios_set_name")
    private String biosSetName;

    /** 完全到货时间<br/>Column: [total_deliver_time] */
    @Column(value = "total_deliver_time")
    private Date totalDeliverTime;

    @Column(value = "device_sla")
    private Integer deviceSla;

    @Column(value = "areaRegion")
    private String areaRegion;

    @Column(value = "vs_zone")
    private String vsZone;

    @Column(value = "opinion")
    private String opinion;

    @Column(value = "sla_date")
    private Date slaDate;

    @Column(value = "need_type")
    private String needType;

    /** 0 主单 1 子单<br/>Column: [order_type] */
    @Column(value = "order_type", isKey = true)
    private Integer orderType;

    @Column(value = "pickup_message_flag")
    private Integer pickupMessageFlag;

    /** 是否自动提货<br/>Column: [auto_pickup_flag] */
    @Column(value = "auto_pickup_flag")
    private Integer autoPickupFlag;

    /** 是否紧急提货<br/>Column: [urgent_pickup_flag] */
    @Column(value = "urgent_pickup_flag")
    private Integer urgentPickupFlag;

    @Column(value = "purpose")
    private String purpose;

    @Column(value = "idc_unit")
    private String idcUnit;

    @Column(value = "idc_shelf")
    private String idcShelf;

    @Column(value = "idc_pos")
    private String idcPos;

    @Column(value = "net_ver")
    private String netVer;

    @Column(value = "model")
    private String model;

    @Column(value = "model_ver")
    private String modelVer;

    @Column(value = "idc_shelf_num")
    private Integer idcShelfNum;

    @Column(value = "switch_num")
    private Integer switchNum;

    @Column(value = "module_num")
    private Integer moduleNum;

    @Column(value = "remark")
    private String remark;

    @Column(value = "manual_config_flag")
    private Integer manualConfigFlag;

    @Column(value = "create_pick_flag_time")
    private Date createPickFlagTime;

    @Column(value = "device_sla_new")
    private Integer deviceSlaNew;

    @Column(value = "proj_set_id")
    private Integer projSetId;

    @Column(value = "proj_set_name")
    private String projSetName;

    @Column(value = "customer_type")
    private String customerType;

    @Column(value = "customer_remark")
    private String customerRemark;

    @Column(value = "mod_business_type_name")
    private String modBusinessTypeName;

    @Column(value = "follower")
    private String follower;

    @Column(value = "predict_delivery_date")
    private Date predictDeliveryDate;

    @Column(value = "expect_delivery_date")
    private Date expectDeliveryDate;

    @Column(value = "product")
    private String product;

    @Column(value = "sub_id", isKey = true)
    private String subId;

    @Column(value = "reuse_principal")
    private String reusePrincipal;

    @Column(value = "risk_level")
    private String riskLevel;

    @Column(value = "assetIds")
    private String assetIds;

    @Column(value = "memo")
    private String memo;

    @Column(value = "deliveryTime")
    private Timestamp deliveryTime;

    @Column(value = "emergency")
    private String emergency;

    @Column(value = "emergency_comment")
    private String emergencyComment;

    @Column(value = "pick_up_policy")
    private String pickUpPolicy;

    @Column(value = "deliverNum")
    private Integer deliverNum;

    @Column(value = "plan_month")
    private String planMonth;

    @Column(value = "plan_use")
    private String planUse;

    @Column(value = "res_pm_auther")
    private String resPmAuther;

    @Column(value = "res_pm_authtime")
    private Date resPmAuthtime;

    @Column(value = "attachment_path")
    private String attachmentPath;

    @Column(value = "matchPosNum")
    private Integer matchPosNum;

    @Column(value = "matchPosInfo")
    private String matchPosInfo;

    @Column(value = "plan_sla")
    private String planSla;

    @Column(value = "customer_name")
    private String customerName;

    @Column(value = "slaPromiseInfo")
    private String slaPromiseInfo;

    @Column(value = "omdPromiseInfo")
    private String omdPromiseInfo;

    @Column(value = "cloud_plan_sla")
    private String cloudPlanSla;

    @Column(value = "cloud_plan_use")
    private String cloudPlanUse;

    @Column(value = "reserve_info")
    private String reserveInfo;

    @Column(value = "idc_pos_id")
    private String idcPosId;

    @Column(value = "budget_use_type")
    private Integer budgetUseType;

    @Column(value = "budget_use_time")
    private Date budgetUseTime;

    /** 1表示结束预留<br/>Column: [reserve_status] */
    @Column(value = "reserve_status")
    private Integer reserveStatus;

    /** 调拨数量<br/>Column: [order_num] */
    @Column(value = "order_num")
    private Integer orderNum;

    /** 调拨备注<br/>Column: [supply_comment] */
    @Column(value = "supply_comment")
    private String supplyComment;

    /** 资产模式<br/>Column: [AssetType] */
    @Column(value = "AssetType")
    private String assetType;

    @Column(value = "update_time")
    private Date updateTime;

    /** 供应月份<br/>Column: [supply_month] */
    @Column(value = "supply_month")
    private String supplyMonth;

    /** 大客户预测类型<br/>Column: [customer_plan_sla] */
    @Column(value = "customer_plan_sla")
    private String customerPlanSla;

    /** 大客户预测使用<br/>Column: [customer_plan_use] */
    @Column(value = "customer_plan_use")
    private String customerPlanUse;

    @Column(value = "delivery_type")
    private String deliveryType;

    /** 是否拷机<br/>Column: [copy_mac] */
    @Column(value = "copy_mac")
    private Integer copyMac;

    /** 是否自动过单<br/>Column: [is_auto_review] */
    @Column(value = "is_auto_review")
    private String isAutoReview;

    /** 区分预测版本号<br/>Column: [auto_review_version] */
    @Column(value = "auto_review_version")
    private String autoReviewVersion;

    /** 自动过单结果<br/>Column: [auto_review_result] */
    @Column(value = "auto_review_result")
    private String autoReviewResult;

    /** 自动审单阶段<br/>Column: [auto_review_approval] */
    @Column(value = "auto_review_approval")
    private String autoReviewApproval;

    @Column(value = "industry")
    private String industry;

    /** 预测类型-532版本<br/>Column: [plan_sla_532] */
    @Column(value = "plan_sla_532")
    private String planSla532;

    /** 预测使用-532版本<br/>Column: [plan_use_532] */
    @Column(value = "plan_use_532")
    private String planUse532;

    /** 大盘预测类型-532版本<br/>Column: [cloud_plan_sla_532] */
    @Column(value = "cloud_plan_sla_532")
    private String cloudPlanSla532;

    /** 大盘预测使用-532版本<br/>Column: [cloud_plan_use_532] */
    @Column(value = "cloud_plan_use_532")
    private String cloudPlanUse532;

    /** 客户预测类型-532版本<br/>Column: [customer_plan_sla_532] */
    @Column(value = "customer_plan_sla_532")
    private String customerPlanSla532;

    /** 客户预测使用-532版本<br/>Column: [customer_plan_use_532] */
    @Column(value = "customer_plan_use_532")
    private String customerPlanUse532;

    /** 行业预测类型<br/>Column: [industry_plan_sla] */
    @Column(value = "industry_plan_sla")
    private String industryPlanSla;

    /** 行业预测使用<br/>Column: [industry_plan_use] */
    @Column(value = "industry_plan_use")
    private String industryPlanUse;

    /** 行业预测类型-532版本<br/>Column: [industry_plan_sla_532] */
    @Column(value = "industry_plan_sla_532")
    private String industryPlanSla532;

    /** 行业预测使用-532版本<br/>Column: [industry_plan_use_532] */
    @Column(value = "industry_plan_use_532")
    private String industryPlanUse532;

    @Column(value = "customer_cloud_plan_use")
    private String customerCloudPlanUse;

    @Column(value = "customer_cloud_plan_sla")
    private String customerCloudPlanSla;

    @Column(value = "industry_cloud_plan_use")
    private String industryCloudPlanUse;

    @Column(value = "industry_cloud_plan_sla")
    private String industryCloudPlanSla;

    @Column(value = "customer_cloud_plan_use_532")
    private String customerCloudPlanUse532;

    @Column(value = "customer_cloud_plan_sla_532")
    private String customerCloudPlanSla532;

    @Column(value = "industry_cloud_plan_use_532")
    private String industryCloudPlanUse532;

    @Column(value = "industry_cloud_plan_sla_532")
    private String industryCloudPlanSla532;

    /** 集群ID<br/>Column: [cluster_id] */
    @Column(value = "cluster_id")
    private String clusterId;

    /** 分组数<br/>Column: [group_no] */
    @Column(value = "group_no")
    private Integer groupNo;

    /** 采购原因<br/>Column: [pur_reason] */
    @Column(value = "pur_reason")
    private String purReason;

}