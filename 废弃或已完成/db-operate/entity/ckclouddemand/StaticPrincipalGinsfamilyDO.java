package com.pugwoo.dboperate.entity.ckclouddemand;

import lombok.Data;
import lombok.ToString;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;

@Data
@ToString
@Table("static_principal_ginsfamily")
public class StaticPrincipalGinsfamilyDO {

    /** 子机好料机型<br/>Column: [ginsfamily] */
    @Column(value = "ginsfamily")
    private String ginsfamily;

    public static StaticPrincipalGinsfamilyDO from(
            com.pugwoo.dboperate.entity.jxctxy.StaticPrincipalGinsfamilyDO d) {
        StaticPrincipalGinsfamilyDO staticPrincipalGinsfamilyDO = new StaticPrincipalGinsfamilyDO();
        staticPrincipalGinsfamilyDO.setGinsfamily(d.getGinsfamily());
        return staticPrincipalGinsfamilyDO;
    }

}