package com.pugwoo.dboperate.entity.ckclouddemand;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import com.pugwoo.wooutils.lang.DateUtils;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDate;

@Data
@ToString
@Table("daily_ka_appid_zoneid_ginstype")
public class DailyKaAppidZoneidGinstypeDO {

    /** 统计时间<br/>Column: [stattime] */
    @Column(value = "stattime")
    private LocalDate stattime;

    /** appid<br/>Column: [appid] */
    @Column(value = "appid")
    private Long appid;

    /** 用户简称<br/>Column: [sname] */
    @Column(value = "sname")
    private String sname;

    /** 是否为内部用户 1=内部用户 0=外部用户<br/>Column: [is_inner] */
    @Column(value = "is_inner")
    private Integer isInner;

    /** zoneid<br/>Column: [zoneid] */
    @Column(value = "zoneid")
    private Long zoneid;

    /** 实例规格<br/>Column: [ginstype] */
    @Column(value = "ginstype")
    private String ginstype;

    /** 每日服务核数<br/>Column: [cur_timecpu] */
    @Column(value = "cur_timecpu")
    private Double curTimecpu;

    /** 购买服务核数<br/>Column: [buy_timecpu] */
    @Column(value = "buy_timecpu")
    private Double buyTimecpu;

    /** 退换服务核数<br/>Column: [rtn_timecpu] */
    @Column(value = "rtn_timecpu")
    private Double rtnTimecpu;

    /** 每日计费核数<br/>Column: [cur_billcpu] */
    @Column(value = "cur_billcpu")
    private Double curBillcpu;

    /** 购买计费核数<br/>Column: [buy_billcpu] */
    @Column(value = "buy_billcpu")
    private Double buyBillcpu;

    /** 退还计费核数<br/>Column: [rtn_billcpu] */
    @Column(value = "rtn_billcpu")
    private Double rtnBillcpu;

    public static DailyKaAppidZoneidGinstypeDO from(
            com.pugwoo.dboperate.entity.jxctxy.DailyKaAppidZoneidGinstypeDO d) {
        DailyKaAppidZoneidGinstypeDO dailyKaAppidZoneidGinstypeDO = new DailyKaAppidZoneidGinstypeDO();
        dailyKaAppidZoneidGinstypeDO.setStattime(DateUtils.toLocalDate(d.getStattime()));
        dailyKaAppidZoneidGinstypeDO.setAppid(d.getAppid());
        dailyKaAppidZoneidGinstypeDO.setSname(d.getSname());
        dailyKaAppidZoneidGinstypeDO.setIsInner(d.getIsInner());
        dailyKaAppidZoneidGinstypeDO.setZoneid(d.getZoneid());
        dailyKaAppidZoneidGinstypeDO.setGinstype(d.getGinstype());
        dailyKaAppidZoneidGinstypeDO.setCurTimecpu(d.getCurTimecpu());
        dailyKaAppidZoneidGinstypeDO.setBuyTimecpu(d.getBuyTimecpu());
        dailyKaAppidZoneidGinstypeDO.setRtnTimecpu(d.getRtnTimecpu());
        dailyKaAppidZoneidGinstypeDO.setCurBillcpu(d.getCurBillcpu());
        dailyKaAppidZoneidGinstypeDO.setBuyBillcpu(d.getBuyBillcpu());
        dailyKaAppidZoneidGinstypeDO.setRtnBillcpu(d.getRtnBillcpu());
        return dailyKaAppidZoneidGinstypeDO;
    }

}