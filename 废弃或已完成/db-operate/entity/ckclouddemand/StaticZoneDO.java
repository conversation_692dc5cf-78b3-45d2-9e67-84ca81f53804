package com.pugwoo.dboperate.entity.ckclouddemand;

import lombok.Data;
import lombok.ToString;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;

@Data
@ToString
@Table("static_zone")
public class StaticZoneDO {

    /** 可用区id<br/>Column: [zoneid] */
    @Column(value = "zoneid")
    private Long zoneid;

    /** 可用区英文标识<br/>Column: [zone] */
    @Column(value = "zone")
    private String zone;

    /** 可用区名称（中文）<br/>Column: [zone_name] */
    @Column(value = "zone_name")
    private String zoneName;

    /** 关境属性中文名，例如“境内”“境外”<br/>Column: [customhouse_title] */
    @Column(value = "customhouse_title")
    private String customhouseTitle;

    /** 地域中文名称<br/>Column: [region_name] */
    @Column(value = "region_name")
    private String regionName;

    /** 地区中文名称<br/>Column: [area_name] */
    @Column(value = "area_name")
    private String areaName;

    public static StaticZoneDO from(com.pugwoo.dboperate.entity.jxctxy.StaticZoneDO d) {
        StaticZoneDO staticZoneDO = new StaticZoneDO();
        staticZoneDO.setZoneid(d.getZoneid());
        staticZoneDO.setZone(d.getZone());
        staticZoneDO.setZoneName(d.getZoneName());
        staticZoneDO.setCustomhouseTitle(d.getCustomhouseTitle());
        staticZoneDO.setRegionName(d.getRegionName());
        staticZoneDO.setAreaName(d.getAreaName());
        return staticZoneDO;
    }

}