package com.pugwoo.dboperate.entity.ckclouddemand;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import com.pugwoo.wooutils.lang.DateUtils;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDate;

@Data
@ToString
@Table("daily_inner_appid_zoneid_ginstype")
public class DailyInnerAppidZoneidGinstypeDO {

    /** 统计时间<br/>Column: [stattime] */
    @Column(value = "stattime")
    private LocalDate stattime;

    /** appid<br/>Column: [appid] */
    @Column(value = "appid")
    private Long appid;

    /** 用户简称<br/>Column: [sname] */
    @Column(value = "sname")
    private String sname;

    /** zoneid<br/>Column: [zoneid] */
    @Column(value = "zoneid")
    private Long zoneid;

    /** 实例规格<br/>Column: [ginstype] */
    @Column(value = "ginstype")
    private String ginstype;

    /** 每日服务核数<br/>Column: [cur_timecpu] */
    @Column(value = "cur_timecpu")
    private Double curTimecpu;

    /** 购买服务核数<br/>Column: [buy_timecpu] */
    @Column(value = "buy_timecpu")
    private Double buyTimecpu;

    /** 退换服务核数<br/>Column: [rtn_timecpu] */
    @Column(value = "rtn_timecpu")
    private Double rtnTimecpu;

    /** 每日计费核数<br/>Column: [cur_billcpu] */
    @Column(value = "cur_billcpu")
    private Double curBillcpu;

    /** 购买计费核数<br/>Column: [buy_billcpu] */
    @Column(value = "buy_billcpu")
    private Double buyBillcpu;

    /** 退还计费核数<br/>Column: [rtn_billcpu] */
    @Column(value = "rtn_billcpu")
    private Double rtnBillcpu;

    @Column(value = "uin")
    private Long uin;

    public static DailyInnerAppidZoneidGinstypeDO from(
            com.pugwoo.dboperate.entity.jxctxy.DailyInnerAppidZoneidGinstypeDO d) {
        DailyInnerAppidZoneidGinstypeDO dailyInnerAppidZoneidGinstypeDO = new DailyInnerAppidZoneidGinstypeDO();
        dailyInnerAppidZoneidGinstypeDO.setStattime(DateUtils.toLocalDate(d.getStattime()));
        dailyInnerAppidZoneidGinstypeDO.setAppid(d.getAppid());
        dailyInnerAppidZoneidGinstypeDO.setSname(d.getSname());
        dailyInnerAppidZoneidGinstypeDO.setZoneid(d.getZoneid());
        dailyInnerAppidZoneidGinstypeDO.setGinstype(d.getGinstype());
        dailyInnerAppidZoneidGinstypeDO.setCurTimecpu(d.getCurTimecpu());
        dailyInnerAppidZoneidGinstypeDO.setBuyTimecpu(d.getBuyTimecpu());
        dailyInnerAppidZoneidGinstypeDO.setRtnTimecpu(d.getRtnTimecpu());
        dailyInnerAppidZoneidGinstypeDO.setCurBillcpu(d.getCurBillcpu());
        dailyInnerAppidZoneidGinstypeDO.setBuyBillcpu(d.getBuyBillcpu());
        dailyInnerAppidZoneidGinstypeDO.setRtnBillcpu(d.getRtnBillcpu());
        dailyInnerAppidZoneidGinstypeDO.setUin(d.getUin());
        return dailyInnerAppidZoneidGinstypeDO;
    }

}