package com.pugwoo.dboperate.entity.ckclouddemand;

import lombok.Data;
import lombok.ToString;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;

@Data
@ToString
@Table("static_ginstype")
public class StaticGinstypeDO {

    /** 实例规格<br/>Column: [ginstype] */
    @Column(value = "ginstype")
    private String ginstype;

    /** CPU，单位：0.01核<br/>Column: [cpu] */
    @Column(value = "cpu")
    private Integer cpu;

    /** GPU，单位：vgpu卡数<br/>Column: [gpu] */
    @Column(value = "gpu")
    private Integer gpu;

    /** 子机机型所属“科”。<br/>Column: [ginsfamily] */
    @Column(value = "ginsfamily")
    private String ginsfamily;

    /** 子机机型所属“科”中文名称。<br/>Column: [ginsfamily_name] */
    @Column(value = "ginsfamily_name")
    private String ginsfamilyName;

    /** 所属业务类型<br/>Column: [biztype] */
    @Column(value = "biztype")
    private String biztype;

    public static StaticGinstypeDO from(com.pugwoo.dboperate.entity.jxctxy.StaticGinstypeDO d) {
        StaticGinstypeDO staticGinstypeDO = new StaticGinstypeDO();
        staticGinstypeDO.setGinstype(d.getGinstype());
        staticGinstypeDO.setCpu(d.getCpu());
        staticGinstypeDO.setGpu(d.getGpu());
        staticGinstypeDO.setGinsfamily(d.getGinsfamily());
        staticGinstypeDO.setGinsfamilyName(d.getGinsfamilyName());
        staticGinstypeDO.setBiztype(d.getBiztype());
        return staticGinstypeDO;
    }

}