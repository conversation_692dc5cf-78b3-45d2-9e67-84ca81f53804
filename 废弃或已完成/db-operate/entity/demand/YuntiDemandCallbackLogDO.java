package com.pugwoo.dboperate.entity.demand;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.util.Date;

@Data
@ToString
@Table("yunti_demand_callback_log")
public class YuntiDemandCallbackLogDO {

    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    @Column(value = "create_time")
    private Date createTime;

    @Column(value = "update_time")
    private Date updateTime;

    /** 执行的ip地址<br/>Column: [ip] */
    @Column(value = "ip")
    private String ip;

    /** 订单号<br/>Column: [order_id] */
    @Column(value = "order_id")
    private String orderId;

    /** 订单状态<br/>Column: [status] */
    @Column(value = "status")
    private Integer status;

    @Column(value = "from")
    private Integer from;

    /** 是否成功<br/>Column: [is_success] */
    @Column(value = "is_success")
    private Boolean isSuccess;

    /** 失败时的错误信息<br/>Column: [err_message] */
    @Column(value = "err_message")
    private String errMessage;

    /** 成功的结果<br/>Column: [result] */
    @Column(value = "result")
    private String result;

}