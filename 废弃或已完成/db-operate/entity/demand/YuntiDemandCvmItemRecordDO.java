package com.pugwoo.dboperate.entity.demand;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * yunti_demand_cvm_item_log   cvm需求变更记录
 */
@Data
@ToString
@Table("yunti_demand_cvm_item_record")
public class YuntiDemandCvmItemRecordDO {

    /** id<br/>Column: [id] */
    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    /** 创建时间<br/>Column: [create_time] */
    @Column(value = "create_time")
    private Date createTime;

    /** 更新时间<br/>Column: [update_time] */
    @Column(value = "update_time")
    private Date updateTime;

    /** 对应需求id<br/>Column: [demand_id] */
    @Column(value = "demand_id")
    private Long demandId;

    /** 此字段只针对自动调整的记录，来源来自自动调整<br/>Column: [source_demand_id] */
    @Column(value = "source_demand_id")
    private Long sourceDemandId;

    /** 需求类型<br/>Column: [source_type] */
    @Column(value = "source_type")
    private String sourceType;

    /** 来源单据类型（需求单和申领单）<br/>Column: [order_type] */
    @Column(value = "order_type")
    private String orderType;

    /** 来源单据id<br/>Column: [order_id] */
    @Column(value = "order_id")
    private String orderId;

    /** 变更描述信息\n<br/>Column: [desc] */
    @Column(value = "desc")
    private String desc;

    /** 实例数变化值<br/>Column: [change_cvm_amount] */
    @Column(value = "change_cvm_amount")
    private BigDecimal changeCvmAmount;

    /** 操作批次号<br/>Column: [operate_batch_no] */
    @Column(value = "operate_batch_no")
    private String operateBatchNo;

    /** 内存变化值<br/>Column: [change_ram_amount] */
    @Column(value = "change_ram_amount")
    private Integer changeRamAmount;

    /** 硬盘变化值<br/>Column: [change_all_disk_amount] */
    @Column(value = "change_all_disk_amount")
    private Integer changeAllDiskAmount;

    /** 变化后实例数变化值<br/>Column: [after_cvm_amount] */
    @Column(value = "after_cvm_amount")
    private BigDecimal afterCvmAmount;

    /** 变化后cpu核数<br/>Column: [after_core_amount] */
    @Column(value = "after_core_amount")
    private Integer afterCoreAmount;

    /** 变化后内存数<br/>Column: [after_ram_amount] */
    @Column(value = "after_ram_amount")
    private Integer afterRamAmount;

    /** 变化后硬盘数<br/>Column: [after_all_disk_amount] */
    @Column(value = "after_all_disk_amount")
    private Integer afterAllDiskAmount;

    /** cpu核数变化值<br/>Column: [change_core_amount] */
    @Column(value = "change_core_amount")
    private Integer changeCoreAmount;

}