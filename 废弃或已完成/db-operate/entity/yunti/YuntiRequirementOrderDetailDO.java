package com.pugwoo.dboperate.entity.yunti;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;

import java.util.Date;

@Data
@Table("yunti_requirement_order_detail")
public class YuntiRequirementOrderDetailDO {

    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    /** 创建时间<br/>Column: [create_time] */
    @Column(value = "create_time")
    private Date createTime;

    /** 更新时间<br/>Column: [update_time] */
    @Column(value = "update_time")
    private Date updateTime;

    /** 需求单号<br/>Column: [order_id] */
    @Column(value = "order_id")
    private String orderId;

    /** 城市id<br/>Column: [city_id] */
    @Column(value = "city_id")
    private Integer cityId;

    /** 城市名称<br/>Column: [city_name] */
    @Column(value = "city_name")
    private String cityName;

    /** 园区类型<br/>Column: [zone_type] */
    @Column(value = "zone_type")
    private Integer zoneType;

    /** 园区名称<br/>Column: [zone_type_name] */
    @Column(value = "zone_type_name")
    private String zoneTypeName;

    /** 核心类型<br/>Column: [core_type] */
    @Column(value = "core_type")
    private Integer coreType;

    /** 核心类型名称<br/>Column: [core_type_name] */
    @Column(value = "core_type_name")
    private String coreTypeName;

    /** 实例规格<br/>Column: [instance_model] */
    @Column(value = "instance_model")
    private String instanceModel;

    /** 实例或者核数<br/>Column: [cvm_amount] */
    @Column(value = "cvm_amount")
    private Double cvmAmount;

    /** io<br/>Column: [instance_io] */
    @Column(value = "instance_io")
    private Integer instanceIo;

    /** 硬盘类型<br/>Column: [cbs_res_class_id] */
    @Column(value = "cbs_res_class_id")
    private Integer cbsResClassId;

    /** 硬盘类型名称<br/>Column: [cbs_res_class_name] */
    @Column(value = "cbs_res_class_name")
    private String cbsResClassName;

    /** 所有硬盘大小<br/>Column: [all_disk_amount] */
    @Column(value = "all_disk_amount")
    private Integer allDiskAmount;

    /** 需求备注<br/>Column: [requirement_desc] */
    @Column(value = "requirement_desc")
    private String requirementDesc;

    /** 项目名称<br/>Column: [project_name] */
    @Column(value = "project_name")
    private String projectName;

    /** 需求年<br/>Column: [year] */
    @Column(value = "year")
    private Integer year;

    /** 需求月<br/>Column: [month] */
    @Column(value = "month")
    private Integer month;

    /** 部门id<br/>Column: [dept_id] */
    @Column(value = "dept_id")
    private Integer deptId;

    /** 部门名称<br/>Column: [dept_name] */
    @Column(value = "dept_name")
    private String deptName;

    /** 规划产品id<br/>Column: [plan_product_id] */
    @Column(value = "plan_product_id")
    private Integer planProductId;

    /** 规划产品名称<br/>Column: [plan_product_name] */
    @Column(value = "plan_product_name")
    private String planProductName;

    /** 需求类型<br/>Column: [source_type] */
    @Column(value = "source_type")
    private Integer sourceType;

    /** 需求类型名称<br/>Column: [source_type_name] */
    @Column(value = "source_type_name")
    private String sourceTypeName;

    /** cpu核数<br/>Column: [core_amount] */
    @Column(value = "core_amount")
    private Integer coreAmount;

    /** 实例类型<br/>Column: [instance_type] */
    @Column(value = "instance_type")
    private String instanceType;

    /** bgID<br/>Column: [bg_id] */
    @Column(value = "bg_id")
    private Integer bgId;

    /** BGName<br/>Column: [bg_name] */
    @Column(value = "bg_name")
    private String bgName;

    /** 期望交货时间<br/>Column: [use_time] */
    @Column(value = "use_time")
    private String useTime;

    /** 可用区id<br/>Column: [zone_id] */
    @Column(value = "zone_id")
    private Integer zoneId;

    /** 可用区<br/>Column: [zone_name] */
    @Column(value = "zone_name")
    private String zoneName;

    /** 属性bitmap<br/>Column: [attr_bitmap] */
    @Column(value = "attr_bitmap")
    private Integer attrBitmap;

    /** 流程状态<br/>Column: [process_status] */
    @Column(value = "process_status")
    private Integer processStatus;

    /** 转换状态<br/>Column: [change_status] */
    @Column(value = "change_status")
    private Integer changeStatus;

    /** 切片ID<br/>Column: [slice_id] */
    @Column(value = "slice_id")
    private String sliceId;

    /** 内存<br/>Column: [ram_amount] */
    @Column(value = "ram_amount")
    private Double ramAmount;

    /** 母机单号<br/>Column: [mj_order_id] */
    @Column(value = "mj_order_id")
    private String mjOrderId;

    @Column(value = "reserved_status")
    private Boolean reservedStatus;

    @Column(value = "is_adjust_patch_data")
    private Boolean isAdjustPatchData;

    /** 13周需求类型<br/>Column: [requirement_week_type] */
    @Column(value = "requirement_week_type")
    private String requirementWeekType;

    /** 是否基准锁定<br/>Column: [is_base_lock] */
    @Column(value = "is_base_lock")
    private Boolean isBaseLock;

    /** 基准锁定时间<br/>Column: [base_lock_time] */
    @Column(value = "base_lock_time")
    private Date baseLockTime;

    /** 是否是优先级调整产生的记录<br/>Column: [is_priority_adjust] */
    @Column(value = "is_priority_adjust")
    private Boolean isPriorityAdjust;

    /** 超过计划百分比<br/>Column: [exceed_plan_percent] */
    @Column(value = "exceed_plan_percent")
    private Integer exceedPlanPercent;

    /** 是否手动调整的13周需求类型<br/>Column: [is_manual_week_type] */
    @Column(value = "is_manual_week_type")
    private Boolean isManualWeekType;

}
