package com.pugwoo.dboperate.entity.yunti;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.sql.Timestamp;

@Data
@ToString
@Table("yunti_requirement_order")
public class YuntiRequirementOrderDO {

    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    @Column(value = "create_time")
    private Timestamp createTime;

    @Column(value = "update_time")
    private Timestamp updateTime;

    /** 单据id<br/>Column: [order_id] */
    @Column(value = "order_id")
    private String orderId;

    /** 提单人<br/>Column: [operator] */
    @Column(value = "operator")
    private String operator;

    /** 部门id<br/>Column: [dept_id] */
    @Column(value = "dept_id")
    private Integer deptId;

    /** 部门name<br/>Column: [dept_name] */
    @Column(value = "dept_name")
    private String deptName;

    /** 规划产品id<br/>Column: [plan_product_id] */
    @Column(value = "plan_product_id")
    private Integer planProductId;

    /** 规划产品名称<br/>Column: [plan_product_name] */
    @Column(value = "plan_product_name")
    private String planProductName;

    /** 预期时间<br/>Column: [use_time] */
    @Column(value = "use_time")
    private String useTime;

    /** 工作流key<br/>Column: [workflow_instance_key] */
    @Column(value = "workflow_instance_key")
    private String workflowInstanceKey;

    /** ,0草稿，待提交，1部门管理员审批，2规划经理审批，3资源经理审批，4审批结束，5，审批驳回<br/>Column: [status] */
    @Column(value = "status")
    private Integer status;

    /** 状态信息<br/>Column: [status_msg] */
    @Column(value = "status_msg")
    private String statusMsg;

    /** 当前处理人<br/>Column: [current_processor] */
    @Column(value = "current_processor")
    private String currentProcessor;

    /** 需求类型<br/>Column: [source_type] */
    @Column(value = "source_type")
    private Integer sourceType;

    /** 需求类型名称<br/>Column: [source_type_name] */
    @Column(value = "source_type_name")
    private String sourceTypeName;

    /** 单据描述<br/>Column: [order_desc] */
    @Column(value = "order_desc")
    private String orderDesc;

    /** 流程状态<br/>Column: [status_desc] */
    @Column(value = "status_desc")
    private String statusDesc;

    @Column(value = "bg_name")
    private String bgName;

    /** bg id<br/>Column: [bg_id] */
    @Column(value = "bg_id")
    private Integer bgId;

    @Column(value = "is_deleted")
    private Integer isDeleted;

    @Column(value = "approve_white_list")
    private String approveWhiteList;

    @Column(value = "source_from")
    private String sourceFrom;

    /** 是否加入已执行<br/>Column: [is_executed] */
    @Column(value = "is_executed")
    private Boolean isExecuted;

    /** 是否来自小额申领<br/>Column: [is_tiny_quota] */
    @Column(value = "is_tiny_quota")
    private Boolean isTinyQuota;

    /** 外部申领单ID<br/>Column: [out_apply_order_id] */
    @Column(value = "out_apply_order_id")
    private String outApplyOrderId;

    /** 是否来自于tke规划<br/>Column: [is_tke_plan] */
    @Column(value = "is_tke_plan")
    private Boolean isTkePlan;

}