package com.pugwoo.dboperate.entity.rrp;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.util.Date;

/**
 * 星云资源中台的region、可用区表
 */
@Data
@ToString
@Table("zysy_region_zone")
public class ZysyRegionZoneDO {

    /**region，例如广州*/
    @Column(value = "region", isKey = true)
    private String region;

    /**可用区，例如广州三区*/
    @Column(value = "zone", isKey = true)
    private String zone;

    /**可用区id*/
    @Column(value = "zone_id")
    private Integer zoneId;

    /**更新时间*/
    @Column(value = "update_time")
    private Date updateTime;

}