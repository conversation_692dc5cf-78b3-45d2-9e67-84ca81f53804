package com.pugwoo.dboperate.entity.rrp;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * CVM进销存报表
 */
@Data
@ToString
@Table("report_cvm_jxc")
public class ReportCvmJxcDO {

    /** id<br/>Column: [id] */
    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    /** 删除标记<br/>Column: [deleted] */
    @Column(value = "deleted", softDelete = {"0", "1"})
    private Boolean deleted;

    /** 创建时间<br/>Column: [create_time] */
    @Column(value = "create_time", setTimeWhenInsert = true)
    private Date createTime;

    /** 更新时间<br/>Column: [update_time] */
    @Column(value = "update_time", setTimeWhenInsert = true, setTimeWhenUpdate = true)
    private Date updateTime;

    /** 切片时间<br/>Column: [stat_time] */
    @Column(value = "stat_time")
    private Date statTime;

    /** 指标代码<br/>Column: [indicator_code] */
    @Column(value = "indicator_code")
    private String indicatorCode;

    /** 指标名称<br/>Column: [indicator_name] */
    @Column(value = "indicator_name")
    private String indicatorName;

    /** 指标大类<br/>Column: [category] */
    @Column(value = "category")
    private String category;

    /** 指标子类<br/>Column: [sub_category] */
    @Column(value = "sub_category")
    private String subCategory;

    /** 指标值<br/>Column: [indicator_value] */
    @Column(value = "indicator_value")
    private BigDecimal indicatorValue;

    /** 业务类型<br/>Column: [biz_type] */
    @Column(value = "biz_type")
    private String bizType;

    /** 指标单位<br/>Column: [indicator_unit] */
    @Column(value = "indicator_unit")
    private String indicatorUnit;

    /** 指标计算公式<br/>Column: [indicator_formula] */
    @Column(value = "indicator_formula")
    private String indicatorFormula;

    /** 明细表对应的uuid<br/>Column: [detail_uuid] */
    @Column(value = "detail_uuid", insertValueScript = "''")
    private String detailUuid;

}