package com.pugwoo.dboperate.entity.rrp;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 星云的机型规格表
 */
@Data
@ToString
@Table("zysy_target_conf")
public class ZysyTargetConfDO {

    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Integer id;

    /** 服务平台<br/>Column: [platform] */
    @Column(value = "platform")
    private String platform;

    @Column(value = "platform_id")
    private Integer platformId;

    /** 实例类型<br/>Column: [instance_type] */
    @Column(value = "instance_type")
    private String instanceType;

    /** 实例规格<br/>Column: [instance_spec] */
    @Column(value = "instance_spec")
    private String instanceSpec;

    /** GPU卡数(卡)，可以为小数<br/>Column: [gpu_num] */
    @Column(value = "gpu_num")
    private BigDecimal gpuNum;

    /** 核心处理器(cpu) （单位：核）<br/>Column: [core] */
    @Column(value = "core")
    private Integer core;

    /** 内存（单位：G）<br/>Column: [storage] */
    @Column(value = "storage")
    private Integer storage;

    /** 子机规格<br/>Column: [instance_detail_spec] */
    @Column(value = "instance_detail_spec")
    private String instanceDetailSpec;

    /** 存储器类型<br/>Column: [storage_type] */
    @Column(value = "storage_type")
    private String storageType;

    @Column(value = "updated_at")
    private Date updatedAt;

}