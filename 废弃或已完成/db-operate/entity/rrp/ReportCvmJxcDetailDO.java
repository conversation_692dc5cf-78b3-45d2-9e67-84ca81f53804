package com.pugwoo.dboperate.entity.rrp;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import com.pugwoo.dbhelper.exception.NoTableAnnotationException;
import com.pugwoo.dbhelper.utils.DOInfoReader;
import com.pugwoo.wooutils.json.JSON;
import lombok.Data;
import lombok.ToString;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * CVM进销存明细表
 */
@Data
@ToString
@Table("report_cvm_jxc_detail")
public class ReportCvmJxcDetailDO {

    /** id<br/>Column: [id] */
    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    /** 删除标记<br/>Column: [deleted] */
    @Column(value = "deleted", softDelete = {"0", "1"})
    private Boolean deleted;

    /** 创建时间<br/>Column: [create_time] */
    @Column(value = "create_time", setTimeWhenInsert = true)
    private Date createTime;

    /** 更新时间<br/>Column: [update_time] */
    @Column(value = "update_time", setTimeWhenInsert = true, setTimeWhenUpdate = true)
    private Date updateTime;

    /** 明细uuid<br/>Column: [uuid] */
    @Column(value = "uuid")
    private String uuid;

    /** 数据来源表名<br/>Column: [table_name] */
    @Column(value = "table_name")
    private String tableName;

    /** 明细数据<br/>Column: [row_data] */
    @Column(value = "row_data")
    private String rowData;

    public static <T> List<ReportCvmJxcDetailDO> transToReportCvmJxcDetailDO(List<T> source, String uuid){
        if (source == null) {
            return new ArrayList<>();
        }
        List<ReportCvmJxcDetailDO> ret = new ArrayList<>();
        for (T each : source) {
            ReportCvmJxcDetailDO detailDO = new ReportCvmJxcDetailDO();
            detailDO.setUuid(uuid);
            try {
                Table table = DOInfoReader.getTable(each.getClass());
                detailDO.setTableName(table.value());
            } catch (NoTableAnnotationException e) {
                // ignore
            }
            detailDO.setRowData(JSON.toJson(each));
            ret.add(detailDO);
        }
        return ret;
    }

}
