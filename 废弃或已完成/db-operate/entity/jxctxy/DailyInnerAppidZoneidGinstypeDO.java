package com.pugwoo.dboperate.entity.jxctxy;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.util.Date;

/**
 * 内部客户详情
 */
@Data
@ToString
@Table("daily_inner_appid_zoneid_ginstype")
public class DailyInnerAppidZoneidGinstypeDO {

    /** 统计时间<br/>Column: [stattime] */
    @Column(value = "stattime", isKey = true)
    private Date stattime;

    /** appid<br/>Column: [appid] */
    @Column(value = "appid", isKey = true)
    private Long appid;

    /** 用户简称<br/>Column: [sname] */
    @Column(value = "sname")
    private String sname;

    /** zoneid<br/>Column: [zoneid] */
    @Column(value = "zoneid", isKey = true)
    private Long zoneid;

    /** 实例规格<br/>Column: [ginstype] */
    @Column(value = "ginstype", isKey = true)
    private String ginstype;

    /** 每日服务核数<br/>Column: [cur_timecpu] */
    @Column(value = "cur_timecpu")
    private Double curTimecpu;

    /** 购买服务核数<br/>Column: [buy_timecpu] */
    @Column(value = "buy_timecpu")
    private Double buyTimecpu;

    /** 退换服务核数<br/>Column: [rtn_timecpu] */
    @Column(value = "rtn_timecpu")
    private Double rtnTimecpu;

    /** 每日计费核数<br/>Column: [cur_billcpu] */
    @Column(value = "cur_billcpu")
    private Double curBillcpu;

    /** 购买计费核数<br/>Column: [buy_billcpu] */
    @Column(value = "buy_billcpu")
    private Double buyBillcpu;

    /** 退还计费核数<br/>Column: [rtn_billcpu] */
    @Column(value = "rtn_billcpu")
    private Double rtnBillcpu;

    /** uin<br/>Column: [uin] */
    @Column(value = "uin")
    private Long uin;

}
