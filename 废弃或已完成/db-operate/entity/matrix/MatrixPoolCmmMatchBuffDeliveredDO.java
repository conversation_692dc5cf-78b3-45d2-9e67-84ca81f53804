package com.pugwoo.dboperate.entity.matrix;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.sql.Timestamp;

/**
 * 已交付的设备信息
 */
@Data
@ToString
@Table("matrix_pool_cmm_match_buff_delivered")
public class MatrixPoolCmmMatchBuffDeliveredDO {

    /** ID<br/>Column: [Id] */
    @Column(value = "Id", isKey = true, isAutoIncrement = true)
    private Integer id;

    /** 服务器ID<br/>Column: [ServerId] */
    @Column(value = "ServerId")
    private Integer serverId;

    /** 匹配状态<br/>Column: [StatusId] */
    @Column(value = "StatusId")
    private Integer statusId;

    /** 资源就绪时间<br/>Column: [ReadyTime] */
    @Column(value = "ReadyTime")
    private Timestamp readyTime;

    /** 预订Seq号<br/>Column: [BookingSeq] */
    @Column(value = "BookingSeq")
    private Integer bookingSeq;

    /** 预订时间<br/>Column: [BookingTime] */
    @Column(value = "BookingTime")
    private Timestamp bookingTime;

    /** 硬件型号ID<br/>Column: [HDModelId] */
    @Column(value = "HDModelId")
    private Integer hDModelId;

    /** 设备制造厂家ID<br/>Column: [ManufModelId] */
    @Column(value = "ManufModelId")
    private Integer manufModelId;

    /** 服务器类型ID<br/>Column: [ServerTypeId] */
    @Column(value = "ServerTypeId")
    private Integer serverTypeId;

    /** 所属父类服务器ID<br/>Column: [ParentServerId] */
    @Column(value = "ParentServerId")
    private Integer parentServerId;

    /** Raid类型ID<br/>Column: [RaidId] */
    @Column(value = "RaidId")
    private Integer raidId;

    /** 所属Zone ID;该字段暂废弃<br/>Column: [MZoneId] */
    @Column(value = "MZoneId")
    private Integer mZoneId;

    /** 所属Zone的Group ID;该字段暂废弃<br/>Column: [MZoneGroupId] */
    @Column(value = "MZoneGroupId")
    private Integer mZoneGroupId;

    /** 入库时间<br/>Column: [InputTime] */
    @Column(value = "InputTime")
    private Timestamp inputTime;

    /** 维保到期时间<br/>Column: [PactTime] */
    @Column(value = "PactTime")
    private Timestamp pactTime;

    /** 机房单元ID<br/>Column: [IdcUnitId] */
    @Column(value = "IdcUnitId")
    private Integer idcUnitId;

    /** 所在机架ID<br/>Column: [RackId] */
    @Column(value = "RackId")
    private Integer rackId;

    /** 所在机位ID<br/>Column: [PositionId] */
    @Column(value = "PositionId")
    private Integer positionId;

    /** 逻辑区域ID<br/>Column: [LogicDomainId] */
    @Column(value = "LogicDomainId")
    private Integer logicDomainId;

    /** 是否有外网端口<br/>Column: [HasOutPort] */
    @Column(value = "HasOutPort")
    private Integer hasOutPort;

    /** 网络拓扑ID<br/>Column: [NetworkTopId] */
    @Column(value = "NetworkTopId")
    private Integer networkTopId;

    /** 内网模块ID<br/>Column: [InnModuleId] */
    @Column(value = "InnModuleId")
    private Integer innModuleId;

    /** 内网接入交换机ID<br/>Column: [InnerAccSWId] */
    @Column(value = "InnerAccSWId")
    private Integer innerAccSWId;

    /** 外网接入交换机ID<br/>Column: [OuterAccSWId] */
    @Column(value = "OuterAccSWId")
    private Integer outerAccSWId;

    /** 服务器 OS ID<br/>Column: [ServerOSId] */
    @Column(value = "ServerOSId")
    private Integer serverOSId;

    /** 资源规划类型ID(1:标准量;2:预约量;3:绑定的预约量;)<br/>Column: [PlanTypeId] */
    @Column(value = "PlanTypeId")
    private Integer planTypeId;

    /** 所属规划ID<br/>Column: [PlanId] */
    @Column(value = "PlanId")
    private String planId;

    /** 资源保留类型<br/>Column: [ReservedType] */
    @Column(value = "ReservedType")
    private Integer reservedType;

    /** 资源保留原因<br/>Column: [ReservedReason] */
    @Column(value = "ReservedReason")
    private String reservedReason;

    /** 资源被保留时间<br/>Column: [ReservedTime] */
    @Column(value = "ReservedTime")
    private Timestamp reservedTime;

    /** 设备进入资源云时间<br/>Column: [CreateTime] */
    @Column(value = "CreateTime")
    private Timestamp createTime;

    /** 设备交付时间<br/>Column: [DeliveryTime] */
    @Column(value = "DeliveryTime")
    private Timestamp deliveryTime;

}