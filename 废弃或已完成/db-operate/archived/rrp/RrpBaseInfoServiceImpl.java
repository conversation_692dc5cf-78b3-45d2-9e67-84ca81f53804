package com.pugwoo.dboperate.rrp;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dboperate.entity.shuttle.BusinessTypeDO;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.string.StringTools;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class RrpBaseInfoServiceImpl {

    @Resource
    private DBHelper shuttleIdcDBHelper;

    public List<BusinessTypeDTO> getPlanProductToBusiness() {
        List<BusinessTypeDO> all = shuttleIdcDBHelper.getAll(BusinessTypeDO.class);
        return ListUtils.transform(all, o -> {
            BusinessTypeDTO dto = new BusinessTypeDTO();
            dto.setPlanProduct(o.getPlanProduct());
            dto.setBusiness(parse(o.getBusiness1()));
            return dto;
        });
    }

    public String generateNotZysySQLCondition(List<BusinessTypeDTO> list) {
        if (ListUtils.isEmpty(list)) {
            return "";
        }

        List<String> conditions = new ArrayList<>();
        for (BusinessTypeDTO dto : list) {
            List<BusinessTypeDTO.BusinessModule> business = dto.getBusiness();
            if (ListUtils.isEmpty(business)) {
                continue;
            }
            for (BusinessTypeDTO.BusinessModule b : business) {
                if (isNotBlank(b.getBusiness1()) && isBlank(b.getBusiness2()) && isBlank(b.getBusiness3())) {
                    conditions.add("(business1!='" + esc(b.getBusiness1()) + "' or business1 IS NULL)");
                }
                if (isNotBlank(b.getBusiness1()) && isNotBlank(b.getBusiness2()) && isBlank(b.getBusiness3())) {
                    conditions.add("((business1!='" + esc(b.getBusiness1()) + "' or business2!='"
                            + esc(b.getBusiness2()) + "') or (business1 IS NULL or business2 IS NULL))");
                }
                if (isNotBlank(b.getBusiness1()) && isNotBlank(b.getBusiness2()) && isNotBlank(b.getBusiness3())) {
                    conditions.add("((business1!='" + esc(b.getBusiness1()) + "' or business2!='"
                            + esc(b.getBusiness2()) + "' or business3!='" + esc(b.getBusiness3())
                            + "') or (business1 IS NULL or business2 IS NULL or business3 IS NULL))");
                }
            }
        }

        if (conditions.isEmpty()) {
            return "";
        }
        return "(" + StringTools.join(conditions, " AND ") + ")";
    }

    private static List<BusinessTypeDTO.BusinessModule> parse(String business) {
        if (StringTools.isBlank(business)) {
            return new ArrayList<>();
        }

        try {
            List<String> list = JSON.parse(business, List.class, String.class);
            List<BusinessTypeDTO.BusinessModule> result = new ArrayList<>();
            for (String bus : list) {
                String[] strs = bus.split("/");
                BusinessTypeDTO.BusinessModule bm = new BusinessTypeDTO.BusinessModule();
                if (strs.length > 0) {
                    bm.setBusiness1(strs[0]);
                }
                if (strs.length > 1) {
                    bm.setBusiness2(strs[1]);
                }
                if (strs.length > 2) {
                    bm.setBusiness3(strs[2]);
                }
                result.add(bm);
            }
            return result;
        } catch (Exception e) {
            log.error("parse business fail:{}", business);
        }

        return new ArrayList<>();
    }

    private static boolean isBlank(String str) {
        return StringTools.isBlank(str);
    }
    private static boolean isNotBlank(String str) {
        return StringTools.isNotBlank(str);
    }

    private static String esc(String str) {
        if (StringTools.isBlank(str)) {
            return "";
        }
        return str.replaceAll("\\'", "\\\\'");
    }
}
