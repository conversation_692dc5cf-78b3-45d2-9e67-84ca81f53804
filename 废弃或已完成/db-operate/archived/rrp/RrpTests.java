package com.pugwoo.dboperate.rrp;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.dboperate.entity.matrix.MatrixPoolCmmMatchBuffDeliveredDO;
import com.pugwoo.dboperate.entity.shuttle.DeviceApplyDO;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.lang.EqualUtils;
import com.pugwoo.wooutils.net.Browser;
import com.pugwoo.wooutils.net.HttpResponse;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import jakarta.annotation.Resource;
import java.io.IOException;
import java.util.List;

@Slf4j
@SpringBootTest
public class RrpTests {

    @Resource
    private RrpBaseInfoServiceImpl rrpBaseInfoService;
    @Resource
    private DBHelper shuttleIdcDBHelper;
    @Resource
    private DBHelper matrixIdcDBHelper;

    @Test
    public void test() throws IOException {
        WhereSQL whereSQL = new WhereSQL();
        // 页面的默认查询条件
        whereSQL.and("(order_type in (1, 100) or (order_type=0 and status in (0,1,3,4,5,6)))");
        // 规划产品 = 腾讯云CVM
        whereSQL.and("product=?", "腾讯云CVM");
        // 项目类型=非自研上云
        whereSQL.and(rrpBaseInfoService.generateNotZysySQLCondition(rrpBaseInfoService.getPlanProductToBusiness()));
        // 全量采购单-Q单号，即：调拨方式=采购
        whereSQL.and("order_type IN ('1')");

        List<DeviceApplyDO> all = shuttleIdcDBHelper.getAll(DeviceApplyDO.class, whereSQL.getSQL(), whereSQL.getParams());

        // 一共有4前多单，对于每一单，查询提货时间的数量，再和erp这边的表里的提货数量比较
        for (DeviceApplyDO deviceApplyDO : all) {
            String qOrder = deviceApplyDO.getQuotaId();
            String url = "https://tcres.woa.com/api/purchase/get_purchase_device?sub_id=" + qOrder;
            Browser browser = new Browser();
            browser.addRequestHeader("Cookie", "x_host_key_access_https=521d8af546dc14a612ab4a678e101046e9a164fe_s; x-client-ssid=***********-696ed96eacf03cfe81013f46eb8767bb340d997b; x-tofapi-host-key=1812211799b-7810001bac8a8f79548c34e55d5cc07163d48794; x_host_key_access=521d8af546dc14a612ab4a678e101046e9a164fe_s; x-host-key-ngn=181228961f2-e15e9b60ae9dc42053f7a015af6f644182ed3485; x-host-key-front=1812289652b-7dfd370f949006fa8d4b916d2c2bdf71d7abd1d3; blueking_language=zh-cn; t_uid=nickxie; sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%22nickxie%22%2C%22first_id%22%3A%2218138a530f971a-09add3c0ca4588-978183a-1057041-18138a530fab75%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E7%9B%B4%E6%8E%A5%E6%B5%81%E9%87%8F%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80%22%2C%22%24latest_referrer%22%3A%22%22%7D%2C%22%24device_id%22%3A%2218138a530f971a-09add3c0ca4588-978183a-1057041-18138a530fab75%22%7D; x-host-key-oaback_https=521d8af546dc14a612ab4a678e101046e9a164fe; userName=nickxie; LoginName=nickxie; _ra=_ra1655350718375.0.36287275979275857; t_u=1eda7fd119d1feb5%7C89bd57b544abacd6; x-host-key-oaback=1818f2663d2-f3d8ea26789d68564f101345ac6cd290a882a991; paas_perm_sessionid=5p72504hrr49xee921yctq65gpgj51dp; DiggerTraceId=06fe8e80-f436-11ec-b888-af72d4d1844f; tapdsession=e992aa1896c9e89f8c405d2612885fe3; pgv_info=ssid=s2346312022; pgv_pvid=9461685825; ERP_USERNAME=nickxie; paas_perm_csrftoken=WzNm5z6dSdBfP0QADkGRnsYQISqXK0y62lYClggZ3HidDOBWIIX3SyKvj7sKpePj; _t_uid=25388; km_u=69a44e0523ce978a20013f32cd15a4e380c87737569be30cb7c176fb81f40e1843c1b604e4294787; km_uid=nickxie; bk_uid=nickxie; bk_ticket=yfnm8CEudWKhLrtnWyQaHZmL13gKAP9yc5FguOCDXgc; tkex_csrf_token=hlacxynccw; RIO_TCOA_TICKET=tof:TOF4TeyJ2IjoiNCIsInRpZCI6ImhwOWZmaTJnWXN4bklDaEZUblJBOHpLazFhZENjOGFBIiwiaXNzIjoiMTAuOTkuMjA4LjYxIiwiaWF0IjoiMjAyMi0wNy0xNFQwOTo1NjoyMy45MDk3ODY4OTQrMDg6MDAiLCJhdWQiOiIxMC44OS4xNC41MCIsImhhc2giOiIzRENGMTM3NjVFMDJDMkExRTFGMUM4RjBENDVBQjBFOTBENjJDQzlGQzRCMDFBM0FBMzI4MTU0M0U4REJFMjU5IiwibmgiOiIxOERBMjU3NkFBNDdFRkQ1MDdBQTc1Rjg4ODYyRjcxRDA3NzgyM0I5NjBBMTY1MDY0QkM4NTA1NTE5N0RDQjE2In0; RIO_TCOA_TICKET_HTTPS=tof:TOF4TeyJ2IjoiNCIsInRpZCI6ImhwOWZmaTJnWXN4bklDaEZUblJBOHpLazFhZENjOGFBIiwiaXNzIjoiMTAuOTkuMjA4LjYxIiwiaWF0IjoiMjAyMi0wNy0xNFQwOTo1NjoyMy45MDk3ODY4OTQrMDg6MDAiLCJhdWQiOiIxMC44OS4xNC41MCIsImhhc2giOiIzRENGMTM3NjVFMDJDMkExRTFGMUM4RjBENDVBQjBFOTBENjJDQzlGQzRCMDFBM0FBMzI4MTU0M0U4REJFMjU5IiwibmgiOiIxOERBMjU3NkFBNDdFRkQ1MDdBQTc1Rjg4ODYyRjcxRDA3NzgyM0I5NjBBMTY1MDY0QkM4NTA1NTE5N0RDQjE2In0; beacon_uin=nickxie");
            browser.disableGzip();
            HttpResponse httpResponse = browser.get(url);

            String body = httpResponse.getContentString();
            Rsp rsp = JSON.parse(body, Rsp.class);
            if (rsp == null || rsp.getCode() != 0) {
                throw new RuntimeException("query device fail:{}" + JSON.toJson(rsp));
            }

            List<MatrixPoolCmmMatchBuffDeliveredDO> matrix = matrixIdcDBHelper.getAll(
                    MatrixPoolCmmMatchBuffDeliveredDO.class, "where planID=?", qOrder);


            boolean isOk = true;

            // 比对两者的数量和取货时间是否一样
            if (rsp.getData().size() != matrix.size()) {
                log.error("qOrder:{} size not match, url:{}, matrix:{}", qOrder, rsp.getData().size(), matrix.size());
                isOk = false;
            }

            // 比较一下提货时间，只要是同一天就可以了
            List<String> d1 = ListUtils.transform(rsp.getData(), o -> DateUtils.formatDate(DateUtils.parse(o.getDeliveryTime())));
            List<String> d2 = ListUtils.transform(matrix, o -> DateUtils.formatDate(o.getDeliveryTime()));
            EqualUtils equalUtils = new EqualUtils();
            equalUtils.ignoreListOrder(true);
            if (!equalUtils.isEqual(d1, d2)) {
                log.error("qOrder:{} 提货时间对不上", qOrder);
                isOk = false;
            }

            if (isOk) {
                log.info("qOrder:{} check ok", qOrder);
            }
        }
    }

    @Data
    public static class Rsp {
        private Integer code;
        private List<Item> data;
    }

    @Data
    public static class Item {
        private String assetId;
        private String deliveryTime;
    }
}
