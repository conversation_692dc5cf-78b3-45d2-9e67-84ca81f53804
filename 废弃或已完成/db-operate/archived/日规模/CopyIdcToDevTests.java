package com.pugwoo.dboperate.日规模不同处理方式;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dboperate.entity.OdsTxyScaleDfDO;
import com.pugwoo.wooutils.collect.ListUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import jakarta.annotation.Resource;
import java.util.List;

@Slf4j
@SpringBootTest
public class CopyIdcToDevTests {

    @Resource
    private DBHelper ckStdCrpNewIdcDBHelper;
    @Resource
    private DBHelper ckStdCrpDevDBHelper;

    @Test
    public void copy() {
        List<String> list = ListUtils.of("2023-06-02",
                "2023-06-03","2023-06-04","2023-06-05","2023-06-06","2023-06-07");

        for (String statTime : list) {
            List<OdsTxyScaleDfDO> all = ckStdCrpNewIdcDBHelper.getAll(OdsTxyScaleDfDO.class, "where stat_time=?", statTime);
            ckStdCrpDevDBHelper.insertBatchWithoutReturnId(all);
        }
    }
}
