package com.pugwoo.dboperate.yunti;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dboperate.entity.obs.ObsBudgetRollAdjustCvmAppendDeviceDO;
import com.pugwoo.wooutils.collect.ListUtils;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 列举出物理机对应的cvm机型
 */
@SpringBootTest
public class ListCvmInstanceType {

    @Resource
    private DBHelper obsIdcDBHelper;

    @Test
    public void test() {
        List<String> deviceTypeList = ListUtils.newArrayList("Y0-MS52A-25G",
                "Y0-MS52-25G", "Y0-MI52-25G",
                "Y0-MS42-25G", "T0-CS56X-25G", "Y0-MS33-25G");

        for (String deviceType : deviceTypeList) {
            System.out.println("========================= " + deviceType + "==================");
            List<ObsBudgetRollAdjustCvmAppendDeviceDO> all = obsIdcDBHelper.getAll(ObsBudgetRollAdjustCvmAppendDeviceDO.class,
                    "where ToType=?",
                    deviceType);
            ListUtils.sortAscNullLast(all, o -> o.getFromType());
            all.forEach(o -> {
                System.out.println(o.getFromType());
            });
        }

    }

}
