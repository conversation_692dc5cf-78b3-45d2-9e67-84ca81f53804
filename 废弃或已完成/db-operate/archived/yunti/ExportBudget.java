package com.pugwoo.dboperate.yunti;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dboperate.entity.yunti.YuntiRequirementOrderDetailDO;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.net.Browser;
import com.pugwoo.wooutils.net.HttpResponse;
import lombok.Data;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import jakarta.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@SpringBootTest
public class ExportBudget {

    @Resource
    private DBHelper yuntiIdcDBHelper;

    @Test
    public void test() throws Exception {
        List<YuntiRequirementOrderDetailDO> list =
                yuntiIdcDBHelper.getAll(YuntiRequirementOrderDetailDO.class,
                        "group by dept_name");

        Map<String, List<YuntiRequirementOrderDetailDO>> map = ListUtils.toMapList(
                list, o -> o.getBgName(), o -> o);

        for (Map.Entry<String, List<YuntiRequirementOrderDetailDO>> e : map.entrySet()) {

            Long availableBudgetCpuCore = 0L;
            Long totalBudgetCpuCore = 0L;

            for (YuntiRequirementOrderDetailDO detail : e.getValue()) {
                Result budget = getBudget(2021, detail.getDeptName());
                if (budget.getStatus() != 0) {
                    System.err.println("查询返回码不为0");
                }

                availableBudgetCpuCore += budget.getData().availableBudgetCpuCore;
                totalBudgetCpuCore += budget.getData().totalBudgetCpuCore;
            }

            System.out.println(e.getKey() + "," + totalBudgetCpuCore + "," + availableBudgetCpuCore);
        }
    }

    @Data
    public static class BudgetData {
        private Long availableBudgetCpuCore;
        private Long totalBudgetCpuCore;
    }

    @Data
    public static class Result {
        private Integer status;
        private BudgetData data;
    }

    private Result getBudget(int year, String deptName) throws Exception {
        String url = "http://api.obs.woa.com/new-query-app/queryCvmBudgetCores";

        Browser browser = new Browser();
        browser.addRequestHeader("APP_NAME", "yunti_cvm_requirement_budget_query");
        browser.addRequestHeader("APP_SECRET", "qzCmYGFss9rMMWfRZsDN+7Ff0fTslMKrHZackEm7QT6DVOjH8CYq0fEJxnwuHona");

        Map<String, Object> params = new HashMap<>();
        params.put("year", year);
        params.put("deptName", deptName);
        params.put("projectName", "");

        HttpResponse resp = browser.postJson(url, params);

        return JSON.parse(resp.getContentString(), Result.class);

    }
}
