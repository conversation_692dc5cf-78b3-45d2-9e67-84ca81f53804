package com.pugwoo.dboperate.yunti;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dboperate.entity.demand.YuntiDemandCallbackLogDO;
import com.pugwoo.dboperate.entity.yunti.YuntiRequirementOrderDO;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import jakarta.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 统计cvm审批的时长
 */
@SpringBootTest
public class CvmApproveTimeStat {

    @Resource
    private DBHelper yuntiIdcDBHelper;

    @Resource
    private DBHelper demandIdcDBHelper;

    @Test
    public void testCheckData() {

        // 取2021年的所有需要审批的订单，一共9200条
        List<YuntiRequirementOrderDO> all = yuntiIdcDBHelper.getAll(YuntiRequirementOrderDO.class,
                "where create_time>='2021-01-01' AND create_time<'2022-01-01' AND is_deleted=0" +
                        " AND workflow_instance_key != ''");
        List<String> approveOrderIds = ListUtils.transform(all, o -> o.getOrderId());

        //
        List<YuntiDemandCallbackLogDO> callbackLogs =
                demandIdcDBHelper.getAll(YuntiDemandCallbackLogDO.class,
                        "where create_time>='2021-01-01' AND create_time<'2022-01-01' group by order_id");

        // 看看有没有callback有但是approveOrderIds没有的 ：结论，没有！这就说明了，可以用callbackLogs来统计
        for (YuntiDemandCallbackLogDO callback : callbackLogs) {
            if (!approveOrderIds.contains(callback.getOrderId())) {
                System.out.println(callback.getOrderId());
            }
        }
    }

    @Test
    public void statStepTime() {

        Map<Integer, Integer> approveStepCount = new HashMap<>();
        Map<Integer, Integer> approveTotalCount = new HashMap<>(); // 以秒为单位

        Map<String, Integer> finishPassOrder = new HashMap<>(); // 审批完成的订单
        Map<String, Integer> finishRejectOrder = new HashMap<>(); // 审批驳回的订单

        // 订单维度
        List<YuntiDemandCallbackLogDO> callbackLogs =
                demandIdcDBHelper.getAll(YuntiDemandCallbackLogDO.class,
                        "where create_time>='2021-10-01' AND create_time<'2022-01-01' group by order_id");

        for (YuntiDemandCallbackLogDO callbackLog : callbackLogs) {
            List<YuntiDemandCallbackLogDO> list = demandIdcDBHelper.getAll(YuntiDemandCallbackLogDO.class,
                    "where order_id=? order by status", callbackLog.getOrderId());
            for (int i = 0; i < list.size() - 1; i++) {
                YuntiDemandCallbackLogDO callback = list.get(i);
                YuntiDemandCallbackLogDO next = list.get(i + 1);
                // 这里只看1、2、3，且下一步不是同状态的callback
                if (!(callback.getStatus().equals(1) || callback.getStatus().equals(2) || callback.getStatus().equals(3))) {
                    continue;
                }
                if (callback.getStatus().equals(next.getStatus())) {
                    continue;
                }

                Integer status = callback.getStatus();
                approveStepCount.putIfAbsent(status, 0);
                approveStepCount.put(status, approveStepCount.get(status) + 1);

                approveTotalCount.putIfAbsent(status, 0);
                long costSeconds = (next.getCreateTime().getTime() - callback.getCreateTime().getTime()) / 1000;
                approveTotalCount.put(status, approveTotalCount.get(status) + ((int)costSeconds));
            }

            if (list.size() > 1) {
                List<Integer> statuses = ListUtils.transform(list, o -> o.getStatus());
                Date begin = list.get(0).getCreateTime();
                if (statuses.contains(4)) {
                    Date end = ListUtils.filter(list, o -> o.getStatus().equals(4)).get(0).getCreateTime();
                    finishPassOrder.put(list.get(0).getOrderId(), (int)((end.getTime() - begin.getTime()) / 1000));
                } else if (statuses.contains(5)) {
                    Date end = ListUtils.filter(list, o -> o.getStatus().equals(5)).get(0).getCreateTime();
                    finishRejectOrder.put(list.get(0).getOrderId(), (int)((end.getTime() - begin.getTime()) / 1000));
                }
            }
        }

        for (int i = 1; i <= 3; i++) {
            System.out.println("步骤" + i + "次数:" + approveStepCount.get(i) + ",总耗时:" +
                    approveTotalCount.get(i) + "秒，平均耗时:" + (approveTotalCount.get(i) * 1.0 / approveStepCount.get(i)));
        }

        // 订单概况
        System.out.println("通过订单数：" + finishPassOrder.size() + "，平均处理时长:" +
                (NumberUtils.sum(finishPassOrder.values()).longValue() * 1.0 / finishPassOrder.size()));
        System.out.println("驳回订单数：" + finishRejectOrder.size() + "，平均处理时长:" +
                (NumberUtils.sum(finishRejectOrder.values()).longValue() * 1.0 / finishRejectOrder.size()));

    }



}
