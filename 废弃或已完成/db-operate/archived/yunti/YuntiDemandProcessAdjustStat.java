package com.pugwoo.dboperate.yunti;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dboperate.entity.yunti.YuntiRequirementOrderDetailVersionInfoDO;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.lang.DateUtils;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import jakarta.annotation.Resource;
import java.sql.Timestamp;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * cvm需求预测调整比例统计
 */
@SpringBootTest
public class YuntiDemandProcessAdjustStat {

    @Resource
    private DBHelper yuntiIdcDBHelper;

    @Test
    public void test() throws Exception {

        List<YuntiRequirementOrderDetailVersionInfoDO> list = yuntiIdcDBHelper.getAll
                (YuntiRequirementOrderDetailVersionInfoDO.class,
                "where create_time>='2021-01-01' AND is_adjust_patch_data=0 ");

        Map<String, List<YuntiRequirementOrderDetailVersionInfoDO>> map =
                ListUtils.toMapList(list, o -> o.getOrderId(), o -> o);

        Map<String, Integer> result = new HashMap<>();

        for (Map.Entry<String, List<YuntiRequirementOrderDetailVersionInfoDO>> e : map.entrySet()) {
            String orderId = e.getKey();
            List<YuntiRequirementOrderDetailVersionInfoDO> details = e.getValue();

            Map<Integer, List<YuntiRequirementOrderDetailVersionInfoDO>> versionMap =
                    ListUtils.toMapList(details, o -> o.getVersion(), o -> o);
            if (versionMap.size() <= 2) {
                continue; // 自动提单的
            }

            List<Integer> versions = ListUtils.toList(versionMap.keySet());
            ListUtils.sortAscNullLast(versions, o -> o);

            List<YuntiRequirementOrderDetailVersionInfoDO> version1 = versionMap.get(versions.get(1));
            List<YuntiRequirementOrderDetailVersionInfoDO> version2 = versionMap.get(versions.get(versions.size() - 1));

            if (isDifferent(version1, version2)) {
                Timestamp createTime = version2.get(0).getCreateTime();
                String date = DateUtils.format(createTime, "yyyyMM");
                if (result.get(date) == null) {
                    result.put(date, 0);
                }
                result.put(date, result.get(date) + 1);
               // System.out.println("===" + orderId);
            }
        }

        System.out.println(JSON.toJson(result));
    }

    private boolean isDifferent(List<YuntiRequirementOrderDetailVersionInfoDO> version1,
                                List<YuntiRequirementOrderDetailVersionInfoDO> version2) {
        if (version1.size() != version2.size()) {
            return true;
        }

        Set<Integer> v2Id = new HashSet<>();
        for (YuntiRequirementOrderDetailVersionInfoDO v1 : version1) {
            boolean isV1Exist = false;
            for (YuntiRequirementOrderDetailVersionInfoDO v2 : version2) {
                if (!v2Id.contains(v2.getVid()) && isEquals(v1, v2)) {
                    v2Id.add(v2.getVid());
                    isV1Exist = true;
                }
            }
            if (!isV1Exist) {
                return true;
            }
        }

        return false;
    }

    private boolean isEquals(YuntiRequirementOrderDetailVersionInfoDO a,
                             YuntiRequirementOrderDetailVersionInfoDO b) {
        return Objects.equals(a.getCityId(), b.getCityId())
                && Objects.equals(a.getInstanceModel(), b.getInstanceModel())
                && a.getCvmAmount().compareTo(b.getCvmAmount()) == 0
                && Objects.equals(a.getCbsResClassId(), b.getCbsResClassId())
                && Objects.equals(a.getAllDiskAmount(), b.getAllDiskAmount())
                && Objects.equals(a.getProjectName(), b.getProjectName())
                && Objects.equals(a.getPlanProductId(), b.getPlanProductId())
                && Objects.equals(a.getCoreAmount(), b.getCoreAmount())
                && Objects.equals(a.getUseTime(), b.getUseTime())
                && Objects.equals(a.getZoneId(), b.getZoneId());
    }

}
