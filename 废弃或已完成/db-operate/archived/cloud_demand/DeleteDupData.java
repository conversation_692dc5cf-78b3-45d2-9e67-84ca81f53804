package com.pugwoo.dboperate.archived.cloud_demand;

import com.pugwoo.dbhelper.DBHelper;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 用于移除重复的数据
 */
@SpringBootTest
public class DeleteDupData {

    @Resource
    private DBHelper cloudDemandIdcDBHelper;

    @Test
    public void remove() {
        List<IndustryDemandRegionZoneInstanceTypeDictDO> dups = cloudDemandIdcDBHelper.getRaw(IndustryDemandRegionZoneInstanceTypeDictDO.class,
                "SELECT * FROM industry_demand_region_zone_instance_type_dict \n" +
                        "GROUP BY region_str_id,zone_str_id,instance_type,instance_model\n" +
                        "HAVING COUNT(*) > 1");
        System.out.println(dups.size());

        for (IndustryDemandRegionZoneInstanceTypeDictDO dup : dups) {
            List<IndustryDemandRegionZoneInstanceTypeDictDO> all = cloudDemandIdcDBHelper.getAll(IndustryDemandRegionZoneInstanceTypeDictDO.class,
                    "where region_str_id=? and zone_str_id=? and instance_type=? and instance_model=? order by id desc",
                    dup.getRegionStrId(), dup.getZoneStrId(), dup.getInstanceType(), dup.getInstanceModel());
            if (all.size() > 1) {
                // 移除最新的一条，剩下的删除
                all.remove(0);
                int rows = cloudDemandIdcDBHelper.deleteByKey(all);
                System.out.println("delete region:" + all.get(0).getRegionStrId()
                        + ",zone:" + all.get(0).getZoneStrId() + ",instance_model" +
                        all.get(0).getInstanceModel() + ",total rows:" + rows);
            }
        }
    }

}
