package com.pugwoo.dboperate.archived.cloud_demand;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.util.Date;

@Data
@ToString
@Table("industry_demand_region_zone_instance_type_dict")
public class IndustryDemandRegionZoneInstanceTypeDictDO {

    /**
     * id<br/>Column: [id]
     */
    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    /**
     * 删除标记<br/>Column: [deleted]
     */
    @Column(value = "deleted", softDelete = {"0", "1"})
    private Integer deleted;

    /*
     * 创建时间<br/>Column: [create_time]
     */
    @Column(value = "create_time", setTimeWhenInsert = true)
    private Date createTime;

    /**
     * 更新时间<br/>Column: [update_time]
     */
    @Column(value = "update_time", setTimeWhenInsert = true, setTimeWhenUpdate = true)
    private Date updateTime;

    /**
     * 创建人<br/>Column: [creator]
     */
    @Column(value = "creator", insertValueScript = "cloud.demand.app.common.utils.LoginUtils.getUserName()")
    private String creator;

    /**
     * 最近改动人<br/>Column: [updater]
     */
    @Column(value = "updater", updateValueScript = "cloud.demand.app.common.utils.LoginUtils.getUserName()",
            insertValueScript = "cloud.demand.app.common.utils.LoginUtils.getUserName()")
    private String updater;

    /**
     * 地域字符ID<br/>Column: [region_str_id]
     */
    @Column(value = "region_str_id")
    private String regionStrId;

    /**
     * 地域名<br/>Column: [region_name]
     */
    @Column(value = "region_name")
    private String regionName;

    /**
     * 地域名缩写<br/>Column: [region_short_name]
     */
    @Column(value = "region_short_name")
    private String regionShortName;

    /**
     * 地域状态<br/>Column: [region_status]
     */
    @Column(value = "region_status")
    private String regionStatus;

    /**
     * 可用区字符ID<br/>Column: [zone_str_id]
     */
    @Column(value = "zone_str_id")
    private String zoneStrId;

    /**
     * 可用区数字ID<br/>Column: [zone_num_id]
     */
    @Column(value = "zone_num_id")
    private Integer zoneNumId;

    /**
     * 可用区名<br/>Column: [zone_name]
     */
    @Column(value = "zone_name")
    private String zoneName;

    /**
     * 可用区状态<br/>Column: [zone_status]
     */
    @Column(value = "zone_status")
    private String zoneStatus;

    /**
     * 实例类型<br/>Column: [instance_type]
     */
    @Column(value = "instance_type")
    private String instanceType;

    /**
     * 实例类型中文名<br/>Column: [instance_type_name]
     */
    @Column(value = "instance_type_name")
    private String instanceTypeName;

    /**
     * 实例类型中文名<br/>Column: [instance_type_name]
     */
    @Column(value = "instance_model")
    private String instanceModel;

    /**
     * 是否有源头<br/>Column: [has_source]
     */
    @Column(value = "has_source")
    private Boolean hasSource;

    /**
     * 地域名中文缩写<br/>Column: [region_short_ch_name]
     */
    @Column(value = "region_short_ch_name")
    private String regionShortChName;

    /**
     * 解析的核心数
     */
    @Column(value = "parse_core")
    private Integer parseCore;

    /**
     * 解析的核心数
     */
    @Column(value = "parse_ram")
    private Integer parseRam;

}
