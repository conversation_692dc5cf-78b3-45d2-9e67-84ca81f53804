package com.pugwoo.dboperate.archived.cmdb_clean;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dboperate.entity.cmdb.DeviceDO;
import com.pugwoo.dboperate.entity.cmdb.EquipmentPositionDO;
import com.pugwoo.dboperate.entity.cmdb.IpEquipmentDO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class CleanService {

    /**抽取出来是为了事务*/
    @Transactional("cmdbDevTransactionManager")
    public void migrateOne(DBHelper dbHelper, EquipmentPositionDO ep) {
        DBHelper.setTableName(EquipmentPositionDO.class, "equipment_position_del");
        dbHelper.insert(ep);
        DBHelper.setTableName(EquipmentPositionDO.class, "equipment_position");

        dbHelper.delete(EquipmentPositionDO.class, "where id=?", ep.getId());
    }

    /**抽取出来是为了事务*/
    @Transactional("cmdbDevTransactionManager")
    public void migrateDevice(DBHelper dbHelper, DeviceDO deviceDO) {
        DBHelper.setTableName(DeviceDO.class, "t_device_del");
        dbHelper.insert(deviceDO);
        DBHelper.setTableName(DeviceDO.class, "t_device");

        dbHelper.delete(DeviceDO.class, "where id=?", deviceDO.getId());
    }

    /**抽取出来是为了事务*/
    @Transactional("cmdbDevTransactionManager")
    public void migrateIpEquipment(DBHelper dbHelper, IpEquipmentDO ipEquipmentDO) {
        DBHelper.setTableName(IpEquipmentDO.class, "ip_equipment_del");
        dbHelper.insert(ipEquipmentDO);
        DBHelper.setTableName(IpEquipmentDO.class, "ip_equipment");

        dbHelper.delete(IpEquipmentDO.class, "where id=?", ipEquipmentDO.getId());
    }



}
