package com.pugwoo.dboperate.archived.cmdb_clean;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dboperate.entity.cmdb.EquipmentPositionDO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

@SpringBootTest
public class CmdbPositionDeleteTask {

    @Autowired @Qualifier("cmdbDevDBHelper")
    private DBHelper dbHelper;
    @Autowired
    private CleanService cleanService;

    @Test
    public void clean() {
        String sql = "SELECT a.* FROM equipment_position a LEFT JOIN t_device b" +
                " ON a.`EquipmentId`=b.`id` AND a.`ConfigItemId`=2" +
                " WHERE a.`ConfigItemId`=2 AND b.id IS NULL" +
                " ORDER BY a.`id` LIMIT 10";

        List<EquipmentPositionDO> list = dbHelper.getRaw(EquipmentPositionDO.class, sql);

        for (EquipmentPositionDO one : list) {
            cleanService.migrateOne(dbHelper, one);
        }

    }

}
