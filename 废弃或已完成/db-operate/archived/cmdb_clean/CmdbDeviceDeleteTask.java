package com.pugwoo.dboperate.archived.cmdb_clean;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.impl.SpringJdbcDBHelper;
import com.pugwoo.dboperate.entity.cmdb.DeviceDO;
import com.pugwoo.wooutils.lang.DateUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Calendar;
import java.util.Date;
import java.util.List;

@SpringBootTest
public class CmdbDeviceDeleteTask {

    @Autowired
    @Qualifier("cmdbDevDBHelper")
    private DBHelper dbHelper;
    @Autowired
    private CleanService cleanService;

    @Test
    public void clean() {
        // 不要检查外键
        ((SpringJdbcDBHelper) dbHelper).getJdbcTemplate().execute("SET FOREIGN_KEY_CHECKS=0");

        Date now = new Date();
        Date sevenDaysBefore = DateUtils.addTime(now, Calendar.DATE, -7);

        String sql = "select * from t_device where isdel=1 and LastUpdate<? limit 100";
        List<DeviceDO> list = dbHelper.getRaw(DeviceDO.class, sql, sevenDaysBefore);

        for (DeviceDO one : list) {
            cleanService.migrateDevice(dbHelper, one);
        }

    }
}
