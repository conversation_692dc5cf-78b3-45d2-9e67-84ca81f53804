package com.pugwoo.dboperate.archived.cmdb_clean;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.impl.SpringJdbcDBHelper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
public class CmdbIpEquipmentDeleteTask {

    @Autowired
    @Qualifier("cmdbDevDBHelper")
    private DBHelper dbHelper;
    @Autowired
    private CleanService cleanService;

    @Test
    public void clean() {
        // 不要检查外键
        ((SpringJdbcDBHelper) dbHelper).getJdbcTemplate().execute("SET FOREIGN_KEY_CHECKS=0");


        // 暂不清理这个，等清理了服务器先
    }

}
