package com.pugwoo.dboperate.archived.others2021;

import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.lang.NumberUtils;
import com.pugwoo.wooutils.task.ExecuteThem;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.List;
import java.util.Map;

@SpringBootTest
public class ClickhouseLabTests {

    @Autowired
    @Qualifier("clickhouseLabJdbcTemplate")
    private JdbcTemplate jdbcTemplate;

    @Autowired @Qualifier("idcrmIdcJdbcTemplate")
    private JdbcTemplate idcrmJdbcTemplate;

    @Test
    public void hello() {
        System.out.println("jdbcTemplate: " + jdbcTemplate);
        List<Map<String, Object>> list = jdbcTemplate.queryForList("select * from account limit 100");
        list.forEach(o -> System.out.println(JSON.toJson(o)));
    }

    /**同步数全通的机位数据过来，大概是200万条数据*/
    @Test
    public void migrateLocateData() {

        Long lastId = null;

        while(true) {
            List<Map<String, Object>> list = idcrmJdbcTemplate.queryForList("select LRID,SubZoneName,SubZoneId,ModuleId,EnableFlag,RegionName" +
                    " from sto_rmdb_locate " + (lastId == null ? "" : " where LRID>" + lastId) + " order by LRID limit 10000");
            if (list.isEmpty()) {
                break;
            }

            lastId = NumberUtils.parseLong(list.get(list.size() - 1).get("LRID"));

            StringBuilder sql = new StringBuilder();
            sql.append("insert into testdb.sto_rmdb_locate(LRID,SubZoneName,SubZoneId,ModuleId,EnableFlag,RegionName)"
                + "values");
            for(Map<String, Object> row : list) {
                Object _enableFlag = row.get("EnableFlag");
                Integer enableFlag = 0;
                if (_enableFlag != null && _enableFlag.toString().equals("true")) {
                    enableFlag = 1;
                }

                sql.append("(").append(row.get("LRID")).append(",'").append(row.get("SubZoneName")).append("',");
                sql.append(row.get("SubZoneId")).append(",").append(row.get("ModuleId")).append(",");
                sql.append(enableFlag).append(",'").append(row.get("RegionName")).append("'),");
            }

            int rows = jdbcTemplate.update(sql.substring(0, sql.length() - 1));
            System.out.println("insert clickhouse lastId:" + lastId);

        }

    }

    /**同步数全通的module数据*/
    @Test
    public void migrateModuleData() {

        Long lastId = null;

        while(true) {
            List<Map<String, Object>> list = idcrmJdbcTemplate.queryForList("select ModId,EnableFlag,ModName" +
                    " from bas_dis_module " + (lastId == null ? "" : " where ModId>" + lastId) + " order by ModId limit 10000");
            if (list.isEmpty()) {
                break;
            }

            lastId = NumberUtils.parseLong(list.get(list.size() - 1).get("ModId"));

            StringBuilder sql = new StringBuilder();
            sql.append("insert into testdb.bas_dis_module(ModId,EnableFlag,ModName)"
                    + "values");
            for(Map<String, Object> row : list) {
                sql.append("(").append(row.get("ModId")).append(",");
                sql.append(row.get("EnableFlag")).append(",'").append(row.get("ModName")).append("'),");
            }

            int rows = jdbcTemplate.update(sql.substring(0, sql.length() - 1));
            System.out.println("insert clickhouse lastId:" + lastId);
        }

    }

    @Test
    public void heavyRequest() {

        ExecuteThem executeThem = new ExecuteThem(20);

        for(int i = 0; i < 10000; i++) {
            executeThem.add(new Runnable() {
                @Override
                public void run() {
                    List<Map<String, Object>> result = jdbcTemplate.queryForList("SELECT t_locate.SubZoneName AS campus_name,\n" +
                            "       any(t_locate.SubZoneId) AS campus_id,\n" +
                            "       COUNT(*) AS num\n" +
                            "FROM sto_rmdb_locate t_locate\n" +
                            "LEFT JOIN bas_dis_module t_module ON t_module.ModId=t_locate.ModuleId\n" +
                            "WHERE t_locate.EnableFlag=1 AND t_module.EnableFlag=1\n" +
                            "  AND t_locate.RegionName != '待定'\n" +
                            "  AND t_module.ModName NOT IN ('资源中转(未确认)')\n" +
                            "  AND t_module.ModName NOT LIKE '%测试%'\n" +
                            "  AND t_locate.SubZoneName != ''\n" +
                            "GROUP BY t_locate.SubZoneName");

                    System.out.println(JSON.toJson(result));
                }
            });
        }

        executeThem.waitAllTerminate();

        // TODO 还缺打印qps
    }

}
