package com.pugwoo.dboperate.archived.others2021;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dboperate.entity.cmdb.CmdbQuerySettingDO;
import com.pugwoo.dboperate.model.ProcessListModel;
import com.pugwoo.dboperate.utils.CmdbUtils;
import com.pugwoo.dboperate.utils.QuerySettingModel;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.string.StringTools;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

@SpringBootTest
public class CmdbDevMysqlTests {

    @Autowired
    @Qualifier("cmdbDevJdbcTemplate")
    private JdbcTemplate jdbcTemplate;

    @Autowired
    @Qualifier("cmdbRefactorDBHelper")
    private DBHelper dbHelper;

    @Test
    public void exportQuerySettingToDB() throws Exception {
        List<QuerySettingModel.SearchScheme> schemas = CmdbUtils.getSchemas();

        for (QuerySettingModel.SearchScheme schema : schemas) {

            List<QuerySettingModel.ResultColumn> resultColumn = schema.getResultColumn();
            for (QuerySettingModel.ResultColumn column : resultColumn) {

                CmdbQuerySettingDO settingDO = new CmdbQuerySettingDO();
                settingDO.setSchemaId(schema.getId());
                settingDO.setSchemaName(schema.getName());
                settingDO.setColumnId(column.getId());
                settingDO.setColumnName(column.getName());

                dbHelper.insert(settingDO);
            }
        }

       // System.out.println(schemas);
    }

    /**
     * 这个主要是获取在mysql执行的sql，并打印出来，不关心执行了多久
     */
    @Test
    public void testProcessList() {
        Set<Long> existIds = new HashSet<>();

        while (true) {
            List<Map<String, Object>> processListRaw = jdbcTemplate.queryForList("show processlist");
            List<ProcessListModel> processList = ListUtils.transform(processListRaw,
                    o -> JSON.parse(JSON.toJson(o), ProcessListModel.class));

            for (ProcessListModel process : processList) {
                if (process == null || !"Query".equalsIgnoreCase(process.getCommand()) || StringTools.isBlank(process.getInfo())) {
                    continue;
                }
                if (existIds.contains(process.getId())) {
                    continue;
                }
                StringBuilder sb = new StringBuilder();
                sb.append("db:"+ process.getDb() + ",host:" + process.getHost() + ",sql:" + process.getInfo());
                System.out.println(sb.toString());

                // System.out.println(JSON.toJson(process));
                existIds.add(process.getId());
            }
        }
    }

}
