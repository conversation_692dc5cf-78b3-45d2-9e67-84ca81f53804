package com.pugwoo.dboperate.archived.others2021;

import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.task.ExecuteThem;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.List;
import java.util.Map;

@SpringBootTest
class CmdbLabMysqlTests {

	@Autowired @Qualifier("cmdbLabJdbcTemplate")
	private JdbcTemplate jdbcTemplate;

    @Test
	public void hello() {
		System.out.println("jdbcTemplate: " + jdbcTemplate);
		List<Map<String, Object>> list = jdbcTemplate.queryForList("select * from t_device limit 100");
		list.forEach(o -> System.out.println(JSON.toJson(o)));
	}

	/**
	 * 模拟大量的慢请求压到mysql上，然后在mysql上alter表，看看能否复现生产的情况
	 */
	@Test
	public void heavyLoad() {

		// 先试试就一直查同一条SQL
		int concurrent = 100;

		ExecuteThem executeThem = new ExecuteThem(concurrent);

		for(int i = 0; i < 10000; i++) {

			executeThem.add(new Runnable() {
				@Override
				public void run() {
					long start = System.currentTimeMillis();

					List<Map<String, Object>> results = jdbcTemplate.queryForList("select idc_parent.id as IdcpId, (select group_concat(idc_network.name SEPARATOR ';') from rs_idcnetworkmodule_position inner join idc_network on rs_idcnetworkmodule_position.IdcNetworkModuleId=idc_network.id where idc_network.type=0 and PositionId=i.id) as InnerIdcnName, i.id as PosId, i.code as PosCode, idc_safedomain.name as DmnName, t_idc.name as IdcName, (select group_concat(idc_network.name SEPARATOR ';') from rs_idcnetworkmodule_position inner join idc_network on rs_idcnetworkmodule_position.IdcNetworkModuleId=idc_network.id where idc_network.type=1 and PositionId=i.id) as OuterIdcnName, i.InnerSwitchPort as PosInnerSwitchPort, i.OuterSwitchPort as PosOuterSwitchPort, idc_safedomain.StructureVersion as DmnStructureVersion, dic_net_version.name as DmnStructureVersionName, idc_parent.name as IdcpName, t_idc.id as IdcId, t_equipment.IsSideBand as RckIsSideBand from idc_parent left join t_idc on idc_parent.id=t_idc.idcparentid  left join t_equipment on t_equipment.idc_id=t_idc.id  left join idc_position i on t_equipment.id=i.EquipmentId  left join idc_safedomain on i.SafeDomain=idc_safedomain.id  left join dic_net_version on idc_safedomain.StructureVersion=dic_net_version.id where i.InnerSwitchPort  in( 'SZ-LJ-0501-A05-R6220XT-LA-01-T0/19' ) limit 0, 1000");

					long end = System.currentTimeMillis();

					System.out.println("result size:" + results.size() + ",cost:" + (end -start) + "ms");
				}
			});

		}

		executeThem.waitAllTerminate();

	}

}
