package com.pugwoo.dboperate.archived.others2021;

import com.pugwoo.wooutils.json.JSON;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.List;
import java.util.Map;

/**
 * 生产环境的数全通数据库
 */
@SpringBootTest
public class IdcrmIdcTests {

    @Autowired
    @Qualifier("idcrmIdcJdbcTemplate")
    private JdbcTemplate jdbcTemplate;

    @Test
    public void hello() {
        System.out.println("jdbcTemplate: " + jdbcTemplate);
        List<Map<String, Object>> list = jdbcTemplate.queryForList(
                "select * from sto_rmdb_locate limit 100");
        list.forEach(o -> System.out.println(JSON.toJson(o)));
    }

}
