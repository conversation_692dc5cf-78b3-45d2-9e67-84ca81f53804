package com.pugwoo.dboperate.archived.mck.omd_inventory;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.lang.DateUtils;
import lombok.Data;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.FileInputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 解析运管库存的数据到麦肯锡的数据库
 */
@SpringBootTest
public class ParseOMDInventoryExcelToMckData {

    @Autowired
    private DBHelper mckIdcDBHelper;

    @Data
    public static class MyRow {
        @ExcelProperty(index = 0) // 第一列,index从0开始
        private String erpBiz;
        @ExcelProperty(index = 1)
        private String deviceSource;
        @ExcelProperty(index = 2)
        private String deviceType;
        @ExcelProperty(index = 3)
        private Integer count20220601;
        @ExcelProperty(index = 4)
        private Integer count20220701;
        @ExcelProperty(index = 5)
        private Integer count20220801;
        @ExcelProperty(index = 6)
        private Integer count20220901;
        @ExcelProperty(index = 7)
        private Integer count20221001;
        @ExcelProperty(index = 8)
        private Integer count20221101;
        @ExcelProperty(index = 9)
        private Integer count20221201;
        @ExcelProperty(index = 10)
        private Integer count20230101;
    }

    @Test
    public void parse() throws Exception {
        FileInputStream in = new FileInputStream("d:/运管库存数据2022年.xlsx");

        final List<OmdInventoryDO> result = new ArrayList<>();

        EasyExcel.read(
                in, MyRow.class,
                new AnalysisEventListener<MyRow>() {
                    @Override
                    public void invoke(MyRow row, AnalysisContext analysisContext) {
                        // 这里看看判断结束是不是要用户自己判断，所有行为空就是结束
                        // 结论：不用！所以下面这几行注释的可以不用加
                        //      当然，row里的数值的校验还是要业务自己去校验
//                        if (ObjUtils.allFieldIsNull(o)) {
//                            log.info("读到第一个空行，结束");
//                            throw new ExcelAnalysisStopException();
//                        }
                        // System.out.println(JSON.toJson(row));

                        OmdInventoryDO d = new OmdInventoryDO();
                        d.setErpBiz(row.getErpBiz());
                        d.setDeviceSource(row.getDeviceSource());
                        d.setDeviceType(row.getDeviceType());

                        {
                            OmdInventoryDO d1 = JSON.clone(d);
                            d1.setStatTime(DateUtils.parseLocalDate("2022-06-01"));
                            d1.setCount(row.getCount20220601());
                            if (d1.getCount() != null) {
                                result.add(d1);
                            }
                        }
                        {
                            OmdInventoryDO d1 = JSON.clone(d);
                            d1.setStatTime(DateUtils.parseLocalDate("2022-07-01"));
                            d1.setCount(row.getCount20220701());
                            if (d1.getCount() != null) {
                                result.add(d1);
                            }
                        }
                        {
                            OmdInventoryDO d1 = JSON.clone(d);
                            d1.setStatTime(DateUtils.parseLocalDate("2022-08-01"));
                            d1.setCount(row.getCount20220801());
                            if (d1.getCount() != null) {
                                result.add(d1);
                            }
                        }
                        {
                            OmdInventoryDO d1 = JSON.clone(d);
                            d1.setStatTime(DateUtils.parseLocalDate("2022-09-01"));
                            d1.setCount(row.getCount20220901());
                            if (d1.getCount() != null) {
                                result.add(d1);
                            }
                        }
                        {
                            OmdInventoryDO d1 = JSON.clone(d);
                            d1.setStatTime(DateUtils.parseLocalDate("2022-10-01"));
                            d1.setCount(row.getCount20221001());
                            if (d1.getCount() != null) {
                                result.add(d1);
                            }
                        }
                        {
                            OmdInventoryDO d1 = JSON.clone(d);
                            d1.setStatTime(DateUtils.parseLocalDate("2022-11-01"));
                            d1.setCount(row.getCount20221101());
                            if (d1.getCount() != null) {
                                result.add(d1);
                            }
                        }
                        {
                            OmdInventoryDO d1 = JSON.clone(d);
                            d1.setStatTime(DateUtils.parseLocalDate("2022-12-01"));
                            d1.setCount(row.getCount20221201());
                            if (d1.getCount() != null) {
                                result.add(d1);
                            }
                        }
                        {
                            OmdInventoryDO d1 = JSON.clone(d);
                            d1.setStatTime(DateUtils.parseLocalDate("2023-01-01"));
                            d1.setCount(row.getCount20230101());
                            if (d1.getCount() != null) {
                                result.add(d1);
                            }
                        }
                    }

                    @Override
                    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                        // 当整个文档解析完时，会调用这个方法
                        mckIdcDBHelper.insertBatchWithoutReturnId(result);
                    }

                    /**
                     * 这里会一行行的返回头（这个方法可以不重写，需要拿到行头时可以重写来拿）
                     */
                    @Override
                    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
                        // headMap的key是index，value是标题
                        System.out.println("行头:" + JSON.toJson(headMap));
                    }
                }
        ).sheet(0).headRowNumber(2).doRead(); // 这里指定第几个sheet，标题有几行

        in.close();
    }

}
