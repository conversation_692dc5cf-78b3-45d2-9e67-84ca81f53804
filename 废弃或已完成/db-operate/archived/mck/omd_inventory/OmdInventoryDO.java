package com.pugwoo.dboperate.archived.mck.omd_inventory;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDate;

/**
 * 运管库存
 */
@Data
@ToString
@Table("omd_inventory")
public class OmdInventoryDO {

    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    /** 数据日期<br/>Column: [stat_time] */
    @Column(value = "stat_time")
    private LocalDate statTime;

    /** erp业务分类<br/>Column: [erp_biz] */
    @Column(value = "erp_biz")
    private String erpBiz;

    /** 设备来源<br/>Column: [device_source] */
    @Column(value = "device_source")
    private String deviceSource;

    /** 设备类型<br/>Column: [device_type] */
    @Column(value = "device_type")
    private String deviceType;

    /** 设备数量<br/>Column: [count] */
    @Column(value = "count")
    private Integer count;

}