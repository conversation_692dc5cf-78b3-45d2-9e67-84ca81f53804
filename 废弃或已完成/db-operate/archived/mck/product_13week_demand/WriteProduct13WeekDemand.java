package com.pugwoo.dboperate.archived.mck.product_13week_demand;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.wooutils.collect.MapUtils;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.lang.NumberUtils;
import com.pugwoo.wooutils.net.Browser;
import com.pugwoo.wooutils.net.HttpResponse;
import com.pugwoo.wooutils.string.StringTools;
import lombok.Data;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@SpringBootTest
public class WriteProduct13WeekDemand {

    @Resource
    private DBHelper rrpIdcDBHelper;
    @Resource
    private DBHelper mckIdcDB<PERSON>elper;

    private String cookie = "x_host_key_access_https=39e9187e9de3d8a7bb7cb0c34f31086a503eeb41_s; x-client-ssid=1827b6cf66e-c5a26775ff000bb22877c84e3c3d652574ff3615; x-tofapi-host-key=1827b6cf6bb-d79a4c4fdfc49dcd52cf0bc1a8b97e5c88e88d21; sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%22nickxie%22%2C%22first_id%22%3A%22182803815376e6-0f21924bb61d5b8-673b5753-921600-18280381538629%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E7%9B%B4%E6%8E%A5%E6%B5%81%E9%87%8F%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80%22%2C%22%24latest_referrer%22%3A%22%22%7D%2C%22%24device_id%22%3A%22182803815376e6-0f21924bb61d5b8-673b5753-921600-18280381538629%22%7D; x_host_key_access=39e9187e9de3d8a7bb7cb0c34f31086a503eeb41_s; userName=nickxie; LoginName=nickxie; _ra=_ra1660220440300.0.7685795750348465; ERP_USERNAME=nickxie; ERP_REAL_USERNAME=nickxie; pgv_info=ssid=s6943018787; pgv_pvid=4571783450; x-host-key-front_https=39e9187e9de3d8a7bb7cb0c34f31086a503eeb41; x-host-key-oaback_https=39e9187e9de3d8a7bb7cb0c34f31086a503eeb41; x-host-key-ngn=18363469cce-986add240ec08a2bd6e71384f1218a972072f341; DiggerTraceId=1049a750-3f1c-11ed-9520-9bd00b19accb; fileDownload=true; x-host-key-front=18450d68e5e-b295fcd688920c564959a0435006b7c37658673d; x-imp-host-key=1848ee6d212-1999060a58adb1049e553681ebe984534192c400; ERP_USERNAME=nickxie; tkex_csrf_token=s681qz1m5p; t_uid=nickxie; bk_uid=nickxie; bk_ticket=-V6Vzn9YDrSONWR06fj2S8xr5pY0eSX7uxm3oBQBpk0; km_u=69a44e0523ce978a0a5c49d28535e05db3a1478cd97a95b83e1f582994702fca6a14f7949ac8c3fa; km_uid=nickxie; RIO_TCOA_TICKET=tof:TOF4TeyJ2IjoiNCIsInRpZCI6IktqUkdZd2hoN3FNNnpKa2lwSFF2NVdzaW5GMjBTYUVKIiwiaXNzIjoiMTAuOTkuMjA4LjU3IiwiaWF0IjoiMjAyMy0wMS0wNFQxMDozOToyMC4zOTE2NjEzNDcrMDg6MDAiLCJhdWQiOiIzMC4yMC4xODUuMTMiLCJoYXNoIjoiOURGODYwODJFMTAyOUMxNDNENzE4RjQ0RENFRTQ4OTYwQUEwNUQ5OUY4NzU3OUM1MzJGNDk0NjM5RjYxQzE5NCIsIm5oIjoiODYyNUE5RkQ5RDc4QzBBODJBRDdFQjNFRDZERjVFMzQ1QzdFNkMwMkRBODE0MERFMEE0N0QwMUI1MTBCMjQyRSJ9; RIO_TCOA_TICKET_HTTPS=tof:TOF4TeyJ2IjoiNCIsInRpZCI6IktqUkdZd2hoN3FNNnpKa2lwSFF2NVdzaW5GMjBTYUVKIiwiaXNzIjoiMTAuOTkuMjA4LjU3IiwiaWF0IjoiMjAyMy0wMS0wNFQxMDozOToyMC4zOTE2NjEzNDcrMDg6MDAiLCJhdWQiOiIzMC4yMC4xODUuMTMiLCJoYXNoIjoiOURGODYwODJFMTAyOUMxNDNENzE4RjQ0RENFRTQ4OTYwQUEwNUQ5OUY4NzU3OUM1MzJGNDk0NjM5RjYxQzE5NCIsIm5oIjoiODYyNUE5RkQ5RDc4QzBBODJBRDdFQjNFRDZERjVFMzQ1QzdFNkMwMkRBODE0MERFMEE0N0QwMUI1MTBCMjQyRSJ9; TCOA_TICKET=TOF4TeyJ2IjoiNCIsInRpZCI6IktqUkdZd2hoN3FNNnpKa2lwSFF2NVdzaW5GMjBTYUVKIiwiaXNzIjoiMTAuOTkuMjA4LjU3IiwiaWF0IjoiMjAyMy0wMS0wNFQxMDozOToyMC4zOTE2NjEzNDcrMDg6MDAiLCJhdWQiOiIzMC4yMC4xODUuMTMiLCJoYXNoIjoiOURGODYwODJFMTAyOUMxNDNENzE4RjQ0RENFRTQ4OTYwQUEwNUQ5OUY4NzU3OUM1MzJGNDk0NjM5RjYxQzE5NCIsIm5oIjoiODYyNUE5RkQ5RDc4QzBBODJBRDdFQjNFRDZERjVFMzQ1QzdFNkMwMkRBODE0MERFMEE0N0QwMUI1MTBCMjQyRSJ9";

    @Data
    public static class Resp {
        private List<Map<String, Object>> data;

        public List<Product13weekDemandDO> trans(FlowVO flow) {
            List<Product13weekDemandDO> list = new ArrayList<>();

            for (Map<String, Object> map : data) {
                Product13weekDemandDO d = toBasicDO(map, flow);
                for (Map.Entry<String, Object> e : map.entrySet()) {
                    if (e.getKey().matches("\\d{4}-\\d{2}")) {
                        String date = e.getKey();
                        Product13weekDemandDO d1 = JSON.clone(d);
                        d1.setDemandDate(date);
                        Map<String, Object> value = (Map<String, Object>) e.getValue();
                        Object num = value.get("num");
                        if (num != null && StringTools.isNotBlank(num.toString())) {
                            d1.setNum(NumberUtils.parseInt(num));
                        }
                        Object curplanAdjust = value.get("curplan_adjust");
                        if (curplanAdjust != null && StringTools.isNotBlank(curplanAdjust.toString())) {
                            d1.setCurplanAdjust(NumberUtils.parseInt(curplanAdjust));
                        }
                        list.add(d1);
                    }
                }
            }
            return list;
        }

        private Product13weekDemandDO toBasicDO(Map<String, Object> map, FlowVO flow) {
            Product13weekDemandDO d = new Product13weekDemandDO();
            d.setPlanVersion(flow.getPlanVersion());
            d.setProduct(flow.getProduct());
            d.setModBusinessTypeName((String) map.get("mod_business_type_name"));
            d.setDemandType((String) map.get("demand_type"));
            d.setProjSetName((String) map.get("proj_set_name"));
            d.setRegion((String) map.get("region"));
            d.setZone((String) map.get("zone"));
            d.setCampus((String) map.get("campus"));
            d.setIndustry((String) map.get("industry"));
            d.setCustomerName((String) map.get("customer_name"));
            d.setDeviceType((String) map.get("deviceType"));
            d.setReasonType((String) map.get("reason_type"));
            d.setReason((String) map.get("reason"));
            d.setProductRemark((String) map.get("product_remark"));
            d.setResourceRemark((String) map.get("resource_remark"));
            return d;
        }
    }

    @Data
    public static class FlowVO {
        @Column(value = "id")
        private String id;

        @Column(value = "product")
        private String product;

        @Column(value = "plan_version")
        private String planVersion;
    }

    @Test
    public void gen() throws Exception {
        List<FlowVO> flows = rrpIdcDBHelper.getRaw(FlowVO.class,
                "SELECT f.id AS id, f.`product` AS product, c.plan_version AS plan_version\n" +
                        "FROM flow_info f\n" +
                        "LEFT JOIN rrp_config c ON f.rrp_config_id=c.id");
        System.out.println(flows);

        String url = "https://tcres.woa.com/api/rrp/get_plan";

        for (FlowVO flow : flows) {
            Map<String, Object> req = MapUtils.of("flow_id", flow.getId());
            Browser browser = new Browser();
            browser.addRequestHeader("Cookie", cookie);

            browser.disableGzip();
            HttpResponse resp = browser.postJson(url, req);
            String respBody = resp.getContentString();
            Resp respDTO = JSON.parse(respBody, Resp.class);

            mckIdcDBHelper.insertBatchWithoutReturnId(respDTO.trans(flow));
        }
    }

}
