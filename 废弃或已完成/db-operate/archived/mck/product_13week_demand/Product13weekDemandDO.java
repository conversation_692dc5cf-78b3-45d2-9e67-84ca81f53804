package com.pugwoo.dboperate.archived.mck.product_13week_demand;

import lombok.Data;
import lombok.ToString;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;

@Data
@ToString
@Table("product_13week_demand")
public class Product13weekDemandDO {

    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    /** 版本编号<br/>Column: [plan_version] */
    @Column(value = "plan_version")
    private String planVersion;

    /** 规划产品<br/>Column: [product] */
    @Column(value = "product")
    private String product;

    /** 需求月份<br/>Column: [demand_date] */
    @Column(value = "demand_date")
    private String demandDate;

    /** 业务类型<br/>Column: [mod_business_type_name] */
    @Column(value = "mod_business_type_name")
    private String modBusinessTypeName;

    /** 需求类型<br/>Column: [demand_type] */
    @Column(value = "demand_type")
    private String demandType;

    /** 项目类型<br/>Column: [proj_set_name] */
    @Column(value = "proj_set_name")
    private String projSetName;

    /** region<br/>Column: [region] */
    @Column(value = "region")
    private String region;

    /** zone<br/>Column: [zone] */
    @Column(value = "zone")
    private String zone;

    /** campus<br/>Column: [campus] */
    @Column(value = "campus")
    private String campus;

    /** 行业<br/>Column: [industry] */
    @Column(value = "industry")
    private String industry;

    /** 客户名称<br/>Column: [customer_name] */
    @Column(value = "customer_name")
    private String customerName;

    /** 机型<br/>Column: [device_type] */
    @Column(value = "device_type")
    private String deviceType;

    /** 需求数量<br/>Column: [num] */
    @Column(value = "num")
    private Integer num;

    /** 资源组调整后的数量<br/>Column: [curplan_adjust] */
    @Column(value = "curplan_adjust")
    private Integer curplanAdjust;

    /** 需求原因归类<br/>Column: [reason_type] */
    @Column(value = "reason_type")
    private String reasonType;

    /** 需求原因细项<br/>Column: [reason] */
    @Column(value = "reason")
    private String reason;

    /** 产品备注<br/>Column: [product_remark] */
    @Column(value = "product_remark")
    private String productRemark;

    /** 资源组备注<br/>Column: [resource_remark] */
    @Column(value = "resource_remark")
    private String resourceRemark;

}