package com.pugwoo.dboperate.archived.mck.product_13week_demand;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.util.Date;

@Data
@ToString
@Table("flow_info")
public class FlowInfoDO {

    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Integer id;

    @Column(value = "flowCreator")
    private String flowCreator;

    @Column(value = "currentStep")
    private Integer currentStep;

    @Column(value = "startTime")
    private Date startTime;

    @Column(value = "endTime")
    private Date endTime;

    @Column(value = "approver")
    private String approver;

    @Column(value = "updateTime")
    private Date updateTime;

    @Column(value = "product")
    private String product;

    @Column(value = "rrp_config_id")
    private Integer rrpConfigId;

    @Column(value = "updator")
    private String updator;

}
