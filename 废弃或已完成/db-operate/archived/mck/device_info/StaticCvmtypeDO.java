package com.pugwoo.dboperate.archived.mck.device_info;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.util.Date;

/**
 * 母机机型表
 */
@Data
@ToString
@Table("static_cvmtype")
public class StaticCvmtypeDO {

    /** vstation的母机机型<br/>Column: [cvmtype] */
    @Column(value = "cvmtype", isKey = true)
    private String cvmtype;

    /** 机型可售卖核数<br/>Column: [cpu_origin] */
    @Column(value = "cpu_origin")
    private Long cpuOrigin;

    /** 机型物理逻辑核数<br/>Column: [cpu_physic] */
    @Column(value = "cpu_physic")
    private Long cpuPhysic;

    /** 机型可售卖内存<br/>Column: [mem] */
    @Column(value = "mem")
    private Long mem;

    /** 机型可售卖GPU卡数<br/>Column: [gpusize] */
    @Column(value = "gpusize")
    private Long gpusize;

    /** 机型GPU芯片数<br/>Column: [gpuchip] */
    @Column(value = "gpuchip")
    private Long gpuchip;

    /** 机型GPU卡数<br/>Column: [gpucard] */
    @Column(value = "gpucard")
    private Long gpucard;

    /** 机型FPGA卡数<br/>Column: [fpga] */
    @Column(value = "fpga")
    private Long fpga;

    /** 机型pmem大小<br/>Column: [pmem] */
    @Column(value = "pmem")
    private Long pmem;

    /** 机型本地盘块数<br/>Column: [block] */
    @Column(value = "block")
    private Long block;

    /** 公司标准机型<br/>Column: [stdtype] */
    @Column(value = "stdtype")
    private String stdtype;

    /** 子机机型<br/>Column: [ginsfamily] */
    @Column(value = "ginsfamily")
    private String ginsfamily;

    /** 统计时间<br/>Column: [stattime] */
    @Column(value = "stattime")
    private Date stattime;

}