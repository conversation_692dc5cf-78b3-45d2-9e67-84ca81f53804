package com.pugwoo.dboperate.archived.mck.device_info;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.Map;

@SpringBootTest
public class GenDeviceInfo {

    @Resource
    private DBHelper mckIdcDBHelper;
    @Resource
    private DBHelper jxcTxyIdcDBHelper;
    @Resource
    private DBHelper idcrmIdcDBHelper;

    @Test
    public void gen() {
        List<StaticCvmtypeDO> all = jxcTxyIdcDBHelper.getAll(StaticCvmtypeDO.class);
        List<ServerPartsExtendedInfoPDO> all2 = idcrmIdcDBHelper.getAll(ServerPartsExtendedInfoPDO.class,
                "where default_flag=1");

        Map<String, StaticCvmtypeDO> allMap = ListUtils.toMap(all, o -> o.getCvmtype(), o -> o);

        List<DeviceTypeInfoDO> result = ListUtils.transform(all, o -> {
            DeviceTypeInfoDO d = new DeviceTypeInfoDO();
            d.setDeviceType(o.getCvmtype());
            d.setCpuLogicCore(o.getCpuPhysic().intValue() / 100);
            d.setMem(o.getMem().intValue()/ 1024);
            return d;
        });

        ListUtils.forEach(all2, o -> {
            if (!allMap.containsKey(o.getDeviceType())) {
                DeviceTypeInfoDO d = new DeviceTypeInfoDO();
                d.setDeviceType(o.getDeviceType());
                d.setCpuLogicCore(o.getCpuLogicCore());
                d.setMem(o.getMemoryTotalVol());
                result.add(d);
            }
        });

        mckIdcDBHelper.insertBatchWithoutReturnId(result);
    }

}
