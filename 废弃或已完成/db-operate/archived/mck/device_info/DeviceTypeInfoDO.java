package com.pugwoo.dboperate.archived.mck.device_info;

import lombok.Data;
import lombok.ToString;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;

@Data
@ToString
@Table("device_type_info")
public class DeviceTypeInfoDO {

    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    /** 设备类型<br/>Column: [device_type] */
    @Column(value = "device_type")
    private String deviceType;

    /** cpu逻辑核心数<br/>Column: [cpu_logic_core] */
    @Column(value = "cpu_logic_core")
    private Integer cpuLogicCore;

    /** 内存数，单位G<br/>Column: [mem] */
    @Column(value = "mem")
    private Integer mem;

}