package com.pugwoo.dboperate.archived.mck.device_info;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;

/**
 * 物理机型到CPU核心数的映射。
 * 服务器部件信息扩展表，来源于server_parts_composition表与server_cpu_extended_info表。
 * 主要用于数据集市使用和业务系统页面展示
 */
@Data
@Table("server_parts_extended_info")
public class ServerPartsExtendedInfoPDO {

    /** 主键<br/>Column: [id] */
    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Integer id;

    /** 设备类型<br/>Column: [device_type] */
    @Column(value = "device_type")
    private String deviceType;

    /** 处理器逻辑核数<br/>Column: [cpu_logic_core] */
    @Column(value = "cpu_logic_core")
    private Integer cpuLogicCore;

    /** GPU数量<br/>Column: [gpu_number]*/
    @Column(value = "gpu_number")
    private Integer gpuNumber;

    @Column("memory_total_vol")
    private Integer memoryTotalVol;

}
