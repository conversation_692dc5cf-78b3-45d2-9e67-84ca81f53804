package com.pugwoo.dboperate.archived.mck.diff_cvm_sale_inventory_summary_detail;

import lombok.Data;
import lombok.ToString;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;

@Data
@ToString
@Table("cvm_sale_inventory_summary_detail")
public class CvmSaleInventorySummaryDetailDO {

    @Column(value = "切片时间")
    private String 切片时间;

    @Column(value = "设备类型")
    private String 设备类型;

    @Column(value = "一级分类")
    private String 一级分类;

    @Column(value = "二级分类")
    private String 二级分类;

    @Column(value = "三级分类")
    private String 三级分类;

    @Column(value = "Zone")
    private String zone;

    @Column(value = "Region")
    private String region;

    @Column(value = "区域")
    private String 区域;

    @Column(value = "地域类型")
    private String 地域类型;

    @Column(value = "单台核心数")
    private Double 单台核心数;

    @Column(value = "总核心数")
    private Double 总核心数;

    @Column(value = "网卡类型")
    private String 网卡类型;

    @Column(value = "CPU平台")
    private String cPU平台;

}