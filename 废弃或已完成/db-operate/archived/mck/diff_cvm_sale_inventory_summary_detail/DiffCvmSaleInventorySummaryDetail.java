package com.pugwoo.dboperate.archived.mck.diff_cvm_sale_inventory_summary_detail;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.lang.NumberUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@SpringBootTest
public class DiffCvmSaleInventorySummaryDetail {

    @Autowired
    private DBHelper mckIdcDBHelper;

    @Test
    public void test() {
        List<String> dateList = mckIdcDBHelper.getRaw(String.class,
                "select distinct `切片时间`\n" +
                        "from cvm_sale_inventory_summary_detail\n" +
                        "order by `切片时间`");

        List<CvmSaleInventorySummaryDetailDO> last;
        List<CvmSaleInventorySummaryDetailDO> current = null;
        for (String date : dateList) {
            System.out.println("begin date:" + date);

            // 0. 把current赋值给last，并查出新的值赋值给current
            last = current;
            current = mckIdcDBHelper.getAll(CvmSaleInventorySummaryDetailDO.class,
                    "where `切片时间`=?", date);

            // 1. 如果只有current有值，那current直接存入db
            if (last == null && current != null) {
                List<CvmSaleInventorySummaryDetailDiffDO> result =
                        ListUtils.transform(current, o -> trans(o));
                mckIdcDBHelper.insertBatchWithoutReturnId(result);
            } else if (last != null && current != null) {
                // 2. 如果last和current都有值，那么current比对last出diff，并把current存入db
                Map<String, List<CvmSaleInventorySummaryDetailDO>> lastMap = ListUtils.toMapList(last,
                        o -> key(o), o -> o);
                Map<String, List<CvmSaleInventorySummaryDetailDO>> currentMap = ListUtils.toMapList(current,
                        o -> key(o), o -> o);

                List<CvmSaleInventorySummaryDetailDiffDO> result = new ArrayList<>();

                // 2.1 current有
                for (Map.Entry<String, List<CvmSaleInventorySummaryDetailDO>> e : currentMap.entrySet()) {
                    CvmSaleInventorySummaryDetailDO lastOne = sumToOne(lastMap.get(e.getKey()));
                    CvmSaleInventorySummaryDetailDiffDO diff = trans(sumToOne(e.getValue()));
                    if (lastOne == null) {
                        diff.setDiff总核心数(diff.get总核心数());
                    } else {
                        diff.setDiff总核心数(diff.get总核心数() - lastOne.get总核心数());
                    }
                    result.add(diff);
                }

                // 2.2 last有而current没有的，补充一条，这里不要看日期
                for (Map.Entry<String, List<CvmSaleInventorySummaryDetailDO>> e : lastMap.entrySet()) {
                    if (currentMap.containsKey(e.getKey())) {
                        continue;
                    }
                    CvmSaleInventorySummaryDetailDiffDO diff = trans(sumToOne(e.getValue()));
                    diff.set切片时间(date);
                    diff.setDiff总核心数(0d - diff.get总核心数());
                    diff.set总核心数(0d);
                    result.add(diff);
                }

                mckIdcDBHelper.insertBatchWithoutReturnId(result);

            } else {
                // 3. 如果都没有值则不处理
            }
        }
    }

    private CvmSaleInventorySummaryDetailDO sumToOne(List<CvmSaleInventorySummaryDetailDO> list) {
        if (list == null || list.isEmpty()) {
            return null;
        }
        CvmSaleInventorySummaryDetailDO one = JSON.clone(list.get(0));
        one.set总核心数(NumberUtils.sum(list, o -> o.get总核心数()).doubleValue());
        return one;
    }

    // 这里不能加入日期
    private String key(CvmSaleInventorySummaryDetailDO d) {
        return String.join("&", d.get设备类型(), d.get一级分类(),
                d.get二级分类(), d.get三级分类(), d.getZone(), d.getRegion(),
                d.get区域(), d.get地域类型(), d.get网卡类型(), d.getCPU平台(), d.get单台核心数() + "");
    }

    private CvmSaleInventorySummaryDetailDiffDO trans(CvmSaleInventorySummaryDetailDO d) {
        CvmSaleInventorySummaryDetailDiffDO cvmSaleInventorySummaryDetailDiffDO = new CvmSaleInventorySummaryDetailDiffDO();
        cvmSaleInventorySummaryDetailDiffDO.set切片时间(d.get切片时间());
        cvmSaleInventorySummaryDetailDiffDO.set设备类型(d.get设备类型());
        cvmSaleInventorySummaryDetailDiffDO.set一级分类(d.get一级分类());
        cvmSaleInventorySummaryDetailDiffDO.set二级分类(d.get二级分类());
        cvmSaleInventorySummaryDetailDiffDO.set三级分类(d.get三级分类());
        cvmSaleInventorySummaryDetailDiffDO.setZone(d.getZone());
        cvmSaleInventorySummaryDetailDiffDO.setRegion(d.getRegion());
        cvmSaleInventorySummaryDetailDiffDO.set区域(d.get区域());
        cvmSaleInventorySummaryDetailDiffDO.set地域类型(d.get地域类型());
        cvmSaleInventorySummaryDetailDiffDO.set单台核心数(d.get单台核心数());
        cvmSaleInventorySummaryDetailDiffDO.set总核心数(d.get总核心数());
        cvmSaleInventorySummaryDetailDiffDO.setDiff总核心数(0d);
        cvmSaleInventorySummaryDetailDiffDO.set网卡类型(d.get网卡类型());
        cvmSaleInventorySummaryDetailDiffDO.setCPU平台(d.getCPU平台());
        return cvmSaleInventorySummaryDetailDiffDO;
    }
}
