package com.pugwoo.dboperate.archived.mck.comd_inventory;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.json.JSON;
import lombok.Data;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.FileInputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 解析云运管库存的数据到麦肯锡的数据库
 */
@SpringBootTest
public class ParseCOMDInventoryExcelToMysql {

    @Autowired
    private DBHelper mckIdcDBHelper;

    @Data
    public static class MyRow {
        @ExcelProperty(index = 0) // 第一列,index从0开始
        private String assetId;
        @ExcelProperty(index = 1)
        private String innerIp;
        @ExcelProperty(index = 2)
        private String product;
        @ExcelProperty(index = 3)
        private String deviceType;
        @ExcelProperty(index = 4)
        private String operateDept;
        @ExcelProperty(index = 5)
        private String bizModule;
        @ExcelProperty(index = 6)
        private String operator;
        @ExcelProperty(index = 7)
        private String zone;
        @ExcelProperty(index = 8)
        private String campus;
        @ExcelProperty(index = 9)
        private String module;
        @ExcelProperty(index = 10)
        private String logicArea;
        @ExcelProperty(index = 11)
        private String upDate;
        @ExcelProperty(index = 12)
        private String band;
        @ExcelProperty(index = 13)
        private String desc;
        @ExcelProperty(index = 14)
        private String status;
        @ExcelProperty(index = 15)
        private String areaType;
    }

    @Test
    public void parse() throws Exception {
        FileInputStream in = new FileInputStream("d:/可复用设备1672899886000.xlsx");

        final List<ParseCOMDInventoryExcelToMysql.MyRow> result = new ArrayList<>();

        EasyExcel.read(
                in, ParseCOMDInventoryExcelToMysql.MyRow.class,
                new AnalysisEventListener<ParseCOMDInventoryExcelToMysql.MyRow>() {
                    @Override
                    public void invoke(ParseCOMDInventoryExcelToMysql.MyRow row, AnalysisContext analysisContext) {
                        result.add(row);
                    }

                    @Override
                    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                        // 当整个文档解析完时，会调用这个方法
                        Map<String, List<MyRow>> map = ListUtils.groupBy(result, o -> String.join("&&",
                                o.getProduct(), o.getDeviceType(), o.getOperateDept(),
                                o.getBizModule(), o.getZone(), o.getCampus()));
                        List<ComdInventoryDO> result = new ArrayList<>();
                        for (Map.Entry<String, List<MyRow>> e : map.entrySet()) {
                            ComdInventoryDO d = new ComdInventoryDO();
                            d.setDataDate(new Date());
                            result.add(d);
                            d.setProduct(e.getValue().get(0).getProduct());
                            d.setDeviceType(e.getValue().get(0).getDeviceType());
                            d.setOperateDept(e.getValue().get(0).getOperateDept());
                            d.setBizModule(e.getValue().get(0).getBizModule());
                            d.setZone(e.getValue().get(0).getZone());
                            d.setCampus(e.getValue().get(0).getCampus());
                            d.setNum(e.getValue().size());
                        }
                        mckIdcDBHelper.insertBatchWithoutReturnId(result);
                    }

                    /**
                     * 这里会一行行的返回头（这个方法可以不重写，需要拿到行头时可以重写来拿）
                     */
                    @Override
                    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
                        // headMap的key是index，value是标题
                        System.out.println("行头:" + JSON.toJson(headMap));
                    }
                }
        ).sheet(0).headRowNumber(1).doRead(); // 这里指定第几个sheet，标题有几行

        in.close();
    }
}
