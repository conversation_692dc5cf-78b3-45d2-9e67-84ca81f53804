package com.pugwoo.dboperate.archived.mck.comd_inventory;

import lombok.Data;
import lombok.ToString;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;

import java.util.Date;

@Data
@ToString
@Table("comd_inventory")
public class ComdInventoryDO {

    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    @Column("data_date")
    private Date dataDate;

    /** 产品<br/>Column: [product] */
    @Column(value = "product")
    private String product;

    /** 设备类型<br/>Column: [device_type] */
    @Column(value = "device_type")
    private String deviceType;

    /** 运维部门<br/>Column: [operate_dept] */
    @Column(value = "operate_dept")
    private String operateDept;

    /** 业务模块<br/>Column: [biz_module] */
    @Column(value = "biz_module")
    private String bizModule;

    /** zone<br/>Column: [zone] */
    @Column(value = "zone")
    private String zone;

    /** campus<br/>Column: [campus] */
    @Column(value = "campus")
    private String campus;

    /** 数量<br/>Column: [num] */
    @Column(value = "num")
    private Integer num;

}