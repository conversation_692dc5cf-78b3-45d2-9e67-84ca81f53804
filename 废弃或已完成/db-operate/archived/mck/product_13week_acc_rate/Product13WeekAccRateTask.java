package com.pugwoo.dboperate.archived.mck.product_13week_acc_rate;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.lang.DateUtils;
import lombok.Data;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.FileInputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;

@SpringBootTest
public class Product13WeekAccRateTask {

    @Autowired
    private DBHelper mckIdcDBHelper;

    @Data
    public static class MyRow {
        @ExcelProperty(index = 0) // 第一列,index从0开始
        private String name;
        @ExcelProperty(index = 1)
        private String d202001;
        @ExcelProperty(index = 2)
        private String d202002;
        @ExcelProperty(index = 3)
        private String d202003;
        @ExcelProperty(index = 4)
        private String d202004;
        @ExcelProperty(index = 5)
        private String d202005;
        @ExcelProperty(index = 6)
        private String d202006;
        @ExcelProperty(index = 7)
        private String d202007;
        @ExcelProperty(index = 8)
        private String d202008;
        @ExcelProperty(index = 9)
        private String d202009;
        @ExcelProperty(index = 10)
        private String d202010;
        @ExcelProperty(index = 11)
        private String d202011;
        @ExcelProperty(index = 12)
        private String d202012;
        @ExcelProperty(index = 13)
        private String d202101;
        @ExcelProperty(index = 14)
        private String d202102;
        @ExcelProperty(index = 15)
        private String d202103;
        @ExcelProperty(index = 16)
        private String d202104;
        @ExcelProperty(index = 17)
        private String d202105;
        @ExcelProperty(index = 18)
        private String d202106;
        @ExcelProperty(index = 19)
        private String d202107;
        @ExcelProperty(index = 20)
        private String d202108;
        @ExcelProperty(index = 21)
        private String d202109;
        @ExcelProperty(index = 22)
        private String d202110;
        @ExcelProperty(index = 23)
        private String d202111;
        @ExcelProperty(index = 24)
        private String d202112;
        @ExcelProperty(index = 25)
        private String d202201;
        @ExcelProperty(index = 26)
        private String d202202;
        @ExcelProperty(index = 27)
        private String d202203;
        @ExcelProperty(index = 28)
        private String d202204;
        @ExcelProperty(index = 29)
        private String d202205;
        @ExcelProperty(index = 30)
        private String d202206;
        @ExcelProperty(index = 31)
        private String d202207;
        @ExcelProperty(index = 32)
        private String d202208;
        @ExcelProperty(index = 33)
        private String d202209;
        @ExcelProperty(index = 34)
        private String d202210;
        @ExcelProperty(index = 35)
        private String d202211;
        @ExcelProperty(index = 36)
        private String d202212;
        @ExcelProperty(index = 37)
        private String d202301;
        @ExcelProperty(index = 38)
        private String d202302;
        @ExcelProperty(index = 39)
        private String d202303;
        @ExcelProperty(index = 40)
        private String d202304;
    }

    @Test
    public void parseProduct() throws Exception {
        FileInputStream in = new FileInputStream("d:/产品13周预测准确率-532.xlsx");

        final List<Product13WeekAccRateTask.MyRow> result = new ArrayList<>();

        EasyExcel.read(
                in, Product13WeekAccRateTask.MyRow.class,
                new AnalysisEventListener<Product13WeekAccRateTask.MyRow>() {
                    @Override
                    public void invoke(Product13WeekAccRateTask.MyRow row, AnalysisContext analysisContext) {
                        result.add(row);
                    }

                    @Override
                    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                        // 当整个文档解析完时，会调用这个方法
                        List<Product13weekDemandAccRateProductDO> db = new ArrayList<>();
                        for (Product13WeekAccRateTask.MyRow row : result) {
                            db.addAll(toProduct(row.getName(), row));
                        }

                        mckIdcDBHelper.insertBatchWithoutReturnId(db);
                    }

                    /**
                     * 这里会一行行的返回头（这个方法可以不重写，需要拿到行头时可以重写来拿）
                     */
                    @Override
                    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
                        // headMap的key是index，value是标题
                        System.out.println("行头:" + JSON.toJson(headMap));
                    }
                }
        ).sheet(0).headRowNumber(2).doRead(); // 这里指定第几个sheet，标题有几行

        in.close();
    }

    @Test
    public void parseDeviceType() throws Exception {
        FileInputStream in = new FileInputStream("d:/机型13周预测准确率-532.xlsx");

        final List<Product13WeekAccRateTask.MyRow> result = new ArrayList<>();

        EasyExcel.read(
                in, Product13WeekAccRateTask.MyRow.class,
                new AnalysisEventListener<Product13WeekAccRateTask.MyRow>() {
                    @Override
                    public void invoke(Product13WeekAccRateTask.MyRow row, AnalysisContext analysisContext) {
                        result.add(row);
                    }

                    @Override
                    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                        // 当整个文档解析完时，会调用这个方法
                        List<Product13weekDemandAccRateProductDO> db = new ArrayList<>();
                        for (Product13WeekAccRateTask.MyRow row : result) {
                            db.addAll(toProduct(row.getName(), row));
                        }

                        // 转换成机型
                        List<Product13weekDemandAccRateDeviceTypeDO> db2 = ListUtils.transform(db, o -> {
                            Product13weekDemandAccRateDeviceTypeDO d = new Product13weekDemandAccRateDeviceTypeDO();
                            d.setDeviceType(o.getProduct());
                            d.setYearMonth(o.getYearMonth());
                            d.setAccuracyRate(o.getAccuracyRate());
                            return d;
                        });

                        mckIdcDBHelper.insertBatchWithoutReturnId(db2);
                    }

                    /**
                     * 这里会一行行的返回头（这个方法可以不重写，需要拿到行头时可以重写来拿）
                     */
                    @Override
                    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
                        // headMap的key是index，value是标题
                        System.out.println("行头:" + JSON.toJson(headMap));
                    }
                }
        ).sheet(0).headRowNumber(2).doRead(); // 这里指定第几个sheet，标题有几行

        in.close();
    }


    private List<Product13weekDemandAccRateProductDO> toProduct(String name, Product13WeekAccRateTask.MyRow row) {
        List<Product13weekDemandAccRateProductDO> list = new ArrayList<>();

        {
            Product13weekDemandAccRateProductDO d = new Product13weekDemandAccRateProductDO();
            d.setProduct(name);
            d.setYearMonth("2020-01");
            d.setAccuracyRate(parse(row.d202001));
            list.add(d);
        }
        {
            Product13weekDemandAccRateProductDO d = new Product13weekDemandAccRateProductDO();
            d.setProduct(name);
            d.setYearMonth("2020-02");
            d.setAccuracyRate(parse(row.d202002));
            list.add(d);
        }
        {
            Product13weekDemandAccRateProductDO d = new Product13weekDemandAccRateProductDO();
            d.setProduct(name);
            d.setYearMonth("2020-03");
            d.setAccuracyRate(parse(row.d202003));
            list.add(d);
        }
        {
            Product13weekDemandAccRateProductDO d = new Product13weekDemandAccRateProductDO();
            d.setProduct(name);
            d.setYearMonth("2020-04");
            d.setAccuracyRate(parse(row.d202004));
            list.add(d);
        }
        {
            Product13weekDemandAccRateProductDO d = new Product13weekDemandAccRateProductDO();
            d.setProduct(name);
            d.setYearMonth("2020-05");
            d.setAccuracyRate(parse(row.d202005));
            list.add(d);
        }
        {
            Product13weekDemandAccRateProductDO d = new Product13weekDemandAccRateProductDO();
            d.setProduct(name);
            d.setYearMonth("2020-06");
            d.setAccuracyRate(parse(row.d202006));
            list.add(d);
        }
        {
            Product13weekDemandAccRateProductDO d = new Product13weekDemandAccRateProductDO();
            d.setProduct(name);
            d.setYearMonth("2020-07");
            d.setAccuracyRate(parse(row.d202007));
            list.add(d);
        }
        {
            Product13weekDemandAccRateProductDO d = new Product13weekDemandAccRateProductDO();
            d.setProduct(name);
            d.setYearMonth("2020-08");
            d.setAccuracyRate(parse(row.d202008));
            list.add(d);
        }
        {
            Product13weekDemandAccRateProductDO d = new Product13weekDemandAccRateProductDO();
            d.setProduct(name);
            d.setYearMonth("2020-09");
            d.setAccuracyRate(parse(row.d202009));
            list.add(d);
        }
        {
            Product13weekDemandAccRateProductDO d = new Product13weekDemandAccRateProductDO();
            d.setProduct(name);
            d.setYearMonth("2020-10");
            d.setAccuracyRate(parse(row.d202010));
            list.add(d);
        }
        {
            Product13weekDemandAccRateProductDO d = new Product13weekDemandAccRateProductDO();
            d.setProduct(name);
            d.setYearMonth("2020-11");
            d.setAccuracyRate(parse(row.d202011));
            list.add(d);
        }
        {
            Product13weekDemandAccRateProductDO d = new Product13weekDemandAccRateProductDO();
            d.setProduct(name);
            d.setYearMonth("2020-12");
            d.setAccuracyRate(parse(row.d202012));
            list.add(d);
        }
        {
            Product13weekDemandAccRateProductDO d = new Product13weekDemandAccRateProductDO();
            d.setProduct(name);
            d.setYearMonth("2021-01");
            d.setAccuracyRate(parse(row.d202101));
            list.add(d);
        }
        {
            Product13weekDemandAccRateProductDO d = new Product13weekDemandAccRateProductDO();
            d.setProduct(name);
            d.setYearMonth("2021-02");
            d.setAccuracyRate(parse(row.d202102));
            list.add(d);
        }
        {
            Product13weekDemandAccRateProductDO d = new Product13weekDemandAccRateProductDO();
            d.setProduct(name);
            d.setYearMonth("2021-03");
            d.setAccuracyRate(parse(row.d202103));
            list.add(d);
        }
        {
            Product13weekDemandAccRateProductDO d = new Product13weekDemandAccRateProductDO();
            d.setProduct(name);
            d.setYearMonth("2021-04");
            d.setAccuracyRate(parse(row.d202104));
            list.add(d);
        }
        {
            Product13weekDemandAccRateProductDO d = new Product13weekDemandAccRateProductDO();
            d.setProduct(name);
            d.setYearMonth("2021-05");
            d.setAccuracyRate(parse(row.d202105));
            list.add(d);
        }
        {
            Product13weekDemandAccRateProductDO d = new Product13weekDemandAccRateProductDO();
            d.setProduct(name);
            d.setYearMonth("2021-06");
            d.setAccuracyRate(parse(row.d202106));
            list.add(d);
        }
        {
            Product13weekDemandAccRateProductDO d = new Product13weekDemandAccRateProductDO();
            d.setProduct(name);
            d.setYearMonth("2021-07");
            d.setAccuracyRate(parse(row.d202107));
            list.add(d);
        }
        {
            Product13weekDemandAccRateProductDO d = new Product13weekDemandAccRateProductDO();
            d.setProduct(name);
            d.setYearMonth("2021-08");
            d.setAccuracyRate(parse(row.d202108));
            list.add(d);
        }
        {
            Product13weekDemandAccRateProductDO d = new Product13weekDemandAccRateProductDO();
            d.setProduct(name);
            d.setYearMonth("2021-09");
            d.setAccuracyRate(parse(row.d202109));
            list.add(d);
        }
        {
            Product13weekDemandAccRateProductDO d = new Product13weekDemandAccRateProductDO();
            d.setProduct(name);
            d.setYearMonth("2021-10");
            d.setAccuracyRate(parse(row.d202110));
            list.add(d);
        }
        {
            Product13weekDemandAccRateProductDO d = new Product13weekDemandAccRateProductDO();
            d.setProduct(name);
            d.setYearMonth("2021-11");
            d.setAccuracyRate(parse(row.d202111));
            list.add(d);
        }
        {
            Product13weekDemandAccRateProductDO d = new Product13weekDemandAccRateProductDO();
            d.setProduct(name);
            d.setYearMonth("2021-12");
            d.setAccuracyRate(parse(row.d202112));
            list.add(d);
        }
        {
            Product13weekDemandAccRateProductDO d = new Product13weekDemandAccRateProductDO();
            d.setProduct(name);
            d.setYearMonth("2022-01");
            d.setAccuracyRate(parse(row.d202201));
            list.add(d);
        }
        {
            Product13weekDemandAccRateProductDO d = new Product13weekDemandAccRateProductDO();
            d.setProduct(name);
            d.setYearMonth("2022-02");
            d.setAccuracyRate(parse(row.d202202));
            list.add(d);
        }
        {
            Product13weekDemandAccRateProductDO d = new Product13weekDemandAccRateProductDO();
            d.setProduct(name);
            d.setYearMonth("2022-03");
            d.setAccuracyRate(parse(row.d202203));
            list.add(d);
        }
        {
            Product13weekDemandAccRateProductDO d = new Product13weekDemandAccRateProductDO();
            d.setProduct(name);
            d.setYearMonth("2022-04");
            d.setAccuracyRate(parse(row.d202204));
            list.add(d);
        }
        {
            Product13weekDemandAccRateProductDO d = new Product13weekDemandAccRateProductDO();
            d.setProduct(name);
            d.setYearMonth("2022-05");
            d.setAccuracyRate(parse(row.d202205));
            list.add(d);
        }
        {
            Product13weekDemandAccRateProductDO d = new Product13weekDemandAccRateProductDO();
            d.setProduct(name);
            d.setYearMonth("2022-06");
            d.setAccuracyRate(parse(row.d202206));
            list.add(d);
        }
        {
            Product13weekDemandAccRateProductDO d = new Product13weekDemandAccRateProductDO();
            d.setProduct(name);
            d.setYearMonth("2022-07");
            d.setAccuracyRate(parse(row.d202207));
            list.add(d);
        }
        {
            Product13weekDemandAccRateProductDO d = new Product13weekDemandAccRateProductDO();
            d.setProduct(name);
            d.setYearMonth("2022-08");
            d.setAccuracyRate(parse(row.d202208));
            list.add(d);
        }
        {
            Product13weekDemandAccRateProductDO d = new Product13weekDemandAccRateProductDO();
            d.setProduct(name);
            d.setYearMonth("2022-09");
            d.setAccuracyRate(parse(row.d202209));
            list.add(d);
        }
        {
            Product13weekDemandAccRateProductDO d = new Product13weekDemandAccRateProductDO();
            d.setProduct(name);
            d.setYearMonth("2022-10");
            d.setAccuracyRate(parse(row.d202210));
            list.add(d);
        }
        {
            Product13weekDemandAccRateProductDO d = new Product13weekDemandAccRateProductDO();
            d.setProduct(name);
            d.setYearMonth("2022-11");
            d.setAccuracyRate(parse(row.d202211));
            list.add(d);
        }
        {
            Product13weekDemandAccRateProductDO d = new Product13weekDemandAccRateProductDO();
            d.setProduct(name);
            d.setYearMonth("2022-12");
            d.setAccuracyRate(parse(row.d202212));
            list.add(d);
        }
        {
            Product13weekDemandAccRateProductDO d = new Product13weekDemandAccRateProductDO();
            d.setProduct(name);
            d.setYearMonth("2023-01");
            d.setAccuracyRate(parse(row.d202301));
            list.add(d);
        }
        {
            Product13weekDemandAccRateProductDO d = new Product13weekDemandAccRateProductDO();
            d.setProduct(name);
            d.setYearMonth("2023-02");
            d.setAccuracyRate(parse(row.d202302));
            list.add(d);
        }
        {
            Product13weekDemandAccRateProductDO d = new Product13weekDemandAccRateProductDO();
            d.setProduct(name);
            d.setYearMonth("2023-03");
            d.setAccuracyRate(parse(row.d202303));
            list.add(d);
        }
        {
            Product13weekDemandAccRateProductDO d = new Product13weekDemandAccRateProductDO();
            d.setProduct(name);
            d.setYearMonth("2023-04");
            d.setAccuracyRate(parse(row.d202304));
            list.add(d);
        }

        return list;
    }

    private BigDecimal parse(String percent) {
        if (percent == null) {
            return null;
        }
        if (percent.endsWith("%")) {
            percent = percent.substring(0, percent.length() - 1);
        }
        return new BigDecimal(percent);
    }

    public static void main(String[] args) {
        Date date = DateUtils.parse("2020-01");
        for (int i = 0; i < 40; i++) {
            System.out.println("            {\n" +
                    "            Product13weekDemandAccRateProductDO d = new Product13weekDemandAccRateProductDO();\n" +
                    "            d.setProduct(name);\n" +
                    "            d.setYearMonth(\"" + DateUtils.format(date, "yyyy-MM") + "\");\n" +
                    "            d.setAccuracyRate(parse(row.d" + DateUtils.format(date, "yyyyMM") +"));\n" +
                    "            list.add(d);\n" +
                    "        }");

            date = DateUtils.addTime(date, Calendar.MONDAY, 1);
        }
    }

}
