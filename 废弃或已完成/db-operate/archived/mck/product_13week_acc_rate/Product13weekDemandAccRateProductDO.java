package com.pugwoo.dboperate.archived.mck.product_13week_acc_rate;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

@Data
@ToString
@Table("product_13week_demand_acc_rate_product")
public class Product13weekDemandAccRateProductDO {

    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    /** 年月<br/>Column: [year_month] */
    @Column(value = "year_month")
    private String yearMonth;

    /** 产品<br/>Column: [product] */
    @Column(value = "product")
    private String product;

    /** 准确率百分比<br/>Column: [accuracy_rate] */
    @Column(value = "accuracy_rate")
    private BigDecimal accuracyRate;

}