package com.pugwoo.dboperate.archived.mck.income_data_import;

import com.pugwoo.dbhelper.DBHelper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.BufferedReader;
import java.io.FileReader;
import java.util.ArrayList;
import java.util.List;

@SpringBootTest
public class IncomeDataImport {

    @Autowired
    private DBHelper mckIdcDBHelper;

    @Test
    public void test() throws Exception {
        String path = "d:/ed073824-b0c0-4320-bf37-6d9ebb77e6fc.csv";
        BufferedReader br = new BufferedReader(new FileReader(path));
        String line;
        int rows = 1;
        List<Cvm订单数据DO> list = new ArrayList<>();
        while((line = br.readLine()) !=null) {
            if (rows > 1) {
                try {
                    Cvm订单数据DO d = parseLine(line);
                    list.add(d);
                } catch (Exception e) {
                    System.out.println("exception line:" + line);
                    return;
                }

                if (list.size() >= 10000) {
                    mckIdcDBHelper.insertBatchWithoutReturnId(list);
                    list = new ArrayList<>();
                }
            }
            rows++;
        }

        if (!list.isEmpty()) {
            mckIdcDBHelper.insertBatchWithoutReturnId(list);
        }

        br.close();
    }


    @Test
    public void test2() throws Exception {
        String path = "d:/39537f1b-88cb-4ad2-b697-1b9f4e207582.csv";
        BufferedReader br = new BufferedReader(new FileReader(path));
        String line;
        int rows = 1;
        List<Cvm订单数据DO> list = new ArrayList<>();
        while((line = br.readLine()) !=null) {
            if (rows > 1) {
                try {
                    Cvm订单数据DO d = parseLine(line);
                    list.add(d);
                } catch (Exception e) {
                    System.out.println("exception line:" + line);
                    return;
                }

                if (list.size() >= 10000) {
                    mckIdcDBHelper.insertBatchWithoutReturnId(list);
                    list = new ArrayList<>();
                }
            }
            rows++;
        }

        if (!list.isEmpty()) {
            mckIdcDBHelper.insertBatchWithoutReturnId(list);
        }

        br.close();
    }

    @Test
    public void test3() throws Exception {
        String path = "d:/cd4e805f-b8a6-4fc9-be91-ea577cca24dc.csv";
        BufferedReader br = new BufferedReader(new FileReader(path));
        String line;
        int rows = 1;
        List<Cvm订单数据DO> list = new ArrayList<>();
        while((line = br.readLine()) !=null) {
            if (rows > 1) {
                try {
                    Cvm订单数据DO d = parseLine(line);
                    list.add(d);
                } catch (Exception e) {
                    System.out.println("exception line:" + line);
                    return;
                }

                if (list.size() >= 10000) {
                    mckIdcDBHelper.insertBatchWithoutReturnId(list);
                    list = new ArrayList<>();
                }
            }
            rows++;
        }

        if (!list.isEmpty()) {
            mckIdcDBHelper.insertBatchWithoutReturnId(list);
        }

        br.close();
    }

    @Test
    public void test4() throws Exception {
        String path = "d:/3548fe9c-1398-42cf-96ce-f1a2a40d3f62.csv";
        BufferedReader br = new BufferedReader(new FileReader(path));
        String line;
        int rows = 1;
        List<Cvm订单数据DO> list = new ArrayList<>();
        while((line = br.readLine()) !=null) {
            if (rows > 1) {
                try {
                    Cvm订单数据DO d = parseLine(line);
                    list.add(d);
                } catch (Exception e) {
                    System.out.println("exception line:" + line);
                    return;
                }

                if (list.size() >= 10000) {
                    mckIdcDBHelper.insertBatchWithoutReturnId(list);
                    list = new ArrayList<>();
                }
            }
            rows++;
        }

        if (!list.isEmpty()) {
            mckIdcDBHelper.insertBatchWithoutReturnId(list);
        }

        br.close();
    }

    private Cvm订单数据DO parseLine(String line) {
        Cvm订单数据DO d = new Cvm订单数据DO();
        int first3 = ordinalIndexOf(line, ",", 3);
        String strs[] = line.substring(0, first3).split(",");
        d.setBigDealId(strs[0]);
        d.setDealName(strs[1]);
        d.setAction(strs[2]);

        int last17 = lastIndexOf(line, ",", 17);
        String strs2[] = line.substring(last17 + 1).split(",");
        d.setOperator(strs2[0]);
        d.setOwnerUin(strs2[1]);
        d.setCreateTime(strs2[2]);
        d.setPayer(strs2[3]);
        d.setPayTime(strs2[4]);
        d.setState(strs2[5]);
        d.setGoodsNum(strs2[6]);
        d.setSinglePrice(strs2[7]);
        d.setDiscount(strs2[8]);
        d.setDiscountPrice(strs2[9]);
        d.setPayMode(strs2[10]);
        d.setVoucherAmount(strs2[11]);
        d.setPayAmount(strs2[12]);
        d.setTimeUnit(strs2[13]);
        d.setTimeSpan(strs2[14]);
        d.setProductcode(strs2[15]);
        if (strs2.length == 17) {
            d.setSubproductcode(strs2[16]);
        } else {
            d.setSubproductcode(""); // 有可能为空，例如20221008121021309738841,20221008121021309704321
        }

        d.setGoodsdetail(line.substring(first3 + 1, last17));

        return d;
    }

    private static int ordinalIndexOf(String str, String substr, int n) {
        int pos = str.indexOf(substr);
        while (--n > 0 && pos != -1)
            pos = str.indexOf(substr, pos + 1);
        return pos;
    }

    private static int lastIndexOf(String str, String substr, int n) {
        int pos = str.lastIndexOf(substr);
        while (--n > 0 && pos != -1)
            pos = str.lastIndexOf(substr, pos - 1);
        return pos;
    }

}
