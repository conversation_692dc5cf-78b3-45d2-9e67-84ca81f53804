package com.pugwoo.dboperate.archived.mck.income_data_import;

import lombok.Data;
import lombok.ToString;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;

@Data
@ToString
@Table("cvm订单数据7")
public class Cvm订单数据DO {

    @Column(value = "bigDealId")
    private String bigDealId;

    @Column(value = "dealName")
    private String dealName;

    @Column(value = "action")
    private String action;

    @Column(value = "goodsdetail")
    private String goodsdetail;

    @Column(value = "operator")
    private String operator;

    @Column(value = "ownerUin")
    private String ownerUin;

    @Column(value = "createTime")
    private String createTime;

    @Column(value = "payer")
    private String payer;

    @Column(value = "payTime")
    private String payTime;

    @Column(value = "state")
    private String state;

    @Column(value = "goodsNum")
    private String goodsNum;

    @Column(value = "singlePrice")
    private String singlePrice;

    @Column(value = "discount")
    private String discount;

    @Column(value = "discountPrice")
    private String discountPrice;

    @Column(value = "payMode")
    private String payMode;

    @Column(value = "voucherAmount")
    private String voucherAmount;

    @Column(value = "payAmount")
    private String payAmount;

    @Column(value = "timeUnit")
    private String timeUnit;

    @Column(value = "timeSpan")
    private String timeSpan;

    @Column(value = "productcode")
    private String productcode;

    @Column(value = "subproductcode")
    private String subproductcode;

}