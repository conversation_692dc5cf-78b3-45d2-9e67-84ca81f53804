package com.pugwoo.dboperate.archived.mck.income_data_import;

import com.pugwoo.dbhelper.DBHelper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.stream.Stream;

@SpringBootTest
public class CopyDataToClickhouse {

    @Autowired
    private DBHelper mckIdcDBHelper;
    @Autowired
    private DBHelper ckCloudDemandDevDBHelper;

    @Test
    public void test() {
        long start = System.currentTimeMillis();
        Stream<Cvm订单数据DO> allStream = mckIdcDBHelper.getAllForStream(Cvm订单数据DO.class);
        long end = System.currentTimeMillis();

        System.out.println("cost:" + (end - start) + "ms");

        // 经过测试，这个并没有stream的效果，等了40min还没有返回
        // 这里还没验证是不是因为表没有主键id的因素
    }

}
