package com.pugwoo.dboperate.operationview;

import com.pugwoo.dbhelper.DBHelper;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import jakarta.annotation.Resource;
import java.util.List;

@SpringBootTest
public class MoveProdDataToDevTests {

    @Resource
    private DBHelper ckCloudDemandDevDBHelper;
    @Resource
    private DBHelper ckCloudDemandIdcDBHelper;

    @Test
    public void move() {
        DBHelper.setTableName(ReportOperationViewDetailDO.class, "report_operation_view_detail_bak");
        List<ReportOperationViewDetailDO> all = ckCloudDemandIdcDBHelper.getAll(ReportOperationViewDetailDO.class);

        DBHelper.setTableName(ReportOperationViewDetailDO.class, "report_operation_view_detail");

        ckCloudDemandDevDBHelper.executeRaw("truncate table cloud_demand.report_operation_view_detail_local ON CLUSTER default_cluster");
        ckCloudDemandDevDBHelper.insertBatchWithoutReturnId(all);

        System.out.println("all size:" + all.size());
    }


}
