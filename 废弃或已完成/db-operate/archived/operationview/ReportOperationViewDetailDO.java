package com.pugwoo.dboperate.operationview;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@ToString
@Table("report_operation_view_detail")
public class ReportOperationViewDetailDO {

    /** 数据日期<br/>Column: [stat_time] */
    @Column(value = "stat_time")
    private LocalDate statTime;

    /** 产品类型<br/>Column: [product_type] */
    @Column(value = "product_type")
    private String productType;

    /** 库存材料类型(好差呆)<br/>Column: [material_type] */
    @Column(value = "material_type")
    private String materialType;

    /** 境内外<br/>Column: [customhouse_title] */
    @Column(value = "customhouse_title")
    private String customhouseTitle;

    /** 区域名<br/>Column: [area_name] */
    @Column(value = "area_name")
    private String areaName;

    /** 地域名<br/>Column: [region_name] */
    @Column(value = "region_name")
    private String regionName;

    /** 可用区名<br/>Column: [zone_name] */
    @Column(value = "zone_name")
    private String zoneName;

    /** 设备类型<br/>Column: [device_type] */
    @Column(value = "device_type")
    private String deviceType;

    /** 机型族<br/>Column: [device_family] */
    @Column(value = "device_family")
    private String deviceFamily;

    /** CPU规格<br/>Column: [cpu_category] */
    @Column(value = "cpu_category")
    private String cpuCategory;

    /** 设备逻辑核心数<br/>Column: [cpu_logic_core] */
    @Column(value = "cpu_logic_core")
    private Integer cpuLogicCore;

    /** 最新总库存<br/>Column: [inv_newest_total] */
    @Column(value = "inv_newest_total")
    private BigDecimal invNewestTotal;

    /** 最新线上库存<br/>Column: [inv_newest_online] */
    @Column(value = "inv_newest_online")
    private BigDecimal invNewestOnline;

    /** 最新线下库存<br/>Column: [inv_newest_offline] */
    @Column(value = "inv_newest_offline")
    private BigDecimal invNewestOffline;

    /** m-1月库存量<br/>Column: [inv_m_1] */
    @Column(value = "inv_m_1")
    private BigDecimal invM1;

    /** m-2月库存量<br/>Column: [inv_m_2] */
    @Column(value = "inv_m_2")
    private BigDecimal invM2;

    /** m-3月库存量<br/>Column: [inv_m_3] */
    @Column(value = "inv_m_3")
    private BigDecimal invM3;

    /** m-4月库存量<br/>Column: [inv_m_4] */
    @Column(value = "inv_m_4")
    private BigDecimal invM4;

    /** m-5月库存量<br/>Column: [inv_m_5] */
    @Column(value = "inv_m_5")
    private BigDecimal invM5;

    /** m-6月库存量<br/>Column: [inv_m_6] */
    @Column(value = "inv_m_6")
    private BigDecimal invM6;

    /** m-7月库存量<br/>Column: [inv_m_7] */
    @Column(value = "inv_m_7")
    private BigDecimal invM7;

    /** m-8月库存量<br/>Column: [inv_m_8] */
    @Column(value = "inv_m_8")
    private BigDecimal invM8;

    /** m-9月库存量<br/>Column: [inv_m_9] */
    @Column(value = "inv_m_9")
    private BigDecimal invM9;

    /** m-10月库存量<br/>Column: [inv_m_10] */
    @Column(value = "inv_m_10")
    private BigDecimal invM10;

    /** m-11月库存量<br/>Column: [inv_m_11] */
    @Column(value = "inv_m_11")
    private BigDecimal invM11;

    /** m-12月库存量<br/>Column: [inv_m_12] */
    @Column(value = "inv_m_12")
    private BigDecimal invM12;

    /** m-1月销售量<br/>Column: [sale_m_1] */
    @Column(value = "sale_m_1")
    private BigDecimal saleM1;

    /** m-2月销售量<br/>Column: [sale_m_2] */
    @Column(value = "sale_m_2")
    private BigDecimal saleM2;

    /** m-3月销售量<br/>Column: [sale_m_3] */
    @Column(value = "sale_m_3")
    private BigDecimal saleM3;

    /** m-4月销售量<br/>Column: [sale_m_4] */
    @Column(value = "sale_m_4")
    private BigDecimal saleM4;

    /** m-5月销售量<br/>Column: [sale_m_5] */
    @Column(value = "sale_m_5")
    private BigDecimal saleM5;

    /** m-6月销售量<br/>Column: [sale_m_6] */
    @Column(value = "sale_m_6")
    private BigDecimal saleM6;

    /** m-7月销售量<br/>Column: [sale_m_7] */
    @Column(value = "sale_m_7")
    private BigDecimal saleM7;

    /** m-8月销售量<br/>Column: [sale_m_8] */
    @Column(value = "sale_m_8")
    private BigDecimal saleM8;

    /** m-9月销售量<br/>Column: [sale_m_9] */
    @Column(value = "sale_m_9")
    private BigDecimal saleM9;

    /** m-10月销售量<br/>Column: [sale_m_10] */
    @Column(value = "sale_m_10")
    private BigDecimal saleM10;

    /** m-11月销售量<br/>Column: [sale_m_11] */
    @Column(value = "sale_m_11")
    private BigDecimal saleM11;

    /** m-12月销售量<br/>Column: [sale_m_12] */
    @Column(value = "sale_m_12")
    private BigDecimal saleM12;

    /** m-1周需求量<br/>Column: [demand_w_1] */
    @Column(value = "demand_w_1")
    private BigDecimal demandW1;

    /** m-2周需求量<br/>Column: [demand_w_2] */
    @Column(value = "demand_w_2")
    private BigDecimal demandW2;

    /** m-3周需求量<br/>Column: [demand_w_3] */
    @Column(value = "demand_w_3")
    private BigDecimal demandW3;

    /** m-4周需求量<br/>Column: [demand_w_4] */
    @Column(value = "demand_w_4")
    private BigDecimal demandW4;

    /** m-5周需求量<br/>Column: [demand_w_5] */
    @Column(value = "demand_w_5")
    private BigDecimal demandW5;

    /** m-6周需求量<br/>Column: [demand_w_6] */
    @Column(value = "demand_w_6")
    private BigDecimal demandW6;

    /** m-7周需求量<br/>Column: [demand_w_7] */
    @Column(value = "demand_w_7")
    private BigDecimal demandW7;

    /** m-8周需求量<br/>Column: [demand_w_8] */
    @Column(value = "demand_w_8")
    private BigDecimal demandW8;

    /** m-9周需求量<br/>Column: [demand_w_9] */
    @Column(value = "demand_w_9")
    private BigDecimal demandW9;

    /** m-10周需求量<br/>Column: [demand_w_10] */
    @Column(value = "demand_w_10")
    private BigDecimal demandW10;

    /** m-11周需求量<br/>Column: [demand_w_11] */
    @Column(value = "demand_w_11")
    private BigDecimal demandW11;

    /** m-12周需求量<br/>Column: [demand_w_12] */
    @Column(value = "demand_w_12")
    private BigDecimal demandW12;

    /** m-13周需求量<br/>Column: [demand_w_13] */
    @Column(value = "demand_w_13")
    private BigDecimal demandW13;

}