package com.pugwoo.dboperate.ck原子性写入;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

@SpringBootTest
public class TestCkAtomicWrite {

    @Resource
    private DBHelper ckStdCrpDevDBHelper;

    // 测试原子性rename

    @Test
    public void test() {
        ckStdCrpDevDBHelper.setSlowSqlWarningValve(100000);
        for (int i = 0; i < 100; i++) {
            List<Map> list = ckStdCrpDevDBHelper.getRaw(Map.class, "select industry_dept,\n" +
                    "       sum(distinct appid) as sumAppId,sum(distinct uin),sum(distinct appid+uin),\n" +
                    "       sum(distinct appid+uin+uin),sum(distinct sqrt(sqrt(appid)*sqrt(uin)*uin)),\n" +
                    "       sum(distinct appid*uin*uin),sum(pow(appid,10)),sum(pow(appid,9)),\n" +
                    "       sum(pow(appid,8)),sum(pow(appid,7)),sum(pow(sqrt(appid),6)),sum(pow(sqrt(appid),5)),\n" +
                    "       sum(distinct appid*1.2),sum(distinct uin*1.2),sum(distinct appid*1.2+uin*1.2),\n" +
                    "       sum(distinct appid*1.2+uin*1.2+uin*1.2),sum(distinct sqrt(sqrt(appid*1.2)*sqrt(uin*1.2)*uin*1.2)),\n" +
                    "       sum(distinct appid*1.2*uin*1.2*uin*1.2),sum(pow(appid*1.2,10)),sum(pow(appid*1.2,9)),\n" +
                    "       sum(pow(appid*1.2,8)),sum(pow(appid*1.2,7)),sum(pow(sqrt(appid*1.2),6)),sum(pow(sqrt(appid*1.2),5))\n" +
                    "       from std_crp.dwd_txy_appid_info_cf\n" +
                    "       group by industry_dept");
            if (list.size() != 17) {
                System.err.println("error size:" + list.size());
            }

            if (NumberUtils.sum(list, o -> o.get("sumAppId")).compareTo(new BigDecimal("3760196293481342")) != 0) {
                System.err.println("error sum:" + NumberUtils.sum(list, o -> o.get("sumAppId")));
            }

            System.out.println(DateUtils.format(new Date()) + " i:" + i + " check ok");
        }

    }

    @Test
    public void testRename() {
        for (int i = 0; i < 10000; i++) {
            ckStdCrpDevDBHelper.executeRaw("rename table\n" +
                    "       std_crp.dwd_txy_appid_info_cf_local to std_crp_swap.dwd_txy_appid_info_cf_local_tmp,\n" +
                    "       std_crp_swap.dwd_txy_appid_info_cf_local to std_crp.dwd_txy_appid_info_cf_local,\n" +
                    "       std_crp_swap.dwd_txy_appid_info_cf_local_tmp to std_crp_swap.dwd_txy_appid_info_cf_local\n" +
                    "on cluster default_cluster");
            System.out.println(DateUtils.format(new Date()) + " i:" + i + " rename ok");
        }

    }

    // 测试原子性replace partition

    @Test
    public void test2() {
        ckStdCrpDevDBHelper.setSlowSqlWarningValve(100000);
        for (int i = 0; i < 1000; i++) {
            List<Map> list = ckStdCrpDevDBHelper.getRaw(Map.class, "select war_zone,count(*),sum(distinct uin) as uinSum,\n" +
                    "       sum(distinct uin*uin),sum(distinct sqrt(uin*2)),sum(distinct sqrt(uin*3))\n" +
                    "       ,sum(distinct sqrt(uin*4)),sum(distinct sqrt(uin*2.5)),sum(distinct sqrt(uin*1.6)),\n" +
                    "        sum(pow(uin*1.2,10)),sum(pow(uin*1.3,9)),sum(pow(uin*1.4,10)),sum(pow(uin*1.5,9))\n" +
                    "    from std_crp.dwd_txy_scale_df where stat_time='2023-07-07'\n" +
                    "     group by war_zone");
            if (list.size() != 18) {
                System.err.println("error size:" + list.size());
            }

            if (NumberUtils.sum(list, o -> o.get("uinSum")).compareTo(new BigDecimal("109075640218303354")) != 0) {
                System.err.println("error sum:" + NumberUtils.sum(list, o -> o.get("sumAppId")));
            }

            System.out.println(DateUtils.format(new Date()) + " i:" + i + " check ok");
        }

    }

    @Test
    public void replacePartition() {
        for (int i = 0; i < 10000; i++) {
            int aff = ckStdCrpDevDBHelper.executeRaw("ALTER TABLE std_crp.dwd_txy_scale_df_local ON CLUSTER default_cluster REPLACE PARTITION ? FROM std_crp_swap.dwd_txy_scale_df_local", "2023-07-07");
            if (aff != 1) {
                System.out.println("alter table fail");
            }
            System.out.println(DateUtils.format(new Date()) + " i:" + i + " repace partition ok");
        }
    }
}
