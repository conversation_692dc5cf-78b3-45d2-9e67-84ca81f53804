package com.pugwoo.dboperate.archived.yunti_return;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dboperate.entity.demand.YuntiReturnRequestLogDO;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.collect.MapUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import com.pugwoo.wooutils.string.RegexUtils;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 用于统计慢的apply请求用的
 */
@SpringBootTest
public class TestSlowApply {

    @Resource
    private DBHelper demandIdcDBHelper;

    @Test
    public void test() {
        // 优化前
        List<YuntiReturnRequestLogDO> before = demandIdcDBHelper.getAll(YuntiReturnRequestLogDO.class,
                "WHERE url LIKE '%webapi/cvm%' AND request_body LIKE '%\"apply\"%' AND request_body LIKE '%\\\"isReject\\\":false%'\n" +
                        "AND create_time >= '2022-06-08' AND create_time <= '2022-06-09 19:30:00' ");
        System.out.println(before.size());

        long totalMs = 0;
        long totalUuid = 0;
        for (YuntiReturnRequestLogDO logDO : before) {
            totalMs += logDO.getCostMs();
            totalUuid += RegexUtils.getAllMatchStr(logDO.getRequestBody(), "\"uuid\"").size();
        }

        System.out.println(totalMs * 1.0 / totalUuid);
        System.out.println(totalUuid * 1.0 / before.size());


        // 优化后
        List<YuntiReturnRequestLogDO> after = demandIdcDBHelper.getAll(YuntiReturnRequestLogDO.class,
                "WHERE url LIKE '%webapi/cvm%' AND request_body LIKE '%\"apply\"%' AND request_body LIKE '%\\\"isReject\\\":false%'\n" +
                        "AND create_time >= '2022-06-09 19:30:00' AND create_time <= '2022-06-14' ");
        System.out.println(after.size());

        totalMs = 0;
        totalUuid = 0;
        for (YuntiReturnRequestLogDO logDO : after) {
            totalMs += logDO.getCostMs();
            int size = RegexUtils.getAllMatchStr(logDO.getRequestBody(), "\"uuid\"").size();
           // System.out.println("size:" + size + ",logid:" + logDO.getId());
            totalUuid += size;
        }

        System.out.println(totalMs * 1.0 / totalUuid);
        System.out.println(totalUuid * 1.0 / after.size());
    }

    @Test
    public void test2() {
        List<YuntiReturnRequestLogDO> list = demandIdcDBHelper.getAll(YuntiReturnRequestLogDO.class,
                "WHERE url LIKE '%webapi/cvm%' AND request_body LIKE '%\"apply\"%' AND request_body LIKE '%\\\"isReject\\\":false%'");

        Map<String, List<YuntiReturnRequestLogDO>> map =
                ListUtils.groupBy(list, o -> DateUtils.formatDate(o.getCreateTime()));

        Map<String, Double> result = MapUtils.transform(map, o -> {
            return NumberUtils.sum(o, i -> RegexUtils.getAllMatchStr(i.getRequestBody(), "\"uuid\"").size()).intValue() * 1.0 / o.size();
        });

        List<Map<String, Object>> list2 = new ArrayList<>();
        for (Map.Entry<String, Double> e : result.entrySet()) {
          //  System.out.println(e.getKey() + "," + e.getValue());
            list2.add(MapUtils.of("date", e.getKey(), "count", e.getValue()));
        }
        ListUtils.sortAscNullLast(list2, o -> (String) o.get("date"));
        for (Map<String, Object> obj : list2) {
            System.out.println(obj.get("date") + "," + obj.get("count"));
        }
    }

}
