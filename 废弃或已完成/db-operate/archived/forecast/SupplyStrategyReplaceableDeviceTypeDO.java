package com.pugwoo.dboperate.archived.forecast;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.sql.Timestamp;

/**
 * 可替换机型策略表
 */
@Data
@ToString
@Table("supply_strategy_replaceable_device_type")
public class SupplyStrategyReplaceableDeviceTypeDO {

    /** ID<br/>Column: [Id] */
    @Column(value = "Id", isKey = true, isAutoIncrement = true)
    private Long id;

    /** 缺口机型<br/>Column: [notch_device_type] */
    @Column(value = "notch_device_type")
    private String notchDeviceType;

    /** 可替换机型<br/>Column: [replace_device_type] */
    @Column(value = "replace_device_type")
    private String replaceDeviceType;

    /** 替换比<br/>Column: [replace_rate] */
    @Column(value = "replace_rate")
    private String replaceRate;

    @Column(value = "update_time")
    private Timestamp updateTime;

}