package com.pugwoo.dboperate.archived.forecast;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;

@Data
@Table("demand_detail_indicator_dept")
public class AccuracyRateDO {

    @Column("year_month")
    private String yearMonth;

    @Column("dept_label")
    private String deptLabel;

    @Column("device_type")
    private String deviceType;

    @Column("month_execution")
    private Integer monthExecution;

    @Column("base_prediction")
    private Integer basePrediction;

}
