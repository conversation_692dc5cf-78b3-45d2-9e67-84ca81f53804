package com.pugwoo.dboperate.archived.forecast;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.collect.MapUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import com.pugwoo.wooutils.string.StringTools;
import lombok.Data;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 这个实验，按照去除后的看准确率的变化情况，来看每个细项对整体准确率的影响
 */
@SpringBootTest
public class AccuracyRateByDeviceTypeTests {

    @Resource
    private DBHelper clickhouseYuntiIdcDBHelper;
    @Resource
    private DBHelper idcrmIdcDBHelper;

    @Data
    private static class YearMonthDeviceRate {
        private String yearMonth;
        private String deviceType;
        private BigDecimal rate;
        private int execution; // 执行量
        private int prediction; // 需求量
    }

    @Test
    public void test() {
        List<AccuracyRateDO> list = clickhouseYuntiIdcDBHelper.getRaw(AccuracyRateDO.class,
                "select year_month,dept_label,device_type,month_execution,base_prediction " +
                        "from cubes.demand_detail_indicator_dept " +
                        "where year_month like '2021-%' and device_type != '汇总'");

        // 对结果进行后处理
        List<BasStrategeDeviceTypeDO> deviceTypeList = idcrmIdcDBHelper.getAll(BasStrategeDeviceTypeDO.class);
        Map<String, String> deviceTypeMap = ListUtils.toMap(deviceTypeList,
                o -> o.getName(), o -> o.getDeviceNameType());

        // 对机型进行替换
        list.forEach(o -> {
            String deviceType = deviceTypeMap.get(o.getDeviceType());
            if (StringTools.isBlank(deviceType)) {
                System.err.println("机型" + o.getDeviceType() + "所属大类不存在；申领量:" + o.getMonthExecution());
                o.setDeviceType("默认");
            } else {
                o.setDeviceType(deviceType);
            }
        });

        // 然后对相同大类进行聚合
        Map<String, List<AccuracyRateDO>> group = ListUtils.groupBy(
                list, o -> o.getYearMonth() + ";" + o.getDeptLabel() + ";" + o.getDeviceType());

        List<AccuracyRateDO> newList = new ArrayList<>();
        for (Map.Entry<String, List<AccuracyRateDO>> e : group.entrySet()) {
            AccuracyRateDO rateDO = new AccuracyRateDO();
            rateDO.setYearMonth(e.getValue().get(0).getYearMonth());
            rateDO.setDeptLabel(e.getValue().get(0).getDeptLabel());
            rateDO.setDeviceType(e.getValue().get(0).getDeviceType());
            rateDO.setMonthExecution(NumberUtils.sum(e.getValue(), o -> o.getMonthExecution()).intValue());
            rateDO.setBasePrediction(NumberUtils.sum(e.getValue(), o -> o.getBasePrediction()).intValue());

            newList.add(rateDO);
        }
        list = newList;

        BigDecimal yearRate = getYearRate(list);

        System.out.println("yearRate: " + yearRate);

        // 统计出不同机型的量
        Map<String, List<AccuracyRateDO>> map = ListUtils.groupBy(list, o -> o.getDeviceType());
        List<Map<String, Object>> count = new ArrayList<>();
        for (Map.Entry<String, List<AccuracyRateDO>> e : map.entrySet()) {
            String deviceType = e.getKey();
            Integer total = NumberUtils.sum(e.getValue(), o -> o.getMonthExecution()).intValue();
            count.add(MapUtils.of("deviceType", deviceType, "execution", total));
        }
        ListUtils.sortDescNullLast(count, o -> (int)o.get("execution"));

        for (Map<String, Object> a : count) {
            System.out.println(a.get("deviceType") + "," + a.get("execution"));
        }

    }

    // 计算全年准确率
    private BigDecimal getYearRate(List<AccuracyRateDO> list) {
        List<YearMonthDeviceRate> rates = new ArrayList<>();

        // 计算一下每个机型每个月的准确率
        Map<String, List<AccuracyRateDO>> map = ListUtils.groupBy(list, o -> o.getYearMonth() + ";" + o.getDeviceType());
        for (Map.Entry<String, List<AccuracyRateDO>> e : map.entrySet()) {
            BigDecimal execution = NumberUtils.sum(e.getValue(), o -> o.getMonthExecution());
            BigDecimal prediction = NumberUtils.sum(e.getValue(), AccuracyRateDO::getBasePrediction);
            BigDecimal rate = accuracyRate(execution.intValue(), prediction.intValue());

            YearMonthDeviceRate rateDTO = new YearMonthDeviceRate();
            String[] split = e.getKey().split(";");
            rateDTO.setYearMonth(split[0]);
            rateDTO.setDeviceType(split[1]);
            rateDTO.setRate(rate);
            rateDTO.setExecution(execution.intValue());
            rateDTO.setPrediction(prediction.intValue());

            rates.add(rateDTO);
        }

        // 然后算出每个月的汇总准确率，按执行量加权
        Map<String, List<YearMonthDeviceRate>> months = ListUtils.groupBy(rates, o -> o.getYearMonth());
        BigDecimal yearSum = BigDecimal.ZERO;
        for (Map.Entry<String, List<YearMonthDeviceRate>> e : months.entrySet()) {
            int totalExecution = NumberUtils.sum(e.getValue(), o -> o.getExecution()).intValue();
            BigDecimal monthRate = NumberUtils.sum(e.getValue(), o -> o.getRate().multiply(
                    BigDecimal.valueOf(o.getExecution() * 1.0 / totalExecution)));
            monthRate = NumberUtils.roundUp(monthRate, 2);

            yearSum = yearSum.add(monthRate);
        }

        return NumberUtils.divide(yearSum, 12, 3);
    }

    private BigDecimal accuracyRate(Integer execution, Integer prediction) {
        if (execution == null || execution <= 0) {
            if (prediction != null && prediction > 0) {
                return BigDecimal.ZERO;
            } else {
                return BigDecimal.valueOf(100);
            }
        }
        if (prediction == null || prediction <= 0) {
            return BigDecimal.ZERO;
        }
        return NumberUtils.roundUp(BigDecimal.valueOf(
                Math.min(execution, prediction) * 100.0 / Math.max(execution, prediction)
        ), 1);
    }


}
