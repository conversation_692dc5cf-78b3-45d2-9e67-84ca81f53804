package com.pugwoo.dboperate.archived.forecast;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.lang.NumberUtils;
import lombok.Data;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 这个实验，按照去除后的看准确率的变化情况，来看每个细项对整体准确率的影响
 */
@SpringBootTest
public class AccuracyRateTests {

    @Resource
    private DBHelper clickhouseYuntiIdcDBHelper;

    @Data
    private static class YearMonthDeviceRate {
        private String yearMonth;
        private String deviceType;
        private BigDecimal rate;
        private int execution; // 执行量
        private int prediction; // 需求量
    }

    @Data
    private static class Delta {
        private AccuracyRateDO rateDO;
        private BigDecimal delta;
    }

    @Test
    public void test() {
        List<AccuracyRateDO> list = clickhouseYuntiIdcDBHelper.getRaw(AccuracyRateDO.class,
                "select year_month,dept_label,device_type,month_execution,base_prediction " +
                        "from cubes.demand_detail_indicator_dept " +
                        "where year_month like '2021-%' and device_type != '汇总'");

        BigDecimal yearRate = getYearRate(list);

        // 对于list的每一条记录，删除之后计算准确率，再计算偏差
        List<Delta> deltas = new ArrayList<>();

        int total = list.size();
        for (int i = 0; i < total; i++) {
            List<AccuracyRateDO> tmp = JSON.clone(list, AccuracyRateDO.class);
            AccuracyRateDO remove = tmp.remove(i);
            BigDecimal yearRateTmp = getYearRate(tmp);

            Delta delta = new Delta();
            delta.setRateDO(remove);
            delta.setDelta(yearRate.subtract(yearRateTmp));
            deltas.add(delta);
        }

        ListUtils.sortAscNullLast(deltas, o -> o.getDelta());

        System.out.println("年月,部门,机型,执行量,需求量,单项准确率,单项执行率,影响年度准确率");
        for (Delta delta : deltas) {
            System.out.println("\"" + delta.getRateDO().getYearMonth() + "\"," + delta.getRateDO().getDeptLabel()
                + "," + delta.getRateDO().getDeviceType() + "," + delta.getRateDO().getMonthExecution()
                + "," + delta.getRateDO().getBasePrediction()
                    + "," + accuracyRate(delta.getRateDO().getMonthExecution(), delta.getRateDO().getBasePrediction())
                    + "," + executeRate(delta.getRateDO().getMonthExecution(), delta.getRateDO().getBasePrediction())
                    + "," + delta.getDelta());
        }


    }

    // 计算全年准确率
    private BigDecimal getYearRate(List<AccuracyRateDO> list) {
        List<YearMonthDeviceRate> rates = new ArrayList<>();

        // 计算一下每个机型每个月的准确率
        Map<String, List<AccuracyRateDO>> map = ListUtils.groupBy(list, o -> o.getYearMonth() + ";" + o.getDeviceType());
        for (Map.Entry<String, List<AccuracyRateDO>> e : map.entrySet()) {
            BigDecimal execution = NumberUtils.sum(e.getValue(), o -> o.getMonthExecution());
            BigDecimal prediction = NumberUtils.sum(e.getValue(), AccuracyRateDO::getBasePrediction);
            BigDecimal rate = accuracyRate(execution.intValue(), prediction.intValue());

            YearMonthDeviceRate rateDTO = new YearMonthDeviceRate();
            String[] split = e.getKey().split(";");
            rateDTO.setYearMonth(split[0]);
            rateDTO.setDeviceType(split[1]);
            rateDTO.setRate(rate);
            rateDTO.setExecution(execution.intValue());
            rateDTO.setPrediction(prediction.intValue());

            rates.add(rateDTO);
        }

        // 然后算出每个月的汇总准确率，按执行量加权
        Map<String, List<YearMonthDeviceRate>> months = ListUtils.groupBy(rates, o -> o.getYearMonth());
        BigDecimal yearSum = BigDecimal.ZERO;
        for (Map.Entry<String, List<YearMonthDeviceRate>> e : months.entrySet()) {
            int totalExecution = NumberUtils.sum(e.getValue(), o -> o.getExecution()).intValue();
            BigDecimal monthRate = NumberUtils.sum(e.getValue(), o -> o.getRate().multiply(
                    BigDecimal.valueOf(o.getExecution() * 1.0 / totalExecution)));
            monthRate = NumberUtils.roundUp(monthRate, 2);

            yearSum = yearSum.add(monthRate);
        }

        return NumberUtils.divide(yearSum, 12, 3);
    }

    private BigDecimal accuracyRate(Integer execution, Integer prediction) {
        if (execution == null || execution <= 0) {
            if (prediction != null && prediction > 0) {
                return BigDecimal.ZERO;
            } else {
                return BigDecimal.valueOf(100);
            }
        }
        if (prediction == null || prediction <= 0) {
            return BigDecimal.ZERO;
        }
        return NumberUtils.roundUp(BigDecimal.valueOf(
                Math.min(execution, prediction) * 100.0 / Math.max(execution, prediction)
        ), 1);
    }

    private BigDecimal executeRate(Integer execution, Integer prediction) {
        if (execution == null || execution <= 0) {
             return BigDecimal.ZERO;
        }
        if (prediction == null || prediction <= 0) {
            return BigDecimal.valueOf(99999999);
        }
        return NumberUtils.divide(BigDecimal.valueOf(execution * 100), prediction, 2);
    }

}
