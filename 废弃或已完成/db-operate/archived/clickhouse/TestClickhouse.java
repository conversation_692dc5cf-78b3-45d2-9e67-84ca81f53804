package com.pugwoo.dboperate.archived.clickhouse;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dboperate.archived.clickhouse.entity.AccountDO;
import com.pugwoo.wooutils.json.JSON;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;
import java.util.UUID;

@SpringBootTest
public class TestClickhouse {

    @Resource
    private DBHelper clickhouseLabDBHelper;

    @Test
    public void testQuery() {
        List<AccountDO> all = clickhouseLabDBHelper.getAll(AccountDO.class);
        System.out.println(JSON.toJson(all));
    }

    @Test
    public void testBatchInsert() {
        List<AccountDO> all = new ArrayList<>();
        for (int i = 0; i < 10000; i++) {
            AccountDO accountDO = new AccountDO();
            accountDO.setAccountId(new Random().nextLong());
            accountDO.setName(UUID.randomUUID().toString());
            accountDO.setAddress("addr:" + UUID.randomUUID().toString());
            accountDO.setYear(new Random().nextLong());

            all.add(accountDO);
        }

        clickhouseLabDBHelper.insertBatchWithoutReturnId(all);
    }

}
