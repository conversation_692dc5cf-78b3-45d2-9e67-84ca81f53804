package com.pugwoo.dboperate.archived.clickhouse.entity;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;

@Data
@Table("account")
public class AccountDO {

    @Column("accountid")
    private Long accountId;

    @Column("name")
    private String name;

    @Column("address")
    private String address;

    @Column("year")
    private Long year;

}
