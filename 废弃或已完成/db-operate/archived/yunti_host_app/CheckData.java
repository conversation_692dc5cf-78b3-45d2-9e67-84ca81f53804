package com.pugwoo.dboperate.archived.yunti_host_app;

import com.pugwoo.dboperate.service.BaseInfoService;
import com.pugwoo.dboperate.utils.AmountUtils;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.io.IOUtils;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.lang.NumberUtils;
import com.pugwoo.wooutils.string.RegexUtils;
import com.pugwoo.wooutils.string.StringTools;
import lombok.Data;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import jakarta.annotation.Resource;
import java.io.FileInputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@SpringBootTest
public class CheckData {

    @Resource
    private BaseInfoService baseInfoService;

    @Data
    public static class Item {
        private String yearMonth;
        private String city;
        private String deptName;
        private String planProductName;
        private String zone;
        private String instanceClass;
        private String instanceModel;
        private Integer leftCvmAmount;
        private Integer leftCoreAmount;
        private Integer returnCvmAmount;
        private Integer returnCoreAmount;
        private Integer inventoryCvmAmount;
        private Integer inventoryCoreAmount;
        private Integer resultCvmAmount;
        private List<String> memos;
    }

    @Data
    private static class InventoryItem {
        private String zone;
        private String instanceModel;
        private Integer inventory;
    }

    private static Item parse(String[] strs) {
        Item item = new Item();
        item.setYearMonth(strs[0]);
        item.setCity(strs[1]);
        item.setDeptName(strs[2]);
        item.setPlanProductName(strs[3]);
        item.setZone(strs[4]);
        item.setInstanceClass(strs[5]);
        item.setInstanceModel(strs[6]);
        item.setLeftCvmAmount(NumberUtils.parseInt(strs[7]));
        item.setLeftCoreAmount(NumberUtils.parseInt(strs[8]));
        item.setReturnCvmAmount(NumberUtils.parseInt(strs[9]));
        item.setReturnCoreAmount(NumberUtils.parseInt(strs[10]));
        item.setInventoryCvmAmount(NumberUtils.parseInt(strs[11]));
        item.setInventoryCoreAmount(NumberUtils.parseInt(strs[12]));
        item.setResultCvmAmount(NumberUtils.parseInt(strs[13]));
        if (strs.length == 15) {
            String[] strs2 = strs[14].split(";");
            List<String> memos = new ArrayList<>();
            item.setMemos(memos);
            for (String s : strs2) {
                if (StringTools.isNotBlank(s)) {
                    memos.add(s.trim());
                }
            }
        }
        return item;
    }

    @Test
    public  void test() throws Exception {
        String csv = IOUtils.readAllAndClose(new FileInputStream("d:/cvm-20220322.csv"), "utf-8");
        String[] lines = StringTools.splitLines(csv);
        List<Item> items = new ArrayList<>();
        for (int i = 0; i < lines.length; i++) {
            if (i == 0) {
                continue;
            }

            String[] strs = lines[i].split(",");
            Item item = parse(strs);
            items.add(item);
        }

        Map<String, InventoryItem> inventoryMap = new HashMap<>();

        for (Item item : items) {
            if (item.getInventoryCvmAmount() - item.getLeftCvmAmount() != item.getResultCvmAmount()) {
                System.err.println(JSON.toJson(item));
            }

            Integer usedInventory = null;
            if (item.getResultCvmAmount() < 0) {
                usedInventory = item.getInventoryCvmAmount();
            } else {
                usedInventory = item.getLeftCvmAmount();
            }

            int total = 0;
            if (item.getMemos() != null) {
                for (String memo : item.getMemos()) {
                    String matched = RegexUtils.getFirstMatchStr(memo, "满足(\\d+)台");
                    Integer count = NumberUtils.parseInt(matched);
                    total += count;
                    Integer inventoryCount = null;

                    // 按memo的值还原为库存map
                    String zone;
                    String instanceModel;
                    if (memo.contains("同可用区")) {
                        zone = item.getZone();
                    } else {
                        zone = RegexUtils.getFirstMatchStr(memo, "(.+)下");
                    }
                    if (memo.contains("同规格")) {
                        instanceModel = item.getInstanceModel();
                        inventoryCount = count;
                    } else {
                        instanceModel = RegexUtils.getFirstMatchStr(memo, "下(.+)库存");
                        if (instanceModel == null) {
                            instanceModel = RegexUtils.getFirstMatchStr(memo, "区(.+)库存");
                        }
                        // 不是同机型规格，还要换算一下
                        int replaceRate = baseInfoService.getReplaceRate(item.getInstanceModel(), instanceModel);
                        inventoryCount = AmountUtils.ceiling(new BigDecimal(count / replaceRate));
                    }

                    if (zone == null || zone.equals("null") || instanceModel == null || instanceModel.equals("null")) {
                        System.err.println(memo);
                    }

                    String key = zone + ":" + instanceModel;
                    InventoryItem inventoryItem = inventoryMap.get(key);
                    if (inventoryItem == null) {
                        inventoryItem = new InventoryItem();
                        inventoryItem.setZone(zone);
                        inventoryItem.setInstanceModel(instanceModel);
                        inventoryItem.setInventory(0);
                        inventoryMap.put(key, inventoryItem);
                    }
                    inventoryItem.setInventory(inventoryItem.getInventory() + inventoryCount);
                }
            }

            if (usedInventory != total) {
                System.err.println(JSON.toJson(item));
            }


        }

        // 拿到实际的库存列表并打印出来：这里只关心被匹配中的库存，没匹配中不管；所以实际库存比这里匹配中的库存大就可以了
        System.out.println("==============================");
        ArrayList<InventoryItem> inventoryItems = new ArrayList<>(inventoryMap.values());
        ListUtils.sortDescNullLast(inventoryItems, o -> o.getInventory());
        for (InventoryItem inventoryItem : inventoryItems) {
            System.out.println(inventoryItem.getZone() + "," + inventoryItem.getInstanceModel() + ","
                + inventoryItem.getInventory());
        }
    }

}
