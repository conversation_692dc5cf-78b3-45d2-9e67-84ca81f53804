package com.pugwoo.dboperate.archived.jxc;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dboperate.entity.ckclouddemand.ReportErpServerDetailDO;
import com.pugwoo.wooutils.lang.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import jakarta.annotation.Resource;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 测试写入clickhouse的原子性（写入完成之前外部不可见，这里单表做到就可以了）
 */
@Slf4j
@SpringBootTest
public class TestWriteCkAtomic {

    @Resource
    private DBHelper ckCloudDemandDevDBHelper;

    /**
     * 实验准备：
     * 1) 用的是report_erp_server_detail，它是个distributed表，底层是report_erp_server_detail_local，rand()
     *
     * 实验猜想：用的是rand()，插入的话，同一partition会只放在同一台机器上吗？
     * 实验步骤：查询某个partition的数据，发现report_erp_server_detail_local上的数量不等于全量，那么就意味着不是一台机器。
     *          而且分布是比较均匀的。
     * 实验结论：同一partition【不会】只放在同一台机器上。
     */
    @Test
    public void testWriteToLocal() {
        LocalDate statTime = DateUtils.parseLocalDate("1970-01-01");

        List<ReportErpServerDetailDO> list = new ArrayList<>();
        for (int i = 0; i < 100000; i++) {
            ReportErpServerDetailDO d = new ReportErpServerDetailDO();
            d.setStatTime(statTime);
            d.setCreateTime(new Date());
            d.setProductType("NIC");
            list.add(d);
        }

        ckCloudDemandDevDBHelper.insertBatchWithoutReturnId(list);
        System.out.println("done");
    }

}
