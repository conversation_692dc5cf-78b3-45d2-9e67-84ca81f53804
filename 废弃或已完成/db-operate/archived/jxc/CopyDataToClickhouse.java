package com.pugwoo.dboperate.archived.jxc;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dboperate.entity.jxctxy.DailyInnerAppidZoneidGinstypeDO;
import com.pugwoo.dboperate.entity.jxctxy.DailyKaAppidZoneidGinstypeDO;
import com.pugwoo.dboperate.entity.jxctxy.DailyLongtailZoneidGinstypeDO;
import com.pugwoo.dboperate.entity.jxctxy.StaticGinstypeDO;
import com.pugwoo.dboperate.entity.jxctxy.StaticPrincipalGinsfamilyDO;
import com.pugwoo.dboperate.entity.jxctxy.StaticZoneDO;
import com.pugwoo.wooutils.collect.ListUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import jakarta.annotation.Resource;
import java.util.List;

@Slf4j
@SpringBootTest
public class CopyDataToClickhouse {

    @Resource
    private DBHelper ckCloudDemandDevDBHelper;
    @Resource
    private DBHelper jxcTxyIdcDBHelper;

    @Test
    public void copyDic() {

        List<StaticGinstypeDO> all1 = jxcTxyIdcDBHelper.getAll(StaticGinstypeDO.class);
        List<com.pugwoo.dboperate.entity.ckclouddemand.StaticGinstypeDO> all11 =
                ListUtils.transform(all1, o ->
                        com.pugwoo.dboperate.entity.ckclouddemand.StaticGinstypeDO.from(o));
        ckCloudDemandDevDBHelper.executeRaw("truncate table static_ginstype");
        ckCloudDemandDevDBHelper.insertBatchWithoutReturnId(all11);

        List<StaticPrincipalGinsfamilyDO> all2 = jxcTxyIdcDBHelper.getAll(StaticPrincipalGinsfamilyDO.class);
        List<com.pugwoo.dboperate.entity.ckclouddemand.StaticPrincipalGinsfamilyDO> all22 =
                ListUtils.transform(all2, o ->
                        com.pugwoo.dboperate.entity.ckclouddemand.StaticPrincipalGinsfamilyDO.from(o));
        ckCloudDemandDevDBHelper.executeRaw("truncate table static_principal_ginsfamily");
        ckCloudDemandDevDBHelper.insertBatchWithoutReturnId(all22);

        List<StaticZoneDO> all3 = jxcTxyIdcDBHelper.getAll(StaticZoneDO.class);
        List<com.pugwoo.dboperate.entity.ckclouddemand.StaticZoneDO> all33 =
                ListUtils.transform(all3, o ->
                        com.pugwoo.dboperate.entity.ckclouddemand.StaticZoneDO.from(o));
        ckCloudDemandDevDBHelper.executeRaw("truncate table static_zone");
        ckCloudDemandDevDBHelper.insertBatchWithoutReturnId(all33);

    }

    @Test
    public void copyKa() {
        List<String> statTimes = jxcTxyIdcDBHelper.getRaw(String.class,
                "select distinct stattime from daily_ka_appid_zoneid_ginstype order by stattime");
        for (String statTime : statTimes) {
            List<DailyKaAppidZoneidGinstypeDO> all = jxcTxyIdcDBHelper.getAll(
                    DailyKaAppidZoneidGinstypeDO.class,
                    "where stattime=?", statTime);
            List<com.pugwoo.dboperate.entity.ckclouddemand.DailyKaAppidZoneidGinstypeDO> all2 =
                    ListUtils.transform(all,
                    o -> com.pugwoo.dboperate.entity.ckclouddemand.DailyKaAppidZoneidGinstypeDO.from(o));

            ckCloudDemandDevDBHelper.executeRaw(
                    "ALTER TABLE cloud_demand.daily_ka_appid_zoneid_ginstype_local ON CLUSTER default_cluster DROP PARTITION ?", statTime);
            ckCloudDemandDevDBHelper.insertBatchWithoutReturnId(all2);

            System.out.println("date:" + statTime + "done");
        }
    }

    @Test
    public void copyLongtail() {
        List<String> statTimes = jxcTxyIdcDBHelper.getRaw(String.class,
                "select distinct stattime from daily_longtail_zoneid_ginstype order by stattime");
        for (String statTime : statTimes) {
            List<DailyLongtailZoneidGinstypeDO> all = jxcTxyIdcDBHelper.getAll(
                    DailyLongtailZoneidGinstypeDO.class,
                    "where stattime=?", statTime);
            List<com.pugwoo.dboperate.entity.ckclouddemand.DailyLongtailZoneidGinstypeDO> all2 =
                    ListUtils.transform(all,
                            o -> com.pugwoo.dboperate.entity.ckclouddemand.DailyLongtailZoneidGinstypeDO.from(o));

            ckCloudDemandDevDBHelper.executeRaw(
                    "ALTER TABLE cloud_demand.daily_longtail_zoneid_ginstype_local ON CLUSTER default_cluster DROP PARTITION ?", statTime);
            ckCloudDemandDevDBHelper.insertBatchWithoutReturnId(all2);

            System.out.println("date:" + statTime + "done");
        }
    }

    @Test
    public void copyInner() {
        List<String> statTimes = jxcTxyIdcDBHelper.getRaw(String.class,
                "select distinct stattime from daily_inner_appid_zoneid_ginstype order by stattime");
        for (String statTime : statTimes) {
            List<DailyInnerAppidZoneidGinstypeDO> all = jxcTxyIdcDBHelper.getAll(
                    DailyInnerAppidZoneidGinstypeDO.class,
                    "where stattime=?", statTime);
            List<com.pugwoo.dboperate.entity.ckclouddemand.DailyInnerAppidZoneidGinstypeDO> all2 =
                    ListUtils.transform(all,
                            o -> com.pugwoo.dboperate.entity.ckclouddemand.DailyInnerAppidZoneidGinstypeDO.from(o));

            ckCloudDemandDevDBHelper.executeRaw(
                    "ALTER TABLE cloud_demand.daily_inner_appid_zoneid_ginstype_local ON CLUSTER default_cluster DROP PARTITION ?", statTime);
            ckCloudDemandDevDBHelper.insertBatchWithoutReturnId(all2);

            System.out.println("date:" + statTime + "done");
        }
    }

}
