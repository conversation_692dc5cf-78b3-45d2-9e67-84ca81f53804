package com.pugwoo.dboperate.obs.impl;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dboperate.model.CvmInstanceTypeInfoDTO;
import com.pugwoo.dboperate.obs.BaseInfoService;
import com.pugwoo.wooutils.collect.ListUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Service
public class BaseInfoServiceImpl implements BaseInfoService {

    @Resource
    private DBHelper obsIdcDBHelper;

    private List<CvmInstanceTypeInfoDTO> getCvmInstanceTypeInfos() {
        String sql = "select b.CvmInstanceClass,\n"
                + "       b.CvmInstanceModel,\n"
                + "       b.CpuAmount,\n"
                + "       b.<PERSON>,\n"
                + "       b.CanReplaceInstanceClass,\n"
                + "       e.ToType HostDeviceClass,\n"
                + "       ifnull(floor(1 / e.TransferRatio), 0) HostProduceNum\n"
                + "from obs_budget_roll_adjust_cvm_append_device e\n"
                + "         left join bas_obs_cloud_cvm_type b on b.CvmInstanceModel = e.FromType\n"
                + "where e.EnableFlag = 1\n"
                + "  and b.CvmInstanceClass is not null\n";

        return obsIdcDBHelper.getRaw(CvmInstanceTypeInfoDTO.class, sql);
    }

    private Map<String, CvmInstanceTypeInfoDTO> getCvmInstanceTypeInfoMap() {
        List<CvmInstanceTypeInfoDTO> list = getCvmInstanceTypeInfos();
        Map<String, CvmInstanceTypeInfoDTO> instanceTypeMap = ListUtils.toMap(list,
                CvmInstanceTypeInfoDTO::getCvmInstanceModel, o -> o);
        return instanceTypeMap;
    }

    @Override
    public int getReplaceRate(String sourceInstanceModel, String targetInstanceModel) {
        if (Objects.equals(sourceInstanceModel, targetInstanceModel)) { // 相同机型置换比例为1
            return 1;
        }

        Map<String, CvmInstanceTypeInfoDTO> instanceTypeMap = getCvmInstanceTypeInfoMap();
        CvmInstanceTypeInfoDTO sourceInfo = instanceTypeMap.get(sourceInstanceModel);
        if (sourceInfo == null) {
            log.error("instance model:{} not exist", sourceInstanceModel);
            return 0;
        }
        CvmInstanceTypeInfoDTO targetInfo = instanceTypeMap.get(targetInstanceModel);
        if (targetInfo == null) {
            log.error("instance model:{} not exist", targetInstanceModel);
            return 0;
        }

        int oriCpuNum = sourceInfo.getCpuAmount();
        int oriMemNum = sourceInfo.getRamAmount();
        int replaceCpuNumByOne = Math.floorDiv(targetInfo.getCpuAmount(), oriCpuNum); // 当目标机型核心数比原机型低，无法转
        int replaceMemNumByOne = Math.floorDiv(targetInfo.getRamAmount(), oriMemNum);
        int replaceRate = Math.min(replaceCpuNumByOne, replaceMemNumByOne); //替换比例 replaceRate : 1
        return replaceRate;
    }

}
