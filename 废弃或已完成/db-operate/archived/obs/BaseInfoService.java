package com.pugwoo.dboperate.obs;

public interface BaseInfoService {

    /**
     * 【这个方法来自于yunti-host-app】
     *
     * 查询机型替换比例，返回的数字是一台targetInstanceModel等于多少台sourceInstanceModel，这里有两层逻辑：
     * 1）如果机型不存在或当目标机型核心数比原机型低，则无法转，返回0
     * 2）取核心和内存置换比例的最低值
     * @param sourceInstanceModel 原机型
     * @param targetInstanceModel 目的机型
     */
    int getReplaceRate(String sourceInstanceModel, String targetInstanceModel);

}
