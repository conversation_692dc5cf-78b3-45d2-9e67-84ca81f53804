package com.pugwoo.dboperate.multi_trans_test;

import com.pugwoo.dbhelper.DBHelper;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import jakarta.annotation.Resource;

@Controller
public class TestController {

    @Resource
    private DataSourceTransactionManager cloudDemandIdcTransactionManager;
    @Resource
    private DataSourceTransactionManager mckIdcTransactionManager;

    @Resource
    private DBHelper cloudDemandIdcDBHelper;
    @Resource
    private DBHelper mckIdcDBHelper;

    @ResponseBody
    @GetMapping("/test")
    public String test(Boolean t) {
        DefaultTransactionDefinition demandDef = new DefaultTransactionDefinition();
        demandDef.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRED);
        DefaultTransactionDefinition mckDef = new DefaultTransactionDefinition();
        mckDef.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRED);

        TransactionStatus demandStatus = cloudDemandIdcTransactionManager.getTransaction(demandDef);
        TransactionStatus mckStatus = mckIdcTransactionManager.getTransaction(mckDef);

        try {
            MultiTransTestDO multiTransTestDO = new MultiTransTestDO();
            cloudDemandIdcDBHelper.insert(multiTransTestDO);
            multiTransTestDO.setId(null);
            mckIdcDBHelper.insert(multiTransTestDO);

            if (t != null && t) {
                int a = 1 / 0;
            }

            mckIdcTransactionManager.commit(mckStatus);
            cloudDemandIdcTransactionManager.commit(demandStatus);
        } catch (Exception e) {
            // 遇到异常回滚事务
            mckIdcTransactionManager.rollback(mckStatus);
            cloudDemandIdcTransactionManager.rollback(demandStatus);
        }

        return "ok";
    }

}
