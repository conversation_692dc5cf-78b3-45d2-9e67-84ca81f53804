package com.pugwoo.dboperate.调试腾讯云接口;

import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.common.profile.Language;
import com.tencentcloudapi.monitor.v20180724.MonitorClient;
import com.tencentcloudapi.monitor.v20180724.models.Dimension;
import com.tencentcloudapi.monitor.v20180724.models.GetMonitorDataInternalRequest;
import com.tencentcloudapi.monitor.v20180724.models.GetMonitorDataInternalResponse;
import com.tencentcloudapi.monitor.v20180724.models.Instance;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class TxySignature {

    public static void main(String[] args) {

        try {
            // 实例化一个认证对象，入参需要传入腾讯云账户secretId，secretKey,此处还需注意密钥对的保密
            Credential cred = new Credential("AKIDvgEm8F9oFSI2ayBC1czgMvU2QfkeMvLa", "980f7nHZfYHpBLJ5e1DD2tZ2JOGwOehX");

            // 实例化一个http选项，可选的，没有特殊需求可以跳过
            HttpProfile httpProfile = new HttpProfile();
            //  从3.1.16版本开始, 单独设置 HTTP 代理
//             httpProfile.setProxyHost("127.0.0.1");
//             httpProfile.setProxyPort(8888);

            httpProfile.setReqMethod("POST"); // get请求(默认为post请求)
            httpProfile.setConnTimeout(30); // 请求连接超时时间，单位为秒(默认60秒)
            httpProfile.setWriteTimeout(30);  // 设置写入超时时间，单位为秒(默认0秒)
            httpProfile.setReadTimeout(30);  // 设置读取超时时间，单位为秒(默认0秒)
            httpProfile.setEndpoint("monitor.ap-guangzhou.tencentcloudapi.woa.com"); // 指定接入地域域名(默认就近接入)

            // 实例化一个client选项，可选的，没有特殊需求可以跳过
            ClientProfile clientProfile = new ClientProfile();
            //clientProfile.setSignMethod("HmacSHA256"); // 指定签名算法(默认为HmacSHA256) // 【这里要去掉】
            // 自3.1.80版本开始，SDK 支持打印日志。
            clientProfile.setHttpProfile(httpProfile);
            clientProfile.setDebug(true);
            // 从3.1.16版本开始，支持设置公共参数 Language, 默认不传，选择(ZH_CN or EN_US)
            clientProfile.setLanguage(Language.EN_US);
            // 实例化要请求产品(以cvm为例)的client对象,clientProfile是可选的
            MonitorClient client = new MonitorClient(cred, "ap-guangzhou", clientProfile);

            // 实例化一个cvm实例信息查询请求对象,每个接口都会对应一个request对象。
            GetMonitorDataInternalRequest req = new GetMonitorDataInternalRequest();

            Map<String, String> header = new HashMap<>();
            header.put("X-Auth-OwnerUin", "3034334170"); // 这个必须
            header.put("X-Auth-AppId", "1258344706"); // 这个必须
            req.SetHeader(header);

            // 填充请求参数,这里request对象的成员变量即对应接口的入参
            req.setNamespace("QCE/CVM");
            req.setMetricName("BaseCpuUsage");
            req.setPeriod(86400L);
            req.setStartTime("2023-11-20T00:00:00+08:00");
            req.setEndTime("2023-12-20T00:00:00+08:00");

            // 结论：可以拿1天的颗粒度（最大了），时间范围1个月，也就是这个月的前7天可以拿到上个月全量的cpu的峰值、均值，每个月存一份即可

            List<Instance> instance = new ArrayList<>();
            Instance ins = new Instance();
            List<Dimension> dimensions = new ArrayList<>();
            Dimension dimension = new Dimension();
            dimension.setName("InstanceId");
            dimension.setValue("ins-cpt8ai0c");
            dimensions.add(dimension);
            instance.add(ins);
            ins.setDimensions(dimensions.toArray(new Dimension[0]));
            req.setInstances(instance.toArray(new Instance[0]));

            GetMonitorDataInternalResponse resp = client.GetMonitorDataInternal(req);

            // 输出json格式的字符串回包
            System.out.println(GetMonitorDataInternalResponse.toJsonString(resp));

            // {"Period":86400,"MetricName":"BaseCpuUsage","DataPoints":[{"Dimensions":[{"Name":"InstanceId","Value":"ins-cpt8ai0c"}],"Timestamps":[1700409600,1700496000,1700582400,1700668800,1700755200,1700841600,1700928000,1701014400,1701100800,1701187200,1701273600,1701360000,1701446400,1701532800,1701619200,1701705600,1701792000,1701878400,1701964800,1702051200,1702137600,1702224000,1702310400,1702396800,1702483200,1702569600,1702656000,1702742400,1702828800,1702915200],"Values":[10.324,12.808,11.158,12.074,12.116,12.991,10.383,11.633,12.708,10.3,12.816,12.408,11.166,11.641,12.4,12.175,12.133,11.633,12.458,11.625,11.474,12.375,12.699,10.95,11.099,12.7,11.633,10.724,12.349,11.949]}],"StartTime":"2023-11-20T00:00:00+08:00","EndTime":"2023-12-20T00:00:00+08:00","Msg":"","RequestId":"3573a44a-ae77-44dc-91ac-26f4241d0fa2"}

        } catch (TencentCloudSDKException e) {
            System.out.println(e.toString());
        }

    }

}
