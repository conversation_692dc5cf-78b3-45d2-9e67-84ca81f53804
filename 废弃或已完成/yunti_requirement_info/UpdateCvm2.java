package com.nickxie.others.yunti_requirement_info;

import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.net.Browser;
import com.pugwoo.wooutils.net.HttpResponse;

import java.util.HashMap;
import java.util.Map;

/**
 * 刷新机型
 */
public class UpdateCvm2 {

    private static String cookie = "x-host-key-ngn=1748f6098bb-7586b09dd3f2bd4938e4b2dc681c5d38d0a62025; x-tofapi-host-key=1748f6099af-751720cb3333618ccc9aba93d793fb2a21368b0c; x-host-key-oaback=1748f687aff-26850f70e274fae1aee6a8816f31349d8938efaf; x-host-key-front=1748f687b03-e014258f13e6bc503c9bf57d75c5101a73d16108; x-mp-host-key=174a4790aeb-e50a11633b6b9c7d5e3d1aff3ab0a1b60ade7cf5; x-imp-host-key=174a47cdbd7-fff2ee4203e9853118cd650baef5dcc89abad7de; x_host_key=174a4938af8-63b3277dfdf8740b4cf7ad4b1303efd4efa93fdb; pgv_info=ssid=s4877452644; x-client-ssid=17a14df2b2f-f7526d96452b4bc58506426d1e1f4722e82a1373; DiggerTraceId=3b42c820-cf14-11eb-a373-619c8e7deeef; x_host_key_access_https=ea5d36ff4371418ef0952e9b13e7a232a1dfa7a2_s; x_host_key_access=ea5d36ff4371418ef0952e9b13e7a232a1dfa7a2_s; ERP_USERNAME=nickxie; km_u=69a44e0523ce978af1d5e50a976cfc66a71e195fd420a2f82cb6d9e8a19d88a7eba1b40fe701e377; t_uid=nickxie; km_uid=nickxie; RIO_TCOA_TICKET=tof:TOF4TeyJ2IjoiNCIsInRpZCI6IktYdWMyR3dmcGhPWjRUQmdRc1FpWktrNUJ3ZzNtMUtGIiwiaXNzIjoiMTAuOTkuMTUuNDkiLCJpYXQiOiIyMDIxLTExLTI5VDIxOjU5OjMwLjAxNDEyNDc4OCswODowMCIsImF1ZCI6IjAuMC4wLjAiLCJoYXNoIjoiRTcxQ0I0MkFEOTZCM0Q2MEE3QjA5NjNCRTM1MEQwN0VCRTJBQzQwODQwNTM3MUJBQzlBMkVDQTZCQjE2QURFMSIsIm5oIjoiOEMyMkE5NzA1MkIzQTI2NDgzQ0FFM0E4NDIxQTI5NDFEMjgwN0ZBOEYwMDA2RDQ4NkFFOTVFNjczOTgyQzhEMyJ9; RIO_TCOA_TICKET_HTTPS=tof:TOF4TeyJ2IjoiNCIsInRpZCI6IktYdWMyR3dmcGhPWjRUQmdRc1FpWktrNUJ3ZzNtMUtGIiwiaXNzIjoiMTAuOTkuMTUuNDkiLCJpYXQiOiIyMDIxLTExLTI5VDIxOjU5OjMwLjAxNDEyNDc4OCswODowMCIsImF1ZCI6IjAuMC4wLjAiLCJoYXNoIjoiRTcxQ0I0MkFEOTZCM0Q2MEE3QjA5NjNCRTM1MEQwN0VCRTJBQzQwODQwNTM3MUJBQzlBMkVDQTZCQjE2QURFMSIsIm5oIjoiOEMyMkE5NzA1MkIzQTI2NDgzQ0FFM0E4NDIxQTI5NDFEMjgwN0ZBOEYwMDA2RDQ4NkFFOTVFNjczOTgyQzhEMyJ9; TCOA_TICKET=TOF4TeyJ2IjoiNCIsInRpZCI6IktYdWMyR3dmcGhPWjRUQmdRc1FpWktrNUJ3ZzNtMUtGIiwiaXNzIjoiMTAuOTkuMTUuNDkiLCJpYXQiOiIyMDIxLTExLTI5VDIxOjU5OjMwLjAxNDEyNDc4OCswODowMCIsImF1ZCI6IjAuMC4wLjAiLCJoYXNoIjoiRTcxQ0I0MkFEOTZCM0Q2MEE3QjA5NjNCRTM1MEQwN0VCRTJBQzQwODQwNTM3MUJBQzlBMkVDQTZCQjE2QURFMSIsIm5oIjoiOEMyMkE5NzA1MkIzQTI2NDgzQ0FFM0E4NDIxQTI5NDFEMjgwN0ZBOEYwMDA2RDQ4NkFFOTVFNjczOTgyQzhEMyJ9; ERP_REAL_USERNAME=nickxie; tgw_l7_route=8c5bfb8d0d069a560830c6d5c4b51eb1";

    public static void main(String[] args) throws Exception {
        operate("广州三区", "S5.SMALL1");
    }

    private static void operate(String zoneName, String instanceModel) throws Exception {
        Browser browser = new Browser();

        browser.addRequestHeader("Cookie", cookie);
        browser.setHttpProxy("127.0.0.1", 8888);

        Map<String, Object> post = new HashMap<>();
        post.put("method", "operateCvmSmallAmountStrategy");
        post.put("jsonrpc", "2.0");
        post.put("id", "16382772565407094");

        Map<String, Object> param = new HashMap<>();
        post.put("params", param);
        param.put("selectType", 1);
        param.put("smallAmountOperateType", 0);
        param.put("filterType", 1);
        param.put("smallAmountFilterType", 0);
        param.put("deptIds", ListUtils.newArrayList(64));
        param.put("zoneNames", ListUtils.newArrayList(zoneName));
        param.put("cvmInstanceModels", ListUtils.newArrayList(instanceModel));

        HttpResponse resp = browser.postJson("https://yunti.woa.com/obs/yunti-requirement-info/strategy/operateCvmSmallAmountStrategy", post);

        System.out.println(resp.getContentString());
    }

}
