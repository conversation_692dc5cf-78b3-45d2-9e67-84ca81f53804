package com.nickxie.others.yunti_requirement_info;

import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.io.IOUtils;
import com.pugwoo.wooutils.net.Browser;
import com.pugwoo.wooutils.net.HttpResponse;
import com.pugwoo.wooutils.string.StringTools;

import java.io.FileInputStream;
import java.util.HashMap;
import java.util.Map;

/**
 * 刷新机型
 */
public class UpdateCvm {

    private static String cookie = "x-host-key-ngn=1748f6098bb-7586b09dd3f2bd4938e4b2dc681c5d38d0a62025; x-tofapi-host-key=1748f6099af-751720cb3333618ccc9aba93d793fb2a21368b0c; x-host-key-oaback=1748f687aff-26850f70e274fae1aee6a8816f31349d8938efaf; x-host-key-front=1748f687b03-e014258f13e6bc503c9bf57d75c5101a73d16108; x-mp-host-key=174a4790aeb-e50a11633b6b9c7d5e3d1aff3ab0a1b60ade7cf5; x-imp-host-key=174a47cdbd7-fff2ee4203e9853118cd650baef5dcc89abad7de; x_host_key=174a4938af8-63b3277dfdf8740b4cf7ad4b1303efd4efa93fdb; pgv_info=ssid=s4877452644; x-client-ssid=17a14df2b2f-f7526d96452b4bc58506426d1e1f4722e82a1373; DiggerTraceId=3b42c820-cf14-11eb-a373-619c8e7deeef; x_host_key_access_https=ea5d36ff4371418ef0952e9b13e7a232a1dfa7a2_s; x_host_key_access=ea5d36ff4371418ef0952e9b13e7a232a1dfa7a2_s; ERP_USERNAME=nickxie; km_u=69a44e0523ce978af1d5e50a976cfc66a71e195fd420a2f82cb6d9e8a19d88a7eba1b40fe701e377; t_uid=nickxie; km_uid=nickxie; RIO_TCOA_TICKET_HTTPS=tof:TOF4TeyJ2IjoiNCIsInRpZCI6IktYdWMyR3dmcGhPWjRUQmdRc1FpWktrNUJ3ZzNtMUtGIiwiaXNzIjoiMTAuOTkuMTUuNDkiLCJpYXQiOiIyMDIxLTExLTI5VDIxOjU5OjMwLjAxNDEyNDc4OCswODowMCIsImF1ZCI6IjAuMC4wLjAiLCJoYXNoIjoiRTcxQ0I0MkFEOTZCM0Q2MEE3QjA5NjNCRTM1MEQwN0VCRTJBQzQwODQwNTM3MUJBQzlBMkVDQTZCQjE2QURFMSIsIm5oIjoiOEMyMkE5NzA1MkIzQTI2NDgzQ0FFM0E4NDIxQTI5NDFEMjgwN0ZBOEYwMDA2RDQ4NkFFOTVFNjczOTgyQzhEMyJ9; TCOA_TICKET=TOF4TeyJ2IjoiNCIsInRpZCI6IktYdWMyR3dmcGhPWjRUQmdRc1FpWktrNUJ3ZzNtMUtGIiwiaXNzIjoiMTAuOTkuMTUuNDkiLCJpYXQiOiIyMDIxLTExLTI5VDIxOjU5OjMwLjAxNDEyNDc4OCswODowMCIsImF1ZCI6IjAuMC4wLjAiLCJoYXNoIjoiRTcxQ0I0MkFEOTZCM0Q2MEE3QjA5NjNCRTM1MEQwN0VCRTJBQzQwODQwNTM3MUJBQzlBMkVDQTZCQjE2QURFMSIsIm5oIjoiOEMyMkE5NzA1MkIzQTI2NDgzQ0FFM0E4NDIxQTI5NDFEMjgwN0ZBOEYwMDA2RDQ4NkFFOTVFNjczOTgyQzhEMyJ9; ERP_REAL_USERNAME=nickxie; RIO_TCOA_TICKET=tof:TOF4TeyJ2IjoiNCIsInRpZCI6Im1GbU56QWtEc2h5c0lhNGVpdTlpSm80STd6U0M3b09HIiwiaXNzIjoiMTAuOTkuMjA4LjM4IiwiaWF0IjoiMjAyMS0xMi0wMVQwOTozNzozMC4xNjQ1NTE0MDMrMDg6MDAiLCJhdWQiOiIwLjAuMC4wIiwiaGFzaCI6IkY2N0VFMjkwQzE3MEFDQTY4OTFCQ0I3OEJBQkU5NDQwRjc1MjEyQjVCMTMwMkQ4MDA2RjA1QzUyOUIyRDVCOEEiLCJuaCI6IjgzODA2Q0RGMTM4MTUzNzQ5ODk3ODFFQjhENkJEMDAyMDg4QzMyRUVCQzQxMjgyODZBRURBMTk1Q0I3NjZBMEUifQ; tgw_l7_route=8c5bfb8d0d069a560830c6d5c4b51eb1";

    public static void main(String[] args) throws Exception {
     //   operate("上海五区", "S5.MEDIUM8");
     //   operateSmall("上海五区", "S5.MEDIUM8");

        // 读取文件并进行操作
        String content = IOUtils.readAll(new FileInputStream("d:/cvm_area.txt"), "utf-8");
        // System.out.println(content);

        String[] strings = StringTools.splitLines(content);
        int count = 0;
        for (String line : strings) {
            count++;
            if (count > 10000) {
                break;
            }
            if (StringTools.isBlank(line)) {
                continue;
            }
            String[] items = line.split(",");

            System.out.println("start operate:" + items[0] + "," + items[1]);
            operate(items[0], items[1]);
            operateSmall(items[0], items[1]);
        }
    }

    private static void operate(String zoneName, String instanceModel) throws Exception {
        Browser browser = new Browser();

        browser.addRequestHeader("Cookie", cookie);
        // browser.setHttpProxy("127.0.0.1", 8888);

        Map<String, Object> post = new HashMap<>();
        post.put("method", "operateDeptCityZoneCvmStrategy");
        post.put("jsonrpc", "2.0");
        post.put("id", "16382772565407094");

        Map<String, Object> param = new HashMap<>();
        post.put("params", param);
        param.put("selectType", 1);
        param.put("operateType", 0);
        param.put("filterType", 0);
        param.put("smallAmountFilterType", 0);
        param.put("bgIds", ListUtils.newArrayList(11,13,14,15,16,17,18,19,23,24,25,26,27,28,29,30,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,20));
        param.put("zoneNames", ListUtils.newArrayList(zoneName));
        param.put("cvmInstanceModels", ListUtils.newArrayList(instanceModel));

        HttpResponse resp = browser.postJson("https://yunti.woa.com/obs/yunti-requirement-info/strategy/operateDeptCityZoneCvmStrategy", post);

        System.out.println("operate:" + resp.getContentString());
    }

    private static void operateSmall(String zoneName, String instanceModel) throws Exception {
        Browser browser = new Browser();

        browser.addRequestHeader("Cookie", cookie);
        // browser.setHttpProxy("127.0.0.1", 8888);

        Map<String, Object> post = new HashMap<>();
        post.put("method", "operateCvmSmallAmountStrategy");
        post.put("jsonrpc", "2.0");
        post.put("id", "16382772565407094");

        Map<String, Object> param = new HashMap<>();
        post.put("params", param);
        param.put("selectType", 1);
        param.put("smallAmountOperateType", 0);
        param.put("filterType", 1);
        param.put("smallAmountFilterType", 0);
        param.put("bgIds", ListUtils.newArrayList(11,13,14,15,16,17,18,19,23,24,25,26,27,28,29,30,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,20));
        param.put("zoneNames", ListUtils.newArrayList(zoneName));
        param.put("cvmInstanceModels", ListUtils.newArrayList(instanceModel));

        HttpResponse resp = browser.postJson("https://yunti.woa.com/obs/yunti-requirement-info/strategy/operateCvmSmallAmountStrategy", post);

        System.out.println("operateSmall:" + resp.getContentString());
    }

}
