package com.nickxie.others.others;

import com.pugwoo.wooutils.io.IOUtils;
import com.pugwoo.wooutils.string.StringTools;

import java.io.FileInputStream;

public class TransQuerySettingConfig {

    public static void main(String[] args) throws Exception {

        String data = IOUtils.readAllAndClose(
                new FileInputStream("d:/query_setting_config.txt"), "utf-8");

        //System.out.println(data);

        String curSchema = "";

        String[] strings = StringTools.splitLines(data);
        for (String line : strings) {
            if (StringTools.isBlank(line)) {
                continue;
            }

            if (line.startsWith("Schema")) {
                String strs[] = line.split(":");
                curSchema = strs[1].trim();
            } else {
                line = line.trim();
                String strs[] = line.split("\\s+");
                if (strs.length == 2) {
                    System.out.println(curSchema + ",\"" + strs[0].trim() + "\",\"" + strs[1].trim() + "\"");
                }
            }


        }

    }
}
