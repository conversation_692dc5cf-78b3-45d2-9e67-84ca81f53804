package com.nickxie.others.others;

import com.pugwoo.wooutils.net.Browser;
import com.pugwoo.wooutils.net.HttpResponse;

import java.io.IOException;

public class Test502Retry {

    public static void main(String[] args) {

        String url = "http://9.138.99.77/error_code?random=0.1";
        //String url = "http://9.138.99.89:8080/error_code?random=0.1";

        int total = 0;
        int ret200 = 0;
        int ret502 = 0;

        for (int i = 0; i < 1000; i++) {
            Browser browser = new Browser();
            browser.setRetryTimes(0);

            total++;

            try {
                HttpResponse resp = browser.get(url);
                if (resp.getResponseCode() == 200) {
                    ret200++;
                } else if (resp.getResponseCode() == 502) {
                    ret502++;
                }
            } catch (IOException e) {
                if (e.getMessage().contains("502")) {
                    ret502++;
                }
            }

        }

        System.out.println("total:" + total + ",return 200:" + ret200 + ",return 502:" + ret502);

    }

}
