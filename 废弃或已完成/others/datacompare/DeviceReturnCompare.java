package com.nickxie.others.datacompare;

import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.io.IOUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import com.pugwoo.wooutils.string.StringTools;
import lombok.Data;

import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class DeviceReturnCompare {

    @Data
    public static class DeviceDTO {
        private String assetId;
        private String deviceType;
        private Date returnDate;
        private Integer cpuCore;
    }

    public static void main(String[] args) throws Exception {
        List<DeviceDTO> crp = getCRP();
        List<DeviceDTO> cubes = getCubes();

        Map<String, DeviceDTO> crpMap = ListUtils.toMap(crp, o -> o.getAssetId(), o -> o);
        Map<String, DeviceDTO> cubesMap = ListUtils.toMap(cubes, o -> o.getAssetId(), o -> o);

        Set<String> both = new HashSet<>();

        for (Map.Entry<String, DeviceDTO> crpMapEntry : crpMap.entrySet()) {
            if (!cubesMap.containsKey(crpMapEntry.getKey())) {
                System.out.println("crp有而cubes没有:" + crpMapEntry.getKey());
            } else {
                both.add(crpMapEntry.getKey());
            }
        }
        for (Map.Entry<String, DeviceDTO> cubesMapEntry : cubesMap.entrySet()) {
            if (!crpMap.containsKey(cubesMapEntry.getKey())) {
                System.out.println("crp没有而cubes有:" + cubesMapEntry.getKey());
            } else {
                both.add(cubesMapEntry.getKey());
            }
        }

        // 两者都有的，核心数的差异
        BigDecimal crpCore = NumberUtils.sum(ListUtils.filter(crp, o -> both.contains(o.getAssetId())), o -> o.getCpuCore());
        BigDecimal cubesCore = NumberUtils.sum(ListUtils.filter(cubes, o -> both.contains(o.getAssetId())), o -> o.getCpuCore());
        System.out.println("都有的，crpCore:" + crpCore + ",cubesCore:" + cubesCore);
        System.out.println("都有的台数:" + both.size());

        System.out.println("=============");
        int diffCount = 0;
        for (String b : both) {
            if (!crpMap.get(b).getCpuCore().equals(cubesMap.get(b).getCpuCore())) {
                diffCount++;
                System.out.println("diff:" + b + ",crp:" + crpMap.get(b).getCpuCore() + ",cubes:" +
                        cubesMap.get(b).getCpuCore() + ",deviceType:" + crpMap.get(b).getDeviceType());
            }
        }
        System.out.println("total diff count:" + diffCount);
    }

    private static List<DeviceDTO> getCubes() throws Exception {
        String all = IOUtils.readAllAndClose(Files.newInputStream(
                Paths.get("d:/cubes-export-2022-11-28.csv")), "utf-8");
        String[] strings = StringTools.splitLines(all);
        List<DeviceDTO> list = new ArrayList<>();
        for (String line : strings) {
            String[] strs = line.split(",");
            DeviceDTO deviceDTO = new DeviceDTO();
            deviceDTO.setAssetId(strs[0]);
            deviceDTO.setReturnDate(DateUtils.parse(DateUtils.formatDate(DateUtils.parse(strs[3]))));
            deviceDTO.setDeviceType(strs[1]);
            deviceDTO.setCpuCore(NumberUtils.parseInt(strs[2]));
            list.add(deviceDTO);
        }
        return list;
    }

    private static List<DeviceDTO> getCRP() throws Exception {
        String all = IOUtils.readAllAndClose(Files.newInputStream(Paths.get("d:/crp.csv")), "utf-8");
        String[] strings = StringTools.splitLines(all);
        List<DeviceDTO> list = new ArrayList<>();
        for (String line : strings) {
            String[] strs = line.split(",");
            DeviceDTO deviceDTO = new DeviceDTO();
            deviceDTO.setAssetId(strs[0]);
            deviceDTO.setReturnDate(DateUtils.parse(strs[1]));
            deviceDTO.setDeviceType(strs[2]);
            deviceDTO.setCpuCore(NumberUtils.parseInt(strs[3]));
            list.add(deviceDTO);
        }
        return list;
    }

}
