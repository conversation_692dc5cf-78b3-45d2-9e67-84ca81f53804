package com.nickxie.others.codereview;

import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.net.Browser;
import com.pugwoo.wooutils.net.HttpResponse;

import java.util.ArrayList;
import java.util.List;

public class CodeReviewUserCR {

    public static void main(String[] args) throws Exception {

        List<String> users = ListUtils.newArrayList(
                "brandohanwu", "cheewang", "cloverhuang", "erachen", "fireflychen", "gaidylin", "galenliu", "illyasu", "jackeewang", "nickxie", "oliviadu", "pengweixu", "psionli", "saishen", "susiessli", "ulricaliu", "wintonwen"
        );

        String begin = "2022-07-01";
        String end = "2022-07-15";

        System.out.println("用户,总数,已评审,评审中,未评审");
        for (String username : users) {
            Result result = getCommitList(begin, end, username);

            List<Result.DetailItem> data_list = result.getData().getData_list();
            int totalCommit = 0;
            int reviewedCommit = 0;
            int reviewingCommit = 0;
            List<String> notCommitedUrl = new ArrayList<>();

            for (Result.DetailItem detailItem : data_list) {
                if ("未评审".equals(detailItem.getState())
                        && detailItem.getCommit_info().toLowerCase().contains("merge branch")) {
                    continue;
                }

                totalCommit++;
                if ("已评审".equals(detailItem.getState())) {
                    reviewedCommit++;
                } else if ("评审中".equals(detailItem.getState())) {
                    reviewingCommit++;
                } else {
                    notCommitedUrl.add(detailItem.getUrl());
                }
            }

            int notCommitted = notCommitedUrl.size();
            System.out.println(username + "," + totalCommit + "," + reviewedCommit
                    + "," + reviewingCommit + "," + notCommitted);
        }

    }

    private static Result getCommitList(String begin, String end, String username) throws Exception {
        String url = "http://tcoder.oa.com/api/code/activity/review/commit/?0=n&1=i&2=c&3=k&4=x&5=i&6=e&department=13&center=67&team=2287&time_range[]=${begin}&time_range[]=${end}&author=${username}&page_num=1&page_size=100";

        url = url.replace("${begin}", begin);
        url = url.replace("${end}", end);
        url = url.replace("${username}", username);

        Browser browser = new Browser();
        browser.disableGzip();
        browser.addRequestHeader("Accept", "application/json, text/plain, */*");
        HttpResponse resp = browser.get(url);

        return JSON.parse(resp.getContentString(), Result.class);
    }

}
