package com.nickxie.others.codereview;

import lombok.Data;

import java.util.List;

@Data
public class Result {

    private Integer ret;
    private String msg;
    private DataItem data;

    @Data
    public static class DataItem {
        private List<DetailItem> data_list;
        private Integer total_num;
        private Integer total_page_num;
        private Integer reviewed_num;
        private Integer reviewing_num;
        private Integer not_review_num;
    }

    @Data
    public static class DetailItem {
        private Integer project_id;
        private String repo;
        private String commit_id;
        private String commit_info;
        private String url;
        private String commit_time;
        private String author;
        private String title;
        private String review_id;
        private String create_at;
        private String closed_at;
        private String state;
    }

}
