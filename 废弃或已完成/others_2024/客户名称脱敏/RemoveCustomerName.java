package com.pugwoo.dboperate.archived.客户名称脱敏;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.lang.DateUtils;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import jakarta.annotation.Resource;
import java.util.Date;
import java.util.List;

@SpringBootTest
public class RemoveCustomerName {

    @Resource
    private DBHelper cloudDemandDevDBHelper;

    @Test
    public void remove() {
        String table = "industry_report_appid_info";
        long lastId = 0;
        long totalRows = 0;
        while (true) {
            String sql = "select id from " + table + " where id > ? order by id limit 100";
            List<Long> ids = cloudDemandDevDBHelper.getRaw(Long.class, sql, lastId);
            if (ids.isEmpty()) {
                break;
            }
            System.out.println(DateUtils.format(new Date()) + "begin");
            int rows = cloudDemandDevDBHelper.executeRaw("update " + table + " set customer_name='********' where id in (?)", ids);
            totalRows += rows;
            System.out.println(DateUtils.format(new Date()) + " total update " + totalRows + " records");

            lastId = ids.get(ids.size() - 1);
        }
    }

    @Test
    public void remove2() {
        String table = "industry_report_appid_info_latest";
        long lastId = 0;
        long totalRows = 0;
        while (true) {
            String sql = "select id from " + table + " where id > ? order by id limit 100";
            List<Long> ids = cloudDemandDevDBHelper.getRaw(Long.class, sql, lastId);
            if (ids.isEmpty()) {
                break;
            }
            System.out.println(DateUtils.format(new Date()) + "begin");
            int rows = cloudDemandDevDBHelper.executeRaw("update " + table + " set customer_name='********' where id in (?)", ids);
            totalRows += rows;
            System.out.println(DateUtils.format(new Date()) + " total update " + totalRows + " records");

            lastId = ids.get(ids.size() - 1);
        }
    }
}
