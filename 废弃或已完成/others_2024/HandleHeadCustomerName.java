package com.pugwoo.dboperate.archived.others;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.pugwoo.dbhelper.DBHelper;
import lombok.Data;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import jakarta.annotation.Resource;
import java.io.FileInputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@SpringBootTest
public class HandleHeadCustomerName {

    @Resource
    private DBHelper ckStdCrpNewIdcDBHelper;

    @Data
    public static class MyRow {
        @ExcelProperty(index = 0)
        private String customerUin;
        @ExcelProperty(index = 1)
        private String cid;
        @ExcelProperty(index = 2)
        private String customerName;
        @ExcelProperty(index = 3)
        private String groupName;
    }

    @Test
    public void test() throws Exception {
        FileInputStream in = new FileInputStream("C:/Users/<USER>/Desktop/处理冬琴提供的大客户名单/战略客户部头部7个客户集团uin映射 20240325.xlsx");

        final List<MyRow> result = new ArrayList<>();

        EasyExcel.read(in, MyRow.class,
                new AnalysisEventListener<MyRow>() {
                    @Override
                    public void invoke(MyRow row, AnalysisContext analysisContext) {
                        result.add(row);
                    }

                    @Override
                    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                        // 当整个文档解析完时，会调用这个方法，从result里可以拿到所有的数据
                        result.forEach(o -> {
                        //    DwdTxyAppidInfoCfDO one = ckStdCrpNewIdcDBHelper.getOne(DwdTxyAppidInfoCfDO.class, "where uin=?",
                       //             NumberUtils.parseLong(o.getCustomerUin()));
                       //     if (one == null) {
                       //         System.err.println("customer_uin: "+ o.getCustomerUin() + " not found in db");
                       //     }
                      //      System.out.println(JSON.toJson(o));
                            System.out.println(o.getCustomerUin() + "," + o.getGroupName());
                        });
                    }

                    /**
                     * 这里会一行行的返回头（这个方法可以不重写，需要拿到行头时可以重写来拿）
                     */
                    @Override
                    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
                        // headMap的key是index，value是标题
                        // System.out.println("行头:" + JSON.toJson(headMap));
                    }
                }
        ).sheet(0).headRowNumber(1).doRead(); // 这里指定第几个sheet，标题有几行

        in.close();
    }

}
