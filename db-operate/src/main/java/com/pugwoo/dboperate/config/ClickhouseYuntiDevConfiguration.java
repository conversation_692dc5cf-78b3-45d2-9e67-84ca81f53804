package com.pugwoo.dboperate.config;

import com.pugwoo.dbhelper.DBHelper;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.sql.DataSource;

/**
 * clickhouse开发环境cloud_demand
 */
@Configuration
public class ClickhouseYuntiDevConfiguration {

    @Bean(name = "ckCloudDemandDevDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.clickhouse-yunti-dev-cloud-demand")
    public DataSource dataSource() {
        return DataSourceBuilder.create().build();
    }

    @Bean("ckCloudDemandDevDBHelper")
    public DBHelper dbHelper(@Qualifier("ckCloudDemandDevDataSource") DataSource dataSource) {
        return DBConfigCommonTools.newDBHelper(dataSource);
    }

}
