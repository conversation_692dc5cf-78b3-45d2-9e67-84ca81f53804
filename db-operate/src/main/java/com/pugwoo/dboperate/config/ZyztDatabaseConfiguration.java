package com.pugwoo.dboperate.config;

import com.pugwoo.dbhelper.DBHelper;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;

/**
 * 资源中台的数据库配置
 */
@Configuration
public class ZyztDatabaseConfiguration {

    // rrp idc
    @Bean(name = "rrpIdcDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.rrp-idc")
    public DataSource rrpDataSource() {
        return DataSourceBuilder.create().build();
    }

    @Bean("rrpIdcDBHelper")
    public DBHelper rrpDbHelper(@Qualifier("rrpIdcDataSource") DataSource dataSource) {
        return DBConfigCommonTools.newDBHelper(dataSource);
    }

    // shuttle idc
    @Bean(name = "shuttleIdcDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.shuttle-idc")
    public DataSource dataSource() {
        return DataSourceBuilder.create().build();
    }

    @Bean("shuttleIdcDBHelper")
    public DBHelper dbHelper(@Qualifier("shuttleIdcDataSource") DataSource dataSource) {
        return DBConfigCommonTools.newDBHelper(dataSource);
    }

    // 麦肯锡数据 idc
    @Bean(name = "mckIdcDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.mck-idc")
    public DataSource mckIdcDataSource() {
        return DataSourceBuilder.create().build();
    }

    @Bean("mckIdcDBHelper")
    public DBHelper mckIdcDBHelper(@Qualifier("mckIdcDataSource") DataSource dataSource) {
        return DBConfigCommonTools.newDBHelper(dataSource);
    }

    @Bean(name = "mckIdcTransactionManager")
    public DataSourceTransactionManager mckIdcTransactionManager(@Qualifier("mckIdcDataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

}
