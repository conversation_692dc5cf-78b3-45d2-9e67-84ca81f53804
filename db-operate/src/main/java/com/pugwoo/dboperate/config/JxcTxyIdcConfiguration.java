package com.pugwoo.dboperate.config;

import com.pugwoo.dbhelper.DBHelper;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.sql.DataSource;

@Configuration
public class JxcTxyIdcConfiguration {

    @Bean(name = "jxcTxyIdcDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.jxc-txy-idc")
    public DataSource dataSource() {
        return DataSourceBuilder.create().build();
    }

    @Bean("jxcTxyIdcDBHelper")
    public DBHelper dbHelper(@Qualifier("jxcTxyIdcDataSource") DataSource dataSource) {
        return DBConfigCommonTools.newDBHelper(dataSource);
    }

}
