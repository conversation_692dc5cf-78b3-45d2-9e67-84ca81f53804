package com.pugwoo.dboperate.config;

import com.pugwoo.dbhelper.DBHelper;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.sql.DataSource;

/**
 * 生产环境的数全通数据库
 */
@Configuration
public class ObsIdcConfiguration {

    @Bean(name = "obsIdcDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.obs-idc")
    public DataSource dataSource() {
        return DataSourceBuilder.create().build();
    }

    @Bean("obsIdcDBHelper")
    public DBHelper dbHelper(@Qualifier("obsIdcDataSource") DataSource dataSource) {
        return DBConfigCommonTools.newDBHelper(dataSource);
    }

}
