package com.pugwoo.dboperate.config;

import com.pugwoo.dbhelper.DBHelper;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.sql.DataSource;

@Configuration
public class CubesConfiguration {

    // cubes的ck数据库(新) idc

    @Bean(name = "ckCubesIdcDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.ck-cubes-idc")
    public DataSource dataSource() {
        return DataSourceBuilder.create().build();
    }

    @Bean("ckCubesIdcDBHelper")
    public DBHelper dbHelper(@Qualifier("ckCubesIdcDataSource") DataSource dataSource) {
        return DBConfigCommonTools.newDBHelper(dataSource);
    }

}
