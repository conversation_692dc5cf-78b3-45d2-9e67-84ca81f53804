package com.pugwoo.dboperate.config;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.enums.FeatureEnum;
import com.pugwoo.dbhelper.impl.SpringJdbcDBHelper;
import org.springframework.jdbc.core.JdbcTemplate;

import javax.sql.DataSource;

public class DBConfigCommonTools {

    public static DBHelper newDBHelper(DataSource dataSource) {
        SpringJdbcDBHelper dbHelper = new SpringJdbcDBHelper();
        dbHelper.turnOnFeature(FeatureEnum.LAZY_DETECT_DATABASE_TYPE);
        dbHelper.setJdbcTemplate(new JdbcTemplate(dataSource));
        return dbHelper;
    }

}
