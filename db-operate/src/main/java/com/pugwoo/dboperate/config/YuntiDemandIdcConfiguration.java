package com.pugwoo.dboperate.config;

import com.pugwoo.dbhelper.DBHelper;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;

/**
 * 生产环境的需求预测
 */
@Configuration
public class YuntiDemandIdcConfiguration {

    @Bean(name = "demandIdcDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.demand-idc")
    public DataSource dataSource() {
        return DataSourceBuilder.create().build();
    }

    @Bean(name = "demandIdcTransactionManager")
    public DataSourceTransactionManager transactionManager(
            @Qualifier("demandIdcDataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    @Bean("demandIdcDBHelper")
    public DBHelper dbHelper(@Qualifier("demandIdcDataSource") DataSource dataSource) {
        return DBConfigCommonTools.newDBHelper(dataSource);
    }

}
