package com.pugwoo.dboperate.config;

import com.pugwoo.dbhelper.DBHelper;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;

/**
 * 云需求的数据库
 */
@Configuration
public class CloudDemandDatabaseConfiguration {

    // 开发环境的cloud_demand

    @Bean(name = "cloudDemandDevDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.cloud-demand-dev")
    public DataSource cloudDemandDevDataSource() {
        return DataSourceBuilder.create().build();
    }

    @Bean("cloudDemandDevDBHelper")
    public DBHelper cloudDemandDevDBHelper(@Qualifier("cloudDemandDevDataSource") DataSource dataSource) {
        return DBConfigCommonTools.newDBHelper(dataSource);
    }

    // 生产环境的cloud_demand

    @Bean(name = "cloudDemandIdcDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.cloud-demand-idc")
    public DataSource cloudDemandIdcDataSource() {
        return DataSourceBuilder.create().build();
    }

    @Bean("cloudDemandIdcDBHelper")
    public DBHelper cloudDemandIdcDBHelper(@Qualifier("cloudDemandIdcDataSource") DataSource dataSource) {
        return DBConfigCommonTools.newDBHelper(dataSource);
    }

    @Bean(name = "cloudDemandIdcTransactionManager")
    public DataSourceTransactionManager cloudDemandIdcTransactionManager(@Qualifier("cloudDemandIdcDataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    // 开发环境的cloud_demand_common

    @Bean(name = "cloudDemandCommonDevDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.cloud-demand-common-dev")
    public DataSource cloudDemandCommonDevDataSource() {
        return DataSourceBuilder.create().build();
    }

    @Bean("cloudDemandCommonDevDBHelper")
    public DBHelper cloudDemandCommonDevDBHelper(@Qualifier("cloudDemandCommonDevDataSource") DataSource dataSource) {
        return DBConfigCommonTools.newDBHelper(dataSource);
    }

    // 生产环境的cloud_demand_common

    @Bean(name = "cloudDemandCommonIdcDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.cloud-demand-common-idc")
    public DataSource cloudDemandCommonIdcDataSource() {
        return DataSourceBuilder.create().build();
    }

    @Bean("cloudDemandCommonIdcDBHelper")
    public DBHelper cloudDemandCommonIdcDBHelper(@Qualifier("cloudDemandCommonIdcDataSource") DataSource dataSource) {
        return DBConfigCommonTools.newDBHelper(dataSource);
    }

    @Bean(name = "cloudDemandCommonIdcTransactionManager")
    public DataSourceTransactionManager cloudDemandCommonIdcTransactionManager(
            @Qualifier("cloudDemandCommonIdcDataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    // 开发环境的cloud_demand_lab

    @Bean(name = "cloudDemandLabDevDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.cloud-demand-lab-dev")
    public DataSource cloudDemandLabDevDataSource() {
        return DataSourceBuilder.create().build();
    }

    @Bean("cloudDemandLabDevDBHelper")
    public DBHelper cloudDemandLabDevDBHelper(@Qualifier("cloudDemandLabDevDataSource") DataSource dataSource) {
        return DBConfigCommonTools.newDBHelper(dataSource);
    }

    // 生产环境的cloud_demand_lab

    @Bean(name = "cloudDemandLabIdcDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.cloud-demand-lab-idc")
    public DataSource cloudDemandLabIdcDataSource() {
        return DataSourceBuilder.create().build();
    }

    @Bean("cloudDemandLabIdcDBHelper")
    public DBHelper cloudDemandLabIdcDBHelper(@Qualifier("cloudDemandLabIdcDataSource") DataSource dataSource) {
        return DBConfigCommonTools.newDBHelper(dataSource);
    }

    @Bean(name = "cloudDemandLabIdcTransactionManager")
    public DataSourceTransactionManager cloudDemandLabIdcTransactionManager(
            @Qualifier("cloudDemandLabIdcDataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    // 开发环境的cloud_demand_data

    @Bean(name = "cloudDemandDataDevDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.cloud-demand-data-dev")
    public DataSource cloudDemandDataDevDataSource() {
        return DataSourceBuilder.create().build();
    }

    @Bean("cloudDemandDataDevDBHelper")
    public DBHelper cloudDemandDataDevDBHelper(@Qualifier("cloudDemandDataDevDataSource") DataSource dataSource) {
        return DBConfigCommonTools.newDBHelper(dataSource);
    }

    // 生产环境的cloud_demand_data

    @Bean(name = "cloudDemandDataIdcDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.cloud-demand-data-idc")
    public DataSource cloudDemandDataIdcDataSource() {
        return DataSourceBuilder.create().build();
    }

    @Bean("cloudDemandDataIdcDBHelper")
    public DBHelper cloudDemandDataIdcDBHelper(@Qualifier("cloudDemandDataIdcDataSource") DataSource dataSource) {
        return DBConfigCommonTools.newDBHelper(dataSource);
    }

}
