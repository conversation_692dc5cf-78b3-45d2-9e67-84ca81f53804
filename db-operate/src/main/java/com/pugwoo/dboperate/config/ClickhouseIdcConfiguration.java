package com.pugwoo.dboperate.config;

import com.pugwoo.dbhelper.DBHelper;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.sql.DataSource;

@Configuration
public class ClickhouseIdcConfiguration {

    // clickhouse cloud_demand idc 新ck

    @Bean(name = "ckCloudDemandNewIdcDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.ck-cloud-demand-new-idc")
    public DataSource ckCloudDemandNewIdcDataSource() {
        return DataSourceBuilder.create().build();
    }

    @Bean("ckCloudDemandNewIdcDBHelper")
    public DBHelper ckCloudDemandNewIdcDBHelper(@Qualifier("ckCloudDemandNewIdcDataSource") DataSource dataSource) {
        return DBConfigCommonTools.newDBHelper(dataSource);
    }

    // clickhouse std_crp idc 新ck

    @Bean(name = "ckStdCrpNewIdcDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.ck-std-crp-new-idc")
    public DataSource ckStdCrpNewIdcDataSource() {
        return DataSourceBuilder.create().build();
    }

    @Bean("ckStdCrpNewIdcDBHelper")
    public DBHelper ckStdCrpNewIdcDBHelper(@Qualifier("ckStdCrpNewIdcDataSource") DataSource dataSource) {
        return DBConfigCommonTools.newDBHelper(dataSource);
    }

    // clickhouse yunti_demand idc 新ck

    @Bean(name = "ckYuntiDemandNewIdcDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.ck-yunti-demand-new-idc")
    public DataSource ckYuntiDemandNewIdcDataSource() {
        return DataSourceBuilder.create().build();
    }

    @Bean("ckYuntiDemandNewIdcDBHelper")
    public DBHelper ckYuntiDemandNewIdcDBHelper(@Qualifier("ckYuntiDemandNewIdcDataSource") DataSource dataSource) {
        return DBConfigCommonTools.newDBHelper(dataSource);
    }

    // clickhouse forecast research idc 新ck

    @Bean(name = "ckForecastResearchNewIdcDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.ck-forecast-research-new-idc")
    public DataSource ckForecastResearchNewIdcDataSource() {
        return DataSourceBuilder.create().build();
    }

    @Bean("ckForecastResearchNewIdcDBHelper")
    public DBHelper ckForecastResearchNewIdcDBHelper(@Qualifier("ckForecastResearchNewIdcDataSource") DataSource dataSource) {
        return DBConfigCommonTools.newDBHelper(dataSource);
    }

    // clickhouse std_crp dev

    @Bean(name = "ckStdCrpDevDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.ck-std-crp-dev")
    public DataSource ckStdCrpDevDataSource() {
        return DataSourceBuilder.create().build();
    }

    @Bean("ckStdCrpDevDBHelper")
    public DBHelper ckStdCrpDevDBHelper(@Qualifier("ckStdCrpDevDataSource") DataSource dataSource) {
        return DBConfigCommonTools.newDBHelper(dataSource);
    }

}
