package com.pugwoo.dboperate.config;

import com.pugwoo.dbhelper.DBHelper;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.sql.DataSource;

/**
 * 生产环境的数全通数据库
 */
@Configuration
public class YuntiIdcConfiguration {

    // yunti idc

    @Bean(name = "yuntiIdcDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.yunti-idc")
    public DataSource dataSource() {
        return DataSourceBuilder.create().build();
    }

    @Bean("yuntiIdcDBHelper")
    public DBHelper dbHelper(@Qualifier("yuntiIdcDataSource") DataSource dataSource) {
        return DBConfigCommonTools.newDBHelper(dataSource);
    }

    // erp idc

    @Bean(name = "erpIdcDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.erp-idc")
    public DataSource erpIdcDataSource() {
        return DataSourceBuilder.create().build();
    }

    @Bean("erpIdcDBHelper")
    public DBHelper erpIdcDBHelper(@Qualifier("erpIdcDataSource") DataSource dataSource) {
        return DBConfigCommonTools.newDBHelper(dataSource);
    }

    // erp res plan idc 物理机需求预测

    @Bean(name = "erpResplanIdcDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.erp-resplan-idc")
    public DataSource erpResplanIdcDataSource() {
        return DataSourceBuilder.create().build();
    }

    @Bean("erpResplanIdcDBHelper")
    public DBHelper erpResplanIdcDBHelper(@Qualifier("erpResplanIdcDataSource") DataSource dataSource) {
        return DBConfigCommonTools.newDBHelper(dataSource);
    }

}
