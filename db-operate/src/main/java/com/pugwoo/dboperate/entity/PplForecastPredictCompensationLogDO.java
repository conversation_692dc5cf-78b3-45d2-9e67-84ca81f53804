package com.pugwoo.dboperate.entity;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 模型预测补充记录表
 */
@Data
@ToString
@Table("ppl_forecast_predict_compensation_log")
public class PplForecastPredictCompensationLogDO {

    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    /** 创建时间<br/>Column: [create_time] */
    @Column(value = "create_time", setTimeWhenInsert = true)
    private Date createTime;

    /** 预测任务id<br/>Column: [task_id] */
    @Column(value = "task_id")
    private Long taskId;

    /** 补偿配置表的id<br/>Column: [source_compensation_id] */
    @Column(value = "source_compensation_id")
    private Long sourceCompensationId;

    /** 干预类型，INPUT/OUTPUT,  默认 INPUT<br/>Column: [compensation_type] */
    @Column(value = "compensation_type")
    private String compensationType;

    /** 调整的来源，使用代号来表示一类调整<br/>Column: [compensation_source] */
    @Column(value = "compensation_source")
    private String compensationSource;

    /** category<br/>Column: [category] */
    @Column(value = "category")
    private String category;

    /** 年<br/>Column: [year] */
    @Column(value = "year")
    private Integer year;

    @Column(value = "month")
    private Integer month;

    /** 机型<br/>Column: [gins_family] */
    @Column(value = "gins_family")
    private String ginsFamily;

    @Column(value = "region_name")
    private String regionName;

    /** 境内外<br/>Column: [customhouse_title] */
    @Column(value = "customhouse_title")
    private String customhouseTitle;

    @Column(value = "core_num")
    private BigDecimal coreNum;

    /** new/ret<br/>Column: [seq_type] */
    @Column(value = "seq_type")
    private String seqType;

    /** 计费模式（包年包月/弹性）<br/>Column: [note] */
    @Column(value = "note")
    private String note;

}