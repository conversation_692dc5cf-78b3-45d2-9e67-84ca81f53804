package com.pugwoo.dboperate.entity;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 中长尾预测补偿数据
 *
 * <AUTHOR>
 */
@Data
@ToString
@Table("ppl_forecast_input_compensation")
public class PplForecastInputCompensationDO {

    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    /** 软删除标记位<br/>Column: [deleted] */
    @Column(value = "deleted", softDelete = {"0", "1"})
    private Boolean deleted;

    @Column(value = "is_enable")
    private Integer isEnable;

    /**
     * 干预的类型：
     */
    @Column(value = "compensation_type")
    private String compensationType;

    /** 调整的来源，使用代号来表示一类调整<br/>Column: [compensation_source] */
    @Column(value = "compensation_source")
    private String compensationSource;

    /**
     * 创建时间<br/>Column: [create_time]
     */
    @Column(value = "create_time")
    private Date createTime;

    @Column(value = "category")
    private String category;

    /** * 计费模式（包年包月/弹性/EMPTY）<br/>Column: [paymode_range_type] */
    @Column(value = "paymode_range_type")
    private String paymodeRangeType;

    /** 资源池类型，PUBLIC腾讯云，ZIYAN自研<br/>Column: [resource_pool] */
    @Column(value = "resource_pool")
    private String resourcePool;

    /** 客户范围，LONGTAIL长尾，ALL全部，MIDDLE腰部，TOP头部<br/>Column: [customer_scope] */
    @Column(value = "customer_scope")
    private String customerScope;

    /** 客户类型<br/>Column: [source_type] INDUSTRY  INNER INDUSTRY_INNER*/
    @Column(value = "source_type")
    private String sourceType;


    @Column(value = "core_num")
    private BigDecimal coreNum;

    /**
     * 年<br/>Column: [year]
     */
    @Column(value = "year")
    private Integer year;

    @Column(value = "month")
    private Integer month;

    /**
     * 机型<br/>Column: [gins_family]
     */
    @Column(value = "gins_family")
    private String ginsFamily;

    @Column(value = "region_name")
    private String regionName;

    /** 境内外<br/>Column: [customhouse_title] */
    @Column(value = "customhouse_title")
    private String customhouseTitle;

    /**
     * new/ret<br/>Column: [seq_type]
     */
    @Column(value = "seq_type")
    private String seqType;

    /**
     * 计费模式（包年包月/弹性）<br/>Column: [note]
     */
    @Column(value = "note")
    private String note;

}