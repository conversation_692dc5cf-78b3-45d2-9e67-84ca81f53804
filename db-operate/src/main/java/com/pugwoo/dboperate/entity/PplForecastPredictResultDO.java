package com.pugwoo.dboperate.entity;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;

@Data
@ToString
@Table("ppl_forecast_predict_result")
public class PplForecastPredictResultDO {

    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    /** 创建时间<br/>Column: [create_time] */
    @Column(value = "create_time")
    private Date createTime;

    /** 预测任务id<br/>Column: [task_id] */
    @Column(value = "task_id")
    private Long taskId;

    /** 预测的日期<br/>Column: [stat_time] */
    @Column(value = "stat_time")
    private LocalDate statTime;

    /** 预测的序列，从1开始<br/>Column: [predict_index] */
    @Column(value = "predict_index")
    private Integer predictIndex;

    /** 预测的需求年<br/>Column: [year] */
    @Column(value = "year")
    private Integer year;

    /** 预测的需求月<br/>Column: [month] */
    @Column(value = "month")
    private Integer month;

    /** 机型<br/>Column: [gins_family] */
    @Column(value = "gins_family")
    private String ginsFamily;

    /** 地域<br/>Column: [region_name] */
    @Column(value = "region_name")
    private String regionName;

    /** new/ret<br/>Column: [type] */
    @Column(value = "type")
    private String type;

    /** 新增或者退回的预测值<br/>Column: [core_num] */
    @Column(value = "core_num")
    private BigDecimal coreNum;

}