package com.pugwoo.dboperate.entity;

import lombok.Data;
import lombok.ToString;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;

@Data
@ToString
@Table("txy_scale_data_masking")
public class TxyScaleDataMaskingDO {

    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    /** 数据类型<br/>Column: [type] */
    @Column(value = "type")
    private String type;

    /** 代号<br/>Column: [code] */
    @Column(value = "code")
    private String code;

    /** 真实值<br/>Column: [real_value] */
    @Column(value = "real_value")
    private String realValue;

}