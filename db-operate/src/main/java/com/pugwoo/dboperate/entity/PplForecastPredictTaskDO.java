package com.pugwoo.dboperate.entity;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;

@Data
@ToString
@Table("ppl_forecast_predict_task")
@NoArgsConstructor
public class PplForecastPredictTaskDO {

    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    /** 任务状态<br/>Column: [status] */
    @Column(value = "status")
    private String status;

    /** 创建时间<br/>Column: [create_time] */
    @Column(value = "create_time", setTimeWhenInsert = true)
    private Date createTime;

    /** 预测完成时间<br/>Column: [finish_time] */
    @Column(value = "finish_time")
    private Date finishTime;

    /** forecast_input_id<br/>Column: [input_id] */
    @Column(value = "input_id")
    private Long inputId;

    /** 包年包月/弹性<br/>Column: [bill_type] */
    @Column(value = "bill_type")
    private String billType;

    /** 新增预测任务uuid<br/>Column: [forecast_task_uuin_new] */
    @Column(value = "forecast_task_uuin_new")
    private String forecastTaskUuinNew;

    /** 退回预测任务uuid<br/>Column: [forecast_task_uuin_ret] */
    @Column(value = "forecast_task_uuin_ret")
    private String forecastTaskUuinRet;

    /** 资源池类型，PUBLIC腾讯云，ZIYAN自研<br/>Column: [resource_pool] */
    @Column(value = "resource_pool")
    private String resourcePool;

    /** 客户范围，LONGTAIL长尾，ALL全部，MIDDLE腰部，TOP头部<br/>Column: [customer_scope] */
    @Column(value = "customer_scope")
    private String customerScope;

    /** 客户类型<br/>Column: [source_type] */
    @Column(value = "source_type")
    private String sourceType;

    /**
     * WEEK_MAX
     * MONTH
     * WEEK_AVG
     */
    @Column(value = "source_from")
    private String sourceFrom;

    /** 预测序列间隔<br/>Column: [serial_interval] */
    @Column(value = "serial_interval")
    private String serialInterval;

    /**输入维度的数量*/
    @Column("input_dims_count")
    private Integer inputDimsCount;

    /** 输入维度<br/>Column: [input_dims_name] */
    @Column(value = "input_dims_name")
    private String inputDimsName;

    /** 输入时用于区分新增退回的group枚举<br/>Column: [input_group_dims] */
    @Column(value = "input_group_dims")
    private String inputGroupDims;

    /** 预测算法<br/>Column: [predict_algorithm] */
    @Column(value = "predict_algorithm")
    private String predictAlgorithm;

    /** 预测参数，逗号隔开<br/>Column: [predict_algorithm_args] */
    @Column(value = "predict_algorithm_args")
    private String predictAlgorithmArgs;

    /** 输入起始日期<br/>Column: [input_date_begin] */
    @Column(value = "input_date_begin")
    private LocalDate inputDateBegin;

    /** 输入结束日期<br/>Column: [input_date_end] */
    @Column(value = "input_date_end")
    private LocalDate inputDateEnd;

    /** 预测起始位置<br/>Column: [predict_index_start] */
    @Column(value = "predict_index_start")
    private Integer predictIndexStart;

    /** 预测结束位置<br/>Column: [predict_index_end] */
    @Column(value = "predict_index_end")
    private Integer predictIndexEnd;

    /** 失败原因<br/>Column: [fail_message] */
    @Column(value = "fail_message")
    private String failMessage;

    @Column(value = "is_enable")
    private Boolean isEnable;

    @Column(value = "category")
    private String category;

    /** 预测任务新增序列的sql<br/>Column: [input_sql_new] */
    @Column(value = "input_sql_new")
    private String inputSqlNew;

    /** 预测任务退回序列的sql<br/>Column: [input_sql_ret] */
    @Column(value = "input_sql_ret")
    private String inputSqlRet;

    /** 新增序列的条件sql，不含日期<br/>Column: [input_sql_condition_new] */
    @Column(value = "input_sql_condition_new")
    private String inputSqlConditionNew;

    /** 退回序列的条件sql，不含日期<br/>Column: [input_sql_condition_ret] */
    @Column(value = "input_sql_condition_ret")
    private String inputSqlConditionRet;

    /** 调整模型输出百分比<br/>Column: [adjust_new_result_ratio] */
    @Column(value = "adjust_new_result_ratio")
    private BigDecimal adjustNewResultRatio;

}