package com.pugwoo.dboperate.entity;


import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDate;


/**
 * <AUTHOR>
 */
@Data
@ToString
@Table("dwd_txy_scale_df")
public class OdsTxyScaleDfDO extends OdsTxyScaleBaseDO{

    /** 统计日期<br/>Column: [stat_time] */
    @Column(value = "stat_time")
    private LocalDate statTime;

    /** stat_time所在月<br/>Column: [month] */
    @Column(value = "month")
    private Integer month;

    /** stat_time所在年<br/>Column: [year] */
    @Column(value = "year")
    private Integer year;

    // month --------------------------------------------------------------------

    /** month_days 所在年<br/>Column: [year] */
    @Column(value = "month_days")
    private Integer monthDays;

    /** 月开始日期<br/>Column: [week_start_date] */
    @Column(value = "month_start_date")
    private LocalDate monthStartDate;

    /** 月结束日期<br/>Column: [week_end_date] */
    @Column(value = "month_end_date")
    private LocalDate monthEndDate;

    // week --------------------------------------------------------------------

    /** week_days 所在年<br/>Column: [year] */
    @Column(value = "week_days")
    private Integer weekDays;

    /** 1-7<br/>Column: [week_num] */
    @Column(value = "week_num")
    private Integer weekNum;

    /** 周编号<br/>Column: [week_num] */
    @Column(value = "week")
    private Integer week;

    /** 周所在月<br/>Column: [week_month] */
    @Column(value = "week_month")
    private Integer weekMonth;

    /** 周所在年<br/>Column: [week_year] */
    @Column(value = "week_year")
    private Integer weekYear;

    /** 周开始日期<br/>Column: [week_start_date] */
    @Column(value = "week_start_date")
    private LocalDate weekStartDate;

    /** 周结束日期<br/>Column: [week_end_date] */
    @Column(value = "week_end_date")
    private LocalDate weekEndDate;


    // 数值字段

    /** 当前计费核数<br/>Column: [cur_bill_core] */
    @Column(value = "cur_bill_core")
    private BigDecimal curBillCore;

    /** 当天净增计费核数<br/>Column: [change_bill_core] */
    @Column(value = "change_bill_core")
    private BigDecimal changeBillCore;

    /** 当前计费核数<br/>Column: [cur_bill_core] */
    @Column(value = "new_bill_core")
    private BigDecimal newBillCore;

    /** 当天净增计费核数<br/>Column: [change_bill_core] */
    @Column(value = "ret_bill_core")
    private BigDecimal retBillCore;


    /** 当天服务 GPU 卡数<br/>Column: [cur_service_gpu] */
    @Column(value = "cur_service_gpu")
    private BigDecimal curServiceGpu;

    /** 当天新增的服务GPU 卡数<br/>Column: [new_service_gpu] */
    @Column(value = "new_service_gpu")
    private BigDecimal newServiceGpu;

    /** 当天退回的服务GPU 卡数<br/>Column: [ret_service_gpu] */
    @Column(value = "ret_service_gpu")
    private BigDecimal retServiceGpu;

    /** 当天净增服务 GPU 卡数<br/>Column: [change_service_gpu] */
    @Column(value = "change_service_gpu")
    private BigDecimal changeServiceGpu;



    /** 当天服务核数<br/>Column: [cur_free_core] */
    @Column(value = "cur_free_core")
    private BigDecimal curFreeCore;

    /** 当天净增服务核数<br/>Column: [change_free_core] */
    @Column(value = "change_free_core")
    private BigDecimal changeFreeCore;


    /** 当天服务核数<br/>Column: [cur_free_core] */
    @Column(value = "new_free_core")
    private BigDecimal newFreeCore;

    /** 当天净增服务核数<br/>Column: [change_free_core] */
    @Column(value = "ret_free_core")
    private BigDecimal retFreeCore;




    /** 当前计费 GPU 卡数<br/>Column: [cur_bill_gpu] */
    @Column(value = "cur_bill_gpu")
    private BigDecimal curBillGpu;

    /** 当天净增计费 GPU 卡数<br/>Column: [change_bill_gpu] */
    @Column(value = "change_bill_gpu")
    private BigDecimal changeBillGpu;

    /** 当前计费 GPU 卡数<br/>Column: [cur_bill_gpu] */
    @Column(value = "new_bill_gpu")
    private BigDecimal newBillGpu;

    /** 当天净增计费 GPU 卡数<br/>Column: [change_bill_gpu] */
    @Column(value = "ret_bill_gpu")
    private BigDecimal retBillGpu;



    /** 当天服务核数<br/>Column: [cur_service_core] */
    @Column(value = "cur_service_core")
    private BigDecimal curServiceCore;

    /** 当天新增的服务核数<br/>Column: [new_service_core] */
    @Column(value = "new_service_core")
    private BigDecimal newServiceCore;

    /** 当天退回的服务核数<br/>Column: [ret_service_core] */
    @Column(value = "ret_service_core")
    private BigDecimal retServiceCore;

    /** 当天净增服务核数<br/>Column: [change_service_core] */
    @Column(value = "change_service_core")
    private BigDecimal changeServiceCore;


    /** 当天服务 GPU 卡数<br/>Column: [cur_free_gpu] */
    @Column(value = "cur_free_gpu")
    private BigDecimal curFreeGpu;

    /** 当天净增服务 GPU 卡数<br/>Column: [change_free_gpu] */
    @Column(value = "change_free_gpu")
    private BigDecimal changeFreeGpu;

    /** 当天服务 GPU 卡数<br/>Column: [cur_free_gpu] */
    @Column(value = "new_free_gpu")
    private BigDecimal newFreeGpu;

    /** 当天净增服务 GPU 卡数<br/>Column: [change_free_gpu] */
    @Column(value = "ret_free_gpu")
    private BigDecimal retFreeGpu;


    /** cpu和上个月底比较<br/>Column: [change_core_from_last_month] */
    @Column(value = "change_bill_core_from_last_month")
    private BigDecimal changeBillCoreFromLastMonth;

    /** cpu和上周末比较<br/>Column: [change_core_from_last_week] */
    @Column(value = "change_bill_core_from_last_week")
    private BigDecimal changeBillCoreFromLastWeek;

    /** gpu和上个月底比较<br/>Column: [change_gpu_from_last_month] */
    @Column(value = "change_bill_gpu_from_last_month")
    private BigDecimal changeBillGpuFromLastMonth;

    /** gpu和上周末比较<br/>Column: [change_gpu_from_last_week] */
    @Column(value = "change_bill_gpu_from_last_week")
    private BigDecimal changeBillGpuFromLastWeek;



    /** cpu和上个月底比较<br/>Column: [change_core_from_last_month] */
    @Column(value = "change_free_core_from_last_month")
    private BigDecimal changeFreeCoreFromLastMonth;

    /** cpu和上周末比较<br/>Column: [change_core_from_last_week] */
    @Column(value = "change_free_core_from_last_week")
    private BigDecimal changeFreeCoreFromLastWeek;

    /** gpu和上个月底比较<br/>Column: [change_gpu_from_last_month] */
    @Column(value = "change_free_gpu_from_last_month")
    private BigDecimal changeFreeGpuFromLastMonth;

    /** gpu和上周末比较<br/>Column: [change_gpu_from_last_week] */
    @Column(value = "change_free_gpu_from_last_week")
    private BigDecimal changeFreeGpuFromLastWeek;


    /** cpu和上个月底比较<br/>Column: [change_service_core_from_last_month] */
    @Column(value = "change_service_core_from_last_month")
    private BigDecimal changeServiceCoreFromLastMonth;

    /** cpu和上周末比较<br/>Column: [change_service_core_from_last_week] */
    @Column(value = "change_service_core_from_last_week")
    private BigDecimal changeServiceCoreFromLastWeek;

    /** gpu和上个月底比较<br/>Column: [change_service_gpu_from_last_month] */
    @Column(value = "change_service_gpu_from_last_month")
    private BigDecimal changeServiceGpuFromLastMonth;

    /** gpu和上周末比较<br/>Column: [change_service_gpu_from_last_week] */
    @Column(value = "change_service_gpu_from_last_week")
    private BigDecimal changeServiceGpuFromLastWeek;

    @Column(value = "cid")
    private String cid;

    @Column(value = "gid")
    private String gid;

    @Column(value = "gname")
    private String gname;

    @Column(value = "gpu_card_type")
    private String gpuCardType;

}
