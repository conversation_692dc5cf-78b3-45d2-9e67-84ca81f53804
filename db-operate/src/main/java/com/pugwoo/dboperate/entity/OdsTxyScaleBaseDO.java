package com.pugwoo.dboperate.entity;


import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@ToString
public class OdsTxyScaleBaseDO {

    /** appid<br/>Column: [app_id] */
    @Column(value = "app_id")
    private Long appId;

    /** uin<br/>Column: [uin] */
    @Column(value = "uin")
    private Long uin;

    /** 主销售名<br/>Column: [business_manager] */
    @Column(value = "business_manager")
    private String businessManager;

    /** 主销售名的组织架构<br/>Column: [business_manager_oa_dept] */
    @Column(value = "business_manager_oa_dept")
    private String businessManagerOaDept;

    /** 行业部门<br/>Column: [industry_dept] */
    @Column(value = "industry_dept")
    private String industryDept;

    /** uin类型，0内部1外部<br/>Column: [is_inner] */
    @Column(value = "is_inner")
    private Boolean isInner;

    /** 战区<br/>Column: [war_zone] */
    @Column(value = "war_zone")
    private String warZone;

    /** 客户名称<br/>Column: [customer_name] */
    @Column(value = "customer_name")
    private String customerName;

    /** 客户简称<br/>Column: [customer_short_name] */
    @Column(value = "customer_short_name")
    private String customerShortName;

    /** 客户类型，0个人1企业<br/>Column: [customer_type] */
    @Column(value = "customer_type")
    private Integer customerType;

    /** 集团id<br/>Column: [corporation_id] */
    @Column(value = "corporation_id")
    private Long corporationId;

    /** 集团名称<br/>Column: [corporation_name] */
    @Column(value = "corporation_name")
    private String corporationName;

    /** 可用区id<br/>Column: [zone_id] */
    @Column(value = "zone_id")
    private Long zoneId;

    /** 可用区名 广州四区<br/>Column: [zone_name] */
    @Column(value = "zone_name")
    private String zoneName;

    /** 可用区编号 ap-guangzhou-4<br/>Column: [zone] */
    @Column(value = "zone")
    private String zone;

    /** 地域 guagnzhou<br/>Column: [region] */
    @Column(value = "region")
    private String region;

    /** 地域中文名称 广州<br/>Column: [region_name] */
    @Column(value = "region_name")
    private String regionName;

    /** 地域名称 华南地区<br/>Column: [area_name] */
    @Column(value = "area_name")
    private String areaName;

    /** 境内外<br/>Column: [customhouse_title] */
    @Column(value = "customhouse_title")
    private String customhouseTitle;

    /** is_inner = true 的时候，这里为内部组织架构<br/>Column: [inner_info_bg_id] */
    @Column(value = "inner_info_bg_id")
    private Integer innerInfoBgId;

    /** is_inner = true 的时候，这里为内部组织架构<br/>Column: [inner_info_bg_name] */
    @Column(value = "inner_info_bg_name")
    private String innerInfoBgName;

    /** is_inner = true 的时候，这里为内部组织架构<br/>Column: [inner_info_bg_short_name] */
    @Column(value = "inner_info_bg_short_name")
    private String innerInfoBgShortName;

    /** is_inner = true 的时候，这里为内部组织架构<br/>Column: [inner_info_dept_id] */
    @Column(value = "inner_info_dept_id")
    private Integer innerInfoDeptId;

    /** is_inner = true 的时候，这里为内部组织架构<br/>Column: [inner_info_dept_name] */
    @Column(value = "inner_info_dept_name")
    private String innerInfoDeptName;

    /** is_inner = true 的时候，这里为内部组织架构<br/>Column: [inner_info_plan_product_id] */
    @Column(value = "inner_info_plan_product_id")
    private Integer innerInfoPlanProductId;

    /** is_inner = true 的时候，这里为内部组织架构<br/>Column: [inner_info_plan_product_name] */
    @Column(value = "inner_info_plan_product_name")
    private String innerInfoPlanProductName;

    /** is_inner = true 的时候，这里为内部组织架构<br/>Column: [inner_info_product_id] */
    @Column(value = "inner_info_product_id")
    private Integer innerInfoProductId;

    /** is_inner = true 的时候，这里为内部组织架构<br/>Column: [inner_info_product_name] */
    @Column(value = "inner_info_product_name")
    private String innerInfoProductName;

    /** instance_model<br/>Column: [instance_model] */
    @Column(value = "instance_model")
    private String instanceModel;

    /** instance_type<br/>Column: [instance_type] */
    @Column(value = "instance_type")
    private String instanceType;

    /** 计费模式<br/>Column: [paymode] */
    @Column(value = "paymode")
    private String paymode;

    /** 业务AppRole<br/>Column: [app_role] */
    @Column(value = "app_role")
    private String appRole;

    /** 新增｜退回<br/>Column: [demand_type] */
    @Column(value = "demand_type")
    private String demandType;

    /** 统计逻辑： paymode in (2,5)，弹性计费； paymode not in (2,5），包年包月<br/>Column: [paymode_range_type] */
    @Column(value = "paymode_range_type")
    private String paymodeRangeType;

    /** 头部客户（名单客户、报备客户）｜中长尾客户<br/>Column: [customer_tab_type] */
    @Column(value = "customer_tab_type")
    private String customerTabType;

    /** 版本所在的 version<br/>Column: [customer_tab_type_version] */
    @Column(value = "customer_tab_type_version")
    private Integer customerTabTypeVersion;

    /** 外部业务-approle（CDH、正常售卖、预扣包、LH）｜内部业务（除外部业务approle之外，剔除外部客户is_inner<br/>Column: [biz_range_type] */
    @Column(value = "biz_range_type")
    private String bizRangeType;

    /** cvm, baremetal<br/>Column: [biz_type] */
    @Column(value = "biz_type")
    private String bizType;

    /** gpu=0 cpu gpu!=0 gpu<br/>Column: [cpu_or_gpu] */
    @Column(value = "cpu_or_gpu")
    private String cpuOrGpu;

    /** CVM：biz_type = cvm and cpu_or_gpu = CPU
     裸金属：biz_type = baremetal and cpu_or_gpu = CPU
     GPU ：cpu_or_gpu = GPU<br/>Column: [product] */
    @Column(value = "product")
    private String product;

    /** 单实例gpu 卡数<br/>Column: [instance_model_gpu] */
    @Column(value = "instance_model_gpu")
    private Integer instanceModelGpu;

    /** 单实例gpu 卡数<br/>Column: [instance_model_gpu] */
    @Column(value = "instance_model_gpu_ratio")
    private Integer instanceModelGpuRatio;

    /** 单实例cpu 核心<br/>Column: [instance_model_cpu] */
    @Column(value = "instance_model_cpu")
    private Integer instanceModelCpu;

    /** 单实例mem 内存<br/>Column: [instance_model_mem] */
    @Column(value = "instance_model_mem")
    private Integer instanceModelMem;

    /** cpu 的类型 INTEL<br/>Column: [cpu_type] */
    @Column(value = "cpu_type")
    private String cpuType;

}
