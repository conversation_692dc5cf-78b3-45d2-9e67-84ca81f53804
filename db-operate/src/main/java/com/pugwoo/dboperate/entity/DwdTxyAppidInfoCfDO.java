package com.pugwoo.dboperate.entity;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.util.Date;

@Data
@ToString
@Table("dwd_txy_appid_info_cf")
public class DwdTxyAppidInfoCfDO {

    /** appid<br/>Column: [appid] */
    @Column(value = "appid")
    private Long appid;

    /** 数据生成时间<br/>Column: [create_time] */
    @Column(value = "create_time")
    private Date createTime;

    /** 数据更新时间<br/>Column: [update_time] */
    @Column(value = "update_time")
    private Date updateTime;

    /** 行业部门<br/>Column: [industry_dept] */
    @Column(value = "industry_dept")
    private String industryDept;

    /** 客户uin<br/>Column: [uin] */
    @Column(value = "uin")
    private Long uin;

    /** 客户名称<br/>Column: [customer_name] */
    @Column(value = "customer_name")
    private String customerName;

    /** 客户简称<br/>Column: [customer_short_name] */
    @Column(value = "customer_short_name")
    private String customerShortName;

    /** 客户类型，0个人1企业<br/>Column: [customer_type] */
    @Column(value = "customer_type")
    private Integer customerType;

    /** 战区<br/>Column: [war_zone] */
    @Column(value = "war_zone")
    private String warZone;

    /** 主销售名<br/>Column: [business_manager] */
    @Column(value = "business_manager")
    private String businessManager;

    /** 主销售名的组织架构<br/>Column: [business_manager_oa_dept] */
    @Column(value = "business_manager_oa_dept")
    private String businessManagerOaDept;

    /** 主销售名的组织架构path<br/>Column: [business_manager_oa_path] */
    @Column(value = "business_manager_oa_path")
    private String businessManagerOaPath;

    /** uin类型，0内部1外部<br/>Column: [uin_type] */
    @Column(value = "uin_type")
    private Integer uinType;

    /** 架构师rtx<br/>Column: [system_architect] */
    @Column(value = "system_architect")
    private String systemArchitect;

    /** 架构师的组织架构<br/>Column: [system_architect_oa_dept] */
    @Column(value = "system_architect_oa_dept")
    private String systemArchitectOaDept;

    /** 架构师的组织架构path<br/>Column: [system_architect_oa_path] */
    @Column(value = "system_architect_oa_path")
    private String systemArchitectOaPath;

    /** 内部客户bg id<br/>Column: [inner_info_bg_id] */
    @Column(value = "inner_info_bg_id")
    private Integer innerInfoBgId;

    /** 内部客户bg名称<br/>Column: [inner_info_bg_name] */
    @Column(value = "inner_info_bg_name")
    private String innerInfoBgName;

    /** 内部客户bg简称<br/>Column: [inner_info_bg_short_name] */
    @Column(value = "inner_info_bg_short_name")
    private String innerInfoBgShortName;

    /** 内部客户部门id<br/>Column: [inner_info_dept_id] */
    @Column(value = "inner_info_dept_id")
    private Integer innerInfoDeptId;

    /** 内部客户部门名称<br/>Column: [inner_info_dept_name] */
    @Column(value = "inner_info_dept_name")
    private String innerInfoDeptName;

    /** 内部客户规划产品id<br/>Column: [inner_info_plan_product_id] */
    @Column(value = "inner_info_plan_product_id")
    private Integer innerInfoPlanProductId;

    /** 内部客户规划产品名称<br/>Column: [inner_info_plan_product_name] */
    @Column(value = "inner_info_plan_product_name")
    private String innerInfoPlanProductName;

    /** 内部客户运营产品id<br/>Column: [inner_info_product_id] */
    @Column(value = "inner_info_product_id")
    private Integer innerInfoProductId;

    /** 内部客户运营产品名称<br/>Column: [inner_info_product_name] */
    @Column(value = "inner_info_product_name")
    private String innerInfoProductName;

    /** 集团cid<br/>Column: [cid] */
    @Column(value = "cid")
    private String cid;

    /** 集团id<br/>Column: [gid] */
    @Column(value = "gid")
    private String gid;

    /** 集团名称<br/>Column: [gname] */
    @Column(value = "gname")
    private String gname;

    /** 收入来源<br/>Column: [channel_mark] */
    @Column(value = "channel_mark")
    private Integer channelMark;

    /** 行业<br/>Column: [industry] */
    @Column(value = "industry")
    private String industry;

}