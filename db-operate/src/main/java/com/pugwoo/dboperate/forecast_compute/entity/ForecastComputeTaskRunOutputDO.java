package com.pugwoo.dboperate.forecast_compute.entity;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@ToString
@Table("forecast_compute_task_run_output")
public class ForecastComputeTaskRunOutputDO {

    @Column(value = "date")
    private LocalDate date;

    @Column(value = "date_str")
    private String dateStr;

    @Column(value = "task_id")
    private Integer taskId;

    @Column(value = "task_run_id")
    private Integer taskRunId;

    @Column(value = "value")
    private BigDecimal value;

    @Column(value = "index")
    private Integer index;
}
