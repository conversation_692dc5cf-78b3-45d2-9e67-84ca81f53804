package com.pugwoo.dboperate.forecast_compute.enums;

import lombok.Getter;

import java.util.Objects;

@Getter
public enum SerialIntervalEnum {

    MONTH("MONTH", "月"),

    WEEK("WEEK", "周"),

    DAY("DAY", "天")

    ;

    final private String code;
    final private String name;

    SerialIntervalEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static SerialIntervalEnum getByCode(String code) {
        for (SerialIntervalEnum e : SerialIntervalEnum.values()) {
            if (Objects.equals(code, e.getCode())) {
                return e;
            }
        }
        return null;
    }

    public static String getNameByCode(String code) {
        SerialIntervalEnum e = getByCode(code);
        return e == null ? "" : e.getName();
    }

}