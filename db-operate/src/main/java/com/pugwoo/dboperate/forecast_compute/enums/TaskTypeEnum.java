package com.pugwoo.dboperate.forecast_compute.enums;

import lombok.Getter;

import java.util.Objects;

@Getter
public enum TaskTypeEnum {

    TEST("TEST", "测算"),

    PREDICT("PREDICT", "预测")

    ;

    final private String code;
    final private String name;

    TaskTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static TaskTypeEnum getByCode(String code) {
        for (TaskTypeEnum e : TaskTypeEnum.values()) {
            if (Objects.equals(code, e.getCode())) {
                return e;
            }
        }
        return null;
    }

    public static String getNameByCode(String code) {
        TaskTypeEnum e = getByCode(code);
        return e == null ? "" : e.getName();
    }

}