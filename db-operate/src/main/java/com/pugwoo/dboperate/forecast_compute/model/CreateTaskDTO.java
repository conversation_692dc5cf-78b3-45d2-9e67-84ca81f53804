package com.pugwoo.dboperate.forecast_compute.model;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * 创建任务需要的信息
 */
@Data
public class CreateTaskDTO {

    @AllArgsConstructor
    @Getter
    public enum AlgorithmEnum {
        MA("MA"),
        /**
         * MA算法的扩展：1）支持滑动窗口预测未来的值
         */
        MAX("MAX"),
        ARIMA("ARIMA"),
        ARIMAX("ARIMAX"),
        ES("ES");
        final private String name;

        public static AlgorithmEnum getByName(String algorithm) {
            for (AlgorithmEnum a : AlgorithmEnum.values()) {
                if (algorithm.equals(a.getName())) {
                    return a;
                }
            }
            return null;
        }
    }

    @Getter
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Algorithm {
        private String name; // 同一种算法的参数一样 @see AlgorithmEnum
        private List<Object> params; // 同一个算法可以传不同的参数，但是形式要一样
    }

    /**创建人*/
    private String createUser;

    /**任务名称*/
    private String taskName;

    /**任务类型，目前只支持预测，不支持测算*/
    private String taskType;

    /**序列间隔（目前只是记录，还没有用于实际的计算或数据校验）
     */
    private String serialInterval;

    /**输入维度个数*/
    private Integer inputDims;
    /**输入维度名称，逗号隔开*/
    private String inputDimsName;

    /**是否自动补全序列缺失的数据，目前只支持补0，后续再支持补平均值，会增加一个参数来指定补全的方式*/
    private Boolean isAutoFillData;

    /**预测开始index，从1开始，例如2表示从输入时间结束后的第2个月开始预测*/
    private Integer predictIndexStart;
    /**预测结束index，从1开始，例如5表示预测到输入时间结束后的第5个月*/
    private Integer predictIndexEnd;

    /**预测算法*/
    private List<Algorithm> algorithms;

    /**输入数据，该数据用于预测的源头
     * 说明：
     * 1) 当taskType为预测类型PREDICT时，将会用到inputData的全部数据，从开始到结束
     * 2) 当taskType为测算类型TEST时，待定
     * */
    private List<CreateTaskInputDataDTO> inputData;

    /**
     * 测试数据集
     */
    private List<CreateTaskInputDataDTO> testDataset;

    // 以下3个字段目前仅用来存在对应的sql，还没有实际支持sql查询明细，只是记录，便于后续排查问题

    private String inputDataDatasource;

    private String inputDataSql;

    private String testDatasetSql;

}
