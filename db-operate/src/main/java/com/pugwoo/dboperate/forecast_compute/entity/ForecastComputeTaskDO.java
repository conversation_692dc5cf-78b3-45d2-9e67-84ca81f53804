package com.pugwoo.dboperate.forecast_compute.entity;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDate;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@ToString
@Table("forecast_compute_task")
public class ForecastComputeTaskDO {

    /** 主键<br/>Column: [id] */
    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Integer id;

    /** 提单人<br/>Column: [create_user] */
    @Column(value = "create_user")
    private String createUser;

    /** 创建时间<br/>Column: [create_time] */
    @Column(value = "create_time", setTimeWhenInsert = true)
    private Date createTime;

    /** 任务uuid<br/>Column: [task_uuid] */
    @Column(value = "task_uuid")
    private String taskUuid;

    /** 任务名称<br/>Column: [task_name] */
    @Column(value = "task_name")
    private String taskName;

    /** 任务类型: 测算TEST/预测PREDICT<br/>Column: [task_type] */
    @Column(value = "task_type")
    private String taskType;

    /** 任务状态，NEW/RUNNING/SUCCESS/FAIL<br/>Column: [status] */
    @Column(value = "status")
    private String status;

    /** 序列时间间隔: 月/周/天/序号<br/>Column: [serial_interval] */
    @Column(value = "serial_interval")
    private String serialInterval;

    /** 输入数据维度数<br/>Column: [input_dims_count] */
    @Column(value = "input_dims_count")
    private Integer inputDimsCount;

    /** 输入数据维度名称，逗号隔开<br/>Column: [input_dims_name] */
    @Column(value = "input_dims_name")
    private String inputDimsName;

    /** 输出数据维度数<br/>Column: [output_dims_count] */
    @Column(value = "output_dims_count")
    private Integer outputDimsCount;

    /** 输出数据维度名称，逗号隔开<br/>Column: [output_dims_name] */
    @Column(value = "output_dims_name")
    private String outputDimsName;

    /** 是否自动补齐数据<br/>Column: [is_auto_fill_data] */
    @Column(value = "is_auto_fill_data")
    private Integer isAutoFillData;

    /** 补齐数据类型，0补齐，均值补齐<br/>Column: [fill_data_type] */
    @Column(value = "fill_data_type")
    private String fillDataType;

    /** 开始运行时间<br/>Column: [run_time_start] */
    @Column(value = "run_time_start")
    private Date runTimeStart;

    /** 结束运行时间<br/>Column: [run_time_end] */
    @Column(value = "run_time_end")
    private Date runTimeEnd;

    /** 预测起始位置,index>=1<br/>Column: [predict_index_start] */
    @Column(value = "predict_index_start")
    private Integer predictIndexStart;

    /** 预测结束位置,index>=1<br/>Column: [predic_index_end] */
    @Column(value = "predict_index_end")
    private Integer predictIndexEnd;

    /** 测算日期起始)，测算类型有值<br/>Column: [test_date_start] */
    @Column(value = "test_date_start")
    private LocalDate testDateStart;

    /** 测算日期(结束)，测算类型有值<br/>Column: [test_date_end] */
    @Column(value = "test_date_end")
    private LocalDate testDateEnd;

    /** 是否计算准确率<br/>Column: [is_cal_predict_rate] */
    @Column(value = "is_cal_predict_rate")
    private Integer isCalPredictRate;

    /** 任务优先级<br/>Column: [task_priority] */
    @Column(value = "task_priority")
    private Integer taskPriority;

    /** 输入数据的数据源<br/>Column: [input_data_datasource] */
    @Column(value = "input_data_datasource", insertValueScript = "''")
    private String inputDataDatasource;

    /** 输入数据的SQL<br/>Column: [input_data_sql] */
    @Column(value = "input_data_sql", insertValueScript = "''")
    private String inputDataSql;

    /** 测试集数据的SQL<br/>Column: [test_dataset_sql] */
    @Column(value = "test_dataset_sql", insertValueScript = "''")
    private String testDatasetSql;

}