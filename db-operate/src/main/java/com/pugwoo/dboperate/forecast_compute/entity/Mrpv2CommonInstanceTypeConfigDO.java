package com.pugwoo.dboperate.forecast_compute.entity;

import lombok.Data;
import lombok.ToString;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;

/**
 * 行业数据看板通用机型配置表，配置到 sysman
 */
@Data
@ToString
@Table("mrpv2_common_instance_type_config")
public class Mrpv2CommonInstanceTypeConfigDO {

    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    /** 通用的实例类型<br/>Column: [common_instance_type] */
    @Column(value = "common_instance_type")
    private String commonInstanceType;

    /** 需要通用化的实例类型，逗号分隔<br/>Column: [instance_types] */
    @Column(value = "instance_types")
    private String instanceTypes;

    /** 是否参与预测<br/>Column: [use_forecast] */
    @Column(value = "use_forecast")
    private Integer useForecast;

}