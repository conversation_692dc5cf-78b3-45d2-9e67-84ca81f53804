package com.pugwoo.dboperate.中长期.按国家的替换比例_2411;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dboperate.common.ReadFileUtils;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.collect.MapUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import com.pugwoo.wooutils.string.StringTools;
import jakarta.annotation.Resource;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.temporal.TemporalAdjusters;
import java.util.List;
import java.util.Map;

/**
 * 这个是从cloud-demand-lab的LongtermPredictArgsTrendServiceImpl中弄过来的
 *
 * 替换比例和采购没有关系，所以这里就不把采购的代码拷贝过来了。
 */
@SpringBootTest
public class 按国家的滚动替换比例 {

    @Resource
    private DBHelper cloudDemandLabIdcDBHelper;
    @Resource
    private DBHelper ckStdCrpNewIdcDBHelper;

    @Test
    public void test() {
        System.out.println("日期,国家,替换比例");

        test("全部", "");
        test("境内", "and customhouse_title='境内'");

        List<Map> list = cloudDemandLabIdcDBHelper.getRaw(Map.class,
                """
                        select distinct region_name,country_name from longterm_predict_input_scale where task_id=
                                    (select max(id) from longterm_predict_task where category_id=1 and deleted=0 and is_enable=1)
                           and country_name!='中国内地'
                        """);
        Map<String, List<String>> map = ListUtils.toMapList(list, o -> o.get("country_name").toString(), o -> o.get("region_name").toString());

        for (Map.Entry<String, List<String>> e : map.entrySet()) {
            test(e.getKey(), "and region_name in (" + StringTools.join(",",
                    ListUtils.transform(e.getValue(), o -> "'" + o + "'")) + ")");
        }
    }

    public void test(String name, String extraWhere) {
        LocalDate start = DateUtils.parseLocalDate("2021-06-30").with(TemporalAdjusters.lastDayOfMonth());
        LocalDate end = DateUtils.parseLocalDate("2024-10-31").with(TemporalAdjusters.lastDayOfMonth());

        while (!start.isAfter(end)) {
            LocalDate statTime = start;

            LocalDate m6month = statTime.minusMonths(6); // 统计周期以半年计
            m6month = YearMonth.from(m6month).atEndOfMonth();

            LongtermPredictCategoryConfigDO category = cloudDemandLabIdcDBHelper.getOne(LongtermPredictCategoryConfigDO.class,
                    "where id=?", 1);

            // mark 可以在这里更改category的where条件

            // 2. 查询存量相关数据
            ScaleInfoDTO scaleInfo = getScaleInfo(category, m6month, statTime, extraWhere);

            BigDecimal notNetCore = scaleInfo.getNewCoreByInstanceModelAndZone().
                    subtract(scaleInfo.getNetChangeCore().compareTo(BigDecimal.ZERO) < 0 ?
                            scaleInfo.getNetChangeCore().divide(BigDecimal.valueOf(2), 2, RoundingMode.HALF_UP)  // 对于净退回的部分修正，取一半
                            : scaleInfo.getNetChangeCore());

            if (scaleInfo.getStartCurCore().compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal ratio = NumberUtils.divide(notNetCore, scaleInfo.getStartCurCore(), 3);
                System.out.println(statTime + "," + name + "," + ratio);
            }

            start = start.plusMonths(1).with(TemporalAdjusters.lastDayOfMonth());;
        }
    }

    @SneakyThrows
    private ScaleInfoDTO getScaleInfo(LongtermPredictCategoryConfigDO category,
                                      LocalDate startDate, LocalDate endDate,
                                      String extraWhere) {
        if ("2020-12-31".equals(startDate.toString())) {
            startDate = LocalDate.of(2021, 1,1); // 因为2020-12-31没有数据
        }
        String inputScaleSql = ReadFileUtils.read("scale.sql");
        inputScaleSql = inputScaleSql.replace("${CATEGORY_CONDITION}", category.getWhereSql() + " " + extraWhere);

        return ckStdCrpNewIdcDBHelper.getRawOne(ScaleInfoDTO.class, inputScaleSql,
                MapUtils.of("startDate", startDate, "endDate", endDate));
    }


}
