package com.pugwoo.dboperate.中长期.按国家的替换比例_2411;

import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class ScaleInfoDTO {

    /** 存量核心数，开始时间点<br/>Column: [start_cur_core] */
    @Column(value = "start_cur_core")
    private BigDecimal startCurCore;

    /** 存量核心数，结束时间点<br/>Column: [end_cur_core] */
    @Column(value = "end_cur_core")
    private BigDecimal endCurCore;

    /** 净增核心数，即end_cur_core-start_cur_core<br/>Column: [net_change_core] */
    @Column(value = "net_change_core")
    private BigDecimal netChangeCore;

    /** 机型规格+可用区维度下的新增核心数<br/>Column: [new_core_by_instance_model_and_zone] */
    @Column(value = "new_core_by_instance_model_and_zone")
    private BigDecimal newCoreByInstanceModelAndZone;

}
