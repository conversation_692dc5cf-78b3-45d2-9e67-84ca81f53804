package com.pugwoo.dboperate.中长期.按国家的替换比例_2411;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 中长期预测方案配置表
 */
@Data
@ToString
@Table("longterm_predict_category_config")
public class LongtermPredictCategoryConfigDO {

    /**
     * id<br/>Column: [id]
     */
    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    /**
     * 删除标记<br/>Column: [deleted]
     */
    @Column(value = "deleted", softDelete = {"0", "1"})
    private Boolean deleted;

    /**
     * 创建时间<br/>Column: [create_time]
     */
    @Column(value = "create_time", setTimeWhenInsert = true)
    private Date createTime;

    /**
     * 更新时间<br/>Column: [update_time]
     */
    @Column(value = "update_time", setTimeWhenInsert = true, setTimeWhenUpdate = true)
    private Date updateTime;

    /** 方案名称<br/>Column: [category] */
    @Column(value = "category")
    private String category;

    /** 预测时间颗粒度，6=半年<br/>Column: [interval_month] */
    @Column(value = "interval_month")
    private Integer intervalMonth;

    /** 预测粒度<br/>Column: [dims_name] */
    @Column(value = "dims_name")
    private String dimsName;

    /** 客户范围<br/>Column: [scope_customer] */
    @Column(value = "scope_customer")
    private String scopeCustomer;

    /** 产品范围<br/>Column: [scope_product] */
    @Column(value = "scope_product")
    private String scopeProduct;

    /** 资源池<br/>Column: [scope_resource_pool] */
    @Column(value = "scope_resource_pool")
    private String scopeResourcePool;

    /** 输入原始数据的表，这里仅用于标识，不会实际参与sql<br/>Column: [table_name] */
    @Column(value = "table_name")
    private String tableName;

    /** 查询条件sql，会参与实际sql<br/>Column: [where_sql] */
    @Column(value = "where_sql")
    private String whereSql;

    /** 采购用到的表名，这里仅用于标识，不会实际参与sql<br/>Column: [purchase_table_name] */
    @Column(value = "purchase_table_name")
    private String purchaseTableName;

    /** 采购量的查询条件，会参与实际sql<br/>Column: [purchase_where_sql] */
    @Column(value = "purchase_where_sql")
    private String purchaseWhereSql;

    /** 星云采购的表名称<br/>Column: [xy_purchase_table_name] */
    @Column(value = "xy_purchase_table_name")
    private String xyPurchaseTableName;

    /** 星云采购的查询条件<br/>Column: [xy_purchase_where_sql] */
    @Column(value = "xy_purchase_where_sql")
    private String xyPurchaseWhereSql;

    /** 预测开始时间，CUR_MONTH表示当月<br/>Column: [predict_start] */
    @Column(value = "predict_start")
    private String predictStart;

    /** 预测结束时间，一般填yyyy-MM<br/>Column: [predict_end] */
    @Column(value = "predict_end")
    private String predictEnd;

    /** 默认的替换比例，不是百分比<br/>Column: [default_replace_rate] */
    @Column(value = "default_replace_rate")
    private BigDecimal defaultReplaceRate;

    /** 默认的采购系数，不是百分比<br/>Column: [default_purchase_rate] */
    @Column(value = "default_purchase_rate")
    private BigDecimal defaultPurchaseRate;

}