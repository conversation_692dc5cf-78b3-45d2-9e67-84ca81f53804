package com.pugwoo.dboperate.中长期.存量交付模型_2407;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.lang.NumberUtils;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.YearMonth;

@SpringBootTest
public class 滚动替换比例 {

    @Resource
    private DBHelper ckStdCrpNewIdcDBHelper;

    @Test
    public void test() {
        ckStdCrpNewIdcDBHelper.setSlowSqlWarningValve(100000);

        LocalDate start = LocalDate.parse("2021-06-30");

        while (start.isBefore(LocalDate.now())) {
            LocalDate m6month = start.minusMonths(6);
            m6month = YearMonth.from(m6month).atEndOfMonth();

            BigDecimal periodBeginStock = getStock(m6month.toString());
            BigDecimal periodEndStock = getStock(start.toString());

            BigDecimal stockNetChange = periodEndStock.subtract(periodBeginStock);
            BigDecimal newCore = getNewCoreByInstanceModelAndZone(m6month.toString(), start.toString());

            BigDecimal notNetCore = newCore.subtract(stockNetChange.compareTo(BigDecimal.ZERO) < 0 ?
                    stockNetChange.divide(BigDecimal.valueOf(2)) : stockNetChange);
            BigDecimal ratio = NumberUtils.divide(notNetCore, periodBeginStock, 3).multiply(BigDecimal.valueOf(100));

            System.out.println(start + "," + ratio);

            start = start.plusMonths(1);
            start = YearMonth.from(start).atEndOfMonth();
        }

    }

    /**
     * 查询指定时间范围内的机型规格+可用区维度下的新增量
     * 例如查询2024年上半年的话，beginDate填2023-12-31，endDate填2024-06-30
     */
    private BigDecimal getNewCoreByInstanceModelAndZone(String beginDate, String endDate) {
        if (beginDate.equals("2020-12-31")) {
            beginDate = "2021-01-01"; // 因为2020-12-31没有数据
        }
        return ckStdCrpNewIdcDBHelper.getRawOne(BigDecimal.class,
                """
                        with raw as (select  instance_model, zone_name,
                               sum(cur_core) as net_change_core from
                            (select instance_model, zone_name,
                                    sum(cur_bill_service_core) as cur_core
                             from std_crp.dwd_txy_scale_df
                             where cpu_or_gpu = 'CPU' and biz_type = 'cvm' and app_role != 'LH' and instance_type not like 'RS%' and instance_type not like 'RM%' and app_id not in (1258344706, 1251316161)
                               and stat_time = ?
                              group by instance_model, zone_name

                             union all

                             select instance_model, zone_name,
                                    -sum(cur_bill_service_core) as cur_core
                             from std_crp.dwd_txy_scale_df
                             where cpu_or_gpu = 'CPU' and biz_type = 'cvm' and app_role != 'LH' and instance_type not like 'RS%' and instance_type not like 'RM%' and app_id not in (1258344706, 1251316161)
                               and stat_time = ?
                              group by instance_model, zone_name
                                )
                         group by instance_model, zone_name)
                        select sum(case when net_change_core>0 then net_change_core else 0 end ) as new_core
                        from raw
                        """, endDate, beginDate);
    }

    private BigDecimal getStock(String begin) {
        if (begin.equals("2020-12-31")) {
            begin = "2021-01-01";
        }
        return ckStdCrpNewIdcDBHelper.getRawOne(BigDecimal.class,
                """
                        select sum(cur_bill_service_core) as cur_core
                        from std_crp.dwd_txy_scale_df
                        where  cpu_or_gpu = 'CPU' and
                            biz_type = 'cvm' and app_role != 'LH' and instance_type not like 'RS%' and instance_type not like 'RM%'
                          and app_id not in (1258344706, 1251316161)
                          and stat_time=?
                        """, begin);
    }

}
