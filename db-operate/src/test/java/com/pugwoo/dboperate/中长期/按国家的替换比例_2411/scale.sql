with raw as (select  instance_model, zone_name,
                     sum(cur_core) as net_change_core1,
                     sum(start_cur_core) as start_cur_core1,
                     sum(end_cur_core) as end_cur_core1
             from
                 (select instance_model, zone_name,
                         sum(cur_service_core) as cur_core,
                         0 as start_cur_core,
                         sum(cur_service_core) as end_cur_core
                  from std_crp.dwd_txy_scale_df
                  where
                    -- condition from category
                      ${CATEGORY_CONDITION}
                    and stat_time = :endDate
                  group by instance_model, zone_name

                  union all

                  select instance_model, zone_name,
                      -sum(cur_service_core) as cur_core,
                      sum(cur_service_core) as start_cur_core,
                      0 as end_cur_core
                  from std_crp.dwd_txy_scale_df
                  where
                    -- condition from category
                      ${CATEGORY_CONDITION}
                    and stat_time = :startDate
                  group by instance_model, zone_name
                 )
             group by instance_model, zone_name)
select sum(case when net_change_core1>0 then net_change_core1 else 0 end ) as new_core_by_instance_model_and_zone,
       sum(net_change_core1) as net_change_core,
       sum(start_cur_core1) as start_cur_core,
       sum(end_cur_core1) as end_cur_core
from raw