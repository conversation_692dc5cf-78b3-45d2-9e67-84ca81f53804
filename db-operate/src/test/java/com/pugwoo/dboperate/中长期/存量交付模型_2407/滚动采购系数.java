package com.pugwoo.dboperate.中长期.存量交付模型_2407;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.lang.NumberUtils;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.YearMonth;
import java.util.List;

/**
 * 采购系数 = 新增量(不同机型规格+不同可用区) /
 */
@SpringBootTest
public class 滚动采购系数 {

    @Resource
    private DBHelper ckStdCrpNewIdcDBHelper;
    @Resource
    private DBHelper erpIdcDBHelper;
    @Resource
    private DBHelper ckCubesIdcDBHelper;

    @Test
    public void test() {
        ckStdCrpNewIdcDBHelper.setSlowSqlWarningValve(100000);
        erpIdcDBHelper.setSlowSqlWarningValve(1000000);
        ckCubesIdcDBHelper.setSlowSqlWarningValve(1000000);

        LocalDate start = LocalDate.parse("2021-06-30");

        while (start.isBefore(LocalDate.now())) {
            LocalDate m6month = start.minusMonths(6);
            m6month = YearMonth.from(m6month).atEndOfMonth();


            BigDecimal deliveryCore = getDeliveryCore(m6month.toString(), start.toString());
            BigDecimal newCore = getNewCoreByInstanceModelAndZone(m6month.toString(), start.toString());

            System.out.println(start + "," + NumberUtils.divide(newCore, deliveryCore, 2));

            start = start.plusMonths(1);
            start = YearMonth.from(start).atEndOfMonth();
        }
    }

    private BigDecimal getDeliveryCore(String beginDate, String endDate) {
        List<String> gpuDeviceTypes = erpIdcDBHelper.getRaw(String.class, """
                select name from bas_stratege_device_type
                where DeviceFamilyName like '%GPU%'
                """);
        return ckCubesIdcDBHelper.getRawOne(BigDecimal.class,
                """
                        select sum(core) from
                        (
                                                select    cloud_business_type,
                                                        quota_plan_product_name as plan_product_name,
                                                        proj_set_name as project_name,
                                                        device_type as machine_type,
                                                        count(1) AS unit,
                                                        sum(cpu_logic_core) as core,
                                                        (case when obs_business_type='云业务' then '云业务'
                                                              when obs_business_type='内部业务' and cloud_business_type != '自研上云' then '自研业务'
                                                              when cloud_business_type='自研上云' then '自研上云' else '' end) as biz_type
                                                 from cubes.demandMarket
                                                 where DAY = (select max(DAY) from cubes.demandMarket)
                                                   and cloud_delivery_time between ? and ?
                                                 group by obs_business_type, cloud_business_type, quota_plan_product_name, device_type, project_name
                        )
                        where biz_type='云业务' and plan_product_name='腾讯云CVM' and machine_type not in (?)
                        """, beginDate + " 23:59:59", endDate + " 23:59:59", gpuDeviceTypes);
    }

    /**
     * 查询指定时间范围内的机型规格+可用区维度下的新增量
     * 例如查询2024年上半年的话，beginDate填2023-12-31，endDate填2024-06-30
     */
    private BigDecimal getNewCoreByInstanceModelAndZone(String beginDate, String endDate) {
        if (beginDate.equals("2020-12-31")) {
            beginDate = "2021-01-01"; // 因为2020-12-31没有数据
        }
        return ckStdCrpNewIdcDBHelper.getRawOne(BigDecimal.class,
                """
                        with raw as (select  instance_model, zone_name,
                               sum(cur_core) as net_change_core from
                            (select instance_model, zone_name,
                                    sum(cur_bill_service_core) as cur_core
                             from std_crp.dwd_txy_scale_df
                             where cpu_or_gpu = 'CPU' and biz_type = 'cvm' and app_role != 'LH' and instance_type not like 'RS%' and instance_type not like 'RM%' and app_id not in (1258344706, 1251316161)
                               and stat_time = ?
                              group by instance_model, zone_name

                             union all

                             select instance_model, zone_name,
                                    -sum(cur_bill_service_core) as cur_core
                             from std_crp.dwd_txy_scale_df
                             where cpu_or_gpu = 'CPU' and biz_type = 'cvm' and app_role != 'LH' and instance_type not like 'RS%' and instance_type not like 'RM%' and app_id not in (1258344706, 1251316161)
                               and stat_time = ?
                              group by instance_model, zone_name
                                )
                         group by instance_model, zone_name)
                        select sum(case when net_change_core>0 then net_change_core else 0 end ) as new_core
                        from raw
                        """, endDate, beginDate);
    }

}
