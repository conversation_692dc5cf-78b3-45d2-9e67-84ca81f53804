package com.pugwoo.dboperate.同步和清理数据.entity;

import lombok.Data;
import lombok.ToString;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;

/**
 * 从tdw出库的cbs底表数据
 */
@Data
@ToString
@Table("cbs_info_for_crp")
public class CbsInfoForCrpDO {

    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    /** 切片日期<br/>Column: [tdbank_imp_date] */
    @Column(value = "tdbank_imp_date")
    private String tdbankImpDate;

    @Column(value = "cbs_zone_id")
    private Long cbsZoneId;

    @Column(value = "appid")
    private Long appid;

    @Column(value = "zone_id")
    private Long zoneId;

    @Column(value = "disk_uuid")
    private String diskUuid;

    @Column(value = "disk_size")
    private Long diskSize;

    /** 系统盘或数据盘，root或data<br/>Column: [disk_type] */
    @Column(value = "disk_type")
    private String diskType;

    @Column(value = "volume_type")
    private String volumeType;

    @Column(value = "create_date_time")
    private String createDateTime;

    @Column(value = "deadline")
    private String deadline;

    /** cbs挂载的虚拟机<br/>Column: [vm_cpu] */
    @Column(value = "vm_cpu")
    private Integer vmCpu;

    @Column(value = "vm_mem")
    private Integer vmMem;

    @Column(value = "vm_type")
    private String vmType;

    @Column(value = "pay_mode")
    private String payMode;

    @Column(value = "set_pool")
    private String setPool;

    @Column(value = "ETL_STAMP")
    private String eTLSTAMP;

}