package com.pugwoo.dboperate.同步和清理数据;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dboperate.同步和清理数据.entity.CbsInfoForCrpDO;
import com.pugwoo.wooutils.lang.DateUtils;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Date;
import java.util.List;

@SpringBootTest
public class 临时同步cbs数据 {

    @Resource
    private DBHelper cloudDemandDataIdcDBHelper;
    @Resource
    private DBHelper cloudDemandIdcDBHelper;

    @Test
    public void test() {
        long id = 0L;

        while(true) {
            List<CbsInfoForCrpDO> all = cloudDemandIdcDBHelper.getAll(CbsInfoForCrpDO.class,
                    "where id>? order by id limit 30000", id);
            if (all.isEmpty()) {
                break;
            }

            cloudDemandDataIdcDBHelper.insertBatchWithoutReturnId(all);
            System.out.println(DateUtils.format(new Date()) + " insert id:" + all.getLast().getId());

            id = all.getLast().getId();
        }

        System.out.println("done");
    }
}
