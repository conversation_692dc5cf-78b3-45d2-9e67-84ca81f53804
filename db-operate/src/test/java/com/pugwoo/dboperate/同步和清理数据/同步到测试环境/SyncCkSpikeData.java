package com.pugwoo.dboperate.同步和清理数据.同步到测试环境;

import com.pugwoo.dbhelper.DBHelper;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 同步生产的毛刺数据到测试环境
 */
@SpringBootTest
public class SyncCkSpikeData {

    @Resource
    private DBHelper ckCloudDemandDevDBHelper;
    @Resource
    private DBHelper ckCloudDemandNewIdcDBHelper;

    @Test
    public void sync() {
        List<String> idcUuid = ckCloudDemandNewIdcDBHelper.getRaw(String.class, "select distinct batch_uuid from ppl_forecast_input_excluded_spike");
        List<String> devUuid = ckCloudDemandDevDBHelper.getRaw(String.class, "select distinct batch_uuid from ppl_forecast_input_excluded_spike");

        // 对于生产有而开发环境没有的，同步过去
        for (String uuid : idcUuid) {
            if (!devUuid.contains(uuid)) {
                List<PplForecastInputExcludedSpikeDO> all = ckCloudDemandNewIdcDBHelper.getAll(PplForecastInputExcludedSpikeDO.class, "where batch_uuid=?", uuid);
                ckCloudDemandDevDBHelper.insertBatchWithoutReturnId(all);
                System.out.println("sync uuid:" + uuid + " done");
            }
        }
    }
}
