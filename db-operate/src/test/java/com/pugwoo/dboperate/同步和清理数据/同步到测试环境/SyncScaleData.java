package com.pugwoo.dboperate.同步和清理数据.同步到测试环境;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.lang.DateUtils;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import jakarta.annotation.Resource;
import java.time.LocalDate;

/**
 * 同步日规模数据到测试环境
 */
@SpringBootTest
public class SyncScaleData {

    @Resource
    private DBHelper ckStdCrpDevDBHelper;

    @Test
    public void test() {
        LocalDate start = DateUtils.parseLocalDate("2024-04-15");
        LocalDate end = DateUtils.parseLocalDate("2024-05-21");

        while(!start.isAfter(end)) {
            String d = DateUtils.format(start);
            ckStdCrpDevDBHelper.executeRaw("""
                    ALTER TABLE std_crp.dwd_txy_scale_df_local ON CLUSTER default_cluster DROP PARTITION ?
                    """, d);

            try {
                Thread.sleep(10000);
            } catch (InterruptedException e) {
            }

            ckStdCrpDevDBHelper.executeRaw("""
                    insert into dwd_txy_scale_df
                    select * from remote('11.135.246.207:9000?receive_timeout=1800000', 'std_crp', 'dwd_txy_scale_df', 'crp_root', 'app@ERP2018')
                    where stat_time=?;
                    """, d);

            System.out.println("sync date:" + d + "done");
            start = start.plusDays(1);
        }
    }

}
