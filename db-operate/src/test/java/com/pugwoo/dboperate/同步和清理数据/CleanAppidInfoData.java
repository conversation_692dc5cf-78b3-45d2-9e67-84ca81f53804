package com.pugwoo.dboperate.同步和清理数据;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;
import java.util.Map;

@SpringBootTest
public class CleanAppidInfoData {

    @Resource
    private DBHelper cloudDemandIdcDBHelper;

    // 说明，实际上这个industry_report_appid_info只需要保留最近2个月的数据就可以了
    @Test
    public void delete() {
        for (int i = 0; i < 10000; i++) {
            int rows = cloudDemandIdcDBHelper.executeRaw("delete from industry_report_appid_info where stat_time between '2023-04-01' and '2023-12-01' limit 10000");
            System.out.println("delete " + rows + " rows");
        }
    }

    @Test
    public void clearFullInfo() {
        List<String> months = ListUtils.of("2024-03-01", "2024-04-01", "2024-05-01", "2024-06-01", "2024-07-01", "2024-08-01");

        for (String month : months) {
            Map<String, Object> minMax = cloudDemandIdcDBHelper.getRawOne(Map.class,
                    "select min(id) as min_id,max(id) as max_id from industry_report_appid_info where stat_time=? and full_info_json is not null", month);
            for (int i = 0; i < 1000; i++) {
                long start = System.currentTimeMillis();
                int rows = cloudDemandIdcDBHelper.executeRaw("""
                      update industry_report_appid_info set full_info_json=null
                      where stat_time=? and full_info_json is not null
                      and id between ? and ? limit 10000
                 """, month, minMax.get("min_id"), minMax.get("max_id"));
                System.out.println("update " + month + " " + rows + " rows" + ", cost " + (System.currentTimeMillis() - start) + "ms");
                if (rows == 0) {
                    break;
                }
            }
        }
    }

}
