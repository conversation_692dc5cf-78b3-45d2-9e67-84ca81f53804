package com.pugwoo.dboperate.同步和清理数据.同步到测试环境;

import com.pugwoo.dbhelper.DBHelper;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
public class SyncAppidInfoData {

    @Resource
    private DBHelper ckStdCrpDevDBHelper;

    @Test
    public void test() {
        ckStdCrpDevDBHelper.executeRaw("""
                    insert into dwd_txy_appid_info_cf
                    select * from remote('11.135.246.207:9000?receive_timeout=1800000', 'std_crp', 'dwd_txy_appid_info_cf', 'crp_root', 'app@ERP2018')
           ;
          """);
    }
}
