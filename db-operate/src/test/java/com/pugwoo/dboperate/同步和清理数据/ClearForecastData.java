package com.pugwoo.dboperate.同步和清理数据;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.lang.DateUtils;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Date;
import java.util.List;

/**
 * 清理生产的和模型预测相关的数据
 */
@SpringBootTest
public class ClearForecastData {

    @Resource
    private DBHelper cloudDemandDevDBHelper;
    @Resource
    private DBHelper cloudDemandIdcDBHelper;

    /**
     * 注意：二八原则，这里只清理量大的表
     */
    @Test
    public void cleanMysqlData() {
        DBHelper dbHelper = cloudDemandIdcDBHelper; // 自行切换环境，默认生产环境！
        dbHelper.setSlowSqlWarningValve(30000);

        // 说明：直接delete from where limit的性能好查，100条删除了3分钟都没有删完；而先查出id，再删除，10000条就只要几秒钟，只能说明mysql自身优化做得还是差了
        // 特别说明：mysql还不支持 delete from where id in (select id where limit) 这种写法，limit在子查询里面，所以没法写成1条sql
        int rows;
        do {
            List<Long> deleteIds = dbHelper.getRaw(Long.class, """
                    select id from ppl_forecast_input_detail
                    WHERE input_id NOT IN (SELECT input_id FROM `ppl_forecast_predict_task`)
                      and task_id not in (select id from ppl_forecast_predict_task)
                    limit 10000
                    """);
            if (deleteIds.isEmpty()) {
                break;
            }
            rows = dbHelper.executeRaw("""
                    DELETE FROM `ppl_forecast_input_detail`
                    WHERE id in (?)
                    """, deleteIds);
            System.out.println(DateUtils.format(new Date()) + "delete " + rows + "rows");
        } while (rows > 0);

        do {
            List<Long> deleteIds = dbHelper.getRaw(Long.class, """
                select id FROM `ppl_forecast_predict_result`
                    WHERE task_id not in (select id from ppl_forecast_predict_task)
                    limit 10000
            """);
            rows = dbHelper.executeRaw("""
                    DELETE FROM `ppl_forecast_predict_result`
                    WHERE id in (?)
                    """, deleteIds);
            System.out.println(DateUtils.format(new Date()) + "delete " + rows + "rows");
        } while (rows > 0);
    }

    @Test
    public void cleanMysqlForecastCommonData() {
        DBHelper dbHelper = cloudDemandIdcDBHelper; // 自行切换环境，默认生产环境！

        dbHelper.executeRaw("truncate table forecast_compute_task");
        dbHelper.executeRaw("truncate table forecast_compute_task_input");
        dbHelper.executeRaw("truncate table forecast_compute_task_input_test_dataset");
        dbHelper.executeRaw("truncate table forecast_compute_task_run");
        dbHelper.executeRaw("truncate table forecast_compute_task_run_input");
        dbHelper.executeRaw("truncate table forecast_compute_task_run_output");
    }

}
