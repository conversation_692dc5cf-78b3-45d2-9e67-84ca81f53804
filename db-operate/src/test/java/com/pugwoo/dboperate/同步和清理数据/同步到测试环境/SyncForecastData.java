package com.pugwoo.dboperate.同步和清理数据.同步到测试环境;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import jakarta.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;

@SpringBootTest
public class SyncForecastData {

    @Resource
    private DBHelper cloudDemandDevDBHelper;
    @Resource
    private DBHelper cloudDemandIdcDBHelper;

    @Resource
    private DBHelper ckStdCrpNewIdcDBHelper;

    @Test
    public void syncClickhouse() {
        ckStdCrpNewIdcDBHelper.executeRaw("""
                truncate table std_crp_forecast_test.dwd_crp_longtail_forecast_item_df_local on cluster default_cluster
                """);
        try {
            Thread.sleep(10000);
        } catch (InterruptedException ignored) {
        }
        ckStdCrpNewIdcDBHelper.executeRaw("""
                    insert into std_crp_forecast_test.dwd_crp_longtail_forecast_item_df
                    select * from std_crp.dwd_crp_longtail_forecast_item_df
          """);
    }

    /**
     * 同步生产的mysql的预测相关的表到测试环境
     */
    @Test
    public void syncMysql() {
        List<String> syncTable = ListUtils.newList(
                "ppl_forecast_config_white_list_to_ppl",
                "mrpv2_common_instance_type_config",
                "ppl_forecast_config_spike_threshold",
                "ppl_forecast_input",
                "ppl_forecast_input_compensation",
                "ppl_forecast_input_detail",
                "ppl_forecast_input_detail_for_ppl",
                "ppl_forecast_input_detail_latest",
                "ppl_forecast_input_detail_latest_for_mrp",
                "ppl_forecast_input_excluded_spike",
                "ppl_forecast_predict_result",
                "ppl_forecast_predict_result_for_ziyan_split",
                "ppl_forecast_predict_result_latest",
                "ppl_forecast_predict_result_split",
                "ppl_forecast_predict_result_split_middle",
                "ppl_forecast_predict_task",
                "ppl_forecast_predict_task_output_version",
                "ppl_forecast_predict_week_max_result",
                "ppl_forecast_transform_record",
                "ppl_forecast_transform_to_ppl"
        );

        for (String table : syncTable) {
            System.out.println(DateUtils.format(new Date()) + " start sync " + table + ",total size: " +
                    cloudDemandIdcDBHelper.getRawOne(Long.class, "select count(*) from " + table));
            cloudDemandDevDBHelper.executeRaw("truncate table " + table);

            Stream<Map> stream = cloudDemandIdcDBHelper.getRawForStream(Map.class, "select * from " + table);
            Stream<List<Map>> listStream = ListUtils.groupByNum(stream, 100000);
            listStream.forEach(raw -> {
                cloudDemandDevDBHelper.insertBatchWithoutReturnId(table,
                        ListUtils.transform(raw, o -> (Map<String, Object>) o));
            });

            stream.close();

            System.out.println(DateUtils.format(new Date()) + " sync " + table + " done");
        }
    }

}
