package com.pugwoo.dboperate.同步和清理数据.同步到测试环境;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

@Data
@ToString
@Table("ppl_forecast_input_excluded_spike")
public class PplForecastInputExcludedSpikeDO {

    /** 批次uuid，用于在任务id产生之前查询用<br/>Column: [batch_uuid] */
    @Column(value = "batch_uuid")
    private String batchUuid;

    /** 毛刺的年<br/>Column: [year] */
    @Column(value = "year")
    private Integer year;

    /** 毛刺的月<br/>Column: [month] */
    @Column(value = "month")
    private Integer month;

    /** 行业部门<br/>Column: [industry_dept] */
    @Column(value = "industry_dept")
    private String industryDept;

    /** 客户uin<br/>Column: [customer_uin] */
    @Column(value = "customer_uin")
    private String customerUin;

    /** 客户简称，简称不一定有<br/>Column: [customer_short_name] */
    @Column(value = "customer_short_name")
    private String customerShortName;

    /** 机型大类<br/>Column: [instance_type] */
    @Column(value = "instance_type")
    private String instanceType;

    /** 地域名称<br/>Column: [region_name] */
    @Column(value = "region_name")
    private String regionName;

    /** 新增核心数<br/>Column: [new_core] */
    @Column(value = "new_core")
    private BigDecimal newCore;

    /** 退回核心数<br/>Column: [ret_core] */
    @Column(value = "ret_core")
    private BigDecimal retCore;

    /** 可用区<br/>Column: [zone_name] */
    @Column(value = "zone_name")
    private String zoneName;

}