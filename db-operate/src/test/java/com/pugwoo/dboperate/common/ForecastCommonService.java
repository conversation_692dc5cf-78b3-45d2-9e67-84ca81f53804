package com.pugwoo.dboperate.common;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dboperate.forecast_compute.entity.Mrpv2CommonInstanceTypeConfigDO;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.string.StringTools;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 相关公共的服务
 */
@Slf4j
@Service
public class ForecastCommonService {

    @Resource
    private DBHelper cloudDemandIdcDBHelper; // 生产环境的，仅用于查询黑名单和机型收敛

    /**
     * 查询黑名单
     */
    public List<String> getBlacklistInstanceType() {
        String instanceTypes = cloudDemandIdcDBHelper.getRawOne(String.class,
                "select instance_types from mrpv2_common_instance_type_config "
                        + "where common_instance_type='黑名单'");
        if (StringTools.isBlank(instanceTypes)) {
            log.error("黑名单为空");
            return new ArrayList<>();
        }

        String[] split = instanceTypes.split(",");
        List<String> list = new ArrayList<>();
        for (String s : split) {
            list.add(s.trim());
        }
        return list;
    }

    /**
     * 机型收敛机型映射map，如果不存在就不用映射了
     */
    public Map<String, String> getInstanceTypeMergeMap() {
        List<Mrpv2CommonInstanceTypeConfigDO> all = cloudDemandIdcDBHelper.getAll(Mrpv2CommonInstanceTypeConfigDO.class,
                "where use_forecast=1");
        if (ListUtils.isEmpty(all)) {
            return new HashMap<>();
        }

        Map<String, String> map = new HashMap<>();
        for (Mrpv2CommonInstanceTypeConfigDO config : all) {
            String instanceTypes = config.getInstanceTypes();
            if (StringTools.isNotBlank(instanceTypes)) {
                String[] split = instanceTypes.split(",");
                if (split.length == 0) {
                    log.error("机型收敛配置为空:{}", JSON.toJson(config));
                    continue;
                }

                for (int i = 1; i < split.length; i++) {
                    map.put(split[i], split[0]);
                }
            }
        }

        return map;
    }

    /**
     * 查询用于机型收敛的case when子句
     */
    public String getInstanceTypeMergeCaseWhen() {
        List<Mrpv2CommonInstanceTypeConfigDO> all = cloudDemandIdcDBHelper.getAll(Mrpv2CommonInstanceTypeConfigDO.class,
                "where use_forecast=1");
        if (ListUtils.isEmpty(all)) {
            log.error("机型收敛配置为空");
            return "instance_type";
        }

        StringBuilder caseWhen = new StringBuilder("(case ");
        for (Mrpv2CommonInstanceTypeConfigDO config : all) {
            String instanceTypes = config.getInstanceTypes();
            if (StringTools.isNotBlank(instanceTypes)) {
                String[] split = instanceTypes.split(",");
                if (split.length == 0) {
                    log.error("机型收敛配置为空:{}", JSON.toJson(config));
                    continue;
                }

                caseWhen.append(" when instance_type in (");
                for (int i = 0; i < split.length; i++) {
                    caseWhen.append("'").append(split[i]).append("'");
                    if (i != split.length - 1) {
                        caseWhen.append(",");
                    }
                }
                caseWhen.append(") then '").append(split[0]).append("' \n");
            }
        }

        caseWhen.append(" else instance_type end)");
        return caseWhen.toString();
    }

    /**
     * 查询用于机型收敛的case when子句，专门给yunti用的
     */
    public String getInstanceTypeMergeCaseWhenForYunti() {
        List<Mrpv2CommonInstanceTypeConfigDO> all = cloudDemandIdcDBHelper.getAll(Mrpv2CommonInstanceTypeConfigDO.class,
                "where use_forecast=1");
        if (ListUtils.isEmpty(all)) {
            log.error("机型收敛配置为空");
            return "instance_type";
        }

        StringBuilder caseWhen = new StringBuilder("(case ");
        for (Mrpv2CommonInstanceTypeConfigDO config : all) {
            String instanceTypes = config.getInstanceTypes();
            if (StringTools.isNotBlank(instanceTypes)) {
                String[] split = instanceTypes.split(",");
                if (split.length == 0) {
                    log.error("机型收敛配置为空:{}", JSON.toJson(config));
                    continue;
                }

                caseWhen.append(" when splitByString('.', instance_model)[1] in (");
                for (int i = 0; i < split.length; i++) {
                    caseWhen.append("'").append(split[i]).append("'");
                    if (i != split.length - 1) {
                        caseWhen.append(",");
                    }
                }
                caseWhen.append(") then '").append(split[0]).append("' \n");
            }
        }

        caseWhen.append(" else instance_type end)");
        return caseWhen.toString();
    }
}
