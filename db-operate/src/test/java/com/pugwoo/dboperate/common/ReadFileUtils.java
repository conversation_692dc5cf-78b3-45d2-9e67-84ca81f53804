package com.pugwoo.dboperate.common;

import com.pugwoo.wooutils.io.IOUtils;
import lombok.SneakyThrows;

import java.nio.file.Files;
import java.nio.file.Paths;

/**
 * 读取放在java目录中的文件
 * 说明：这种做法并不规范，因为这个项目只是个自己用的测试项目，所以为了方便组织文件，才这么做
 *
 * 说明：这个可能打包package就不能用了，故不提取了，临时自己用而已
 */
public class ReadFileUtils {

    @SneakyThrows
    public static String read(String filename) {
        // 获取调用者所在的类
        Class<?> callerClass = getCallerClass();
        String currentDirectory = System.getProperty("user.dir");

        assert callerClass != null;
        String filePath = currentDirectory + "/src/test/java/" +
                callerClass.getPackage().getName().replace(".", "/")
                + "/" + filename;

        return IOUtils.readAllAndClose(Files.newInputStream(Paths.get(filePath)), "utf-8");
    }

    @SneakyThrows
    public static Class<?> getCallerClass() {
        StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
        String className = null;

        // 遍历堆栈信息，查找调用者的类名
        for (int i = 0; i < stackTrace.length; i++) {
            StackTraceElement frame = stackTrace[i];
            if (frame.getClassName().equals(ReadFileUtils.class.getName())) {
                // 找到调用者所在的类的堆栈帧
                if (i + 2 < stackTrace.length) {
                    className = stackTrace[i + 2].getClassName();
                    break;
                }
            }
        }

        if (className != null) {
            // 使用反射获取调用者所在的类的Class对象
            return Class.forName(className);
        }

        return null;
    }
}
