package com.pugwoo.dboperate.模型预测调优;

import com.pugwoo.dboperate.模型预测调优.utils.RunForecastCommonUtils;
import com.pugwoo.dboperate.模型预测调优.utils.RunForecastResultDTO;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@SpringBootTest
public class RunForecast7xx系列 {

    @Resource
    private RunForecastCommonUtils runForecastCommonUtils;

    @Test
    public void test705原版() throws Exception {
        Map<String, Object> commonParam = new HashMap<>();
        commonParam.put("category", "方案705：全部-月切保留客户维度(机型规格)-机型收敛-包弹-外部客户-月度ARIMAX");
        commonParam.put("serialInterval", "MONTH");
        commonParam.put("sourceType", "INDUSTRY");
        commonParam.put("billType", "ALL");
        commonParam.put("alName", "ARIMAXL");
        commonParam.put("alArg", "0,1,3,0,1,6,0.2"); // 当值为-1(只有一个值)时表示自动选择参数
        commonParam.put("retAlArgs", "0,1,3,0.2"); // 退回采用0,1,3
        commonParam.put("transInstanceType", "true");
        commonParam.put("groupDimType", "UIN_INSTANCE_MODEL_ZONE_NAME");
        commonParam.put("isRemoveSpike", "true");
        commonParam.put("forecastScope", 1); // /*模型预测范围，1为预测长尾，2是预测31个头部(且不含毛刺)，3是预测31个头部+毛刺*/

        LocalDate curMonth = DateUtils.getFirstDayOfMonth(new Date());
        List<RunForecastResultDTO> result = runForecastCommonUtils.run("2025-02-01", curMonth.toString(), "", commonParam);

        // 算一下2023年的准确率
        BigDecimal newAccuracyRate2023 = NumberUtils.avg(
                ListUtils.filter(result, o -> o.getPredictMonth().getYear() == 2023), 6,
                o -> o.getNewAccuracyRatePercent());
        BigDecimal retAccuracyRate2023 = NumberUtils.avg(
                ListUtils.filter(result, o -> o.getPredictMonth().getYear() == 2023), 6,
                o -> o.getRetAccuracyRatePercent());
        System.out.println("2023年平均准确率，新增:" + newAccuracyRate2023 + "%, 退回:" + retAccuracyRate2023 + "%");
    }


    private static Map<String, String> headCustomers = new HashMap<>(){{
        put("小红书", "Galacurz,安准信息科技(武汉)有限公司,泓书信息科技(上海)有限公司,薯一薯二文化传媒(上海)有限公司,小红书,小红书-超超世世,小红书武汉分公司,小红书相关主体,伊普西龙信息科技(北京)有限公司,伊普西龙信息科技（上海）有限公司");
        put("拼多多", "ELEMENTARY INNOVATION PTE.LTD,付费通,杭州埃米网络科技有限公司,杭州微米,宽带山,拼多多,曦昶健康科技(上海)有限公司");

        // 说明：为什么不太适合于不知名的公司，因为这些公司可能最近才出现，如果它们也作为头部公司，那么会导致头部名单变化过快

        // 二线的头部公司
        put("美团点评", "北京宝宝爱吃餐饮管理有限公司,北京摩拜科技有限公司,北京三快科技有限公司,北京三快在线科技有限公司,成都众亨源商贸有限公司,饭否,光年之外,互诚信息,黄小兜,美团,美团点评,美团点评-黑石ARM,美团点评-综合业务,摩拜,屏芯,钱袋宝,厦门三快在线科技有限公司,厦门榛果假期旅行社有限公司,上海别样红信息技术有限公司,上海汉涛信息咨询有限公司,上海路团科技有限公司,上海三快好物科技有限公司,上海三快省心购科技有限公司,深圳航路旅行科技有限公司,深圳美团优选网络科技有限公司,天津三快飞跃科技有限公司");
        put("荣耀终端", "荣耀终端,荣耀终端有限公司");

    }};

    /**
     * 实验自定义的头部客户和阈值
     */
    @Test
    public void test709HeadCustomerAndSpikeThreshold() throws Exception {
        Map<String, Object> commonParam = new HashMap<>();
        commonParam.put("category", "方案709：全部-月切保留客户维度(机型规格)-机型收敛-包弹-外部客户-月度ARIMAX");
        commonParam.put("serialInterval", "MONTH");
        commonParam.put("sourceType", "INDUSTRY");
        commonParam.put("billType", "ALL");
        commonParam.put("alName", "ARIMAXL");
        commonParam.put("alArg", "0,1,3,0,1,6,0.2"); // 当值为-1(只有一个值)时表示自动选择参数
        commonParam.put("retAlArgs", "0,1,3,0.2"); // 退回采用0,1,3
        commonParam.put("transInstanceType", "true");
        commonParam.put("groupDimType", "UIN_INSTANCE_MODEL_ZONE_NAME");
        commonParam.put("isRemoveSpike", "true");
        commonParam.put("forecastScope", 1); // /*模型预测范围，1为预测长尾，2是预测31个头部(且不含毛刺)，3是预测31个头部+毛刺*/
        commonParam.put("customHeadCustomers", headCustomers.get("小红书") + "," + headCustomers.get("拼多多")); // 小红书 拼多多，填NONE表示没有头部
        commonParam.put("customSpikeThreshold", "1500");

        LocalDate curMonth = DateUtils.getFirstDayOfMonth(new Date());
        List<RunForecastResultDTO> result = runForecastCommonUtils.run("2024-01-01", curMonth.toString(), "", commonParam);

        // 计算一下2024年4-9月的准确率
        BigDecimal newAccuracyRate2024 = NumberUtils.avg(
                ListUtils.filter(result, o -> o.getPredictMonth().getYear() == 2024 && o.getPredictMonth().getMonthValue() >= 4 && o.getPredictMonth().getMonthValue() <= 9), 6,
                o -> o.getNewAccuracyRatePercent());
        BigDecimal retAccuracyRate2024 = NumberUtils.avg(
                ListUtils.filter(result, o -> o.getPredictMonth().getYear() == 2024 && o.getPredictMonth().getMonthValue() >= 4 && o.getPredictMonth().getMonthValue() <= 9), 6,
                o -> o.getRetAccuracyRatePercent());
        System.out.println("2024年4-9月平均准确率，新增:" + newAccuracyRate2024 + "%, 退回:" + retAccuracyRate2024 + "%");
    }

    @Test
    public void test707清华() throws Exception {
        Map<String, Object> commonParam = new HashMap<>();
        commonParam.put("category", "方案707：全部-月切保留客户维度(机型规格)-机型收敛-包弹-外部客户-月度-MIXED");
        commonParam.put("serialInterval", "MONTH");
        commonParam.put("sourceType", "INDUSTRY");
        commonParam.put("billType", "ALL");
        commonParam.put("alName", "MIXED");
        commonParam.put("alArg", "");
        commonParam.put("transInstanceType", "true");
        commonParam.put("groupDimType", "UIN_INSTANCE_MODEL_ZONE_NAME");
        commonParam.put("isRemoveSpike", "true");
        commonParam.put("forecastScope", 1); // /*模型预测范围，1为预测长尾，2是预测31个头部(且不含毛刺)，3是预测31个头部+毛刺*/

        LocalDate curMonth = DateUtils.getFirstDayOfMonth(new Date());
        List<RunForecastResultDTO> result = runForecastCommonUtils.run("2024-12-01", curMonth.toString(), "", commonParam);

        // 算一下2023年的准确率
        BigDecimal newAccuracyRate2023 = NumberUtils.avg(
                ListUtils.filter(result, o -> o.getPredictMonth().getYear() == 2023), 6,
                o -> o.getNewAccuracyRatePercent());
        BigDecimal retAccuracyRate2023 = NumberUtils.avg(
                ListUtils.filter(result, o -> o.getPredictMonth().getYear() == 2023), 6,
                o -> o.getRetAccuracyRatePercent());
        System.out.println("2023年平均准确率，新增:" + newAccuracyRate2023 + "%, 退回:" + retAccuracyRate2023 + "%");
    }

    @Test
    public void test706是705无干预版ARIMAX版() throws Exception {
        Map<String, Object> commonParam = new HashMap<>();
        commonParam.put("category", "方案706：全部-月切保留客户维度(机型规格)-机型收敛-包弹-外部客户-月度ARIMAX-无干预");
        commonParam.put("serialInterval", "MONTH");
        commonParam.put("sourceType", "INDUSTRY");
        commonParam.put("billType", "ALL");
        commonParam.put("alName", "ARIMAXL");
        commonParam.put("alArg", "0,1,3,0,1,6,0.2"); // 当值为-1(只有一个值)时表示自动选择参数
        commonParam.put("retAlArgs", "0,1,3,0.2"); // 退回采用0,1,3
        commonParam.put("transInstanceType", "true");
        commonParam.put("groupDimType", "UIN_INSTANCE_MODEL_ZONE_NAME");
        commonParam.put("isRemoveSpike", "true");
        commonParam.put("forecastScope", 1);

        LocalDate curMonth = DateUtils.getFirstDayOfMonth(new Date());
        List<RunForecastResultDTO> result = runForecastCommonUtils.run(curMonth.toString(), curMonth.toString(), "", commonParam);

        // 算一下2023年的准确率
        BigDecimal newAccuracyRate2023 = NumberUtils.avg(
                ListUtils.filter(result, o -> o.getPredictMonth().getYear() == 2023), 6,
                o -> o.getNewAccuracyRatePercent());
        BigDecimal retAccuracyRate2023 = NumberUtils.avg(
                ListUtils.filter(result, o -> o.getPredictMonth().getYear() == 2023), 6,
                o -> o.getRetAccuracyRatePercent());
        System.out.println("2023年平均准确率，新增:" + newAccuracyRate2023 + "%, 退回:" + retAccuracyRate2023 + "%");
    }

    @Test
    public void test715原版() throws Exception {
        Map<String, Object> commonParam = new HashMap<>();
        commonParam.put("category", "方案715：全部-月切保留客户维度(机型规格)-机型收敛-包弹-内部客户-月度ARIMAX");
        commonParam.put("serialInterval", "MONTH");
        commonParam.put("sourceType", "INNER");
        commonParam.put("billType", "ALL");
        commonParam.put("alName", "ARIMAX");
        commonParam.put("alArg", "0,1,3,0,1,6,0.2");
        commonParam.put("transInstanceType", "true");
        commonParam.put("groupDimType", "UIN_INSTANCE_MODEL_ZONE_NAME");
        commonParam.put("isRemoveSpike", "true");
        commonParam.put("forecastScope", 1);

        LocalDate curMonth = DateUtils.getFirstDayOfMonth(new Date());
        List<RunForecastResultDTO> result = runForecastCommonUtils.run("2024-11-01", curMonth.toString(), "", commonParam);

        // 算一下2023年的准确率
        BigDecimal newAccuracyRate2023 = NumberUtils.avg(
                ListUtils.filter(result, o -> o.getPredictMonth().getYear() == 2023), 6,
                o -> o.getNewAccuracyRatePercent());
        BigDecimal retAccuracyRate2023 = NumberUtils.avg(
                ListUtils.filter(result, o -> o.getPredictMonth().getYear() == 2023), 6,
                o -> o.getRetAccuracyRatePercent());
        System.out.println("2023年平均准确率，新增:" + newAccuracyRate2023 + "%, 退回:" + retAccuracyRate2023 + "%");
    }

    @Test
    public void test725原版() throws Exception {
        Map<String, Object> commonParam = new HashMap<>();
        commonParam.put("category", "方案725：全部-月切保留客户维度(机型规格)-机型收敛-包弹-内外部客户-月度ARIMAX");
        commonParam.put("serialInterval", "MONTH");
        commonParam.put("sourceType", "INDUSTRY_INNER");
        commonParam.put("billType", "ALL");
        commonParam.put("alName", "ARIMAX");
        commonParam.put("alArg", "0,1,3,0,1,6,0.2");
        commonParam.put("transInstanceType", "true");
        commonParam.put("groupDimType", "UIN_INSTANCE_MODEL_ZONE_NAME");
        commonParam.put("isRemoveSpike", "true");
        commonParam.put("forecastScope", 1);

        LocalDate curMonth = DateUtils.getFirstDayOfMonth(new Date());
        List<RunForecastResultDTO> result = runForecastCommonUtils.run("2024-12-01", curMonth.toString(), "", commonParam);

        // 算一下2023年的准确率
        BigDecimal newAccuracyRate2023 = NumberUtils.avg(
                ListUtils.filter(result, o -> o.getPredictMonth().getYear() == 2023), 6,
                o -> o.getNewAccuracyRatePercent());
        BigDecimal retAccuracyRate2023 = NumberUtils.avg(
                ListUtils.filter(result, o -> o.getPredictMonth().getYear() == 2023), 6,
                o -> o.getRetAccuracyRatePercent());
        System.out.println("2023年平均准确率，新增:" + newAccuracyRate2023 + "%, 退回:" + retAccuracyRate2023 + "%");
    }

    /**
     * 这个对应于清华同学的研究
     */
    @Test
    public void test不剔毛刺的长尾_原版() throws Exception {
        Map<String, Object> commonParam = new HashMap<>();
        commonParam.put("category", "方案755：全部不剔毛刺-月切保留客户维度(机型规格)-机型收敛-包弹-外部客户-月度ARIMAX");
        commonParam.put("serialInterval", "MONTH");
        commonParam.put("sourceType", "INDUSTRY");
        commonParam.put("billType", "ALL");
        commonParam.put("alName", "ARIMAX");
        commonParam.put("alArg", "0,1,3,0,1,6,0.2");
        commonParam.put("transInstanceType", "true");
        commonParam.put("groupDimType", "UIN_INSTANCE_MODEL_ZONE_NAME");
        commonParam.put("isRemoveSpike", "false");
        commonParam.put("forecastScope", 1);

        List<RunForecastResultDTO> result = runForecastCommonUtils.run("2024-07-01", "2024-08-01", "", commonParam);

        // 算一下2023年的准确率
//        BigDecimal newAccuracyRate2023 = NumberUtils.avg(
//                ListUtils.filter(result, o -> o.getPredictMonth().getYear() == 2023), 6,
//                o -> o.getNewAccuracyRatePercent());
//        BigDecimal retAccuracyRate2023 = NumberUtils.avg(
//                ListUtils.filter(result, o -> o.getPredictMonth().getYear() == 2023), 6,
//                o -> o.getRetAccuracyRatePercent());
//        System.out.println("2023年平均准确率，新增:" + newAccuracyRate2023 + "%, 退回:" + retAccuracyRate2023 + "%");

        commonParam = new HashMap<>();
        commonParam.put("category", "方案765：全部不剔毛刺-月切保留客户维度(机型规格)-机型收敛-包弹-内部客户-月度ARIMAX");
        commonParam.put("serialInterval", "MONTH");
        commonParam.put("sourceType", "INNER");
        commonParam.put("billType", "ALL");
        commonParam.put("alName", "ARIMAX");
        commonParam.put("alArg", "0,1,3,0,1,6,0.2");
        commonParam.put("transInstanceType", "true");
        commonParam.put("groupDimType", "UIN_INSTANCE_MODEL_ZONE_NAME");
        commonParam.put("isRemoveSpike", "false");
        commonParam.put("forecastScope", 1);

        runForecastCommonUtils.run("2024-07-01", "2024-08-01", "", commonParam);

        commonParam = new HashMap<>();
        commonParam.put("category", "方案775：全部不剔毛刺-月切保留客户维度(机型规格)-机型收敛-包弹-内外部客户-月度ARIMAX");
        commonParam.put("serialInterval", "MONTH");
        commonParam.put("sourceType", "INDUSTRY_INNER");
        commonParam.put("billType", "ALL");
        commonParam.put("alName", "ARIMAX");
        commonParam.put("alArg", "0,1,3,0,1,6,0.2");
        commonParam.put("transInstanceType", "true");
        commonParam.put("groupDimType", "UIN_INSTANCE_MODEL_ZONE_NAME");
        commonParam.put("isRemoveSpike", "false");
        commonParam.put("forecastScope", 1);

        runForecastCommonUtils.run("2024-07-01", "2024-08-01", "", commonParam);

    }

    /**
     * 这个经过试验论证，是没有效果的
     */
    @Test
    public void test705原版_调整新增比例() throws Exception {
        Map<String, Object> commonParam = new HashMap<>();
        commonParam.put("category", "方案705：全部-月切保留客户维度(机型规格)-机型收敛-包弹-外部客户-月度ARIMAX");
        commonParam.put("serialInterval", "MONTH");
        commonParam.put("sourceType", "INDUSTRY");
        commonParam.put("billType", "ALL");
        commonParam.put("alName", "ARIMAX");
        commonParam.put("alArg", "0,1,3,0,1,6,0.2");
        commonParam.put("transInstanceType", "true");
        commonParam.put("groupDimType", "UIN_INSTANCE_MODEL_ZONE_NAME");
        commonParam.put("isRemoveSpike", "true");
        commonParam.put("forecastScope", 1);
        commonParam.put("adjustNewResultRatio", "1.01");

        runForecastCommonUtils.run("2022-08-01", "2024-06-01", "", commonParam);
    }


}
