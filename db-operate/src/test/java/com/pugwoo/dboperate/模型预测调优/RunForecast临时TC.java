package com.pugwoo.dboperate.模型预测调优;

import com.pugwoo.dboperate.模型预测调优.utils.RunForecastCommonUtils;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.HashMap;
import java.util.Map;

@SpringBootTest
public class RunForecast临时TC {

    @Resource
    private RunForecastCommonUtils runForecastCommonUtils;

    @Test
    public void testTC无阈值() throws Exception {
        Map<String, Object> commonParam = new HashMap<>();
        commonParam.put("category", "方案920：无阈值-TC-月切保留客户维度(机型规格)-机型收敛-包弹-全部客户-月度ARIMAX");
        commonParam.put("serialInterval", "MONTH");
        commonParam.put("sourceType", "INDUSTRY");
        commonParam.put("billType", "ALL");
        commonParam.put("alName", "ARIMAX");
        commonParam.put("alArg", "0,1,3,0,1,6,0.2");
        commonParam.put("transInstanceType", "true");
        commonParam.put("groupDimType", "UIN_INSTANCE_MODEL_ZONE_NAME");
        commonParam.put("isRemoveSpike", "true");
        commonParam.put("forecastScope", 1);

        runForecastCommonUtils.run("2023-08-01", "2025-05-01", "custom_sql/tc/TC无阈值.sql", commonParam);
    }

    @Test
    public void testTC阈值1000() throws Exception {
        Map<String, Object> commonParam = new HashMap<>();
        commonParam.put("category", "方案921：阈值1000-TC-月切保留客户维度(机型规格)-机型收敛-包弹-全部客户-月度ARIMAX");
        commonParam.put("serialInterval", "MONTH");
        commonParam.put("sourceType", "INDUSTRY");
        commonParam.put("billType", "ALL");
        commonParam.put("alName", "ARIMAX");
        commonParam.put("alArg", "0,1,3,0,1,6,0.2");
        commonParam.put("transInstanceType", "true");
        commonParam.put("groupDimType", "UIN_INSTANCE_MODEL_ZONE_NAME");
        commonParam.put("isRemoveSpike", "true");
        commonParam.put("forecastScope", 1);

        runForecastCommonUtils.run("2023-08-01", "2025-05-01", "custom_sql/tc/TC阈值1000.sql", commonParam);
    }

    @Test
    public void testTC阈值2000() throws Exception {
        Map<String, Object> commonParam = new HashMap<>();
        commonParam.put("category", "方案922：阈值2000-TC-月切保留客户维度(机型规格)-机型收敛-包弹-全部客户-月度ARIMAX");
        commonParam.put("serialInterval", "MONTH");
        commonParam.put("sourceType", "INDUSTRY");
        commonParam.put("billType", "ALL");
        commonParam.put("alName", "ARIMAX");
        commonParam.put("alArg", "0,1,3,0,1,6,0.2");
        commonParam.put("transInstanceType", "true");
        commonParam.put("groupDimType", "UIN_INSTANCE_MODEL_ZONE_NAME");
        commonParam.put("isRemoveSpike", "true");
        commonParam.put("forecastScope", 1);

        runForecastCommonUtils.run("2023-08-01", "2025-05-01", "custom_sql/tc/TC阈值2000.sql", commonParam);
    }

}
