with raw as (
    select stat_time, uin,instance_type, region_name,
           sum(case when change_service_core_from_last_month>0 then change_service_core_from_last_month else 0 end) as new_core,
           sum(case when change_service_core_from_last_month<0 then -change_service_core_from_last_month else 0 end) as ret_core
    from std_crp.dwd_txy_scale_df
    where  stat_time in (
        SELECT toDate(formatDateTime(subtractDays(addMonths(toDate('2021-01-01'), number + 1), 1), '%Y-%m-%d')) AS last_day_of_month
        FROM numbers(dateDiff('month', toDate('2021-01-01'), toDate('2050-12-31')) + 1)
    )
      and app_role='EMR' and paymode_range_type in ('包年包月','弹性') and paymode!='5'
group by stat_time,uin,instance_type,region_name
    )

select year,month,
    any(month_start_date1) as first_day,
    any(month_end_date1) as last_day,
    any(customhouse_title1) as customhouse_title,
    any(region1) as region,
    region_name1 as region_name,
    instance_type1 as gins_family,
    sum(case when diff_bill_num>0 then diff_bill_num else 0 end) as new_diff, -- 增量：max-月初，>0部分
    sum(case when diff_bill_num<0 then -diff_bill_num else 0 end) as ret_diff, --   退回：max-月末，>0部分
    sum(cur_core) as last_day_num -- 当前：月末
from (
    select year,month,
    any(month_start_date) as month_start_date1,
    any(month_end_date) as month_end_date1,
    any(customhouse_title) as customhouse_title1,
    any(region) as region1,
    any(region_name) as region_name1,
    any(instance_type) as instance_type1,
    sum(case when stat_time=month_end_date then change_service_core_from_last_month else 0 end) as diff_bill_num,
    sum(case when stat_time=month_end_date then cur_service_core else 0 end) as cur_core
    from dwd_txy_scale_df
    where 1=1

    -- condition 的条件格式不可以改变， WEB 查询会 append  " and sql "

    -- and cpu_or_gpu = 'CPU' and biz_type = 'cvm'
    --  and app_role != 'LH'
    and app_role='EMR'
    --  and instance_type not like 'RS%' and instance_type not like 'RM%'
    and paymode_range_type in ('包年包月','弹性') and paymode!='5'
    -- and biz_range_type in ('外部业务')
    --  and zone_name in ('上海一区','上海七区','上海三区','上海二区','上海五区','上海八区','上海六区','上海四区','东京一区','东京二区','北京一区','北京七区','北京三区','北京二区','北京五区','北京八区','北京六区','北京四区','南京一区','南京三区','南京二区','台北一区','圣保罗一区','多伦多一区','天津三区','天津二区','孟买一区','孟买二区','广州七区','广州三区','广州二区','广州五区','广州八区','广州六区','广州四区','弗吉尼亚一区','弗吉尼亚二区','成都一区','成都二区','新加坡一区','新加坡三区','新加坡二区','新加坡四区','曼谷一区','曼谷二区','欧洲东北一区','法兰克福一区','法兰克福三区','法兰克福二区','深圳一区','深圳三区','深圳二区','深圳四区','硅谷一区','硅谷二区','贵阳一区','重庆一区','雅加达一区','雅加达二区','首尔一区','首尔二区','香港一区','香港三区','香港二区','上海自动驾驶云一区')
    --  and instance_type not in ('BC1','VSCVMMS','I6t','IA3se','SH2','BF1','BS1','C2','CN2','D1','DR1','DS2','DSW13','DSW14','FX4','HC20','HI1','HI20','HM1','HM20','HS10','HS1A','HS20','I1','I2','IT2','M1','M2','S1','S2','S2ne','SA1','SH1','SHARED','SK1','SN2','SPECIAL','SR1','TS2','TS3','VSCCS','VSCNAS','VSVPN','S5nt','VSVPNDPDK','SA1','SK1','SH1','SR1','OCA2','OC1','HS51','S3','S2','S1','TS3','TS2','HS30','HS20','HS10','S2ne','HS31','SN3ne','S4','TS4','M3','M2','M1','HM20','TM3','M4','C5','C3','C2','HC20','TC3','TC3ne','C4','C4ne','TC4','C4t','TC4t','IT5','IT5c','I3','I2','I1','HI20','IT3','IT3c','ITA3','CN3','CN2','TCN3','TCN3ne','D2','D1','D3','M3','M2','M1','HM20','TM3','M4') -- 黑名单机型

    and app_id not in (1258344706, 1251316161) -- 内部的2个appid，王丽丽给的，目前固定的，排除掉的

    and stat_time < :predictDate

    -- performance optimize
    and stat_time in (
    SELECT toDate(formatDateTime(subtractDays(addMonths(toDate('2020-01-01'), number + 1), 1), '%Y-%m-%d')) AS last_day_of_month
    FROM numbers(dateDiff('month', toDate('2020-01-01'), toDate('2050-12-31')) + 1)
    )

    and (stat_time, uin,instance_type, region_name) global not in (
        select stat_time,uin,instance_type,region_name from raw where (new_core>2000 or ret_core>2000)
    )

    -- 因为数据量太大，导致ck查询超过内存限制，因此分阶段查询

    -- 下面 WEB_CONDITION 是给版本ppl 用的，不可删除
    /*${WEB_CONDITION}*/


    group by year,month,uin,zone_name,instance_model
    )
group by region_name,year,month,gins_family

