package com.pugwoo.dboperate.模型预测调优;

import com.pugwoo.dboperate.模型预测调优.utils.RunForecastCommonUtils;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.HashMap;
import java.util.Map;

@SpringBootTest
public class RunForecast临时CBS {

    @Resource
    private RunForecastCommonUtils runForecastCommonUtils;

    // 先跑一个没有机型收敛的
    @Test
    public void testCBS境内阈值500境外阈值100() throws Exception {
        Map<String, Object> commonParam = new HashMap<>();
        commonParam.put("category", "方案918：境内阈值500境外阈值100-CBS-月切保留客户维度-地域磁盘类型-全部客户-月度ARIMAX");
        commonParam.put("serialInterval", "MONTH");
        commonParam.put("sourceType", "INDUSTRY_INNER");
        commonParam.put("billType", "ALL");
        commonParam.put("alName", "ARIMAX");
        commonParam.put("alArg", "0,1,3,0,1,6,0.2");
        commonParam.put("transInstanceType", "false");
        commonParam.put("groupDimType", "UIN_INSTANCE_MODEL_ZONE_NAME");
        commonParam.put("isRemoveSpike", "true");
        commonParam.put("forecastScope", 1);

        runForecastCommonUtils.run("2023-08-01", "2025-04-01", "custom_sql/cbs/CBS境内阈值500境外阈值100.sql", commonParam);
    }

    @Test
    public void testCBS境内阈值500境外阈值100机型收敛() throws Exception {
        Map<String, Object> commonParam = new HashMap<>();
        commonParam.put("category", "方案919：境内阈值500境外阈值100机型收敛-CBS-月切保留客户维度-地域磁盘类型-全部客户-月度ARIMAX");
        commonParam.put("serialInterval", "MONTH");
        commonParam.put("sourceType", "INDUSTRY_INNER");
        commonParam.put("billType", "ALL");
        commonParam.put("alName", "ARIMAX");
        commonParam.put("alArg", "0,1,3,0,1,6,0.2");
        commonParam.put("transInstanceType", "false");
        commonParam.put("groupDimType", "UIN_INSTANCE_MODEL_ZONE_NAME");
        commonParam.put("isRemoveSpike", "true");
        commonParam.put("forecastScope", 1);

        runForecastCommonUtils.run("2023-08-01", "2025-04-01", "custom_sql/cbs/CBS境内阈值500境外阈值100-磁盘类型收敛.sql", commonParam);
    }

    // 加一个按磁盘类型+可用区，而不是按disk uuid累计的量来剔除的方案
    @Test
    public void testCBS境内阈值500境外阈值100机型收敛2() throws Exception {
        Map<String, Object> commonParam = new HashMap<>();
        commonParam.put("category", "方案979：境内阈值500境外阈值100机型收敛-CBS-月切保留客户维度-地域磁盘类型-全部客户-月度ARIMAX");
        commonParam.put("serialInterval", "MONTH");
        commonParam.put("sourceType", "INDUSTRY_INNER");
        commonParam.put("billType", "ALL");
        commonParam.put("alName", "ARIMAX");
        commonParam.put("alArg", "0,1,3,0,1,6,0.2");
        commonParam.put("transInstanceType", "false");
        commonParam.put("groupDimType", "UIN_INSTANCE_MODEL_ZONE_NAME");
        commonParam.put("isRemoveSpike", "true");
        commonParam.put("forecastScope", 1);

        runForecastCommonUtils.run("2023-08-01", "2025-04-01", "custom_sql/cbs/CBS境内阈值500境外阈值100-磁盘类型收敛-2.sql", commonParam);
    }


    @Test
    public void testCBS境内阈值500境外阈值100机型收敛3() throws Exception {
        Map<String, Object> commonParam = new HashMap<>();
        commonParam.put("category", "方案978：境内阈值500境外阈值100机型收敛-CBS-月切保留客户维度-地域磁盘类型-全部客户-月度ARIMAX");
        commonParam.put("serialInterval", "MONTH");
        commonParam.put("sourceType", "INDUSTRY_INNER");
        commonParam.put("billType", "ALL");
        commonParam.put("alName", "ARIMAXL");
        commonParam.put("alArg", "0,1,3,0,1,6,0.2"); // 当值为-1(只有一个值)时表示自动选择参数
        commonParam.put("retAlArgs", "0,1,3,0.2"); // 退回采用0,1,3
        commonParam.put("transInstanceType", "false");
        commonParam.put("groupDimType", "UIN_INSTANCE_MODEL_ZONE_NAME");
        commonParam.put("isRemoveSpike", "true");
        commonParam.put("forecastScope", 1);

        runForecastCommonUtils.run("2023-08-01", "2025-04-01", "custom_sql/cbs/CBS境内阈值500境外阈值100-磁盘类型收敛-2.sql", commonParam);
    }

    @Test
    public void testCBS境内阈值500境外阈值100机型收敛4() throws Exception {
        Map<String, Object> commonParam = new HashMap<>();
        commonParam.put("category", "方案977：境内阈值500境外阈值100机型收敛-CBS-月切保留客户维度-地域磁盘类型-全部客户-月度ARIMAX");
        commonParam.put("serialInterval", "MONTH");
        commonParam.put("sourceType", "INDUSTRY_INNER");
        commonParam.put("billType", "ALL");
        commonParam.put("alName", "ARIMAXL");
        commonParam.put("alArg", "0,1,3,0,1,6,0.2"); // 当值为-1(只有一个值)时表示自动选择参数
        commonParam.put("retAlArgs", "0,1,3,0.2"); // 退回采用0,1,3
        commonParam.put("transInstanceType", "false");
        commonParam.put("groupDimType", "UIN_INSTANCE_MODEL_ZONE_NAME");
        commonParam.put("isRemoveSpike", "true");
        commonParam.put("forecastScope", 1);

        runForecastCommonUtils.run("2023-08-01", "2025-04-01", "custom_sql/cbs/CBS境内阈值500境外阈值100-磁盘类型收敛-3.sql", commonParam);
    }

    // 先跑一个没有机型收敛的
    @Test
    public void testCBS境内阈值500境外阈值150() throws Exception {
        Map<String, Object> commonParam = new HashMap<>();
        commonParam.put("category", "方案916：境内阈值500境外阈值150-CBS-月切保留客户维度-地域磁盘类型-全部客户-月度ARIMAX");
        commonParam.put("serialInterval", "MONTH");
        commonParam.put("sourceType", "INDUSTRY_INNER");
        commonParam.put("billType", "ALL");
        commonParam.put("alName", "ARIMAX");
        commonParam.put("alArg", "0,1,3,0,1,6,0.2");
        commonParam.put("transInstanceType", "false");
        commonParam.put("groupDimType", "UIN_INSTANCE_MODEL_ZONE_NAME");
        commonParam.put("isRemoveSpike", "true");
        commonParam.put("forecastScope", 1);

        runForecastCommonUtils.run("2023-08-01", "2025-04-01", "custom_sql/cbs/CBS境内阈值500境外阈值150.sql", commonParam);
    }


    @Test
    public void testCBS境内阈值500境外阈值150机型收敛() throws Exception {
        Map<String, Object> commonParam = new HashMap<>();
        commonParam.put("category", "方案917：境内阈值500境外阈值150机型收敛-CBS-月切保留客户维度-地域磁盘类型-全部客户-月度ARIMAX");
        commonParam.put("serialInterval", "MONTH");
        commonParam.put("sourceType", "INDUSTRY_INNER");
        commonParam.put("billType", "ALL");
        commonParam.put("alName", "ARIMAX");
        commonParam.put("alArg", "0,1,3,0,1,6,0.2");
        commonParam.put("transInstanceType", "false");
        commonParam.put("groupDimType", "UIN_INSTANCE_MODEL_ZONE_NAME");
        commonParam.put("isRemoveSpike", "true");
        commonParam.put("forecastScope", 1);

        runForecastCommonUtils.run("2023-08-01", "2025-04-01", "custom_sql/cbs/CBS境内阈值500境外阈值150-磁盘类型收敛.sql", commonParam);
    }

    @Test
    public void testCBS阈值500() throws Exception {
        Map<String, Object> commonParam = new HashMap<>();
        commonParam.put("category", "方案914：阈值500-CBS-月切保留客户维度-地域磁盘类型-全部客户-月度ARIMAX");
        commonParam.put("serialInterval", "MONTH");
        commonParam.put("sourceType", "INDUSTRY_INNER");
        commonParam.put("billType", "ALL");
        commonParam.put("alName", "ARIMAX");
        commonParam.put("alArg", "0,1,3,0,1,6,0.2");
        commonParam.put("transInstanceType", "false");
        commonParam.put("groupDimType", "UIN_INSTANCE_MODEL_ZONE_NAME");
        commonParam.put("isRemoveSpike", "true");
        commonParam.put("forecastScope", 1);

        runForecastCommonUtils.run("2023-08-01", "2025-03-01", "custom_sql/cbs/CBS阈值500.sql", commonParam);
    }


    @Test
    public void testCBS阈值1000() throws Exception {
        Map<String, Object> commonParam = new HashMap<>();
        commonParam.put("category", "方案911：阈值1000-CBS-月切保留客户维度-地域磁盘类型-全部客户-月度ARIMAX");
        commonParam.put("serialInterval", "MONTH");
        commonParam.put("sourceType", "INDUSTRY_INNER");
        commonParam.put("billType", "ALL");
        commonParam.put("alName", "ARIMAX");
        commonParam.put("alArg", "0,1,3,0,1,6,0.2");
        commonParam.put("transInstanceType", "false");
        commonParam.put("groupDimType", "UIN_INSTANCE_MODEL_ZONE_NAME");
        commonParam.put("isRemoveSpike", "true");
        commonParam.put("forecastScope", 1);

        runForecastCommonUtils.run("2023-08-01", "2025-03-01", "custom_sql/cbs/CBS阈值1000.sql", commonParam);
    }

    @Test
    public void testCBS阈值2000() throws Exception {
        Map<String, Object> commonParam = new HashMap<>();
        commonParam.put("category", "方案912：阈值2000-CBS-月切保留客户维度-地域磁盘类型-全部客户-月度ARIMAX");
        commonParam.put("serialInterval", "MONTH");
        commonParam.put("sourceType", "INDUSTRY_INNER");
        commonParam.put("billType", "ALL");
        commonParam.put("alName", "ARIMAX");
        commonParam.put("alArg", "0,1,3,0,1,6,0.2");
        commonParam.put("transInstanceType", "false");
        commonParam.put("groupDimType", "UIN_INSTANCE_MODEL_ZONE_NAME");
        commonParam.put("isRemoveSpike", "true");
        commonParam.put("forecastScope", 1);

        runForecastCommonUtils.run("2023-08-01", "2025-03-01", "custom_sql/cbs/CBS阈值2000.sql", commonParam);
    }

    @Test
    public void testCBS阈值3000() throws Exception {
        Map<String, Object> commonParam = new HashMap<>();
        commonParam.put("category", "方案913：阈值3000-CBS-月切保留客户维度-地域磁盘类型-全部客户-月度ARIMAX");
        commonParam.put("serialInterval", "MONTH");
        commonParam.put("sourceType", "INDUSTRY_INNER");
        commonParam.put("billType", "ALL");
        commonParam.put("alName", "ARIMAX");
        commonParam.put("alArg", "0,1,3,0,1,6,0.2");
        commonParam.put("transInstanceType", "false");
        commonParam.put("groupDimType", "UIN_INSTANCE_MODEL_ZONE_NAME");
        commonParam.put("isRemoveSpike", "true");
        commonParam.put("forecastScope", 1);

        runForecastCommonUtils.run("2023-08-01", "2025-03-01", "custom_sql/cbs/CBS阈值3000.sql", commonParam);
    }

    @Test
    public void testCBS无阈值() throws Exception {
        Map<String, Object> commonParam = new HashMap<>();
        commonParam.put("category", "方案915：无阈值-CBS-月切保留客户维度-地域磁盘类型-全部客户-月度ARIMAX");
        commonParam.put("serialInterval", "MONTH");
        commonParam.put("sourceType", "INDUSTRY_INNER");
        commonParam.put("billType", "ALL");
        commonParam.put("alName", "ARIMAX");
        commonParam.put("alArg", "0,1,3,0,1,6,0.2");
        commonParam.put("transInstanceType", "false");
        commonParam.put("groupDimType", "UIN_INSTANCE_MODEL_ZONE_NAME");
        commonParam.put("isRemoveSpike", "true");
        commonParam.put("forecastScope", 1);

        runForecastCommonUtils.run("2023-08-01", "2025-03-01", "custom_sql/cbs/CBS无阈值.sql", commonParam);
    }
}
