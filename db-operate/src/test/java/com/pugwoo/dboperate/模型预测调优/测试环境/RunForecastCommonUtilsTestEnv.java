package com.pugwoo.dboperate.模型预测调优.测试环境;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.dboperate.common.ReadFileUtils;
import com.pugwoo.dboperate.entity.PplForecastInputDetailDO;
import com.pugwoo.dboperate.entity.PplForecastPredictResultDO;
import com.pugwoo.dboperate.entity.PplForecastPredictTaskDO;
import com.pugwoo.dboperate.模型预测调优.utils.RunForecastResultDTO;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import com.pugwoo.wooutils.net.Browser;
import com.pugwoo.wooutils.string.StringTools;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.Vector;

@Service
public class RunForecastCommonUtilsTestEnv {

    @Autowired
    private DBHelper cloudDemandDevDBHelper;

    /**测试环境的cloud-demand-app task部署的ip地址*/
    private static final String ip = "*************";

    public List<RunForecastResultDTO> calAccuracyRate(List<Long> allTaskIds) {
        List<RunForecastResultDTO> result = new ArrayList<>();
        // 开始计算准确率
        for (int N = allTaskIds.size() - 1; N >= 2; N--) {
            // 对于N，拿到N-1月的执行量
            Long taskIdN = allTaskIds.get(N);
            PplForecastPredictTaskDO taskDO = cloudDemandDevDBHelper.getOne(PplForecastPredictTaskDO.class, "where id=?", taskIdN);
            LocalDate monthN = DateUtils.parseLocalDate(String.valueOf(taskDO.getInputDateEnd())).plusDays(1);
            LocalDate monthN_minus_1 = monthN.minusMonths(1);
            // N-1月的执行量
            List<PplForecastInputDetailDO> executeN_minus_1 = cloudDemandDevDBHelper.getAll(PplForecastInputDetailDO.class,
                    "where task_id=? and year=? and month=?", taskIdN, monthN_minus_1.getYear(), monthN_minus_1.getMonthValue());

            Long taskIdN_minus_1 = allTaskIds.get(N - 1);
            List<PplForecastPredictResultDO> predictN_minus_1 = cloudDemandDevDBHelper.getAll(PplForecastPredictResultDO.class,
                    "where task_id=? and year=? and month=?", taskIdN_minus_1,
                    monthN_minus_1.getYear(), monthN_minus_1.getMonthValue());

            Long taskIdN_minus_2 = allTaskIds.get(N - 2);
            List<PplForecastPredictResultDO> predictN_minus_2 = cloudDemandDevDBHelper.getAll(PplForecastPredictResultDO.class,
                    "where task_id=? and year=? and month=?", taskIdN_minus_2,
                    monthN_minus_1.getYear(), monthN_minus_1.getMonthValue());

            // predictN_minus_1和predictN_minus_2取平均值
            List<PplForecastPredictResultDO> predictN = ListUtils.merge(predictN_minus_1, predictN_minus_2,
                    o -> StringTools.join("@", o.getGinsFamily(), o.getRegionName(), o.getType()),
                    o -> StringTools.join("@", o.getGinsFamily(), o.getRegionName(), o.getType()),
                    (o1, o2) -> {
                        PplForecastPredictResultDO r = ListUtils.isNotEmpty(o1) ? JSON.clone(o1.getFirst()) : JSON.clone(o2.getFirst());
                        BigDecimal total = NumberUtils.sum(o1, o -> o.getCoreNum()).add(NumberUtils.sum(o2, o -> o.getCoreNum()));
                        r.setCoreNum(NumberUtils.divide(total, 2, 6));
                        return r;
                    });

            List<BigDecimal> newAccuracyRate = new Vector<>();
            BigDecimal newTotalExecute = NumberUtils.sum(ListUtils.filter(executeN_minus_1, o -> "NEW".equals(o.getType())),
                    o -> o.getDiffCoreNum());

            List<BigDecimal> retAccuracyRate = new Vector<>();
            BigDecimal retTotalExecute = NumberUtils.sum(ListUtils.filter(executeN_minus_1, o -> "RET".equals(o.getType())),
                    o -> o.getDiffCoreNum());

            ListUtils.merge(executeN_minus_1, predictN,
                    o -> StringTools.join("@", o.getGinsFamily(), o.getRegionName(), o.getType()),
                    o -> StringTools.join("@", o.getGinsFamily(), o.getRegionName(), o.getType()),
                    (execute, predict) -> {

                        if (ListUtils.isEmpty(execute)) {
                            return null; // 执行量没有，加权为0，不处理
                        }
                        if (execute.size() > 1) {
                            System.err.println("执行量条数不应该大于1条:" + JSON.toJson(execute));
                        }

                        if (ListUtils.isEmpty(predict)) {
                            return null; // 预测量为0，准确率肯定为0，不处理
                        }
                        if (predict.size() > 1) {
                            System.err.println("预测量条数不应该大于1条:" + JSON.toJson(predict));
                        }

                        BigDecimal executeCore = execute.getFirst().getDiffCoreNum();
                        if (executeCore.compareTo(BigDecimal.ZERO) <= 0) {
                            return null; // 执行量为0，准确率为0，不用加
                        }

                        BigDecimal predictCore = predict.getFirst().getCoreNum();
                        BigDecimal accuracyRate =
                                NumberUtils.divide(NumberUtils.min(executeCore, predictCore),
                                        NumberUtils.max(executeCore, predictCore), 8).multiply(BigDecimal.valueOf(100));

                        if ("NEW".equals(execute.getFirst().getType())) {
                            BigDecimal weightedAccuracyRate =
                                    NumberUtils.divide(executeCore, newTotalExecute, 6).multiply(accuracyRate);
                            newAccuracyRate.add(weightedAccuracyRate);
                        } else {
                            BigDecimal weightedAccuracyRate =
                                    NumberUtils.divide(executeCore, retTotalExecute, 6).multiply(accuracyRate);
                            retAccuracyRate.add(weightedAccuracyRate);
                        }

                        return null; // 不需要返回
                    });

            BigDecimal newAccuracyRateSum = NumberUtils.sum(newAccuracyRate);
            BigDecimal retAccuracyRateSum = NumberUtils.sum(retAccuracyRate);

            System.out.println(monthN_minus_1 + " 新增准确率:" + newAccuracyRateSum + "%, 退回准确率" + retAccuracyRateSum + "%");

            RunForecastResultDTO dto = new RunForecastResultDTO();
            dto.setPredictMonth(monthN_minus_1);
            dto.setNewAccuracyRatePercent(newAccuracyRateSum);
            dto.setRetAccuracyRatePercent(retAccuracyRateSum);
            result.add(dto);
        }

        return result;
    }

    public List<RunForecastResultDTO> run(String startDate, String endDate, String customSqlFile, Map<String, Object> commonParam) throws Exception {
        List<Long> allTaskIds = new ArrayList<>();

        while (startDate.compareTo(endDate) <= 0) {
            Map<String, Object> param = JSON.clone(commonParam);
            param.put("start", startDate);
            param.put("end", startDate);

            Long maxPredictTaskId = cloudDemandDevDBHelper.getRawOne(Long.class, "select max(id) from ppl_forecast_predict_task");

            Date startTime = new Date();

            String customeSql = "";
            if (StringTools.isNotBlank(customSqlFile)) {
                customeSql = ReadFileUtils.read(customSqlFile);
            }

            try {
                Browser browser = new Browser();
                browser.setReadTimeoutSeconds(3600);
                ByteArrayOutputStream out = new ByteArrayOutputStream();
                browser.postAsync("http://" + ip + "/cloud-demand-app/ops/createMonthForecastForWhole?"
                        + toQueryString(param), customeSql.getBytes(), out);
            } catch (Exception e) {
                continue; // 继续尝试
            }

            // 这里就不等前端返回了，直接查数据库等待
            boolean needRetry = false;
            while (true) {
                WhereSQL whereSQL = new WhereSQL();
                whereSQL.and("id>?", maxPredictTaskId);
                whereSQL.and("category=?", commonParam.get("category"));

                LocalDate tmp = DateUtils.parseLocalDate(startDate);
                tmp = tmp.minusDays(1);

                whereSQL.and("input_date_end=?", DateUtils.formatDate(tmp));
                // whereSQL.and("status=?", "DONE"); // 这里不看状态，只要提上来就可以了

                Long taskId = cloudDemandDevDBHelper.getRawOne(Long.class, "select id from ppl_forecast_predict_task where 1=1 "
                        + whereSQL.getSQLForWhereAppend(), whereSQL.getParams());
                if (taskId != null) {
                    System.out.println("predictMonth:" + startDate + ",taskId:" + taskId);
                    allTaskIds.add(taskId);
                    break;
                } else {
                    // 等待如果超过10分钟没有提交上来，则重试
                    if (System.currentTimeMillis() - startTime.getTime() > 10 * 60 * 1000) {
                        needRetry = true;
                        break;
                    }
                }

                try {
                    Thread.sleep(10000);
                } catch (Exception ignored) {
                }
            }

            if (needRetry) {
                continue;
            }

            // 如果当前提交上来的任务中，处于SUBMITTED状态的个数超过2个，那么就等待，先不提交了
            Long submittedStatusCount = 0L;
            do {
                submittedStatusCount = cloudDemandDevDBHelper.getRawOne(Long.class,
                        "select count(*) from ppl_forecast_predict_task where status='SUBMITTED' and id in (?)",
                        allTaskIds);
                try {
                    Thread.sleep(10000);
                } catch (Exception ignored) {
                }
            } while (submittedStatusCount > 2);

            LocalDate startD = DateUtils.parseLocalDate(startDate);
            startD = startD.plusMonths(1);
            startDate = DateUtils.formatDate(startD);
        }

        // 等待所有的task全部完成
        while (true) {
            boolean allDone = true;
            for (Long taskId : allTaskIds) {
                String status = cloudDemandDevDBHelper.getRawOne(String.class, "select status from ppl_forecast_predict_task where id=?", taskId);
                if (!"DONE".equals(status)) {
                    allDone = false;
                    break;
                }
            }

            if (allDone) {
                break;
            } else {
                Thread.sleep(30000); // 等待30秒再检查
            }
        }

        System.out.println("all task ids is done:" + JSON.toJson(allTaskIds));
        return calAccuracyRate(allTaskIds);
    }

    private static String toQueryString(Map<String, Object> param) {
        StringBuilder queryString = new StringBuilder();
        Set<Map.Entry<String, Object>> entrySet = param.entrySet();

        for (Map.Entry<String, Object> entry : entrySet) {
            String key = entry.getKey();
            Object value = entry.getValue();
            if (value != null) {
                String encodedValue = URLEncoder.encode(value.toString(), StandardCharsets.UTF_8);
                if (!queryString.isEmpty()) {
                    queryString.append("&");
                }
                queryString.append(key).append("=").append(encodedValue);
            }
        }

        return queryString.toString();
    }

}
