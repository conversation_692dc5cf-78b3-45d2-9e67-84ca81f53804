with raw as (
    select stat_time, uin, zone_name,
           sum(case when change_service_core_from_last_month>0 then change_service_core_from_last_month else 0 end) as new_core,
           sum(case when change_service_core_from_last_month<0 then -change_service_core_from_last_month else 0 end) as ret_core
    from std_crp.dwd_txy_es_scale_df
    where  stat_time in (
        SELECT toDate(formatDateTime(subtractDays(addMonths(toDate('2021-01-01'), number + 1), 1), '%Y-%m-%d')) AS last_day_of_month
        FROM numbers(dateDiff('month', toDate('2021-01-01'), toDate('2050-12-31')) + 1)
    )
    group by stat_time,uin,zone_name
)

select year,month,
    any(month_start_date1) as first_day,
    any(month_end_date1) as last_day,
    any(customhouse_title1) as customhouse_title,
    any(region1) as region,
    region_name1 as region_name,
    '统一机型' as gins_family,
    sum(case when diff_bill_num>0 then diff_bill_num else 0 end) as new_diff, -- 增量：max-月初，>0部分
    sum(case when diff_bill_num<0 then -diff_bill_num else 0 end) as ret_diff, --   退回：max-月末，>0部分
    sum(cur_core) as last_day_num -- 当前：月末
from (
    select year,month,
    any(month_start_date) as month_start_date1,
    any(month_end_date) as month_end_date1,
    any(customhouse_title) as customhouse_title1,
    zone_name,
    any(region) as region1,
    any(region_name) as region_name1,
    sum(case when stat_time=month_end_date then change_service_core_from_last_month else 0 end) as diff_bill_num,
    sum(case when stat_time=month_end_date then cur_service_core else 0 end) as cur_core
    from std_crp.dwd_txy_es_scale_df
    where stat_time < :predictDate

    -- performance optimize
    and stat_time in (
    SELECT toDate(formatDateTime(subtractDays(addMonths(toDate('2020-01-01'), number + 1), 1), '%Y-%m-%d')) AS last_day_of_month
    FROM numbers(dateDiff('month', toDate('2020-01-01'), toDate('2050-12-31')) + 1)
    )

    and (stat_time, uin, zone_name) global not in (
    select stat_time,uin,zone_name from raw where (new_core>2000 or ret_core>2000)
    )

    group by year,month,uin,zone_name
    )
group by region_name,year,month

