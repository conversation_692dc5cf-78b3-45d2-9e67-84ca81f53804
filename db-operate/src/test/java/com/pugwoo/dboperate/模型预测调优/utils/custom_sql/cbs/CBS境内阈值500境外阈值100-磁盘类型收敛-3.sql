with raw as (
    select stat_time, (case when customer_short_name='(空值)' then concat('uin:',toString(uin)) else customer_short_name end) as customer,
           disk_volume_type_name, zone_name,
           any(customhouse_title) as customhouse_title,
           sum(change_service_disk_from_last_month)/1024 as net_core
    from std_crp.dwd_txy_cbs_scale_df
    where stat_time in (
        SELECT toDate(formatDateTime(subtractDays(addMonths(toDate('2020-01-01'), number + 1), 1), '%Y-%m-%d')) AS last_day_of_month
        FROM numbers(dateDiff('month', toDate('2020-01-01'), toDate('2050-12-31')) + 1)
    )
    group by stat_time,customer,disk_volume_type_name,zone_name
),
    raw2 as (

select year, month, any (month_start_date1) as first_day, any (month_end_date1) as last_day, any (customhouse_title1) as customhouse_title, any (region1) as region, region_name1 as region_name, disk_volume_type_name as gins_family, sum (case when diff_bill_num>0 then diff_bill_num else 0 end) as new_diff, -- 增量：max-月初，>0部分
    sum (case when diff_bill_num<0 then -diff_bill_num else 0 end) as ret_diff,                                                                                                                                                                                                                               --   退回：max-月末，>0部分
    sum (cur_core) as last_day_num                                                                                                                                                                                                                                                                            -- 当前：月末
from (
    select year, month, any (month_start_date) as month_start_date1, any (month_end_date) as month_end_date1, any (customhouse_title) as customhouse_title1, any (region) as region1, any (region_name) as region_name1, disk_volume_type_name, sum (case when stat_time=month_end_date then change_service_disk_from_last_month else 0 end) as diff_bill_num, sum (case when stat_time=month_end_date then cur_service_disk else 0 end) as cur_core
    from std_crp.dwd_txy_cbs_scale_df
    where stat_time < :predictDate

    -- performance optimize
    and stat_time in (
    SELECT toDate(formatDateTime(subtractDays(addMonths(toDate('2020-01-01'), number + 1), 1), '%Y-%m-%d')) AS last_day_of_month
    FROM numbers(dateDiff('month', toDate('2020-01-01'), toDate('2050-12-31')) + 1)
    )
    and (stat_time, (case when customer_short_name='(空值)' then concat('uin:',toString(uin)) else customer_short_name end),
        disk_volume_type_name, zone_name) global not in (
    select stat_time, customer, disk_volume_type_name, zone_name from raw
    where ((customhouse_title='境内' and (abs(net_core)>500))
    or (customhouse_title='境外' and (abs(net_core)>100)))
    )
    group by year, month, uin, zone_name, disk_volume_type_name
    )
group by region_name, year, month, gins_family
having not (year =2021
   and month =1)
    )

select year,month,first_day,last_day,
    customhouse_title, region, region_name,
    (case when gins_family in ('SSD云硬盘','增强型云硬盘','极速型云硬盘','通用型SSD云硬盘','高IO云硬盘') then 'SSD'
    when gins_family in ('高性能云硬盘','普通云硬盘') then '高效'
    else '其他' end) as gins_family,
    sum(new_diff) as new_diff, sum(ret_diff) as ret_diff, sum(last_day_num) as last_day_num
from raw2
group by year,month,first_day,last_day,customhouse_title, region, region_name,gins_family
