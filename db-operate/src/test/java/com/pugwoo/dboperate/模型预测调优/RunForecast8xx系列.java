package com.pugwoo.dboperate.模型预测调优;

import com.pugwoo.dboperate.模型预测调优.utils.RunForecastCommonUtils;
import com.pugwoo.dboperate.模型预测调优.utils.RunForecastResultDTO;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@SpringBootTest
public class RunForecast8xx系列 {

    @Resource
    private RunForecastCommonUtils runForecastCommonUtils;

    @Test
    public void test方案881_一部的头部加毛刺() throws Exception {
        Map<String, Object> commonParam = new HashMap<>();
        commonParam.put("category", "方案881：大客户加毛刺(一部)-月切保留客户维度(机型规格)-机型收敛-包弹-外部客户-月度ARIMAX");
        commonParam.put("serialInterval", "MONTH");
        commonParam.put("sourceType", "INDUSTRY");
        commonParam.put("billType", "ALL");
        commonParam.put("alName", "ARIMAX");
        commonParam.put("alArg", "0,1,3,0,1,6,0.2");
        commonParam.put("transInstanceType", "true");
        commonParam.put("groupDimType", "UIN_INSTANCE_MODEL_ZONE_NAME");
        commonParam.put("isRemoveSpike", "true");
        commonParam.put("forecastScope", 1);

        runForecastCommonUtils.run("2023-08-01", "2025-02-01", "custom_sql/方案881.sql", commonParam);
    }

    @Test
    public void test方案882_一部的头部加毛刺剔除指定uin() throws Exception {
        Map<String, Object> commonParam = new HashMap<>();
        commonParam.put("category", "方案882：大客户加毛刺(一部)剔除指定uin-月切保留客户维度(机型规格)-机型收敛-包弹-外部客户-月度ARIMAX");
        commonParam.put("serialInterval", "MONTH");
        commonParam.put("sourceType", "INDUSTRY");
        commonParam.put("billType", "ALL");
        commonParam.put("alName", "ARIMAX");
        commonParam.put("alArg", "0,1,3,0,1,6,0.2");
        commonParam.put("transInstanceType", "true");
        commonParam.put("groupDimType", "UIN_INSTANCE_MODEL_ZONE_NAME");
        commonParam.put("isRemoveSpike", "true");
        commonParam.put("forecastScope", 1);

        runForecastCommonUtils.run("2023-08-01", "2025-02-01", "custom_sql/方案882剔除指定uin.sql", commonParam);
    }


    @Test
    public void test头部加毛刺() throws Exception {
        Map<String, Object> commonParam = new HashMap<>();
        commonParam.put("category", "方案855：大客户加毛刺-月切保留客户维度(机型规格)-机型收敛-包弹-外部客户-月度ARIMAX");
        commonParam.put("serialInterval", "MONTH");
        commonParam.put("sourceType", "INDUSTRY");
        commonParam.put("billType", "ALL");
        commonParam.put("alName", "ARIMAX");
        commonParam.put("alArg", "0,1,3,0,1,6,0.2");
        commonParam.put("transInstanceType", "true");
        commonParam.put("groupDimType", "UIN_INSTANCE_MODEL_ZONE_NAME");
        commonParam.put("isRemoveSpike", "true");
        commonParam.put("forecastScope", 3);
        commonParam.put("extraZones", "上海自动驾驶云一区"); // ,分开

        runForecastCommonUtils.run("2023-01-01", "2024-08-01", "", commonParam);

        commonParam = new HashMap<>();
        commonParam.put("category", "方案865：大客户加毛刺-月切保留客户维度(机型规格)-机型收敛-包弹-内部客户-月度ARIMAX");
        commonParam.put("serialInterval", "MONTH");
        commonParam.put("sourceType", "INNER");
        commonParam.put("billType", "ALL");
        commonParam.put("alName", "ARIMAX");
        commonParam.put("alArg", "0,1,3,0,1,6,0.2");
        commonParam.put("transInstanceType", "true");
        commonParam.put("groupDimType", "UIN_INSTANCE_MODEL_ZONE_NAME");
        commonParam.put("isRemoveSpike", "true");
        commonParam.put("forecastScope", 3);
        commonParam.put("extraZones", "上海自动驾驶云一区"); // ,分开

        runForecastCommonUtils.run("2023-01-01", "2024-08-01", "", commonParam);

        commonParam = new HashMap<>();
        commonParam.put("category", "方案875：大客户加毛刺-月切保留客户维度(机型规格)-机型收敛-包弹-内外部客户-月度ARIMAX");
        commonParam.put("serialInterval", "MONTH");
        commonParam.put("sourceType", "INDUSTRY_INNER");
        commonParam.put("billType", "ALL");
        commonParam.put("alName", "ARIMAX");
        commonParam.put("alArg", "0,1,3,0,1,6,0.2");
        commonParam.put("transInstanceType", "true");
        commonParam.put("groupDimType", "UIN_INSTANCE_MODEL_ZONE_NAME");
        commonParam.put("isRemoveSpike", "true");
        commonParam.put("forecastScope", 3);
        commonParam.put("extraZones", "上海自动驾驶云一区"); // ,分开

        runForecastCommonUtils.run("2023-01-01", "2024-08-01", "", commonParam);
    }

    /**
     * 傻逼大强要跑的模型
     */
    @Test
    public void test自研和云合并() throws Exception {
        Map<String, Object> commonParam = new HashMap<>();
        commonParam.put("category", "方案850：云和自研合并-月切保留客户维度(机型规格)-机型收敛-包弹-内外部客户-月度ARIMAX");
        commonParam.put("serialInterval", "MONTH");
        commonParam.put("sourceType", "INDUSTRY");
        commonParam.put("billType", "ALL");
        commonParam.put("alName", "ARIMAX");
        commonParam.put("alArg", "0,1,3,0,1,6,0.2");
        commonParam.put("transInstanceType", "true");
        commonParam.put("groupDimType", "UIN_INSTANCE_MODEL_ZONE_NAME");
        commonParam.put("isRemoveSpike", "true");
        commonParam.put("forecastScope", 1);

        runForecastCommonUtils.run("2022-08-01", "2024-05-01", "custom_sql/云和自研合并.sql", commonParam);
    }

    /**
     * all task ids is done:[6625,6626,6627,6628,6629,6630,6631,6632,6633,6634,6635,6636,6637,6638,6639,6640,6641,6642,6643,6644,6645,6646,6647,6648,6649]
     * 2024-09-01 新增准确率:99.53053700000000%, 退回准确率88.87615300000000%
     * 2024-08-01 新增准确率:95.48182700000000%, 退回准确率98.35463600000000%
     * 2024-07-01 新增准确率:94.31964100000000%, 退回准确率94.05712900000000%
     * 2024-06-01 新增准确率:93.74464200000000%, 退回准确率78.27081500000000%
     * 2024-05-01 新增准确率:85.08659600000000%, 退回准确率85.86819000000000%
     * 2024-04-01 新增准确率:81.77930300000000%, 退回准确率97.58675000000000%
     * 2024-03-01 新增准确率:93.80815100000000%, 退回准确率98.24897100000000%
     * 2024-02-01 新增准确率:68.29747700000000%, 退回准确率78.40381000000000%
     * 2024-01-01 新增准确率:94.77170500000000%, 退回准确率99.69093900000000%
     * 2023-12-01 新增准确率:97.07535900000000%, 退回准确率90.39579100000000%
     * 2023-11-01 新增准确率:91.59627900000000%, 退回准确率85.14523900000000%
     * 2023-10-01 新增准确率:92.77418000000000%, 退回准确率87.37191000000000%
     * 2023-09-01 新增准确率:97.46077100000000%, 退回准确率99.56998000000000%
     * 2023-08-01 新增准确率:97.02123600000000%, 退回准确率99.74259500000000%
     * 2023-07-01 新增准确率:97.90283200000000%, 退回准确率92.46870500000000%
     * 2023-06-01 新增准确率:97.98232600000000%, 退回准确率80.97093300000000%
     * 2023-05-01 新增准确率:89.84810200000000%, 退回准确率97.27201100000000%
     * 2023-04-01 新增准确率:98.32076500000000%, 退回准确率84.27944100000000%
     * 2023-03-01 新增准确率:81.40529700000000%, 退回准确率88.20953200000000%
     * 2023-02-01 新增准确率:86.22497700000000%, 退回准确率96.29472200000000%
     * 2023-01-01 新增准确率:70.94131600000000%, 退回准确率73.70262900000000%
     * 2022-12-01 新增准确率:85.36969600000000%, 退回准确率89.84813300000000%
     * 2022-11-01 新增准确率:93.51231800000000%, 退回准确率97.26497800000000%
     * 2023年平均准确率，新增:91.546120%, 退回:89.618624%
     * @throws Exception
     */
    @Test
    public void test大包() throws Exception {
        Map<String, Object> commonParam = new HashMap<>();
        commonParam.put("category", "方案851：主力机型主力地域-不分机型不分地域-剔除毛刺-月度ARIMAX");
        commonParam.put("serialInterval", "MONTH");
        commonParam.put("sourceType", "INDUSTRY");
        commonParam.put("billType", "ALL");
        commonParam.put("alName", "ARIMAXL");
        commonParam.put("alArg", "0,1,3,0,1,6,0.2"); // 当值为-1(只有一个值)时表示自动选择参数
        commonParam.put("retAlArgs", "0,1,3,0.2"); // 退回采用0,1,3
        commonParam.put("transInstanceType", "true");
        commonParam.put("groupDimType", "UIN_INSTANCE_MODEL_ZONE_NAME");
        commonParam.put("isRemoveSpike", "true");
        commonParam.put("forecastScope", 1);

        LocalDate curMonth = DateUtils.getFirstDayOfMonth(new Date());
        List<RunForecastResultDTO> result = runForecastCommonUtils.run("2022-10-01", curMonth.toString(),
                "custom_sql/主力园区主力机型不分机型地域.sql", commonParam);

        // 算一下2023年的准确率
        BigDecimal newAccuracyRate2023 = NumberUtils.avg(
                ListUtils.filter(result, o -> o.getPredictMonth().getYear() == 2023), 6,
                o -> o.getNewAccuracyRatePercent());
        BigDecimal retAccuracyRate2023 = NumberUtils.avg(
                ListUtils.filter(result, o -> o.getPredictMonth().getYear() == 2023), 6,
                o -> o.getRetAccuracyRatePercent());
        System.out.println("2023年平均准确率，新增:" + newAccuracyRate2023 + "%, 退回:" + retAccuracyRate2023 + "%");
    }


    /**
     * 这个是强哥要跑的
     */
    @Test
    public void test31个头部客户不含毛刺() throws Exception {
        Map<String, Object> commonParam = new HashMap<>();
        commonParam.put("category", "方案805：仅31个大客户不含毛刺-月切保留客户维度(机型规格)-机型收敛-包弹-外部客户-月度ARIMAX");
        commonParam.put("serialInterval", "MONTH");
        commonParam.put("sourceType", "INDUSTRY");
        commonParam.put("billType", "ALL");
        commonParam.put("alName", "ARIMAX");
        commonParam.put("alArg", "0,1,3,0,1,6,0.2");
        commonParam.put("transInstanceType", "true");
        commonParam.put("groupDimType", "UIN_INSTANCE_MODEL_ZONE_NAME");
        commonParam.put("isRemoveSpike", "false");
        commonParam.put("forecastScope", 2);

        runForecastCommonUtils.run("2022-06-01", "2024-06-01", "", commonParam);

        commonParam = new HashMap<>();
        commonParam.put("category", "方案815：仅31个大客户不含毛刺-月切保留客户维度(机型规格)-机型收敛-包弹-内部客户-月度ARIMAX");
        commonParam.put("serialInterval", "MONTH");
        commonParam.put("sourceType", "INNER");
        commonParam.put("billType", "ALL");
        commonParam.put("alName", "ARIMAX");
        commonParam.put("alArg", "0,1,3,0,1,6,0.2");
        commonParam.put("transInstanceType", "true");
        commonParam.put("groupDimType", "UIN_INSTANCE_MODEL_ZONE_NAME");
        commonParam.put("isRemoveSpike", "false");
        commonParam.put("forecastScope", 2);

        runForecastCommonUtils.run("2022-06-01", "2024-06-01", "", commonParam);

        commonParam = new HashMap<>();
        commonParam.put("category", "方案825：仅31个大客户不含毛刺-月切保留客户维度(机型规格)-机型收敛-包弹-内外部客户-月度ARIMAX");
        commonParam.put("serialInterval", "MONTH");
        commonParam.put("sourceType", "INDUSTRY_INNER");
        commonParam.put("billType", "ALL");
        commonParam.put("alName", "ARIMAX");
        commonParam.put("alArg", "0,1,3,0,1,6,0.2");
        commonParam.put("transInstanceType", "true");
        commonParam.put("groupDimType", "UIN_INSTANCE_MODEL_ZONE_NAME");
        commonParam.put("isRemoveSpike", "false");
        commonParam.put("forecastScope", 2);

        runForecastCommonUtils.run("2022-06-01", "2024-06-01", "", commonParam);
    }

}
