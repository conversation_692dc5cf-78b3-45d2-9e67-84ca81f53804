package com.pugwoo.dboperate.模型预测调优;

import com.pugwoo.dboperate.模型预测调优.utils.RunForecastCommonUtils;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.HashMap;
import java.util.Map;

@SpringBootTest
public class RunForecast临时CRS {

    @Resource
    private RunForecastCommonUtils runForecastCommonUtils;

    @Test
    public void testCRS阈值1000() throws Exception {
        Map<String, Object> commonParam = new HashMap<>();
        commonParam.put("category", "方案981：阈值1000-CRS-月切保留客户维度-地域磁盘类型-全部客户-月度ARIMAX");
        commonParam.put("serialInterval", "MONTH");
        commonParam.put("sourceType", "INDUSTRY_INNER");
        commonParam.put("billType", "ALL");
        commonParam.put("alName", "ARIMAX");
        commonParam.put("alArg", "0,1,3,0,1,6,0.2");
        commonParam.put("transInstanceType", "false");
        commonParam.put("groupDimType", "UIN_INSTANCE_MODEL_ZONE_NAME");
        commonParam.put("isRemoveSpike", "true");
        commonParam.put("forecastScope", 1);

        runForecastCommonUtils.run("2023-08-01", "2025-05-01", "custom_sql/crs/CRS阈值1000.sql", commonParam);
    }

    @Test
    public void testCRS阈值2000() throws Exception {
        Map<String, Object> commonParam = new HashMap<>();
        commonParam.put("category", "方案982：阈值2000-CRS-月切保留客户维度-地域磁盘类型-全部客户-月度ARIMAX");
        commonParam.put("serialInterval", "MONTH");
        commonParam.put("sourceType", "INDUSTRY_INNER");
        commonParam.put("billType", "ALL");
        commonParam.put("alName", "ARIMAX");
        commonParam.put("alArg", "0,1,3,0,1,6,0.2");
        commonParam.put("transInstanceType", "false");
        commonParam.put("groupDimType", "UIN_INSTANCE_MODEL_ZONE_NAME");
        commonParam.put("isRemoveSpike", "true");
        commonParam.put("forecastScope", 1);

        runForecastCommonUtils.run("2023-08-01", "2025-05-01", "custom_sql/crs/CRS阈值2000.sql", commonParam);
    }

    @Test
    public void testCRS阈值5000() throws Exception {
        Map<String, Object> commonParam = new HashMap<>();
        commonParam.put("category", "方案983：阈值5000-CRS-月切保留客户维度-地域磁盘类型-全部客户-月度ARIMAX");
        commonParam.put("serialInterval", "MONTH");
        commonParam.put("sourceType", "INDUSTRY_INNER");
        commonParam.put("billType", "ALL");
        commonParam.put("alName", "ARIMAX");
        commonParam.put("alArg", "0,1,3,0,1,6,0.2");

