package com.pugwoo.dboperate.模型预测调优;

import com.pugwoo.dboperate.模型预测调优.utils.RunForecastCommonUtils;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.HashMap;
import java.util.Map;

@SpringBootTest
public class RunForecast临时EKS {

    @Resource
    private RunForecastCommonUtils runForecastCommonUtils;

    @Test
    public void testEKS全部() throws Exception {
        Map<String, Object> commonParam = new HashMap<>();
        commonParam.put("category", "方案905：全部-EKS-月切保留客户维度(机型规格)-机型收敛-包弹-全部客户-月度ARIMAX");
        commonParam.put("serialInterval", "MONTH");
        commonParam.put("sourceType", "INDUSTRY");
        commonParam.put("billType", "ALL");
        commonParam.put("alName", "ARIMAX");
        commonParam.put("alArg", "0,1,3,0,1,6,0.2");
        commonParam.put("transInstanceType", "true");
        commonParam.put("groupDimType", "UIN_INSTANCE_MODEL_ZONE_NAME");
        commonParam.put("isRemoveSpike", "true");
        commonParam.put("forecastScope", 1);

        runForecastCommonUtils.run("2025-04-01", "2025-05-01", "custom_sql/eks/EKS无阈值.sql", commonParam);
    }

    @Test
    public void testEKS阈值5000() throws Exception {
        Map<String, Object> commonParam = new HashMap<>();
        commonParam.put("category", "方案906：阈值5000-EKS-月切保留客户维度(机型规格)-机型收敛-包弹-全部客户-月度ARIMAX");
        commonParam.put("serialInterval", "MONTH");
        commonParam.put("sourceType", "INDUSTRY");
        commonParam.put("billType", "ALL");
        commonParam.put("alName", "ARIMAX");
        commonParam.put("alArg", "0,1,3,0,1,6,0.2");
        commonParam.put("transInstanceType", "true");
        commonParam.put("groupDimType", "UIN_INSTANCE_MODEL_ZONE_NAME");
        commonParam.put("isRemoveSpike", "true");
        commonParam.put("forecastScope", 1);

        runForecastCommonUtils.run("2023-08-01", "2025-03-01", "custom_sql/eks/EKS阈值5000.sql", commonParam);
    }

    @Test
    public void testEKS阈值3000() throws Exception {
        Map<String, Object> commonParam = new HashMap<>();
        commonParam.put("category", "方案907：阈值3000-EKS-月切保留客户维度(机型规格)-机型收敛-包弹-全部客户-月度ARIMAX");
        commonParam.put("serialInterval", "MONTH");
        commonParam.put("sourceType", "INDUSTRY");
        commonParam.put("billType", "ALL");
        commonParam.put("alName", "ARIMAX");
        commonParam.put("alArg", "0,1,3,0,1,6,0.2");
        commonParam.put("transInstanceType", "true");
        commonParam.put("groupDimType", "UIN_INSTANCE_MODEL_ZONE_NAME");
        commonParam.put("isRemoveSpike", "true");
        commonParam.put("forecastScope", 1);

        runForecastCommonUtils.run("2023-08-01", "2025-03-01", "custom_sql/eks/EKS阈值3000.sql", commonParam);
    }

    @Test
    public void testEKS阈值2000() throws Exception {
        Map<String, Object> commonParam = new HashMap<>();
        commonParam.put("category", "方案908：阈值2000-EKS-月切保留客户维度(机型规格)-机型收敛-包弹-全部客户-月度ARIMAX");
        commonParam.put("serialInterval", "MONTH");
        commonParam.put("sourceType", "INDUSTRY");
        commonParam.put("billType", "ALL");
        commonParam.put("alName", "ARIMAX");
        commonParam.put("alArg", "0,1,3,0,1,6,0.2");
        commonParam.put("transInstanceType", "true");
        commonParam.put("groupDimType", "UIN_INSTANCE_MODEL_ZONE_NAME");
        commonParam.put("isRemoveSpike", "true");
        commonParam.put("forecastScope", 1);

        runForecastCommonUtils.run("2023-08-01", "2025-03-01", "custom_sql/eks/EKS阈值2000.sql", commonParam);
    }

    @Test
    public void testEKS阈值1000() throws Exception {
        Map<String, Object> commonParam = new HashMap<>();
        commonParam.put("category", "方案909：阈值1000-EKS-月切保留客户维度(机型规格)-机型收敛-包弹-全部客户-月度ARIMAX");
        commonParam.put("serialInterval", "MONTH");
        commonParam.put("sourceType", "INDUSTRY");
        commonParam.put("billType", "ALL");
        commonParam.put("alName", "ARIMAX");
        commonParam.put("alArg", "0,1,3,0,1,6,0.2");
        commonParam.put("transInstanceType", "true");
        commonParam.put("groupDimType", "UIN_INSTANCE_MODEL_ZONE_NAME");
        commonParam.put("isRemoveSpike", "true");
        commonParam.put("forecastScope", 1);

        runForecastCommonUtils.run("2025-04-01", "2025-05-01", "custom_sql/eks/EKS阈值1000.sql", commonParam);
    }
}
