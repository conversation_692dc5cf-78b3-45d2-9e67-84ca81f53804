select year,month,
    any(month_start_date1) as first_day,
    any(month_end_date1) as last_day,
    any(customhouse_title1) as customhouse_title,
    any(region1) as region,
    region_name1 as region_name,
    instance_type1 as gins_family,
    sum(case when diff_bill_num>0 then diff_bill_num else 0 end) as new_diff, -- 增量：max-月初，>0部分
    sum(case when diff_bill_num<0 then -diff_bill_num else 0 end) as ret_diff, --   退回：max-月末，>0部分
    sum(cur_core) as last_day_num -- 当前：月末
from (
    select year,month,
    any(month_start_date) as month_start_date1,
    any(month_end_date) as month_end_date1,
    any(customhouse_title) as customhouse_title1,
    any(region) as region1,
    any(region_name) as region_name1,
    any(instance_type) as instance_type1,
    sum(case when stat_time=month_end_date then change_bill_service_core_from_last_month else 0 end) as diff_bill_num,
    sum(case when stat_time=month_end_date then cur_bill_service_core else 0 end) as cur_core
    from dwd_txy_scale_df
    where 1=1

    and cpu_or_gpu = 'CPU' and biz_type = 'cvm'
    -- 这个含了LH和渲染机型，实验用

    and app_id not in (1258344706, 1251316161) -- 内部的2个appid，王丽丽给的，目前固定的，排除掉的

    and stat_time < :predictDate

    and stat_time >= subtractMonths(toDate(:predictDate),18) -- 因为数据内存超了，所以只需要最近1.5年的数据即可

    -- performance optimize
    and stat_time in (
    SELECT toDate(formatDateTime(subtractDays(addMonths(toDate('2020-01-01'), number + 1), 1), '%Y-%m-%d')) AS last_day_of_month
    FROM numbers(dateDiff('month', toDate('2020-01-01'), toDate('2050-12-31')) + 1)
    )

    group by year,month,uin,zone_name,instance_model
    )
group by region_name,year,month,gins_family