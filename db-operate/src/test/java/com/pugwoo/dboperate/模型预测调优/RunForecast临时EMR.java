package com.pugwoo.dboperate.模型预测调优;

import com.pugwoo.dboperate.模型预测调优.utils.RunForecastCommonUtils;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.HashMap;
import java.util.Map;

@SpringBootTest
public class RunForecast临时EMR {

    @Resource
    private RunForecastCommonUtils runForecastCommonUtils;

    @Test
    public void testEMR全部() throws Exception {
        Map<String, Object> commonParam = new HashMap<>();
        commonParam.put("category", "方案901：重点加常规-EMR-月切保留客户维度(机型规格)-机型收敛-包弹-全部客户-月度ARIMAX");
        commonParam.put("serialInterval", "MONTH");
        commonParam.put("sourceType", "INDUSTRY");
        commonParam.put("billType", "ALL");
        commonParam.put("alName", "ARIMAX");
        commonParam.put("alArg", "0,1,3,0,1,6,0.2");
        commonParam.put("transInstanceType", "true");
        commonParam.put("groupDimType", "UIN_INSTANCE_MODEL_ZONE_NAME");
        commonParam.put("isRemoveSpike", "true");
        commonParam.put("forecastScope", 1);

        runForecastCommonUtils.run("2023-08-01", "2025-02-01", "custom_sql/emr/EMR单独跑-方案901-全量不设阈值.sql", commonParam);
    }

    @Test
    public void testEMR阈值1000() throws Exception {
        Map<String, Object> commonParam = new HashMap<>();
        commonParam.put("category", "方案902：阈值1000-EMR-月切保留客户维度(机型规格)-机型收敛-包弹-全部客户-月度ARIMAX");
        commonParam.put("serialInterval", "MONTH");
        commonParam.put("sourceType", "INDUSTRY");
        commonParam.put("billType", "ALL");
        commonParam.put("alName", "ARIMAX");
        commonParam.put("alArg", "0,1,3,0,1,6,0.2");
        commonParam.put("transInstanceType", "true");
        commonParam.put("groupDimType", "UIN_INSTANCE_MODEL_ZONE_NAME");
        commonParam.put("isRemoveSpike", "true");
        commonParam.put("forecastScope", 1);

        runForecastCommonUtils.run("2023-08-01", "2025-02-01", "custom_sql/emr/EMR单独跑-方案902-阈值1000.sql", commonParam);
    }

    @Test
    public void testEMR阈值2000() throws Exception {
        Map<String, Object> commonParam = new HashMap<>();
        commonParam.put("category", "方案903：阈值2000-EMR-月切保留客户维度(机型规格)-机型收敛-包弹-全部客户-月度ARIMAX");
        commonParam.put("serialInterval", "MONTH");
        commonParam.put("sourceType", "INDUSTRY");
        commonParam.put("billType", "ALL");
        commonParam.put("alName", "ARIMAX");
        commonParam.put("alArg", "0,1,3,0,1,6,0.2");
        commonParam.put("transInstanceType", "true");
        commonParam.put("groupDimType", "UIN_INSTANCE_MODEL_ZONE_NAME");
        commonParam.put("isRemoveSpike", "true");
        commonParam.put("forecastScope", 1);

        runForecastCommonUtils.run("2023-08-01", "2025-02-01", "custom_sql/emr/EMR单独跑-方案903-阈值2000.sql", commonParam);
    }

    @Test
    public void testEMR阈值5000() throws Exception {
        Map<String, Object> commonParam = new HashMap<>();
        commonParam.put("category", "方案904：阈值5000-EMR-月切保留客户维度(机型规格)-机型收敛-包弹-全部客户-月度ARIMAX");
        commonParam.put("serialInterval", "MONTH");
        commonParam.put("sourceType", "INDUSTRY");
        commonParam.put("billType", "ALL");
        commonParam.put("alName", "ARIMAX");
        commonParam.put("alArg", "0,1,3,0,1,6,0.2");
        commonParam.put("transInstanceType", "true");
        commonParam.put("groupDimType", "UIN_INSTANCE_MODEL_ZONE_NAME");
        commonParam.put("isRemoveSpike", "true");
        commonParam.put("forecastScope", 1);

        runForecastCommonUtils.run("2023-08-01", "2025-02-01", "custom_sql/emr/EMR单独跑-方案904-阈值5000.sql", commonParam);
    }
}
