package com.pugwoo.dboperate.模型预测调优;

import com.pugwoo.dboperate.模型预测调优.utils.RunForecastCommonUtils;
import com.pugwoo.dboperate.模型预测调优.utils.RunForecastResultDTO;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.math.BigDecimal;
import java.util.List;

@SpringBootTest
public class RunForecast {

    @Resource
    private RunForecastCommonUtils runForecastCommonUtils;

    // 方案705
    /**
     * 2024-11-01 新增准确率:60.63626341205900%, 退回准确率80.02514233708500%
     * 2024-10-01 新增准确率:77.03711770263000%, 退回准确率80.14626892646300%
     * 2024-09-01 新增准确率:78.16634394590200%, 退回准确率78.61702380313800%
     * 2024-08-01 新增准确率:80.96290474349000%, 退回准确率77.95044286357100%
     * 2024-07-01 新增准确率:78.46548707440300%, 退回准确率80.80777245604400%
     */
    @Test
    public void testCalAccucyRate() {
        // 用于已经跑完了，再拿taskId来计算准确率
        List<Integer> taskIds = ListUtils.of(5814, 5942, 6289, 6338, 6423, 6918 /*12月task的放到这里*/, 6958);
        List<RunForecastResultDTO> result = runForecastCommonUtils.calAccuracyRate(ListUtils.transform(taskIds, o -> Long.valueOf(o)));

        // 算一下2023年的准确率
        BigDecimal newAccuracyRate2023 = NumberUtils.avg(
                ListUtils.filter(result, o -> o.getPredictMonth().getYear() == 2023), 6,
                o -> o.getNewAccuracyRatePercent());
        BigDecimal retAccuracyRate2023 = NumberUtils.avg(
                ListUtils.filter(result, o -> o.getPredictMonth().getYear() == 2023), 6,
                o -> o.getRetAccuracyRatePercent());
        System.out.println("2023年平均准确率，新增:" + newAccuracyRate2023 + "%, 退回:" + retAccuracyRate2023 + "%");
    }

    // 方案706
    /**
     * 2024-11-01 新增准确率:56.79404980117300%, 退回准确率79.35827530540000%
     * 2024-10-01 新增准确率:77.60124073023400%, 退回准确率80.03475308922400%
     * 2024-09-01 新增准确率:81.26759633990500%, 退回准确率79.29400814819800%
     * 2024-08-01 新增准确率:78.48113259990900%, 退回准确率78.67907580802600%
     * 2024-07-01 新增准确率:79.12122727465300%, 退回准确率82.20334034799900%
     */
    @Test
    public void testCalAccucyRate2() {
        // 用于已经跑完了，再拿taskId来计算准确率
        List<Integer> taskIds = ListUtils.of(6545, 6546, 6547, 6548, 6549, 6915 /*12月task的放到这里*/, 6958);
        List<RunForecastResultDTO> result = runForecastCommonUtils.calAccuracyRate(ListUtils.transform(taskIds, o -> Long.valueOf(o)));
    }


    // 方案707
    /**
     *2024-11-01 新增准确率:58.71269658785700%, 退回准确率80.06841042804500%
     * 2024-10-01 新增准确率:76.66864728410800%, 退回准确率82.18309256205000%
     * 2024-09-01 新增准确率:78.47199839627200%, 退回准确率81.26491079105700%
     * 2024-08-01 新增准确率:80.58477792013800%, 退回准确率83.48898784750500%
     */
    @Test
    public void testCalAccucyRate3() {
        // 用于已经跑完了，再拿taskId来计算准确率
        List<Integer> taskIds = ListUtils.of(6469, 6470,6471, 6472, 6908, 6959);
        List<RunForecastResultDTO> result = runForecastCommonUtils.calAccuracyRate(ListUtils.transform(taskIds, o -> Long.valueOf(o)));
    }

}
