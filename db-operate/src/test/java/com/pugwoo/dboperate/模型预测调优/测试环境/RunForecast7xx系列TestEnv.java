package com.pugwoo.dboperate.模型预测调优.测试环境;

import com.pugwoo.dboperate.模型预测调优.utils.RunForecastResultDTO;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@SpringBootTest
public class RunForecast7xx系列TestEnv {

    @Resource
    private RunForecastCommonUtilsTestEnv runForecastCommonUtilsTestEnv;

    @Test
    public void test705原版() throws Exception {
        Map<String, Object> commonParam = new HashMap<>();
        commonParam.put("category", "方案705：全部-月切保留客户维度(机型规格)-机型收敛-包弹-外部客户-月度ARIMAX");
        commonParam.put("serialInterval", "MONTH");
        commonParam.put("sourceType", "INDUSTRY");
        commonParam.put("billType", "ALL");
        commonParam.put("alName", "ARIMAXL");
        commonParam.put("alArg", "0,1,3,0,1,6,0.2"); // 当值为-1(只有一个值)时表示自动选择参数
        commonParam.put("retAlArgs", "0,1,3,0.2"); // 退回采用0,1,3
        commonParam.put("transInstanceType", "true");
        commonParam.put("groupDimType", "UIN_INSTANCE_MODEL_ZONE_NAME");
        commonParam.put("isRemoveSpike", "true");
        commonParam.put("forecastScope", 1); // /*模型预测范围，1为预测长尾，2是预测31个头部(且不含毛刺)，3是预测31个头部+毛刺*/

        LocalDate curMonth = DateUtils.getFirstDayOfMonth(new Date());
        List<RunForecastResultDTO> result = runForecastCommonUtilsTestEnv.run(curMonth.toString(), curMonth.toString(), "", commonParam);

        // 算一下2023年的准确率
        BigDecimal newAccuracyRate2023 = NumberUtils.avg(
                ListUtils.filter(result, o -> o.getPredictMonth().getYear() == 2023), 6,
                o -> o.getNewAccuracyRatePercent());
        BigDecimal retAccuracyRate2023 = NumberUtils.avg(
                ListUtils.filter(result, o -> o.getPredictMonth().getYear() == 2023), 6,
                o -> o.getRetAccuracyRatePercent());
        System.out.println("2023年平均准确率，新增:" + newAccuracyRate2023 + "%, 退回:" + retAccuracyRate2023 + "%");
    }

    @Test
    public void test707清华() throws Exception {
        Map<String, Object> commonParam = new HashMap<>();
        commonParam.put("category", "方案707：全部-月切保留客户维度(机型规格)-机型收敛-包弹-外部客户-月度-MIXED");
        commonParam.put("serialInterval", "MONTH");
        commonParam.put("sourceType", "INDUSTRY");
        commonParam.put("billType", "ALL");
        commonParam.put("alName", "MIXED");
        commonParam.put("alArg", "");
        commonParam.put("transInstanceType", "true");
        commonParam.put("groupDimType", "UIN_INSTANCE_MODEL_ZONE_NAME");
        commonParam.put("isRemoveSpike", "true");
        commonParam.put("forecastScope", 1); // /*模型预测范围，1为预测长尾，2是预测31个头部(且不含毛刺)，3是预测31个头部+毛刺*/

        LocalDate curMonth = DateUtils.getFirstDayOfMonth(new Date());
        List<RunForecastResultDTO> result = runForecastCommonUtilsTestEnv.run("2022-10-01", curMonth.toString(), "", commonParam);

        // 算一下2023年的准确率
        BigDecimal newAccuracyRate2023 = NumberUtils.avg(
                ListUtils.filter(result, o -> o.getPredictMonth().getYear() == 2023), 6,
                o -> o.getNewAccuracyRatePercent());
        BigDecimal retAccuracyRate2023 = NumberUtils.avg(
                ListUtils.filter(result, o -> o.getPredictMonth().getYear() == 2023), 6,
                o -> o.getRetAccuracyRatePercent());
        System.out.println("2023年平均准确率，新增:" + newAccuracyRate2023 + "%, 退回:" + retAccuracyRate2023 + "%");
    }

}
