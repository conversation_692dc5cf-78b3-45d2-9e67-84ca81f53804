package com.pugwoo.dboperate.模型预测调优.测试环境;

import com.pugwoo.dboperate.模型预测调优.utils.RunForecastResultDTO;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@SpringBootTest
public class RunForecast5xx系列TestEnv {

    @Resource
    private RunForecastCommonUtilsTestEnv runForecastCommonUtilsTestEnv;

    @Test
    public void testEMR510原版() throws Exception {
        Map<String, Object> commonParam = new HashMap<>();
        commonParam.put("category", "方案510：EMR中长尾-月切保留客户维度(机型规格)-机型收敛-包弹-全部客户-月度ARIMAX");
        commonParam.put("product", "EMR"); // 【重要】
        commonParam.put("serialInterval", "MONTH");
        commonParam.put("alName", "ARIMAXL");
        commonParam.put("alArg", "0,1,3,0,1,6,0.2"); // 当值为-1(只有一个值)时表示自动选择参数
        commonParam.put("retAlArgs", "0,1,3,0.2"); // 退回采用0,1,3
        commonParam.put("transInstanceType", "true");
        commonParam.put("isRemoveSpike", "true");
        commonParam.put("customSpikeThreshold", "2000"); // 【重要】

        LocalDate curMonth = DateUtils.getFirstDayOfMonth(new Date());
        List<RunForecastResultDTO> result = runForecastCommonUtilsTestEnv.run("2022-08-01", curMonth.toString(), "", commonParam);

        // 算一下2023年的准确率
        BigDecimal newAccuracyRate2023 = NumberUtils.avg(
                ListUtils.filter(result, o -> o.getPredictMonth().getYear() == 2023), 6,
                o -> o.getNewAccuracyRatePercent());
        BigDecimal retAccuracyRate2023 = NumberUtils.avg(
                ListUtils.filter(result, o -> o.getPredictMonth().getYear() == 2023), 6,
                o -> o.getRetAccuracyRatePercent());
        System.out.println("2023年平均准确率，新增:" + newAccuracyRate2023 + "%, 退回:" + retAccuracyRate2023 + "%");
    }

    @Test
    public void testEKS520原版() throws Exception {
        Map<String, Object> commonParam = new HashMap<>();
        commonParam.put("category", "方案520：EKS中长尾-月切保留客户维度(机型规格)-机型收敛-全部客户-月度ARIMAX");
        commonParam.put("product", "EKS"); // 【重要】
        commonParam.put("serialInterval", "MONTH");
        commonParam.put("alName", "ARIMAXL");
        commonParam.put("alArg", "0,1,3,0,1,6,0.2"); // 当值为-1(只有一个值)时表示自动选择参数
        commonParam.put("retAlArgs", "0,1,3,0.2"); // 退回采用0,1,3
        commonParam.put("transInstanceType", "true");
        commonParam.put("isRemoveSpike", "true");
        commonParam.put("customSpikeThreshold", "1000"); // 【重要】

        LocalDate curMonth = DateUtils.getFirstDayOfMonth(new Date());
        List<RunForecastResultDTO> result = runForecastCommonUtilsTestEnv.run("2023-09-01", curMonth.toString(), "", commonParam);

        // 算一下2023年的准确率
        BigDecimal newAccuracyRate2023 = NumberUtils.avg(
                ListUtils.filter(result, o -> o.getPredictMonth().getYear() == 2023), 6,
                o -> o.getNewAccuracyRatePercent());
        BigDecimal retAccuracyRate2023 = NumberUtils.avg(
                ListUtils.filter(result, o -> o.getPredictMonth().getYear() == 2023), 6,
                o -> o.getRetAccuracyRatePercent());
        System.out.println("2023年平均准确率，新增:" + newAccuracyRate2023 + "%, 退回:" + retAccuracyRate2023 + "%");
    }

    @Test
    public void testCDB530原版() throws Exception {
        Map<String, Object> commonParam = new HashMap<>();
        commonParam.put("category", "方案530：CDB中长尾-月切保留客户维度-内存收敛-全部客户-月度ARIMAX");
        commonParam.put("product", "CDB"); // 【重要】
        commonParam.put("serialInterval", "MONTH");
        commonParam.put("alName", "ARIMAXL");
        commonParam.put("alArg", "0,1,3,0,1,6,0.2"); // 当值为-1(只有一个值)时表示自动选择参数
        commonParam.put("retAlArgs", "0,1,3,0.2"); // 退回采用0,1,3
        commonParam.put("transInstanceType", "false");
        commonParam.put("isRemoveSpike", "true");
        commonParam.put("customSpikeThreshold", "2000"); // 【重要】
        commonParam.put("cdbMemGroup", "SMALL_OR_BIG"); // 大小内存聚合

        LocalDate curMonth = DateUtils.getFirstDayOfMonth(new Date());
        List<RunForecastResultDTO> result = runForecastCommonUtilsTestEnv.run("2023-09-01", curMonth.toString(), "", commonParam);

        // 算一下2023年的准确率
        BigDecimal newAccuracyRate2023 = NumberUtils.avg(
                ListUtils.filter(result, o -> o.getPredictMonth().getYear() == 2023), 6,
                o -> o.getNewAccuracyRatePercent());
        BigDecimal retAccuracyRate2023 = NumberUtils.avg(
                ListUtils.filter(result, o -> o.getPredictMonth().getYear() == 2023), 6,
                o -> o.getRetAccuracyRatePercent());
        System.out.println("2023年平均准确率，新增:" + newAccuracyRate2023 + "%, 退回:" + retAccuracyRate2023 + "%");
    }

    @Test
    public void testCBS540原版() throws Exception {
        Map<String, Object> commonParam = new HashMap<>();
        commonParam.put("category", "方案540：CBS中长尾-月切保留客户维度-内存收敛-全部客户-月度ARIMAX");
        commonParam.put("product", "CBS"); // 【重要】
        commonParam.put("serialInterval", "MONTH");
        commonParam.put("alName", "ARIMAXL");
        commonParam.put("alArg", "0,1,3,0,1,6,0.2"); // 当值为-1(只有一个值)时表示自动选择参数
        commonParam.put("retAlArgs", "0,1,3,0.2"); // 退回采用0,1,3
        commonParam.put("transInstanceType", "false");
        commonParam.put("isRemoveSpike", "true");

        LocalDate curMonth = DateUtils.getFirstDayOfMonth(new Date());
        List<RunForecastResultDTO> result = runForecastCommonUtilsTestEnv.run("2023-09-01", curMonth.toString(), "", commonParam);

        // 算一下2024年的准确率
        BigDecimal newAccuracyRate2023 = NumberUtils.avg(
                ListUtils.filter(result, o -> o.getPredictMonth().getYear() == 2024), 6,
                o -> o.getNewAccuracyRatePercent());
        BigDecimal retAccuracyRate2023 = NumberUtils.avg(
                ListUtils.filter(result, o -> o.getPredictMonth().getYear() == 2024), 6,
                o -> o.getRetAccuracyRatePercent());
        System.out.println("2024年平均准确率，新增:" + newAccuracyRate2023 + "%, 退回:" + retAccuracyRate2023 + "%");
    }


}
