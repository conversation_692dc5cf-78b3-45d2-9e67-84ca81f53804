package com.pugwoo.dboperate.高校合作;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dboperate.entity.TxyScaleDataMaskingDO;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import jakarta.annotation.Resource;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 脱敏数据给清华研究同学，包括：日规模、毛刺
 */
@SpringBootTest
public class SyncMarkingScaleAndSpikeData {

    @Resource
    private DBHelper ckForecastResearchNewIdcDBHelper;
    @Resource
    private DBHelper ckStdCrpNewIdcDBHelper;
    @Resource
    private DBHelper cloudDemandCommonIdcDBHelper;

    /**
     * 同步最新毛刺数据
     */
    @Test
    public void syncSpikeData() throws InterruptedException {
        ckForecastResearchNewIdcDBHelper.executeRaw("""
                truncate table forecast_research.ppl_forecast_input_excluded_spike_local ON CLUSTER default_cluster
                """);
        Thread.sleep(10000);
        syncSpikeData("d8af05417a4244bb99b869da0bd39ad1"); // 最新的毛刺批次uuid，外部，705方案
        syncSpikeData("92fdc5aa64c640d58f4b3f98bff9b361"); // 内部，715方案
    }

    /**
     * 同步日规模数据
     */
    @Test
    public void syncScaleData() {
        LocalDate start = DateUtils.parseLocalDate("2025-02-01");
        LocalDate end = DateUtils.parseLocalDate("2025-02-28");

        while(!start.isAfter(end)) {
            String statTime = DateUtils.format(start);
            ckForecastResearchNewIdcDBHelper.executeRaw("""
                    ALTER TABLE forecast_research.dwd_txy_scale_df_local ON CLUSTER default_cluster DROP PARTITION ?
                    """, statTime);

            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
            }

            Map<String, String> regionNameMapping = getMapping(statTime, "region_name", "R", 100);
            String regionNameMappingCaseWhen = toCaseWhen("region_name", regionNameMapping);

            String regionMappingCaseWhen = regionNameMappingCaseWhen.replace("as region_name", "as region");
            // 妈的，clickhouse先写的select并不能就认为用原表得的字段
            regionNameMappingCaseWhen = regionNameMappingCaseWhen.replace("as region_name", "as region_name1");

            Map<String, String> areaNameMapping = getMapping(statTime, "area_name", "A", 10);
            String areaNameCaseWhen = toCaseWhen("area_name", areaNameMapping);

            Map<String, String> instanceModelMapping = getMapping(statTime, "instance_model", "M", 1000);
            String instanceModelCaseWhen = toCaseWhen("instance_model", instanceModelMapping);

            Map<String, String> instanceTypeMapping = getMapping(statTime, "instance_type", "T", 100);
            String instanceTypeCaseWhen = toCaseWhen("instance_type", instanceTypeMapping);


            ckForecastResearchNewIdcDBHelper.executeRaw("""
                    insert into forecast_research.dwd_txy_scale_df
                    (    stat_time,month,year,month_start_date,month_end_date,week_days,month_days,week,week_num,week_month,week_year,week_start_date,week_end_date,
                         app_id,uin,industry_dept,is_inner,war_zone,
                         customer_short_name,
                            customer_type,
                            zone_id, zone_name, zone,
                            region, region_name, area_name, customhouse_title,
                            instance_model,instance_type,
                            paymode,app_role,demand_type,paymode_range_type,customer_tab_type,
                            biz_range_type,biz_type,cpu_or_gpu,product,instance_model_cpu,instance_model_mem,
                            cpu_type,
                            cur_bill_core,new_bill_core,ret_bill_core,change_bill_core,cur_free_core,new_free_core,ret_free_core,change_free_core,cur_service_core,new_service_core,ret_service_core,change_service_core,change_bill_core_from_last_month,change_bill_core_from_last_week,change_free_core_from_last_month,change_free_core_from_last_week,change_service_core_from_last_month,change_service_core_from_last_week,
                            cid,gid,gname
                         )
                         select
                         stat_time,month,year,month_start_date,month_end_date,week_days,month_days,week,week_num,week_month,week_year,week_start_date,week_end_date,
                         app_id,uin,industry_dept,is_inner,war_zone,
    (case when customer_short_name='(空值)' then '(空值)'
          else concat('C', toString(cityHash64(customer_short_name))) end) as customer_short_name,
    customer_type,
    zone_id,
    (case when zone_name='(空值)' then '(空值)'
        else concat('Z', toString(zone_id)) end) as zone_name,
    (case when zone='(空值)' then '(空值)'
        else concat('Z', toString(zone_id)) end) as zone,
        ${regionMappingCaseWhen},
        ${regionNameMappingCaseWhen},
        ${areaNameCaseWhen},
        customhouse_title,
        ${instanceModelCaseWhen},
        ${instanceTypeCaseWhen},
        paymode,app_role,demand_type,paymode_range_type,customer_tab_type,
        biz_range_type,biz_type,cpu_or_gpu,product,instance_model_cpu,instance_model_mem,
        cpu_type,
                 cur_bill_core,new_bill_core,ret_bill_core,change_bill_core,cur_free_core,new_free_core,ret_free_core,change_free_core,cur_service_core,new_service_core,ret_service_core,change_service_core,change_bill_core_from_last_month,change_bill_core_from_last_week,change_free_core_from_last_month,change_free_core_from_last_week,change_service_core_from_last_month,change_service_core_from_last_week,
             cid,gid, gid as gname
                      from std_crp.dwd_txy_scale_df
                      where stat_time=? and product='CVM'
                    """.replace("${regionNameMappingCaseWhen}", regionNameMappingCaseWhen)
                            .replace("${regionMappingCaseWhen}", regionMappingCaseWhen)
                            .replace("${areaNameCaseWhen}", areaNameCaseWhen)
                            .replace("${instanceModelCaseWhen}", instanceModelCaseWhen)
                            .replace("${instanceTypeCaseWhen}", instanceTypeCaseWhen)
                    , statTime);

            System.out.println("sync date:" + statTime + "done");
            start = start.plusDays(1);
        }
    }

    /**同步带有到期时间的日规模数据*/
    @Test
    public void syncScaleWithDeadlineData() {
        LocalDate start = DateUtils.parseLocalDate("2024-06-27");
        LocalDate end = DateUtils.parseLocalDate("2024-11-12");

        while(!start.isAfter(end)) {
            String statTime = DateUtils.format(start);
            ckForecastResearchNewIdcDBHelper.executeRaw("""
                    ALTER TABLE forecast_research.dwd_txy_scale_with_deadline_df_local ON CLUSTER default_cluster DROP PARTITION ?
                    """, statTime);
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
            }

            Map<String, String> regionNameMapping = getMapping(statTime, "region_name", "R", 100);
            String regionNameMappingCaseWhen = toCaseWhen("region_name", regionNameMapping);

            String regionMappingCaseWhen = regionNameMappingCaseWhen.replace("as region_name", "as region");
            // 妈的，clickhouse先写的select并不能就认为用原表得的字段
            regionNameMappingCaseWhen = regionNameMappingCaseWhen.replace("as region_name", "as region_name1");

            Map<String, String> areaNameMapping = getMapping(statTime, "area_name", "A", 10);
            String areaNameCaseWhen = toCaseWhen("area_name", areaNameMapping);

            Map<String, String> instanceModelMapping = getMapping(statTime, "instance_model", "M", 1000);
            String instanceModelCaseWhen = toCaseWhen("instance_model", instanceModelMapping);

            Map<String, String> instanceTypeMapping = getMapping(statTime, "instance_type", "T", 100);
            String instanceTypeCaseWhen = toCaseWhen("instance_type", instanceTypeMapping);

            ckForecastResearchNewIdcDBHelper.executeRaw("""
                    insert into forecast_research.dwd_txy_scale_with_deadline_df
                    (    stat_time,
                                                        create,
                                                        deadline,
                                                        month,
                                                        year,
                                                        month_days,
                                                        month_start_date,
                                                        month_end_date,
                                                        week_days,
                                                        week,
                                                        week_num,
                                                        week_month,
                                                        week_year,
                                                        week_start_date,
                                                        week_end_date,
                                                        app_id,
                                                        uin,
                                                        business_manager,
                                                        business_manager_oa_dept,
                                                        industry_dept,
                                                        is_inner,
                                                        war_zone,
                                                        customer_name,
                                                        customer_short_name,
                                                        customer_type,
                                                        inner_info_bg_id,
                                                        inner_info_bg_name,
                                                        inner_info_bg_short_name,
                                                        inner_info_dept_id,
                                                        inner_info_dept_name,
                                                        inner_info_plan_product_id,
                                                        inner_info_plan_product_name,
                                                        inner_info_product_id,
                                                        inner_info_product_name,
                                                        zone_id,
                                                        zone_name,
                                                        zone,
                                                        region,
                                                        region_name,
                                                        area_name,
                                                        customhouse_title,
                                                        instance_model,
                                                        instance_type,
                                                        paymode,
                                                        app_role,
                                                        paymode_range_type,
                                                        biz_range_type,
                                                        biz_type,
                                                        cpu_or_gpu,
                                                        product,
                                                        instance_model_gpu,
                                                        instance_model_cpu,
                                                        instance_model_mem,
                                                        instance_model_gpu_ratio,
                                                        cpu_type,
                                                        cur_bill_core,
                                                        cur_free_core,
                                                        cur_service_core,
                                                        cid,
                                                        gid,
                                                        gname,
                                                        gpu_card_type,
                                                        data_type
                         )
                         select
                                                        stat_time,
                                                        create,
                                                        deadline,
                                                        month,
                                                        year,
                                                        month_days,
                                                        month_start_date,
                                                        month_end_date,
                                                        week_days,
                                                        week,
                                                        week_num,
                                                        week_month,
                                                        week_year,
                                                        week_start_date,
                                                        week_end_date,
                                                        app_id,
                                                        uin,
                                                        business_manager,
                                                        business_manager_oa_dept,
                                                        industry_dept,
                                                        is_inner,
                                                        war_zone,
                                                        customer_name,
                                                            (case when customer_short_name='(空值)' then '(空值)'
          else concat('C', toString(cityHash64(customer_short_name))) end) as customer_short_name,
                                                        customer_type,
                                                        inner_info_bg_id,
                                                        inner_info_bg_name,
                                                        inner_info_bg_short_name,
                                                        inner_info_dept_id,
                                                        inner_info_dept_name,
                                                        inner_info_plan_product_id,
                                                        inner_info_plan_product_name,
                                                        inner_info_product_id,
                                                        inner_info_product_name,
                                                        zone_id,
                                 (case when zone_name='(空值)' then '(空值)'
        else concat('Z', toString(zone_id)) end) as zone_name,
                                     (case when zone='(空值)' then '(空值)'
        else concat('Z', toString(zone_id)) end) as zone,
                            ${regionMappingCaseWhen},
        ${regionNameMappingCaseWhen},
                                                     ${areaNameCaseWhen},
                                                        customhouse_title,
                              ${instanceModelCaseWhen},
        ${instanceTypeCaseWhen},
                                                        paymode,
                                                        app_role,
                                                        paymode_range_type,
                                                        biz_range_type,
                                                        biz_type,
                                                        cpu_or_gpu,
                                                        product,
                                                        instance_model_gpu,
                                                        instance_model_cpu,
                                                        instance_model_mem,
                                                        instance_model_gpu_ratio,
                                                        cpu_type,
                                                        cur_bill_core,
                                                        cur_free_core,
                                                        cur_service_core,
                                                        cid,
                                                        gid,
                                                        gid as gname,
                                                        gpu_card_type,
                                                        data_type
                         
                      from std_crp.dwd_txy_scale_with_deadline_df
                      where stat_time=? and data_type=0
                    """.replace("${regionNameMappingCaseWhen}", regionNameMappingCaseWhen)
                            .replace("${regionMappingCaseWhen}", regionMappingCaseWhen)
                            .replace("${areaNameCaseWhen}", areaNameCaseWhen)
                            .replace("${instanceModelCaseWhen}", instanceModelCaseWhen)
                            .replace("${instanceTypeCaseWhen}", instanceTypeCaseWhen)
                    , statTime);

            System.out.println("sync date:" + statTime + "done");
            start = start.plusDays(1);

        }
    }

    private void syncSpikeData(String batchUuid) {
        String statTime = LocalDate.now().minusDays(1).toString();

        Map<String, String> regionNameMapping = getMapping(statTime, "region_name", "R", 100);
        String regionNameMappingCaseWhen = toCaseWhen("region_name", regionNameMapping);

        Map<String, String> instanceTypeMapping = getMapping(statTime, "instance_type", "T", 100);
        String instanceTypeCaseWhen = toCaseWhen("instance_type", instanceTypeMapping);

        Map<String, String> zoneMapping = getZoneMapping(statTime);
        String zoneNameMappingCaseWhen = toCaseWhen("zone_name", zoneMapping);

        ckForecastResearchNewIdcDBHelper.executeRaw("""
                insert into forecast_research.ppl_forecast_input_excluded_spike
                (year,month,industry_dept,customer_uin,instance_type,region_name,zone_name,new_core,ret_core)
                select year,month,industry_dept,customer_uin,
                    ${instanceTypeCaseWhen},
                    ${regionNameMappingCaseWhen},
                    ${zoneNameMappingCaseWhen},
                    new_core,ret_core
                from cloud_demand.ppl_forecast_input_excluded_spike
                where batch_uuid=?
                """.replace("${regionNameMappingCaseWhen}", regionNameMappingCaseWhen)
                        .replace("${instanceTypeCaseWhen}", instanceTypeCaseWhen)
                        .replace("${zoneNameMappingCaseWhen}", zoneNameMappingCaseWhen),
                batchUuid);
    }

    private String toCaseWhen(String columnName, Map<String, String> mapping) {
        StringBuilder sb = new StringBuilder();
        sb.append("(case ");
        for (Map.Entry<String, String> entry : mapping.entrySet()) {
            sb.append("when ").append(columnName).append("='")
                    .append(entry.getKey().replace("'", "''")).append("' then '").append(entry.getValue()).append("' \n");
        }
        sb.append(" else 'UNKNOWN' end) as ").append(columnName);
        return sb.toString();
    }

    private Map<String, String> getMapping(String statTime, String columnName, String prefix, int startIndex) {
        List<String> values = ckStdCrpNewIdcDBHelper.getRaw(String.class, "select distinct " + columnName +
                " from std_crp.dwd_txy_scale_df where stat_time=?", statTime);
        String type = columnName;

        List<TxyScaleDataMaskingDO> all = cloudDemandCommonIdcDBHelper.getAll(
                TxyScaleDataMaskingDO.class, "where type=?", type);

        Map<String, String> dbMapping = ListUtils.toMap(all, o -> o.getRealValue(), o -> o.getCode());

        ListUtils.sortDescNullLast(all, o -> o.getCode());
        if (!all.isEmpty()) {
            startIndex = NumberUtils.parseInt(all.get(0).getCode().substring(1)) + 1;
        }

        Map<String, String> result = new HashMap<>();
        List<TxyScaleDataMaskingDO> toInsert = new ArrayList<>();
        for (String val : values) {
            if (dbMapping.containsKey(val)) {
                result.put(val, dbMapping.get(val));
            } else if ("(空值)".equals(val)) {
                result.put(val, "(空值)");
            } else {
                String code = prefix + (startIndex++);
                result.put(val, code);
                TxyScaleDataMaskingDO one = new TxyScaleDataMaskingDO();
                one.setType(type);
                one.setCode(code);
                one.setRealValue(val);
                toInsert.add(one);
            }
        }
        cloudDemandCommonIdcDBHelper.insertBatchWithoutReturnId(toInsert);

        return result;
    }

    /**可用区的代号直接就是Z + zoneId*/
    private Map<String, String> getZoneMapping(String statTime) {
        List<Map> list = ckStdCrpNewIdcDBHelper.getRaw(Map.class, """
                select distinct zone_name,zone_id from dwd_txy_scale_df where stat_time=?
                """, statTime);
        Map<String, String> result = new HashMap<>();
        list.forEach(o -> {
            Object zoneName = o.get("zone_name");
            Object zoneId = o.get("zone_id");
            result.put(zoneName.toString(), "Z" + zoneId);
        });
        return result;
    }

}
