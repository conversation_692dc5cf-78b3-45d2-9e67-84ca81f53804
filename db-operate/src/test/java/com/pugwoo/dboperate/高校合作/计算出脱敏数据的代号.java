package com.pugwoo.dboperate.高校合作;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.string.StringTools;
import jakarta.annotation.Resource;
import lombok.Data;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

@SpringBootTest
public class 计算出脱敏数据的代号 {

    @Resource
    private DBHelper cloudDemandIdcDBHelper;
    @Resource
    private DBHelper ckStdCrpNewIdcDBHelper;
    @Resource
    private DBHelper cloudDemandCommonIdcDBHelper;

    @Data
    public static class CodeAndValue {
        @Column("code")
        private String code;
        @Column("real_value")
        private String realValue;
    }

    @Data
    public static class InstanceMergeDTO {
        @Column("common_instance_type")
        private String commonInstanceType;
        @Column("instance_types")
        private String instanceTypes;
    }

    /**
     * 黑名单机型，2024年9月10日15:26:33更新
     */
    @Test
    public void testBlackInstanceType() {
        // 这个黑名单是来自于：select * from ppl_forecast_config_spike_threshold where threshold<0
        // 如底表有更新，则需要手工更新下这里
        String blackInstanceType = "BC1,VSCVMMS,I6t,,IA3se,SH2,BF1,BS1,C2,CN2,D1,DR1,DS2,DSW13,DSW14,FX4,HC20,HI1,HI20,HM1,HM20,HS10,HS1A,HS20,I1,I2,IT2,M1,M2,S1,S2,S2ne,SA1,SH1,SHARED,SK1,SN2,SPECIAL,SR1,TS2,TS3,VSCCS,VSCNAS,VSVPN,S5nt,VSVPNDPDK,SA1,SK1,SH1,SR1,OCA2,OC1,HS51,S3,S2,S1,TS3,TS2,HS30,HS20,HS10,S2ne,HS31,SN3ne,S4,TS4,M3,M2,M1,HM20,TM3,M4,C5,C3,C2,HC20,TC3,TC3ne,C4,C4ne,TC4,C4t,TC4t,IT5,IT5c,I3,I2,I1,HI20,IT3,IT3c,ITA3,CN3,CN2,TCN3,TCN3ne,D2,D1,D3,M3,M2,M1,HM20,TM3,M4";

        List<CodeAndValue> list = cloudDemandCommonIdcDBHelper.getRaw(CodeAndValue.class,
                "select distinct code,real_value from txy_scale_data_masking where type='instance_type'");
        Map<String, String> map = ListUtils.toMap(list, o -> o.getRealValue(), o -> o.getCode());

        Set<String> result = new HashSet<>();
        String[] split = blackInstanceType.split(",");
        for (String s :split) {
            if (StringTools.isNotBlank(s)) {
                result.add(map.getOrDefault(s, "未知"));
            }
        }

        System.out.println(JSON.toJson(result));
        System.out.println(StringTools.join(",", result));
    }

    /**
     * 机型收敛配置
     */
    @Test
    public void testInstanceMergeConfig() {
        List<InstanceMergeDTO> all = cloudDemandIdcDBHelper.getRaw(InstanceMergeDTO.class,
                "select common_instance_type,instance_types from mrpv2_common_instance_type_config where use_forecast=1");

        List<CodeAndValue> list = cloudDemandCommonIdcDBHelper.getRaw(CodeAndValue.class,
                "select distinct code,real_value from txy_scale_data_masking where type='instance_type'");
        Map<String, String> map = ListUtils.toMap(list, o -> o.getRealValue(), o -> o.getCode());

        for (InstanceMergeDTO instanceMergeDTO : all) {
            String commonInstanceType = instanceMergeDTO.getCommonInstanceType();
            String instanceTypes = instanceMergeDTO.getInstanceTypes();

            String[] split = instanceTypes.split(",");
            List<String> transform = ListUtils.transform(split, o -> map.getOrDefault(o, o));

            System.out.println(map.getOrDefault(commonInstanceType, commonInstanceType) + ",\"" +
                   StringTools.join(",", transform) + "\"");
        }
    }

    /**
     * 大客户的简称（新） 大客户简称融入到ppl auth相关配置页面里了
     */
    @Test
    public void genBigCustomerName() {
        List<String> raw = cloudDemandIdcDBHelper.getRaw(String.class,
                "select customer_name from cloud_demand.industry_demand_industry_war_zone_dict\n" +
                        "where deleted = 0  and customer_name != '' and  is_big_customer = 1");
        for (String r : raw) {
            //System.out.println(r);
            String rawOne = ckStdCrpNewIdcDBHelper.getRawOne(String.class,
                    "select concat('C', toString(cityHash64(?))) ", r);
            System.out.println(rawOne);
        }
    }

}
