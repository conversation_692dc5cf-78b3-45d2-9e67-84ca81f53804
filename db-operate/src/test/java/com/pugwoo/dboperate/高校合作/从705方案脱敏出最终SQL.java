package com.pugwoo.dboperate.高校合作;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.string.RegexUtils;
import com.pugwoo.wooutils.string.StringTools;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@SpringBootTest
public class 从705方案脱敏出最终SQL {

    @Resource
    private DBHelper cloudDemandIdcDBHelper;
    @Resource
    private DBHelper ckStdCrpNewIdcDBHelper;
    @Resource
    private DBHelper cloudDemandCommonIdcDBHelper;

    /**
     * 直接跑就行了，会自动查出705方案的最新sql并自动脱敏
     */
    @Test
    public void trans() {
        String sql = cloudDemandIdcDBHelper.getRawOne(String.class,
                """
                        select input_sql_new from ppl_forecast_predict_task
                        where category like '方案705%' and is_enable=1
                        order by input_date_end desc limit 1
                        """);

        // 处理zoneName
        String zoneNameRegex = "(and\\s+zone_name\\s+in\\s*\\([^)]+\\))";
        String zoneSql = RegexUtils.getFirstMatchStr(sql, zoneNameRegex);
        zoneSql = maskZoneName(zoneSql, getZoneMapping());
        sql = RegexUtils.replaceFirstGroup(sql, zoneNameRegex, zoneSql);

        // 处理instanceType
        String instanceTypeRegex = "(and\\s+instance_type\\s+not\\s+in\\s*\\([^)]+\\))";
        String instanceTypeSql = RegexUtils.getFirstMatchStr(sql, instanceTypeRegex);
        instanceTypeSql = maskInstanceType(instanceTypeSql, getInstanceTypeMapping());
        sql = RegexUtils.replaceFirstGroup(sql, instanceTypeRegex, instanceTypeSql);

        // 处理customerName
        String customerNameRegex = "(and customer_short_name not in \\(.*\\))";
        String customerNameSql = RegexUtils.getFirstMatchStr(sql, customerNameRegex);
        customerNameSql = maskCustomerShortName(customerNameSql);
        sql = RegexUtils.replaceFirstGroup(sql, customerNameRegex, customerNameSql);

        // 处理spkie
        sql = sql.replace("cloud_demand.ppl_forecast_input_excluded_spike",
                "forecast_research.ppl_forecast_input_excluded_spike");
        sql = RegexUtils.replaceFirstGroup(sql, "(where\\s+batch_uuid='[0-9a-fA-F]+')", "");

        // 处理表名
        sql = sql.replace("dwd_txy_scale_df", "forecast_research.dwd_txy_scale_df");

        // 处理RS和RM
        List<String> rsAndRM = getRSAndRM();
        StringBuilder sb = new StringBuilder("and instance_type not in (");
        for (int i = 0; i < rsAndRM.size(); i++) {
            sb.append("'").append(rsAndRM.get(i)).append("'");
            if (i < rsAndRM.size() - 1) {
                sb.append(",");
            }
        }
        sb.append(") -- RS and RM ");
        sql = sql.replace("and instance_type not like 'RS%' and instance_type not like 'RM%'", sb.toString());

        System.out.println("===================================================================");
        System.out.println(sql);
    }

    private String maskCustomerShortName(String customerNameSql) {
        List<String> allCompany = RegexUtils.getAllMatchStr(customerNameSql, "'([^']+?)'");

        for (String company : allCompany) {
            String c = ckStdCrpNewIdcDBHelper.getRawOne(String.class,
                    "select concat('C', toString(cityHash64(?)))", company);
            customerNameSql = customerNameSql.replace("'" + company + "'", "'" + c + "'");
        }

        return customerNameSql;
    }

    private static String maskZoneName(String zoneSql, Map<String, String> zoneMapping) {
        for (Map.Entry<String, String> e : zoneMapping.entrySet()) {
            zoneSql = zoneSql.replace("'" + e.getKey() + "'", "'" + e.getValue() + "'");
        }

        // 特别去掉 深圳二区 贵阳一区
        if (StringTools.isBlank(zoneMapping.get("深圳二区"))) {
            zoneSql = zoneSql.replace(",'深圳二区'", "");
        }
        if (StringTools.isBlank(zoneMapping.get("贵阳一区"))) {
            zoneSql = zoneSql.replace(",'贵阳一区'", "");
        }

        return zoneSql;
    }

    private static String maskInstanceType(String instanceTypeSql, Map<String, String> instanceTypeMapping) {
        for (Map.Entry<String, String> e : instanceTypeMapping.entrySet()) {
            instanceTypeSql = instanceTypeSql.replace("'" + e.getKey() + "'", "'" + e.getValue() + "'");
        }
        return instanceTypeSql;
    }

    private Map<String, String> getInstanceTypeMapping() {
        List<Map> list = cloudDemandCommonIdcDBHelper.getRaw(Map.class,
                """
                        select real_value,code from txy_scale_data_masking where type='instance_type'
                        """);
        Map<String, String> result = new HashMap<>();
        list.forEach(o -> {
            Object realValue = o.get("real_value");
            Object code = o.get("code");
            result.put(realValue.toString(), code.toString());
        });
        return result;
    }

    private List<String> getRSAndRM() {
        return cloudDemandCommonIdcDBHelper.getRaw(String.class,
                """
               select code from txy_scale_data_masking where type='instance_type' and (real_value like 'RS%' or real_value like 'RM%')
                        """);
    }

    /**可用区的代号直接就是Z + zoneId*/
    private Map<String, String> getZoneMapping() {
        List<Map> list = ckStdCrpNewIdcDBHelper.getRaw(Map.class, """
                select distinct zone_name,zone_id from dwd_txy_scale_df
                """);
        Map<String, String> result = new HashMap<>();
        list.forEach(o -> {
            Object zoneName = o.get("zone_name");
            Object zoneId = o.get("zone_id");
            result.put(zoneName.toString(), "Z" + zoneId);
        });
        return result;
    }
}
