package com.pugwoo.dboperate.八大产品.tc;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.string.StringTools;
import jakarta.annotation.Resource;
import lombok.Data;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;
import java.util.Map;

@SpringBootTest
public class TC数据处理 {

    @Resource
    private DBHelper cloudDemandDevDBHelper;
    @Resource
    private DBHelper ckCloudDemandNewIdcDBHelper;

    /**
     * 检查数据的基本情况，没有输出则表示通过
     */
    @Test
    public void checkDestroy() {

        // 检查一下是否一个cluster最多只有一个destroy事件，目前是通过sql验证过了
        List<TcOrderDO> all = cloudDemandDevDBHelper.getAll(TcOrderDO.class,
                "  where operate_type='destroy' group by cluster_id having count(*) > 1");
        assert all.isEmpty();

        List<TcOrderDO> destoryList = cloudDemandDevDBHelper.getAll(TcOrderDO.class,
                "where operate_type='destroy' order by start_date");

        for (TcOrderDO record : destoryList) {
            List<TcOrderDO> sameClusterIds = cloudDemandDevDBHelper.getAll(TcOrderDO.class,
                    "where cluster_id=? order by start_time", record.getClusterId());

            // 如果最后一个事件不是销毁，那么告警
            if (!sameClusterIds.getLast().getOperateType().equals("destroy")) {
                System.out.println("cluster_id: " + record.getClusterId());
            }
        }

    }

    @Data
    public static class CountResult {
        @Column("cluster_id")
        private String clusterId;
        @Column("cur_core")
        private Integer totalCore;
    }

    /**
     * 检查每一条明细
     */
    @Test
    public void checkDetail() {
        List<CountResult> liushui = cloudDemandDevDBHelper.getRaw(CountResult.class,
                """
                        select cluster_id,
     ifnull(sum(case when operate_type='add' then ck_total_core else -ck_total_core end),0)
     + ifnull(sum(case when operate_type='add' then zk_total_core else -zk_total_core end),0) as cur_core
                        from cloud_demand_data.ap_guangzhou where start_time<'2025-04-08'
                        group by cluster_id
                        order by cur_core desc
                        """);
        List<CountResult> cunliang = ckCloudDemandNewIdcDBHelper.getRaw(CountResult.class,
                """
                        select cluster_id,sum(total_core) as cur_core
                        from cloud_demand.dwd_tc_house_customer_data
                        where region_name='广州' and stat_time='2025-04-07'
                        group by cluster_id
                        order by sum(total_core)  desc
                        """
        );

        // 比较两者
        Map<String, Integer> liushuiMap = ListUtils.toMap(liushui, o -> o.getClusterId(), o -> o.getTotalCore());
        Map<String, Integer> cunliangMap = ListUtils.toMap(cunliang, o -> o.getClusterId(), o -> o.getTotalCore());

        for (Map.Entry<String, Integer> liushuiMapEntry : liushuiMap.entrySet()) {
            if (!cunliangMap.containsKey(liushuiMapEntry.getKey())) {
                if (liushuiMapEntry.getValue() > 0) {
                    System.out.println("cluster_id not exist1: " + liushuiMapEntry.getKey());
                }
            } else {
                Integer c = cunliangMap.get(liushuiMapEntry.getKey());
                Integer l = liushuiMapEntry.getValue();
                if (!c.equals(l)) {
                    System.out.println("cluster_id: " +liushuiMapEntry.getKey() + "c:" + c + ",l:" + l);
                }
            }
        }

        for (Map.Entry<String, Integer> cunliangMapEntry : cunliangMap.entrySet()) {
            if (!liushuiMap.containsKey(cunliangMapEntry.getKey())) {
                System.out.println("cluster_id not exist2: " + cunliangMapEntry.getKey());
            } else {
                Integer c = cunliangMapEntry.getValue();
                Integer l = liushuiMap.get(cunliangMapEntry.getKey());
                if (!c.equals(l)) {
                    System.out.println("cluster_id: " +cunliangMapEntry.getKey() + "c:" + c + ",l:" + l );
                }
            }
        }

    }

    /**
     * 已经检查过了，一个cluster最多只有一个destroy事件
     */
    @Test
    public void handleDestroy() {
        List<TcOrderDO> destoryList = cloudDemandDevDBHelper.getAll(TcOrderDO.class,
                "where operate_type='destroy' order by start_date");

        for (TcOrderDO record : destoryList) {
            List<TcOrderDO> sameClusterIds = cloudDemandDevDBHelper.getAll(TcOrderDO.class,
                    "where cluster_id=? and start_time<=? order by start_time, job_id",
                    record.getClusterId(), record.getStartTime());

            // 从头开始模拟，知道碰到当前的destroy id
            String ckInstanceType = null;
            String ckSubProductType = null;
            String zkInstanceType = null;
            String zkSubProductType = null;

            int ckCpuCore = 0;
            int zkCpuCore = 0;

            int ckTotalCore = 0;
            int zkTotalCore = 0;

            for (TcOrderDO ap : sameClusterIds) {
                if ("destroy".equalsIgnoreCase(ap.getOperateType())) {
                    break;
                }

                // 以最后一个不为空的值为准
                if (StringTools.isNotBlank(ap.getCkInstanceType())) {
                    ckInstanceType = ap.getCkInstanceType();
                }
                if (StringTools.isNotBlank(ap.getCkSubProductType())) {
                    ckSubProductType = ap.getCkSubProductType();
                }
                if (StringTools.isNotBlank(ap.getZkInstanceType())) {
                    zkInstanceType = ap.getZkInstanceType();
                }
                if (StringTools.isNotBlank(ap.getZkSubProductType())) {
                    zkSubProductType = ap.getZkSubProductType();
                }
                if (ap.getCkCpuCore() != null) {
                    ckCpuCore = ap.getCkCpuCore();
                }
                if (ap.getZkCpuCore() != null) {
                    zkCpuCore = ap.getZkCpuCore();
                }


                if ("add".equalsIgnoreCase(ap.getOperateType())) {
                    if (ap.getCkTotalCore() != null) {
                        ckTotalCore += ap.getCkTotalCore();
                    }
                    if (ap.getZkTotalCore() != null) {
                        zkTotalCore += ap.getZkTotalCore();
                    }
                } else if ("reduce".equalsIgnoreCase(ap.getOperateType())) {
                    if (ap.getCkTotalCore() != null) {
                        ckTotalCore -= ap.getCkTotalCore();
                    }
                    if (ap.getZkTotalCore() != null) {
                        zkTotalCore -= ap.getZkTotalCore();
                    }
                }
            }

            // 检查数据
            if (ckTotalCore < 0 || zkTotalCore < 0) {
                System.out.println("cluster_id: " + record.getClusterId() + " num less than 0"
                        + " ckTotalCore:" + ckTotalCore + " zkTotalCore:" + zkTotalCore);
                throw new RuntimeException("wrong data");
            }

            // 更新数据
            record.setCkInstanceType(ckInstanceType);
            record.setCkSubProductType(ckSubProductType);
            record.setZkInstanceType(zkInstanceType);
            record.setZkSubProductType(zkSubProductType);
            record.setCkTotalCore(ckTotalCore);
            record.setZkTotalCore(zkTotalCore);
            record.setCkCpuCore(ckTotalCore > 0 ? (ckCpuCore <= 0 ? 1 : ckCpuCore) : 0);
            record.setZkCpuCore(zkTotalCore > 0 ? (zkCpuCore <= 0 ? 1 : zkCpuCore) : 0);
            record.setNodeNum(ckTotalCore > 0 ? (ckCpuCore / record.getCkCpuCore()) : 0);
            record.setZkNodeNum(zkTotalCore > 0 ? (zkTotalCore / record.getZkCpuCore()) : 0);

            cloudDemandDevDBHelper.update(record);
        }
    }


}
