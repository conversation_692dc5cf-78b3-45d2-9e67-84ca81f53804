package com.pugwoo.dboperate.八大产品.es;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.io.IOUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import com.pugwoo.wooutils.string.StringTools;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.FileInputStream;
import java.util.ArrayList;
import java.util.List;

@SpringBootTest
public class WriteToDB {

    @Resource
    private DBHelper cloudDemandDataDevDBHelper;
    @Resource
    private DBHelper cloudDemandDataIdcDBHelper;

    @Test
    public void test() throws Exception {

        String content1 = IOUtils.readAllAndClose(new FileInputStream("D:\\code-personal\\scripts\\模型预测-8大产品\\大数据\\ES\\ces_idea_daily_sql_result_20250328155953_encode_副本.csv"), "utf-8");

        String content2 = IOUtils.readAllAndClose(new FileInputStream("D:\\code-personal\\scripts\\模型预测-8大产品\\大数据\\ES\\part2.csv"), "utf-8");

        List<EsDailyDO> esDailyDOS = parseContent(content2);
        List<EsDailyDO> esDailyDOS1 = parseContent(content1);

        List<EsDailyDO> all = new ArrayList<>();
        all.addAll(esDailyDOS);
        all.addAll(esDailyDOS1);

     //   cloudDemandDataDevDBHelper.insertBatchWithoutReturnId(all);
        cloudDemandDataIdcDBHelper.insertBatchWithoutReturnId(all);
    }


    private static List<EsDailyDO> parseContent(String content) {
        String[] lines = StringTools.splitLines(content);
        // 不要第一行
        List<EsDailyDO> result = new ArrayList<>();
        for (int i = 1; i < lines.length; i++) {
            String[] strs = lines[i].split(",");
            EsDailyDO esDailyDO = new EsDailyDO();
            esDailyDO.setInstanceId(strs[0]);
            esDailyDO.setRegionId(NumberUtils.parseInt(strs[1]));
            esDailyDO.setZoneId(NumberUtils.parseInt(strs[2]));
            esDailyDO.setUin(strs[3]);
            esDailyDO.setCpu(NumberUtils.parseInt(strs[4]));
            esDailyDO.setMem(NumberUtils.parseInt(strs[5]));
            esDailyDO.setDisk(NumberUtils.parseInt(strs[6]));
            esDailyDO.setStatTime(DateUtils.parseLocalDate(strs[7]).minusDays(1));
            result.add(esDailyDO);
        }
        return result;
    }



}
