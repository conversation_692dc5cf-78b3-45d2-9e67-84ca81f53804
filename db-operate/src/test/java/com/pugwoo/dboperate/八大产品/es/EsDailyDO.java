package com.pugwoo.dboperate.八大产品.es;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDate;

@Data
@ToString
@Table("es_daily")
public class EsDailyDO {

    /** 切片日期<br/>Column: [stat_time] */
    @Column(value = "stat_time")
    private LocalDate statTime;

    /** 集群id<br/>Column: [instance_id] */
    @Column(value = "instance_id")
    private String instanceId;

    /** 地域id<br/>Column: [region_id] */
    @Column(value = "region_id")
    private Integer regionId;

    /** 可用区id<br/>Column: [zone_id] */
    @Column(value = "zone_id")
    private Integer zoneId;

    /** 脱敏的客户名称<br/>Column: [uin] */
    @Column(value = "uin")
    private String uin;

    /** 总的cpu数量<br/>Column: [cpu] */
    @Column(value = "cpu")
    private Integer cpu;

    /** 总的内存数量<br/>Column: [mem] */
    @Column(value = "mem")
    private Integer mem;

    /** 总的存储量<br/>Column: [disk] */
    @Column(value = "disk")
    private Integer disk;

}