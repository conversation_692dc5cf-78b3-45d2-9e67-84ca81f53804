package com.pugwoo.dboperate.八大产品.tc;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@ToString
@Table("cloud_demand_data.tc_order")
public class TcOrderDO {

    @Column(value = "job_id", isKey = true)
    private Integer jobId;

    @Column(value = "cluster_id", isKey = true)
    private String clusterId;

    @Column(value = "user_appid")
    private Long userAppid;

    @Column(value = "user_uin")
    private Long userUin;

    @Column(value = "region")
    private String region;

    @Column(value = "zone")
    private String zone;

    @Column(value = "start_date")
    private LocalDate startDate;

    @Column(value = "start_time")
    private LocalDateTime startTime;

    @Column(value = "operate_type")
    private String operateType;

    @Column(value = "ck_instance_type")
    private String ckInstanceType;

    @Column(value = "ck_sub_product_type")
    private String ckSubProductType;

    @Column(value = "node_num")
    private Integer nodeNum;

    @Column(value = "ck_cpu_core")
    private Integer ckCpuCore;

    @Column(value = "ck_total_core")
    private Integer ckTotalCore;

    @Column(value = "zk_instance_type")
    private String zkInstanceType;

    @Column(value = "zk_sub_product_type")
    private String zkSubProductType;

    @Column(value = "zk_node_num")
    private Integer zkNodeNum;

    @Column(value = "zk_cpu_core")
    private Integer zkCpuCore;

    @Column(value = "zk_total_core")
    private Integer zkTotalCore;

}