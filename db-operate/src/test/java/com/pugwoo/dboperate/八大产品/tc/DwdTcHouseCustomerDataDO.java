package com.pugwoo.dboperate.八大产品.tc;

import lombok.Data;
import lombok.ToString;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;

@Data
@ToString
@Table("dwd_tc_house_customer_data")
public class DwdTcHouseCustomerDataDO {

    /** 日期<br/>Column: [stat_time] */
    @Column(value = "stat_time")
    private String statTime;

    /** 集群id<br/>Column: [cluster_id] */
    @Column(value = "cluster_id")
    private String clusterId;

    /** 集群名称<br/>Column: [cluster_name] */
    @Column(value = "cluster_name")
    private String clusterName;

    /** 客户名称<br/>Column: [customer_short_name] */
    @Column(value = "customer_short_name")
    private String customerShortName;

    @Column(value = "uin")
    private String uin;

    @Column(value = "app_id")
    private String appId;

    /** 地域<br/>Column: [region_name] */
    @Column(value = "region_name")
    private String regionName;

    /** 可用区<br/>Column: [zone_name] */
    @Column(value = "zone_name")
    private String zoneName;

    /** 区域<br/>Column: [area_name] */
    @Column(value = "area_name")
    private String areaName;

    /** 境内外<br/>Column: [customhouse_title] */
    @Column(value = "customhouse_title")
    private String customhouseTitle;

    /** 集群状态<br/>Column: [cluster_status] */
    @Column(value = "cluster_status")
    private String clusterStatus;

    /** 付费类型<br/>Column: [pay_mode] */
    @Column(value = "pay_mode")
    private String payMode;

    /** 创建时间<br/>Column: [create_time] */
    @Column(value = "create_time")
    private String createTime;

    /** 实例规格<br/>Column: [instance_type] */
    @Column(value = "instance_type")
    private String instanceType;

    /** 实例类型<br/>Column: [instance_family] */
    @Column(value = "instance_family")
    private String instanceFamily;

    /** 节点数<br/>Column: [node_num] */
    @Column(value = "node_num")
    private Integer nodeNum;

    /** 单节点核心数<br/>Column: [cpu_core] */
    @Column(value = "cpu_core")
    private Integer cpuCore;

    /** 总核心数<br/>Column: [total_core] */
    @Column(value = "total_core")
    private Integer totalCore;

    private boolean markDelete = false;

}