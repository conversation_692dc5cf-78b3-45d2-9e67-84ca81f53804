package com.pugwoo.dboperate.八大产品.tc;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.string.StringTools;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@SpringBootTest
public class TC回溯日切片 {

    @Resource
    private DBHelper cloudDemandDevDBHelper;
    @Resource
    private DBHelper ckCloudDemandNewIdcDBHelper;
    @Resource
    private DBHelper jxcTxyIdcDBHelper;
    @Resource
    private DBHelper ckStdCrpNewIdcDBHelper;

    private Map<String, StaticZoneDO> zoneMap = new HashMap<>();

    @Test
    public void debug() {
        List<StaticZoneDO> all = jxcTxyIdcDBHelper.getAll(StaticZoneDO.class);
        zoneMap = ListUtils.toMap(all, o -> o.getZone(), o -> o);

        LocalDate date = DateUtils.parseLocalDate("2021-01-03"); // 这是要生成的切片的日期

        List<DwdTcHouseCustomerDataDO> result = generate(date);

        System.out.println(result);
        //int rows = ckCloudDemandNewIdcDBHelper.insertBatchWithoutReturnId(result);
        //System.out.println("finished:" + date + ",count:" + rows);
    }

    @Test
    public void makeSnapshot() {
        // 初始化字典
        List<StaticZoneDO> all = jxcTxyIdcDBHelper.getAll(StaticZoneDO.class);
        zoneMap = ListUtils.toMap(all, o -> o.getZone(), o -> o);

        // LocalDate date = DateUtils.parseLocalDate("2021-01-03");
        LocalDate date = DateUtils.parseLocalDate("2025-02-26"); // 这是要生成的切片的日期

        while (true) {
            List<DwdTcHouseCustomerDataDO> result = generate(date);
            int rows = ckCloudDemandNewIdcDBHelper.insertBatchWithoutReturnId(result);
            System.out.println("finished:" + date + ",count:" + rows);

            // 这里需要等待ck全部写入后再继续
            long count = 0;
            for (int i = 0; i < 60; i++) {
                count = ckCloudDemandNewIdcDBHelper.getCount(DwdTcHouseCustomerDataDO.class,
                        "where stat_time=?", date);
                try {
                    Thread.sleep(3000); // 这里也即无论如何等待3秒
                } catch (InterruptedException ignore) {
                }

                if (count == ((long)result.size())) {
                    break;
                }
            }

            if (count != ((long)result.size())) {
                throw new RuntimeException("ck insert not matched, expect:" + result.size() + ", in db:" + count);
            }

            date = date.minusDays(1);
            if (date.equals(DateUtils.parseLocalDate("2020-12-27"))) { // 也即最早的是2020-12-28
                break;
            }
        }

    }

    private List<DwdTcHouseCustomerDataDO> generate(LocalDate date) {
        // 1. 先拿到要生成的切片日期的往后1天的数据
        List<DwdTcHouseCustomerDataDO> baseList = ckCloudDemandNewIdcDBHelper.getAll(DwdTcHouseCustomerDataDO.class,
                "where stat_time=?", date.plusDays(1));

        Map<String, List<DwdTcHouseCustomerDataDO>> baseMap = ListUtils.toMapList(baseList,
                o -> o.getClusterId(), o -> o);

        // 2. 再拿 date+1的 05:00:00 至 date+2 的 05:00:00 之间的流水，这里已经确认了，历史流水，没有刚刚处于05:00:00的流水
        List<TcOrderDO> orders = cloudDemandDevDBHelper.getAll(TcOrderDO.class,
                "where start_time between ? and ?",
                DateUtils.formatDate(DateUtils.parse(date.plusDays(1) + " 05:00:00")),
                DateUtils.formatDate(DateUtils.parse(date.plusDays(2) + " 05:00:00")));

        // 3. 将流水时间逆序排列（按start_time desc, job_id desc）逆序
        ListUtils.sortDescNullLast(orders, o -> o.getStartTime(), o -> o.getJobId());

        // 4. 按逆序的顺序，对每一条流水，回溯修改第一步的数据，最终得到结果
        List<DwdTcHouseCustomerDataDO> newAdd = new ArrayList<>();
        for (TcOrderDO order : orders) {
            if ("add".equalsIgnoreCase(order.getOperateType())) {
                List<DwdTcHouseCustomerDataDO> dwdTcHouseCustomerDataDOS = baseMap.get(order.getClusterId());
                if (dwdTcHouseCustomerDataDOS != null) {
                    sub(dwdTcHouseCustomerDataDOS, order);
                } else {
                    // 说明：这种情况确认可以忽略
                    // System.out.println("error: cluster_id:" + order.getClusterId() + "add jobId:" + order.getJobId()
                    //    + " and base cluster not exist, ck core:" + order.getCkTotalCore() + "zk core:" + order.getZkTotalCore());
                }
            } else if ("reduce".equalsIgnoreCase(order.getOperateType()) || "destroy".equalsIgnoreCase(order.getOperateType())) {
                List<DwdTcHouseCustomerDataDO> dwdTcHouseCustomerDataDOS = baseMap.get(order.getClusterId());
                List<DwdTcHouseCustomerDataDO> add = add(dwdTcHouseCustomerDataDOS, order);
                newAdd.addAll(add);
            } else {
                throw new RuntimeException("unknown operate type");
            }
        }

        baseList.addAll(newAdd);

        // 设置statTime
        ListUtils.forEach(baseList, o -> o.setStatTime(DateUtils.formatDate(date)));

        // 移除掉已删除的
        baseList = ListUtils.filter(baseList, o -> !o.isMarkDelete());
        return baseList;
    }

    private List<DwdTcHouseCustomerDataDO> add(List<DwdTcHouseCustomerDataDO> dwdTcHouseCustomerDataDOS, TcOrderDO order) {
        List<DwdTcHouseCustomerDataDO> newAdd = new ArrayList<>();

        if (order.getCkTotalCore() != null && order.getCkTotalCore() > 0) {
            int ckTotalCore = order.getCkTotalCore();
            // 先看看有没有机型匹配上的
            List<DwdTcHouseCustomerDataDO> matched = ListUtils.filter(dwdTcHouseCustomerDataDOS,
                    o -> Objects.equals(o.getInstanceFamily(), order.getCkInstanceType()));
            ckTotalCore = doAdd(matched, ckTotalCore);
            // 再试试加到没匹配上的
            ckTotalCore = doAdd(dwdTcHouseCustomerDataDOS, ckTotalCore);

            if (ckTotalCore > 0) { // 如果还有剩余，就当做完全是新的处理
                newAdd.add(build(true, ckTotalCore, order));
            }
        }

        if (order.getZkTotalCore() != null && order.getZkTotalCore() > 0) {
            int zkTotalCore = order.getZkTotalCore();
            List<DwdTcHouseCustomerDataDO> matched = ListUtils.filter(dwdTcHouseCustomerDataDOS,
                    o -> Objects.equals(o.getInstanceFamily(), order.getZkInstanceType()));
            zkTotalCore = doAdd(matched, zkTotalCore);
            zkTotalCore = doAdd(dwdTcHouseCustomerDataDOS, zkTotalCore);
            if (zkTotalCore > 0) {
                newAdd.add(build(false, zkTotalCore, order));
            }
        }

        return newAdd;
    }

    private String getZoneName(TcOrderDO order) {
        if (StringTools.isNotBlank(order.getZone())) {
            return order.getZone();
        }

        // 只有当销毁事件时，zone为空，所以从流水上再去拿
        return cloudDemandDevDBHelper.getRawOne(String.class,
                "select zone from cloud_demand_data.tc_order where cluster_id=? and zone!='' order by start_time desc limit 1",
                order.getClusterId());
    }

    private DwdTcHouseCustomerDataDO build(boolean isCk, int totalCore, TcOrderDO order) {
        DwdTcHouseCustomerDataDO dwd = new DwdTcHouseCustomerDataDO();
        dwd.setClusterId(order.getClusterId());
        dwd.setClusterName("");
        dwd.setUin(order.getUserUin() == null ? null : order.getUserUin().toString());
        dwd.setAppId(order.getUserAppid() == null ? null : order.getUserAppid().toString());

        String customerShortName = null;
        if (StringTools.isNotBlank(dwd.getAppId())) {
            String rawOne = ckStdCrpNewIdcDBHelper.getRawOne(String.class,
                    "select customer_short_name from dwd_txy_appid_info_cf where appid=?",
                    order.getUserAppid());
            if (StringTools.isNotBlank(rawOne)) {
                customerShortName = rawOne;
            }
        }
        if (StringTools.isNotBlank(customerShortName) && StringTools.isNotBlank(dwd.getUin())) {
            String rawOne = ckStdCrpNewIdcDBHelper.getRawOne(String.class,
                    "select customer_short_name from dwd_txy_appid_info_cf where uin=?",
                    order.getUserUin());
            if (StringTools.isNotBlank(rawOne)) {
                customerShortName = rawOne;
            }
        }
        if (StringTools.isBlank(customerShortName)) {
            customerShortName = "(空值)";
        }

        dwd.setCustomerShortName(customerShortName);

        String zoneName = getZoneName(order);
        if (StringTools.isBlank(zoneName)) {
            throw new RuntimeException("cluster_id:" + order.getClusterId() + " zone is blank");
        }
        StaticZoneDO staticZoneDO = zoneMap.get(zoneName);
        if (staticZoneDO == null) {
            throw new RuntimeException("zone:" + zoneName + " not exist");
        }

        dwd.setZoneName(staticZoneDO.getZoneName());
        dwd.setRegionName(staticZoneDO.getRegionName());
        dwd.setAreaName(staticZoneDO.getAreaName());
        dwd.setCustomhouseTitle(staticZoneDO.getCustomhouseTitle());
        dwd.setClusterStatus("");
        dwd.setPayMode("");
        dwd.setCreateTime("");
        dwd.setInstanceType(isCk ? order.getCkInstanceType() : order.getZkInstanceType());
        dwd.setInstanceFamily(isCk ? order.getCkInstanceType() : order.getZkInstanceType());
        if (isCk) {
            dwd.setCpuCore(order.getCkCpuCore() == null || order.getCkCpuCore() == 0 ? 1 : order.getCkCpuCore());
        } else {
            dwd.setCpuCore(order.getZkCpuCore() == null || order.getZkCpuCore() == 0 ? 1 : order.getZkCpuCore());
        }
        dwd.setTotalCore(totalCore);
        dwd.setNodeNum(totalCore / dwd.getCpuCore());

        return dwd;
    }

    private int doAdd(List<DwdTcHouseCustomerDataDO> matched, int totalCore) {
        if (ListUtils.isEmpty(matched)) {
            return totalCore;
        }

        DwdTcHouseCustomerDataDO first = matched.getFirst();
        if (first.getTotalCore() == null) {
            first.setTotalCore(totalCore);
        } else {
            first.setTotalCore(first.getTotalCore() + totalCore);
        }
        return 0;
    }

    private void sub(List<DwdTcHouseCustomerDataDO> dwdTcHouseCustomerDataDOS, TcOrderDO order) {
        if (order.getCkTotalCore() != null && order.getCkTotalCore() > 0) {
            int ckTotalCore = order.getCkTotalCore();
            // 先减相同机型的
            List<DwdTcHouseCustomerDataDO> matched = ListUtils.filter(dwdTcHouseCustomerDataDOS,
                    o -> Objects.equals(o.getInstanceFamily(), order.getCkInstanceType()));
            ckTotalCore = doSub(matched, ckTotalCore);
            // 再减匹配不到的机型
            ckTotalCore = doSub(dwdTcHouseCustomerDataDOS, ckTotalCore);
            //if (ckTotalCore > 0) { // 这种就直接忽略了，现在已经扣到0了
                //throw new RuntimeException("error: cluster_id:" + order.getClusterId() + "add jobId:" + order.getJobId()
                //        + "DwdTcHouseCustomerDataDO ck not enough, still need:" + ckTotalCore);
            //}
        }
        if (order.getZkTotalCore() != null && order.getZkTotalCore() > 0) {
            int zkTotalCore = order.getZkTotalCore();
            List<DwdTcHouseCustomerDataDO> matched = ListUtils.filter(dwdTcHouseCustomerDataDOS,
                    o -> Objects.equals(o.getInstanceFamily(), order.getZkInstanceType()));
            zkTotalCore = doSub(matched, zkTotalCore);
            // 再匹配其它的
            zkTotalCore = doSub(dwdTcHouseCustomerDataDOS, zkTotalCore);
            //if (zkTotalCore > 0) { // 这种就直接忽略了，现在已经扣到0了
                //throw new RuntimeException("error: cluster_id:" + order.getClusterId() + "add jobId:" + order.getJobId()
                //        + "DwdTcHouseCustomerDataDO zk not enough, still need:" + zkTotalCore);
            //}
        }

        // 如果order的ck和zk已经是0了，那么删除
        for (DwdTcHouseCustomerDataDO d : dwdTcHouseCustomerDataDOS) {
            if (d.getTotalCore() == null || d.getTotalCore() <= 0) {
                d.setMarkDelete(true);
            }
        }
    }

    private int doSub(List<DwdTcHouseCustomerDataDO> matched, int totalCore) {
        if (matched == null) {
            return totalCore;
        }
        for (DwdTcHouseCustomerDataDO dwdTcHouseCustomerDataDO : matched) {
            if (dwdTcHouseCustomerDataDO.getTotalCore() == null || dwdTcHouseCustomerDataDO.getTotalCore() == 0) {
                continue;
            }
            if (totalCore == 0) {
                break;
            }
            if (dwdTcHouseCustomerDataDO.getTotalCore() >= totalCore) {
                dwdTcHouseCustomerDataDO.setTotalCore(dwdTcHouseCustomerDataDO.getTotalCore() - totalCore);
                if (dwdTcHouseCustomerDataDO.getTotalCore() == 0) {
                    dwdTcHouseCustomerDataDO.setNodeNum(0);
                    dwdTcHouseCustomerDataDO.setCpuCore(0);
                } else {
                    if (dwdTcHouseCustomerDataDO.getCpuCore() == null || dwdTcHouseCustomerDataDO.getCpuCore() < 1) {
                        dwdTcHouseCustomerDataDO.setCpuCore(1);
                    }
                    dwdTcHouseCustomerDataDO.setNodeNum(dwdTcHouseCustomerDataDO.getTotalCore() / dwdTcHouseCustomerDataDO.getCpuCore());
                }
                totalCore = 0;
            } else {
                totalCore -= dwdTcHouseCustomerDataDO.getTotalCore();
                dwdTcHouseCustomerDataDO.setTotalCore(0);
                dwdTcHouseCustomerDataDO.setNodeNum(0);
                dwdTcHouseCustomerDataDO.setCpuCore(0);
            }
        }
        return totalCore;
    }

}
