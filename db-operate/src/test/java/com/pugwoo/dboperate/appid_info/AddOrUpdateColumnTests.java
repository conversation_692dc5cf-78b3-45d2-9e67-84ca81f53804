package com.pugwoo.dboperate.appid_info;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.string.StringTools;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@SpringBootTest
public class AddOrUpdateColumnTests {

    @Resource
    private DBHelper cloudDemandIdcDBHelper;

    @Test
    public void testAddIncomeSalesDesc() {
        Long lastId = 0L;

        while(true) {
            List<IndustryReportAppidInfoLatestDO> all = cloudDemandIdcDBHelper.getAll(IndustryReportAppidInfoLatestDO.class,
                    "where id>? order by id limit 2000", lastId);
            if (all.isEmpty()) {
                break;
            }
            for (IndustryReportAppidInfoLatestDO i : all) {
                fillIncomeSalesDesc(i);
            }

            // 只要update一个字段就好了
            List<IndustryReportAppidInfoLatestDO> toUpdate = ListUtils.transform(all, o -> {
                IndustryReportAppidInfoLatestDO l = new IndustryReportAppidInfoLatestDO();
                l.setId(o.getId());
                l.setIncomeSalesDesc(o.getIncomeSalesDesc());
                return l;
            });

            cloudDemandIdcDBHelper.update(toUpdate);
            System.out.println(DateUtils.format(LocalDateTime.now()) + " update done, lastId:" + all.getLast().getId() + ",size:" + toUpdate.size());

            lastId = all.getLast().getId();
        }
    }

    private void fillIncomeSalesDesc(IndustryReportAppidInfoLatestDO one) {
        if (one == null) {
            return;
        }
        if (StringTools.isBlank(one.getFullInfoJson())) {
            return;
        }

        Map<String, Object> map = JSON.parseToMap(one.getFullInfoJson());
        Object decodeDataMap = map.get("decodeDataMap");
        if (decodeDataMap instanceof Map) {
            Map<String, Object> decodeDataMapMap = (Map<String, Object>) decodeDataMap;
            Object value = decodeDataMapMap.get("income_sales_desc");
            if (value != null) {
                one.setIncomeSalesDesc(value.toString());
            }
        }
    }

    @Test
    public void testUpdateCustomerName() {
        Long lastId = 0L;

        while(true) {
            List<IndustryReportAppidInfoLatestDO> all = cloudDemandIdcDBHelper.getAll(IndustryReportAppidInfoLatestDO.class,
                    "where id>? order by id limit 2000", lastId);
            if (all.isEmpty()) {
                break;
            }
            for (IndustryReportAppidInfoLatestDO i : all) {
                fillCustomerName(i);
            }

            // 只要update一个字段就好了
            List<IndustryReportAppidInfoLatestDO> toUpdate = ListUtils.transform(
                    ListUtils.filter(all, o -> StringTools.isNotBlank(o.getCustomerName())),
                    o -> {
                IndustryReportAppidInfoLatestDO l = new IndustryReportAppidInfoLatestDO();
                l.setId(o.getId());
                l.setCustomerName(o.getCustomerName());
                return l;
            });

            cloudDemandIdcDBHelper.update(toUpdate);
            System.out.println(DateUtils.format(LocalDateTime.now()) + " update done, lastId:" + all.getLast().getId() + ",size:" + toUpdate.size());

            lastId = all.getLast().getId();
        }
    }

    private void fillCustomerName(IndustryReportAppidInfoLatestDO one) {
        if (one == null) {
            return;
        }
        if (StringTools.isBlank(one.getFullInfoJson())) {
            return;
        }

        Map<String, Object> map = JSON.parseToMap(one.getFullInfoJson());
        Object decodeDataMap = map.get("decodeDataMap");
        if (decodeDataMap instanceof Map) {
            Map<String, Object> decodeDataMapMap = (Map<String, Object>) decodeDataMap;
            Object value = decodeDataMapMap.get("customer_info");
            if (value instanceof Map) {
                Object name = ((Map) value).get("name");
                if (name != null && StringTools.isNotBlank(name.toString())) {
                    one.setCustomerName(name.toString());
                }
            }
        }
    }


}
