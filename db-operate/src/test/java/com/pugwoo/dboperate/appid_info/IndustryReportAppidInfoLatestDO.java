package com.pugwoo.dboperate.appid_info;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.util.Date;

@Data
@ToString
@Table("industry_report_appid_info_latest")
public class IndustryReportAppidInfoLatestDO {

    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    @Column(value = "create_time")
    private Date createTime;

    @Column(value = "update_time")
    private Date updateTime;

    @Column(value = "appid")
    private Long appid;

    /** 行业部门<br/>Column: [industry_dept] */
    @Column(value = "industry_dept")
    private String industryDept;

    /** 客户uin<br/>Column: [uin] */
    @Column(value = "uin")
    private Long uin;

    /** 客户名称<br/>Column: [customer_name] */
    @Column(value = "customer_name")
    private String customerName;

    /** 客户简称<br/>Column: [customer_short_name] */
    @Column(value = "customer_short_name")
    private String customerShortName;

    /** 客户类型，0个人1企业<br/>Column: [customer_type] */
    @Column(value = "customer_type")
    private Integer customerType;

    /** 战区<br/>Column: [war_zone] */
    @Column(value = "war_zone")
    private String warZone;

    /** 主销售名<br/>Column: [business_manager] */
    @Column(value = "business_manager")
    private String businessManager;

    /** 主销售名的组织架构<br/>Column: [business_manager_oa_dept] */
    @Column(value = "business_manager_oa_dept")
    private String businessManagerOaDept;

    /** 主销售名的组织架构path<br/>Column: [business_manager_oa_path] */
    @Column(value = "business_manager_oa_path")
    private String businessManagerOaPath;

    /** uin类型，0内部1外部<br/>Column: [uin_type] */
    @Column(value = "uin_type")
    private Integer uinType;

    /** 架构师rtx<br/>Column: [system_architect] */
    @Column(value = "system_architect")
    private String systemArchitect;

    /** 架构师的组织架构<br/>Column: [system_architect_oa_dept] */
    @Column(value = "system_architect_oa_dept")
    private String systemArchitectOaDept;

    /** 架构师的组织架构path<br/>Column: [system_architect_oa_path] */
    @Column(value = "system_architect_oa_path")
    private String systemArchitectOaPath;

    /** 客户信息cid<br/>Column: [cid] */
    @Column(value = "cid")
    private String cid;

    /** 集团id<br/>Column: [gid] */
    @Column(value = "gid")
    private String gid;

    /** 集团名称<br/>Column: [gname] */
    @Column(value = "gname")
    private String gname;

    @Column(value = "income_sales_desc")
    private String incomeSalesDesc;

    /** 完整的用户信息json<br/>Column: [full_info_json] */
    @Column(value = "full_info_json")
    private String fullInfoJson;

}