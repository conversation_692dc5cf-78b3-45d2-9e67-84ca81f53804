package com.pugwoo.dboperate.archived.中长期;

import com.pugwoo.dboperate.模型预测调优.utils.RunForecastCommonUtils;
import com.pugwoo.dboperate.模型预测调优.utils.RunForecastResultDTO;
import com.pugwoo.wooutils.lang.DateUtils;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.LocalDate;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@SpringBootTest
public class RunForecast中长期 {

    @Resource
    private RunForecastCommonUtils runForecastCommonUtils;

    @Test
    public void test中长期新增退回按月方案901() throws Exception {
        Map<String, Object> commonParam = new HashMap<>();
        commonParam.put("category", "方案901：全部总量-月切保留客户维度(机型规格)-月度ARIMAX");
        commonParam.put("serialInterval", "MONTH");
        commonParam.put("sourceType", "INDUSTRY_INNER"); // 这个参数在自定义sql之前无效
        commonParam.put("billType", "ALL"); // 这个参数在自定义sql之前无效
        commonParam.put("alName", "ARIMAXL");
        commonParam.put("alArg", "0,1,3,0,1,6,0.2"); // 当值为-1(只有一个值)时表示自动选择参数
        commonParam.put("retAlArgs", "0,1,3,0.2"); // 退回采用0,1,3
        commonParam.put("transInstanceType", "false");
        commonParam.put("groupDimType", "UIN_INSTANCE_MODEL_ZONE_NAME"); // 这个参数在自定义sql之前无效
        commonParam.put("isRemoveSpike", "false");
        commonParam.put("forecastScope", 1);
        commonParam.put("predictN", 18);

        // LocalDate curMonth = DateUtils.getFirstDayOfMonth(new Date());
        String curMonth = "2024-07-01"; // 因预测需要，限定在2024-07
        List<RunForecastResultDTO> result = runForecastCommonUtils.run("2022-10-01", curMonth,
                "custom_sql/中长期新增退回_方案901.sql", commonParam);
    }

    @Test
    public void test中长期新增退回按月方案902() throws Exception {
        Map<String, Object> commonParam = new HashMap<>();
        commonParam.put("category", "方案902：全部总量-月切保留客户维度(机型规格)-月度ARIMAX");
        commonParam.put("serialInterval", "MONTH");
        commonParam.put("sourceType", "INDUSTRY_INNER"); // 这个参数在自定义sql之前无效
        commonParam.put("billType", "ALL"); // 这个参数在自定义sql之前无效
        commonParam.put("alName", "ARIMAXL");
        commonParam.put("alArg", "0,1,3,0,1,6,0.2"); // 当值为-1(只有一个值)时表示自动选择参数
        commonParam.put("retAlArgs", "0,1,3,0.2"); // 退回采用0,1,3
        commonParam.put("transInstanceType", "false");
        commonParam.put("groupDimType", "UIN_INSTANCE_MODEL_ZONE_NAME"); // 这个参数在自定义sql之前无效
        commonParam.put("isRemoveSpike", "false");
        commonParam.put("forecastScope", 1);

        LocalDate curMonth = DateUtils.getFirstDayOfMonth(new Date());
        List<RunForecastResultDTO> result = runForecastCommonUtils.run("2022-10-01", curMonth.toString(), "custom_sql/中长期新增退回_方案902.sql", commonParam);
    }

    @Test
    public void test中长期新增退回按月方案903() throws Exception {
        Map<String, Object> commonParam = new HashMap<>();
        commonParam.put("category", "方案903：全部总量-月切保留客户维度(机型规格)-月度ARIMAX");
        commonParam.put("serialInterval", "MONTH");
        commonParam.put("sourceType", "INDUSTRY_INNER"); // 这个参数在自定义sql之前无效
        commonParam.put("billType", "ALL"); // 这个参数在自定义sql之前无效
        commonParam.put("alName", "ARIMAXL");
        commonParam.put("alArg", "0,1,3,0,1,6,0.2"); // 当值为-1(只有一个值)时表示自动选择参数
        commonParam.put("retAlArgs", "0,1,3,0.2"); // 退回采用0,1,3
        commonParam.put("transInstanceType", "false");
        commonParam.put("groupDimType", "UIN_INSTANCE_MODEL_ZONE_NAME"); // 这个参数在自定义sql之前无效
        commonParam.put("isRemoveSpike", "false");
        commonParam.put("forecastScope", 1);

        LocalDate curMonth = DateUtils.getFirstDayOfMonth(new Date());
        List<RunForecastResultDTO> result = runForecastCommonUtils.run("2022-10-01", curMonth.toString(), "custom_sql/中长期新增退回_方案903.sql", commonParam);
    }
}
