package com.pugwoo.dboperate.archived.自研部分.自研CVM需求准确率;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import com.pugwoo.wooutils.string.StringTools;
import lombok.Data;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.YearMonth;
import java.util.List;
import java.util.Map;

@SpringBootTest
public class ZiyanCVMAccuracyRateTest {

    @Resource
    private DBHelper ckYuntiDemandNewIdcDBHelper;
    @Resource
    private ZiyanCommonSql2 ziyanCommonSql2;

    @Data
    public static class ResultDTO {
        @Column(value = "year")
        private String year;
        @Column(value = "month")
        private String month;
        @Column(value = "stime")
        private String stime;
        @Column(value = "region_name")
        private String regionName;
        @Column(value = "gins_family")
        private String ginsFamily;
        @Column(value = "total_cores")
        private Long totalCores;
        @Column(value = "applied_cores")
        private Long appliedCores;
    }

    @Test
    public void test() {
        LocalDate start = DateUtils.parseLocalDate("2023-01-01");
        while (start.isBefore(DateUtils.parseLocalDate("2024-02-01"))) {

            YearMonth yearMonth = YearMonth.from(start);
            calAccuracyRate(yearMonth);

            start = start.plusMonths(1);
        }
    }

    public void calAccuracyRate(YearMonth yearMonth) {

        // YearMonth yearMonth = YearMonth.of(2024, 1); // 要统计的准确率的年月

        // 1. 取要统计的月份的下一个月的5号的切片的当前月份的执行量
        LocalDate executeSnapshotDate = DateUtils.parseLocalDate(yearMonth.toString())
                .plusMonths(1).plusDays(4);
        String sql = ziyanCommonSql2.getSql();
        sql = sql.replace("${snapshotDate}", executeSnapshotDate.toString());
        sql = sql.replace("${timeRange}", " and toYear(toDate(use_time))=" + yearMonth.getYear()
                + " and toMonth(toDate(use_time))=" + yearMonth.getMonthValue());

        List<ResultDTO> executed = ckYuntiDemandNewIdcDBHelper.getRaw(ResultDTO.class, sql);

        Map<String, ResultDTO> executeMap = ListUtils.toMap(executed,
                o -> StringTools.join("@", o.getRegionName(), o.getGinsFamily()), o -> o);

        // 2.1 再取提前1月的需求量
        LocalDate before1Month = DateUtils.parseLocalDate(yearMonth.toString());
        sql = ziyanCommonSql2.getSql();
        sql = sql.replace("${snapshotDate}", before1Month.toString());
        sql = sql.replace("${timeRange}", " and toYear(toDate(use_time))=" + yearMonth.getYear()
                + " and toMonth(toDate(use_time))=" + yearMonth.getMonthValue());
        List<ResultDTO> before1MonthList = ckYuntiDemandNewIdcDBHelper.getRaw(ResultDTO.class, sql);
        Map<String, ResultDTO> before1MonthMap = ListUtils.toMap(before1MonthList,
                o -> StringTools.join("@", o.getRegionName(), o.getGinsFamily()), o -> o);

        // 2.2 再取提前2个月的需求量
        LocalDate before2Month = DateUtils.parseLocalDate(yearMonth.toString()).minusMonths(1);
        sql = ziyanCommonSql2.getSql();
        sql = sql.replace("${snapshotDate}", before2Month.toString());
        sql = sql.replace("${timeRange}", " and toYear(toDate(use_time))=" + yearMonth.getYear()
                + " and toMonth(toDate(use_time))=" + yearMonth.getMonthValue());
        List<ResultDTO> before2MonthList = ckYuntiDemandNewIdcDBHelper.getRaw(ResultDTO.class, sql);
        Map<String, ResultDTO> before2MonthMap = ListUtils.toMap(before2MonthList,
                o -> StringTools.join("@", o.getRegionName(), o.getGinsFamily()), o -> o);

        // 2.3 再取提前3个月的需求量
        LocalDate before3Month = DateUtils.parseLocalDate(yearMonth.toString()).minusMonths(2);
        sql = ziyanCommonSql2.getSql();
        sql = sql.replace("${snapshotDate}", before3Month.toString());
        sql = sql.replace("${timeRange}", " and toYear(toDate(use_time))=" + yearMonth.getYear()
                + " and toMonth(toDate(use_time))=" + yearMonth.getMonthValue());
        List<ResultDTO> before3MonthList = ckYuntiDemandNewIdcDBHelper.getRaw(ResultDTO.class, sql);
        Map<String, ResultDTO> before3MonthMap = ListUtils.toMap(before3MonthList,
                o -> StringTools.join("@", o.getRegionName(), o.getGinsFamily()), o -> o);

        // 3. 刷新执行map的需求量，按5 3 2
        for (ResultDTO dto : executed) {
            String key = StringTools.join("@", dto.getRegionName(), dto.getGinsFamily());
            ResultDTO before1MonthDTO = before1MonthMap.get(key);
            ResultDTO before2MonthDTO = before2MonthMap.get(key);
            ResultDTO before3MonthDTO = before3MonthMap.get(key);
            BigDecimal sum = new BigDecimal(0);
            if (before1MonthDTO != null) {
                sum = sum.add(new BigDecimal(before1MonthDTO.getTotalCores()).multiply(BigDecimal.valueOf(0.2)));
            }
            if (before2MonthDTO != null) {
                sum = sum.add(new BigDecimal(before2MonthDTO.getTotalCores()).multiply(BigDecimal.valueOf(0.3)));
            }
            if (before3MonthDTO != null) {
                sum = sum.add(new BigDecimal(before3MonthDTO.getTotalCores()).multiply(BigDecimal.valueOf(0.5)));
            }
            dto.setTotalCores(sum.longValue());
        }

        // 4. 计算准确率
        BigDecimal accuracyRate = new BigDecimal(0);
        BigDecimal totalExecutedCore = NumberUtils.sum(executed, ResultDTO::getAppliedCores);
        for (ResultDTO dto : executed) {
            BigDecimal executedCore = BigDecimal.valueOf(dto.getAppliedCores());
            if (executedCore.compareTo(BigDecimal.ZERO) == 0) {
                continue;
            }

            BigDecimal demandCore = BigDecimal.valueOf(dto.getTotalCores());

            BigDecimal min = executedCore.compareTo(demandCore) > 0 ? demandCore : executedCore;
            BigDecimal max = executedCore.compareTo(demandCore) > 0 ? executedCore : demandCore;

            BigDecimal accuracy = min.divide(max, 6, BigDecimal.ROUND_HALF_UP)
                    .multiply(executedCore).divide(totalExecutedCore, 6, BigDecimal.ROUND_HALF_UP);
            accuracyRate = accuracyRate.add(accuracy);
        }

        // 5. 打印最终结果
        System.out.println(yearMonth + " 532准确率：" + accuracyRate.multiply(BigDecimal.valueOf(100)) + "%");

    }

}
