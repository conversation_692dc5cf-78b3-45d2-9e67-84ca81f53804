package com.pugwoo.dboperate.archived.中长期.公司级别;

import com.pugwoo.wooutils.string.StringTools;
import lombok.Data;
import lombok.ToString;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;

import java.math.BigDecimal;
import java.util.function.Function;

/**
 * 中长期的需求和执行数据
 */
@Data
@ToString
@Table("longterm_demand_and_execute_data")
public class LongtermDemandAndExecuteDataDO {

    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    /** 周数，从1开始到53<br/>Column: [week_num] */
    @Column(value = "week_num")
    private Integer weekNum;

    /** 年份<br/>Column: [year] */
    @Column(value = "year")
    private Integer year;

    /** 资源类型，cvm、物理机<br/>Column: [resource_type] */
    @Column(value = "resource_type")
    private String resourceType;

    /** 业务类型，云业务，自研业务，自研上云<br/>Column: [biz_type] */
    @Column(value = "biz_type")
    private String bizType;

    /** 指标周<br/>Column: [quota_week] */
    @Column(value = "quota_week")
    private Integer quotaWeek;

    /** 年度总需求，核<br/>Column: [anual_total_demand_core] */
    @Column(value = "anual_total_demand_core")
    private BigDecimal anualTotalDemandCore;

    /** 年度总需求，台<br/>Column: [anual_total_demand_unit] */
    @Column(value = "anual_total_demand_unit")
    private BigDecimal anualTotalDemandUnit;

    /** 年度总完成，核<br/>Column: [anual_total_delivery_core] */
    @Column(value = "anual_total_delivery_core")
    private BigDecimal anualTotalDeliveryCore;

    /** 年度总完成，台<br/>Column: [anual_total_delivery_unit] */
    @Column(value = "anual_total_delivery_unit")
    private BigDecimal anualTotalDeliveryUnit;

    /** 年度累计到当周已经交付的核心数<br/>Column: [anual_cumulative_delivery_core] */
    @Column(value = "anual_cumulative_delivery_core")
    private BigDecimal anualCumulativeDeliveryCore;

    /** 年度累计到当周已经交付的台数<br/>Column: [anual_cumulative_delivery_unit] */
    @Column(value = "anual_cumulative_delivery_unit")
    private BigDecimal anualCumulativeDeliveryUnit;

    /** 项目类型<br/>Column: [project_name] */
    @Column(value = "project_name")
    private String projectName;

    @Column(value = "plan_product_name")
    private String planProductName;

    /** 部门，可选<br/>Column: [dept] */
    @Column(value = "dept")
    private String dept;

    /** 自定义事业群，可选<br/>Column: [custom_bg] */
    @Column(value = "custom_bg")
    private String customBg;

    /** 机型<br/>Column: [machine_type] */
    @Column(value = "machine_type")
    private String machineType;

    /** 物理机机型族，可选<br/>Column: [machine_type_family] */
    @Column(value = "machine_type_family")
    private String machineTypeFamily;

    public static Function<LongtermDemandAndExecuteDataDO, String> mergeKey =
            o -> StringTools.join("@",
                    o.getWeekNum(), o.getYear(),
                    o.getResourceType(), o.getBizType(),
                    o.getQuotaWeek(),
                    o.getProjectName(), o.getPlanProductName(), o.getDept(), o.getCustomBg(),
                    o.getMachineType(), o.getMachineTypeFamily());

}