package com.pugwoo.dboperate.archived.中长期.公司级别;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.cache.HiSpeedCache;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 负责交付数据
 */
@Service
public class DeliveryDataService {

    @Resource
    private DBHelper ckCubesIdcDBHelper;
    @Resource
    private DBHelper yuntiIdcDBHelper;
    @Resource
    private DBHelper erpIdcDBHelper;

    /**
     * 获得自定义事业群
     */
    private List<YuntiDemandCustomBgDO> getCustomBg() {
        return yuntiIdcDBHelper.getAll(YuntiDemandCustomBgDO.class);
    }

    /**
     * 查询当年度，在指定日期之前的交付数据
     * @param utilDate 包含当天23:59:59
     */
    @HiSpeedCache(expireSecond = 120, keyScript = "args[0] + args[1]")
    public List<DeliveryDataDTO> getPhysicalData(int year, String utilDate, List<ResPlanHolidayWeekDO> weeks) {
        String begin = year + "-01-01 00:00:00";
        String end = utilDate + " 23:59:59";

        List<YuntiDemandCustomBgDO> customBg = getCustomBg();
        Map<String, String> planProductToDept = ListUtils.toMap(customBg, o -> o.getPlanProductName(), o -> o.getDeptName());
        Map<String, String> planProductToCustomBg = ListUtils.toMap(customBg, o -> o.getPlanProductName(), o -> o.getCustomBg());

        StringBuilder sb = new StringBuilder("(case ");
        for (ResPlanHolidayWeekDO week : weeks) {
            sb.append(" when formatDateTime(cloud_delivery_time, '%Y-%m-%d') between '")
                    .append(DateUtils.formatDate(week.getStart())).append("' and '")
                    .append(DateUtils.formatDate(week.getEnd())).append("' then ").append(week.getWeek());
        }
        sb.append(" else -1 end) as quota_week");
        String weekCaseWhen = sb.toString();

        List<DeliveryDataDTO> list = ckCubesIdcDBHelper.getRaw(DeliveryDataDTO.class,
                """
                        select
                            biz_type,plan_product_name,machine_type, project_name, quota_week,
                            sum(unit) as unit,
                            sum(core) as core
                        from
                        (SELECT obs_business_type as business_type,
                                cloud_business_type,
                                quota_plan_product_name as plan_product_name,
                                proj_set_name as project_name,
                                device_type as machine_type,
                                quota_week,
                                count(1)            AS unit,
                                sum(cpu_logic_core) as core,
                                (case when obs_business_type='云业务' then '云业务'
                                      when obs_business_type='内部业务' and cloud_business_type != '自研上云' then '自研业务'
                                      when cloud_business_type='自研上云' then '自研上云' else '' end) as biz_type
                         from cubes.demandMarket
                         where DAY = (select max(DAY) from cubes.demandMarket)
                           and cloud_delivery_time between ? and ?
                         group by obs_business_type, cloud_business_type, quota_plan_product_name, device_type, project_name,
                         """ + weekCaseWhen +
                          """
                        ) group by biz_type,plan_product_name,machine_type, project_name, quota_week
                         having biz_type!=''
                        """, begin, end);

        // 后处理字段关联
        for (DeliveryDataDTO dto : list) {
            dto.setDept(planProductToDept.getOrDefault(dto.getPlanProductName(), "(空值)"));
            dto.setCustomBg(planProductToCustomBg.getOrDefault(dto.getPlanProductName(), "(空值)"));
        }

        List<Map> types = erpIdcDBHelper.getRaw(Map.class, "select name,DeviceFamilyName from bas_stratege_device_type");
        Map<String, String> deviceFamilyMap = ListUtils.toMap(types, o -> o.get("name").toString(), o -> o.get("DeviceFamilyName").toString());
        for (DeliveryDataDTO dto : list) {
            dto.setMachineTypeFamily(deviceFamilyMap.getOrDefault(dto.getMachineType(), "(空值)"));
        }

        return list;
    }

    @HiSpeedCache(expireSecond = 120, keyScript = "args[0] + args[1]")
    public List<DeliveryDataDTO> getCvmData(int year, String utilDate, List<ResPlanHolidayWeekDO> weeks) {
        String begin = year + "-01-01 00:00:00";
        String end = utilDate + " 23:59:59";

        List<YuntiDemandCustomBgDO> customBg = getCustomBg();
        Map<String, String> planProductToDept = ListUtils.toMap(customBg, o -> o.getPlanProductName(), o -> o.getDeptName());
        Map<String, String> planProductToCustomBg = ListUtils.toMap(customBg, o -> o.getPlanProductName(), o -> o.getCustomBg());

        StringBuilder sb = new StringBuilder("(case ");
        for (ResPlanHolidayWeekDO week : weeks) {
            sb.append(" when formatDateTime(create_time, '%Y-%m-%d') between '")
                    .append(DateUtils.formatDate(week.getStart())).append("' and '")
                    .append(DateUtils.formatDate(week.getEnd())).append("' then ").append(week.getWeek());
        }
        sb.append(" else -1 end) as quota_week");
        String weekCaseWhen = sb.toString();


        List<DeliveryDataDTO> list = ckCubesIdcDBHelper.getRaw(DeliveryDataDTO.class,
                """
                        select
                            biz_type,plan_product_name,machine_type,project_name,quota_week,
                            sum(unit) as unit,
                            sum(core) as core
                        from
                        (
                        select if(bg.BgType = 1 or project_name = '自研上云', '内部业务', '云业务') AS obs_business_type,
                               PlanProductName as plan_product_name,
                               project_name as project_name,
                               substring(instance_type, 1, position(instance_type, '.') - 1) as machine_type,
                               quota_week,
                               count(1) AS unit, sum(cpu) as core,
                               (case when obs_business_type='云业务' then '云业务'
                                     when obs_business_type='内部业务' then '自研业务' else '' end) as biz_type
                        from cubes.yunti_cvm_market cvm global left join sharedb.bas_obs_bg bg ON bg.BgName = cvm.bg_name
                        where create_time between ? AND ?
                              and pool = '自研云' and virtual_dept !='算力平台'  and order_type != 'CA申领'
                        group by obs_business_type,plan_product_name,machine_type,project_name,
                        """ + weekCaseWhen +
                        """
                            ) group by biz_type,plan_product_name,machine_type,project_name,quota_week
                            having biz_type!=''
                        """, begin, end);

        // 后处理字段关联
        for (DeliveryDataDTO dto : list) {
            dto.setDept(planProductToDept.getOrDefault(dto.getPlanProductName(), "(空值)"));
            dto.setCustomBg(planProductToCustomBg.getOrDefault(dto.getPlanProductName(), "(空值)"));
        }

        List<Map> types = yuntiIdcDBHelper.getRaw(Map.class, "select CvmInstanceTypeCode,CvmInstanceGroup,CvmInstanceType from bas_obs_cloud_cvm_type");
        Map<String, String> deviceFamilyMap = ListUtils.toMap(types, o -> o.get("CvmInstanceTypeCode").toString(), o -> o.get("CvmInstanceGroup").toString());
        Map<String, String> deviceTypeToNameMap = ListUtils.toMap(types, o -> o.get("CvmInstanceTypeCode").toString(), o -> o.get("CvmInstanceType").toString());
        for (DeliveryDataDTO dto : list) {
            dto.setMachineTypeFamily(deviceFamilyMap.getOrDefault(dto.getMachineType(), "(空值)"));
            dto.setMachineType(deviceTypeToNameMap.getOrDefault(dto.getMachineType(), dto.getMachineType()));
        }

        return list;
    }


}
