select
    toYear(toDate(use_time)) as year,
    toMonth(toDate(use_time)) as month,
      subtractDays(addMonths(toDate(concat(toString(toYear(toDate(use_time))), '-',
          case when toMonth(toDate(use_time))<10 then '0' else '' end, toString(toMonth(toDate(use_time))), '-01')), 1), 1) as stime,
    ${region} as region_name,
    ${instance_type} as gins_family,
     sum(applied_core_amount) as cores
from dwd_yunti_cvm_demand_forecast_item_df
where stat_date='${snapshotDate}'
  and dept_id NOT IN (32,1129) -- 去掉运营资源中心、算力平台两个部门
  and resource_pool_type = 0 -- 自研池

    ${excludePlanProductIds}

   -- and custom_bg_name='WXG微信事业群' -- 61.2%
   -- and custom_bg_name='IEG互动娱乐事业群' --65%
   -- and custom_bg_name='CSIG云与智慧产业事业群' -- 40.7%
   -- and custom_bg_name='CDG企业发展事业群' -- 53.15%
   -- and custom_bg_name='TEG-数据平台部' -- 48.3%

  --  and plan_product_id not in (858,54,50,1194,712,473,1158,28,567,1195,747,68,1159,91,87,563,39,2886,109,559)
 -- and plan_product_id in (858,54,50,1194,712,473,1158,28,567,1195,747,68,1159,91,87)

  -- 本次只预测主力机型主力地域
  and region_name in ('上海','南京','深圳','广州','天津')
  and splitByChar('.', instance_model)[1] in ('SA2','S5','IT5','S6t','S5t','I6t','SA3','S6','ITA3','IT5c','D2','D3','DA2','DA3','PNV4ne','PNV4','GI3X')

    ${spikeCondition} -- 毛刺剔除条件

    ${timeRange} -- 时间范围

group by toYear(toDate(use_time)),toMonth(toDate(use_time)),${region},${instance_type}
having cores>0