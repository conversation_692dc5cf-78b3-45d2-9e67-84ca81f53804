package com.pugwoo.dboperate.archived.中长期.公司级别;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DemandDataService {

    @Resource
    private DBHelper ckStdCrpNewIdcDBHelper;

    public List<LongtermDemandAndExecuteDataDO> getAll() {
        List<DwsSopAllYearDemandReportDO> all =
                ckStdCrpNewIdcDBHelper.getAll(DwsSopAllYearDemandReportDO.class, "where business_type!='(空值)'");
        return ListUtils.transform(all, o -> {
            LongtermDemandAndExecuteDataDO one = new LongtermDemandAndExecuteDataDO();
            one.setWeekNum(o.getWeek());
            one.setYear(o.getYear());
            one.setResourceType(o.getResourceType());
            one.setBizType(o.getBusinessType());
            one.setAnualTotalDemandCore(o.getTotalCoreNum());
            one.setAnualTotalDemandUnit(o.getTotalNum());
            one.setDept(o.getDeptName());
            one.setCustomBg(o.getCustomBgName());
            one.setPlanProductName(o.getPlanProductName());
            one.setProjectName(o.getObsProjectType());
            one.setQuotaWeek(o.getIndexWeek());
            if ("物理机".equals(o.getResourceType())) {
                one.setMachineType(o.getDeviceType());
                one.setMachineTypeFamily(o.getDeviceFamily());
            } else if ("CVM".equals(o.getResourceType())) {
                one.setMachineType(o.getInstanceType());
                one.setMachineTypeFamily(o.getInstanceFamily());
            }
            return one;
        });
    }

}
