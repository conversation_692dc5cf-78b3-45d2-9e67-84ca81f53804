package com.pugwoo.dboperate.archived.自研部分.自研需求预测模型;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dboperate.forecast_compute.entity.ForecastComputeTaskDO;
import com.pugwoo.dboperate.forecast_compute.entity.ForecastComputeTaskInputTestDatasetDO;
import com.pugwoo.dboperate.forecast_compute.enums.SerialIntervalEnum;
import com.pugwoo.dboperate.forecast_compute.enums.TaskTypeEnum;
import com.pugwoo.dboperate.forecast_compute.model.CreateTaskDTO;
import com.pugwoo.dboperate.forecast_compute.model.CreateTaskInputDataDTO;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.io.IOUtils;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import com.pugwoo.wooutils.net.Browser;
import com.pugwoo.wooutils.net.HttpResponse;
import com.pugwoo.wooutils.string.StringTools;
import lombok.Data;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;

@SpringBootTest
public class ForecastTest_自研_废弃 {

    @Resource
    private DBHelper ckYuntiDemandNewIdcDBHelper;
    @Resource
    private DBHelper cloudDemandCommonDevDBHelper;
    @Resource
    private ZiyanCommonSql ziyanCommonSql;

    private boolean isCalNewCore = true; // 是否计算新增的准确率，目前只有新增，没有退回

    private int mode = 2; // 0=不分，1=分地域+机型大类，2=分国家+机型族

    private List<Long> excludePlanProductIds = new ArrayList<>();

    @Test
    public void test() throws Exception {
        cloudDemandCommonDevDBHelper.setSlowSqlWarningValve(100000);
        ckYuntiDemandNewIdcDBHelper.setSlowSqlWarningValve(100000);

        BigDecimal rate1 = calAccuracyRateAvg(1);
        System.out.println("rate1:" + rate1);
        BigDecimal rate2 = calAccuracyRateAvg(2);
        System.out.println("rate2:" + rate2);
        BigDecimal rate3 = calAccuracyRateAvg(3);
        System.out.println("rate3:" + rate3);

        System.out.println("准确率:" + rate3.multiply(BigDecimal.valueOf(0.5)).add(rate2.multiply(BigDecimal.valueOf(0.3)))
                .add(rate1.multiply(BigDecimal.valueOf(0.2))));
    }

    private BigDecimal calAccuracyRateAvg(int beforeNMonth) throws Exception {
        LocalDate start = DateUtils.parseLocalDate("2023-01");
        List<BigDecimal> accuracyRates = ListUtils.newArrayList();

        Map<Integer, LocalDate> taskIds = new LinkedHashMap<>();
        while (start.isBefore(DateUtils.parseLocalDate("2023-11-01"))) { // 最长填到当月的1号
            Integer taskId = createTask("自研CVM需求预测", start, beforeNMonth); // 提前N个月预测
            taskIds.put(taskId, start);
            start = start.plusMonths(1);
        }

        for (Integer taskId : taskIds.keySet()) {
            // 等待任务完成
            while(true) {
                ForecastComputeTaskDO task = cloudDemandCommonDevDBHelper.getOne(ForecastComputeTaskDO.class,
                        "where id=?", taskId);
                if (!"DONE".equals(task.getStatus())) {
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException ignored) {
                    }
                } else {
                    break;
                }
            }

            BigDecimal accuracyRate = calAccuracyRate(taskId, null).getAccuracyRate();
            accuracyRates.add(accuracyRate);
            System.out.println(taskIds.get(taskId) + "准确率:" + accuracyRate + "% taskId:" + taskId);
        }

        // System.out.println("--------------");
        // System.out.println("平均准确率:" + NumberUtils.avg(accuracyRates, 6) + "%");

        return NumberUtils.avg(accuracyRates, 6);
    }


    private Integer createTask(String taskName, LocalDate predictMonth, int beforeNMonth) throws Exception {
        CreateTaskDTO task = new CreateTaskDTO();

        task.setCreateUser("nickxie");
        task.setTaskName(taskName);
        task.setTaskType(TaskTypeEnum.PREDICT.getCode());
        task.setSerialInterval(SerialIntervalEnum.MONTH.getCode());
        task.setInputDims(2);
        task.setInputDimsName("机型族,地域");
        task.setIsAutoFillData(true);
        task.setPredictIndexStart(1);
        task.setPredictIndexEnd(6); // 预测未来1到6个月

        CreateTaskDTO.Algorithm algorithm = new CreateTaskDTO.Algorithm(
                //       CreateTaskDTO.AlgorithmEnum.MA.getName(), ListUtils.newList(3));
                //       CreateTaskDTO.AlgorithmEnum.MAX.getName(), ListUtils.newList(3));
                //       CreateTaskDTO.AlgorithmEnum.ARIMA.getName(), ListUtils.newList(0,1,6));
                CreateTaskDTO.AlgorithmEnum.ARIMAX.getName(), ListUtils.newList(0,1,3,0,1,6,0.2));
        task.setAlgorithms(ListUtils.newList(algorithm));


        String sql = ziyanCommonSql.getSql(isCalNewCore, mode, excludePlanProductIds, null);

        task.setInputDataDatasource("ck.yunti_demand");

        LocalDate inputBefore = predictMonth.minusMonths(beforeNMonth - 1);
        inputBefore = inputBefore.withDayOfMonth(1);

        // 是否剔除弹性异常点，这个放在这里是因为它的输入和测试集sql不同
        String sqlForInput = "";

        sqlForInput = sql.replace("${timeRange}",
                "and toDate(use_time) < '" + DateUtils.formatDate(inputBefore) +"'"); // 用提前的数据来预测
        sqlForInput = sqlForInput.replace("${snapshotDate}", DateUtils.formatDate(inputBefore.minusDays(1)));

        task.setInputDataSql(sqlForInput);

        List<InputDTO> all = ckYuntiDemandNewIdcDBHelper.getRaw(InputDTO.class, sqlForInput);

        task.setInputData(ListUtils.transform(all, o -> {
            CreateTaskInputDataDTO d = new CreateTaskInputDataDTO();
            d.setDate(o.getStatTime());
            d.setValue(o.getCores());
            d.setDim1(o.getGinsFamily());
            d.setDim2(o.getRegionName());
            d.setDim3("");
            d.setDim4("");
            d.setDim5("");
            return d;
        }));

        // 测试集，测试集只留下要测试的数据接口，其它的不要，【所以】这里要测试的是5月，也就是提前1个月做预测

        // 是否剔除弹性异常点，这个放在这里是因为它的输入和测试集sql不同
        String testSql = sql.replace("${timeRange}",
                "and toYYYYMM(toDate(use_time))='" + DateUtils.format(predictMonth, "yyyyMM") +"'");
        testSql = testSql.replace("${snapshotDate}",
                DateUtils.format(predictMonth.withDayOfMonth(1).plusMonths(1).minusDays(1)));
        task.setTestDatasetSql(testSql);

        // 由于查询量非常大，这里分时间段进行查询，再合并起来
        List<InputDTO> test = ckYuntiDemandNewIdcDBHelper.getRaw(InputDTO.class, testSql);

        task.setTestDataset(ListUtils.transform(test, o -> {
            CreateTaskInputDataDTO d = new CreateTaskInputDataDTO();
            d.setDate(o.getStatTime());
            d.setValue(o.getCores());
            d.setDim1(o.getGinsFamily());
            d.setDim2(o.getRegionName());
            d.setDim3("");
            d.setDim4("");
            d.setDim5("");
            return d;
        }));

        Browser browser = new Browser();
        browser.addRequestHeader("Cookie", IOUtils.readClasspathResourceAsString("/废弃或已完成/db-operate/resources/exp-crp-cookie.txt").trim());

        HttpResponse resp = browser.postJson(
                "https://exp-crp.woa.com/cloud-demand-common/ops/submitForecastTask", task);
        SubmitResult parsed = JSON.parse(resp.getContentString(), SubmitResult.class);

        return parsed.getTaskId();
    }

    @Data
    public static class InputDTO {
        @Column("stime")
        private Date statTime;
        @Column("gins_family")
        private String ginsFamily;
        @Column("region_name")
        private String regionName;
        @Column("cores")
        private BigDecimal cores;
    }

    @Data
    public static class SubmitResult {
        private String uuid;
        private Integer taskId;
        private Boolean success;
    }


    @Data
    public static class CalAccuracyRateResult {
        List<ForecastComputeTaskInputTestDatasetVO> testDataset;
        BigDecimal accuracyRate;
    }

    public CalAccuracyRateResult calAccuracyRate(Integer taskId, String testDatasetExtraWhere) {
        // 1. 先读取测试集
        // 【特别说明】
        // 1) 现在是以真实值作为权重，也就是说，如果有预测，实际结果没有出现，还是认为准的，所以这个计算是按这种算法来简化了
        // 2) 现在是每次只测试一个月，所以直接对结果进行加权，不用分月份
        List<ForecastComputeTaskInputTestDatasetVO> testDataset = cloudDemandCommonDevDBHelper.getAll(
                ForecastComputeTaskInputTestDatasetVO.class,
                "where task_id=? " + (testDatasetExtraWhere == null ? "" : testDatasetExtraWhere), taskId);

        // 2. 再读取预测结果
        List<PredictResultDTO> predictResults = cloudDemandCommonDevDBHelper.getRaw(PredictResultDTO.class,
                "SELECT a.date,a.value,b.dim1,b.dim2,b.dim3,b.dim4,b.dim5\n" +
                        "FROM `forecast_compute_task_run_output` a\n" +
                        "LEFT JOIN `forecast_compute_task_run` b\n" +
                        "ON a.`task_run_id`=b.`id`\n" +
                        "WHERE a.task_id=?", taskId);

        // 3.1 合并数据
        testDataset = ListUtils.merge(testDataset, predictResults,
                o -> o.groupKey(), o -> o.groupKey(),
                (test, predict) -> {
                    if (ListUtils.isNotEmpty(test) && ListUtils.isNotEmpty(predict)) {
                        test.get(0).setPredictValue(predict.get(0).getValue());
                        return test.get(0);
                    } else {
                        return null;
                    }
                });
        testDataset = ListUtils.filter(testDataset, o -> o != null);

        // 4. 计算准确率
        ListUtils.forEach(testDataset, o -> {
            BigDecimal min = NumberUtils.min(o.getPredictValue(), o.getValue());
            BigDecimal max = NumberUtils.max(o.getPredictValue(), o.getValue());
            if (max.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal rate = NumberUtils.divide(min.multiply(BigDecimal.valueOf(100)), max, 6);
                o.setAccuracyRate(rate.doubleValue());
            } else {
                BigDecimal rate = BigDecimal.valueOf(100);
                o.setAccuracyRate(rate.doubleValue());
            }
        });

        // 5. 输出准确率，加权【现在是每次只测试一个月，所以直接对结果进行加权，不用分月份】
        BigDecimal total = NumberUtils.sum(testDataset, ForecastComputeTaskInputTestDatasetDO::getValue);
        ListUtils.forEach(testDataset, o -> {
            //   if (total.compareTo(BigDecimal.ZERO) <= 0) {
            //       o.setWeightedAccuracyRate(0d);
            //   } else {
            double rate = NumberUtils.divide(o.getValue(), total, 6)
                    .multiply(BigDecimal.valueOf(o.getAccuracyRate())).doubleValue();
            o.setWeightedAccuracyRate(rate);
            //  }
        });

        CalAccuracyRateResult result = new CalAccuracyRateResult();
        result.setTestDataset(testDataset);
        result.setAccuracyRate(NumberUtils.sum(testDataset, o -> o.getWeightedAccuracyRate()));

        return result;
    }

    @Data
    public static class ForecastComputeTaskInputTestDatasetVO extends  ForecastComputeTaskInputTestDatasetDO {

        /**预测值*/
        private BigDecimal predictValue;

        /**准确率百分比，已经乘以100*/
        private double accuracyRate;

        /**加权的百分比*/
        private double weightedAccuracyRate;


        /**当前测算的整体准确率*/
        private double globalAccuracyRate;
        /**移除了当前组合之后的准确率*/
        private double globalAccuracyRateIfRemove;
        /**差异，这个值为正表示这项预测做得好，为负表示预测做得不好*/
        private double diffAccuracyRate;


        public String groupKey() {
            return StringTools.join("&", getDate(), getDim1(), getDim2(), getDim3(), getDim4(), getDim5());
        }
    }

    @Data
    public static class PredictResultDTO {
        /** 时间序列日期<br/>Column: [date] */
        @Column(value = "date")
        private LocalDate date;
        /** 值<br/>Column: [value] */
        @Column(value = "value")
        private BigDecimal value;
        @Column(value = "dim1")
        private String dim1;
        @Column(value = "dim2")
        private String dim2;
        @Column(value = "dim3")
        private String dim3;
        @Column(value = "dim4")
        private String dim4;
        @Column(value = "dim5")
        private String dim5;

        public String groupKey() {
            return StringTools.join("&", getDate(), getDim1(), getDim2(), getDim3(), getDim4(), getDim5());
        }
    }
}
