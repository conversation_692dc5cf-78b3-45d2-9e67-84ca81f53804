package com.pugwoo.dboperate.archived.自研部分.自研需求预测模型;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dboperate.common.ForecastCommonService;
import com.pugwoo.dboperate.common.ReadFileUtils;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.string.StringTools;
import lombok.Data;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.List;

@Service
public class ZiyanCommonSql {

    @Resource
    private DBHelper cloudDemandCommonDevDBHelper;
    @Resource
    private DBHelper erpIdcDBHelper;
    @Resource
    private ForecastCommonService forecastCommonService;
    @Resource
    private DBHelper obsIdcDBHelper;

    @Data
    public static class InstanceType2DeviceGroupDTO {
        @Column("cvm_instance_type")
        private String cvmInstanceType;
        @Column("device_group")
        private String deviceGroup;
    }

    @Data
    public static class City2CountryDTO {
        @Column("CityName")
        private String cityName;
        @Column("CountryChinese")
        private String countryChinese;
    }

    @Data
    public static class SpikeDTO {
        private Integer year;
        private Integer month;
        private Long planProductId;
        private String regionName;
        private String ginsFamily;
    }

    public String getSpikeSql() {
        String sql = ReadFileUtils.read("forecast_input_region_instance_type_spike.sql");
        sql = sql.replace("${region}", "city_name");

        // 机型 -> 实例族
        List<InstanceType2DeviceGroupDTO> instanceType2DeviceGroup =
                obsIdcDBHelper.getRaw(InstanceType2DeviceGroupDTO.class,
                        """
                                select distinct CvmInstanceTypeCode as cvm_instance_type,CvmInstanceGroup as device_group from bas_obs_cloud_cvm_type
                             """);
        StringBuilder instanceTypeCaseWhen = new StringBuilder("(case ");
        for (InstanceType2DeviceGroupDTO dto : instanceType2DeviceGroup) {
            instanceTypeCaseWhen.append("WHEN splitByChar('.', instance_model)[1]='").append(dto.getCvmInstanceType()).append("' THEN '").append(dto.getDeviceGroup()).append("' ");
        }
        instanceTypeCaseWhen.append(" else if(splitByChar('.', instance_model)[1] IS NULL OR splitByChar('.', instance_model)[1] = '', instance_model, splitByChar('.', instance_model)[1]) end) ");
        sql = sql.replace("${instance_type}", instanceTypeCaseWhen.toString());

        sql = sql.replace("${spikeCondition}", "");
        return sql;
    }

    public String getSql(boolean isCalNewCore, int mode, List<Long> excludePlanProductIds,
                         List<SpikeDTO> spikes) {
        // mark 退回的先不弄

        if (mode == 0) {
            String sql = ReadFileUtils.read("forecast_input_total_as_all.sql");
            sql = sql.replace("${excludePlanProductIds}", getExcludePlanProductIds(excludePlanProductIds));
            sql = sql.replace("${spikeCondition}", "");
            return sql;
        } else if (mode == 1) {
            String sql = ReadFileUtils.read("forecast_input_region_instance_type.sql");
            sql = sql.replace("${region}", "city_name");
            sql = sql.replace("${instance_type}", "instance_type");
            sql = sql.replace("${excludePlanProductIds}", getExcludePlanProductIds(excludePlanProductIds));
            sql = sql.replace("${spikeCondition}", "");
            return sql;
        } else if (mode == 2) {
            String sql = ReadFileUtils.read("forecast_input_region_instance_type.sql");

            // 机型 -> 机型族
            List<InstanceType2DeviceGroupDTO> instanceType2DeviceGroup =
                    cloudDemandCommonDevDBHelper.getRaw(InstanceType2DeviceGroupDTO.class,
                            """
                                    SELECT distinct cvm_instance_type,device_group FROM `dim_instance_device_group_mapping`
                                    WHERE cvm_instance_type!=''
                                 """);
            StringBuilder instanceTypeCaseWhen = new StringBuilder("(case ");
            for (InstanceType2DeviceGroupDTO dto : instanceType2DeviceGroup) {
                instanceTypeCaseWhen.append("WHEN instance_type='").append(dto.getCvmInstanceType()).append("' THEN '").append(dto.getDeviceGroup()).append("' ");
            }
            instanceTypeCaseWhen.append(" else if(instance_type IS NULL OR instance_type = '', instance_model, instance_type) end) ");
            sql = sql.replace("${instance_type}", instanceTypeCaseWhen.toString());

            // 地域 -> 国家
            List<City2CountryDTO> list = erpIdcDBHelper.getRaw(City2CountryDTO.class,
                    """
                               select distinct CityName,CountryChinese from bas_cmdb_city
                            """);
            StringBuilder regionCaseWhen = new StringBuilder("(case ");
            for (City2CountryDTO dto : list) {
                regionCaseWhen.append("WHEN city_name='").append(dto.getCityName()).append("' THEN '").append(dto.getCountryChinese()).append("' ");
            }
            regionCaseWhen.append(" else if(city_name IS NULL OR city_name = '', zone_name, city_name) end) ");
            sql = sql.replace("${region}", regionCaseWhen.toString());

            sql = sql.replace("${excludePlanProductIds}", getExcludePlanProductIds(excludePlanProductIds));
            sql = sql.replace("${spikeCondition}", "");

            return sql;
        } else if (mode == 3) {
            String sql = ReadFileUtils.read("forecast_input_region_instance_type.sql");
            sql = sql.replace("${region}", "city_name");

            // 增加一个机型收敛
            String instanceTypeMergeCaseWhen = forecastCommonService.getInstanceTypeMergeCaseWhenForYunti();
            sql = sql.replace("${instance_type}", instanceTypeMergeCaseWhen);

            sql = sql.replace("${excludePlanProductIds}", getExcludePlanProductIds(excludePlanProductIds));
            sql = sql.replace("${spikeCondition}", "");
            return sql;
        } else if (mode == 4) {
            String sql = ReadFileUtils.read("forecast_input_region_instance_type.sql");
            sql = sql.replace("${region}", "city_name");

            // 机型 -> 实例族
            List<InstanceType2DeviceGroupDTO> instanceType2DeviceGroup =
                    obsIdcDBHelper.getRaw(InstanceType2DeviceGroupDTO.class,
                            """
                                    select distinct CvmInstanceTypeCode as cvm_instance_type,CvmInstanceGroup as device_group from bas_obs_cloud_cvm_type
                                 """);
            StringBuilder instanceTypeCaseWhen = new StringBuilder("(case ");
            for (InstanceType2DeviceGroupDTO dto : instanceType2DeviceGroup) {
                instanceTypeCaseWhen.append("WHEN splitByChar('.', instance_model)[1]='").append(dto.getCvmInstanceType()).append("' THEN '").append(dto.getDeviceGroup()).append("' ");
            }
            instanceTypeCaseWhen.append(" else if(splitByChar('.', instance_model)[1] IS NULL OR splitByChar('.', instance_model)[1] = '', instance_model, splitByChar('.', instance_model)[1]) end) ");
            sql = sql.replace("${instance_type}", instanceTypeCaseWhen.toString());

            sql = sql.replace("${excludePlanProductIds}", getExcludePlanProductIds(excludePlanProductIds));
            sql = sql.replace("${spikeCondition}", "");
            return sql;
        } else if (mode == 5) { // 相比于4，多了一个毛刺剔除
            String sql = ReadFileUtils.read("forecast_input_region_instance_type.sql");
            sql = sql.replace("${region}", "city_name");

            // 机型 -> 实例族
            List<InstanceType2DeviceGroupDTO> instanceType2DeviceGroup =
                    obsIdcDBHelper.getRaw(InstanceType2DeviceGroupDTO.class,
                            """
                                    select distinct CvmInstanceTypeCode as cvm_instance_type,CvmInstanceGroup as device_group from bas_obs_cloud_cvm_type
                                 """);
            StringBuilder instanceTypeCaseWhen = new StringBuilder("(case ");
            for (InstanceType2DeviceGroupDTO dto : instanceType2DeviceGroup) {
                instanceTypeCaseWhen.append("WHEN splitByChar('.', instance_model)[1]='").append(dto.getCvmInstanceType()).append("' THEN '").append(dto.getDeviceGroup()).append("' ");
            }
            instanceTypeCaseWhen.append(" else if(splitByChar('.', instance_model)[1] IS NULL OR splitByChar('.', instance_model)[1] = '', instance_model, splitByChar('.', instance_model)[1]) end) ");
            sql = sql.replace("${instance_type}", instanceTypeCaseWhen.toString());

            sql = sql.replace("${excludePlanProductIds}", getExcludePlanProductIds(excludePlanProductIds));

            // 加毛刺的剔除
            sql = sql.replace("${spikeCondition}", getSpkieSqlCondition(spikes, instanceTypeCaseWhen.toString()));

            return sql;
        }

        return "";
    }

    private String getSpkieSqlCondition(List<SpikeDTO> spikes, String instanceTypeCaseWhen) {
        if (ListUtils.isEmpty(spikes)) {
            return "";
        }

        StringBuilder sb = new StringBuilder();
        sb.append(" and (toYear(toDate(use_time)),toMonth(toDate(use_time)),plan_product_id,city_name," + instanceTypeCaseWhen + ") ");
        sb.append(" not in (");
        for (SpikeDTO spike : spikes) {
            sb.append("(").append(spike.getYear()).append(",").append(spike.getMonth()).append(",").append(spike.getPlanProductId()).append(",'").append(spike.getRegionName()).append("','").append(spike.getGinsFamily()).append("'),");
        }
        sb.deleteCharAt(sb.length() - 1);
        sb.append(")");

        return sb.toString();
    }

    private String getExcludePlanProductIds(List<Long> excludePlanProductIds) {
        if (ListUtils.isEmpty(excludePlanProductIds)) {
            return "";
        }

        StringBuilder sb = new StringBuilder();
        sb.append(" and plan_product_id not in (");
        sb.append(StringTools.join(",", excludePlanProductIds));
        sb.append(")");
        return sb.toString();
    }

}
