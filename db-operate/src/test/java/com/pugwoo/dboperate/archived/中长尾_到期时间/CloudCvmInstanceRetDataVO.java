package com.pugwoo.dboperate.archived.中长尾_到期时间;

import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
public class CloudCvmInstanceRetDataVO {

    @Column(value = "uin")
    private String uin;

    @Column(value = "instance_id")
    private String instanceId;

    @Column(value = "customhouse_title")
    private String customhouseTitle;

    @Column(value = "region_name")
    private String regionName;

    @Column(value = "zone_name")
    private String zoneName;

    @Column(value = "instance_model")
    private String instanceModel;

    @Column(value = "instance_type")
    private String instanceType;

    @Column(value = "cpu_core_num")
    private String cpuCoreNum;

    @Column(value = "buy_date")
    private String buyDate;

    @Column(value = "buy_year")
    private String buyYear;

    @Column(value = "buy_month")
    private String buyMonth;

    @Column(value = "buy_year_month")
    private String buyYearMonth;

    @Column(value = "deadline_date")
    private String deadlineDate;

    @Column(value = "deadline_year")
    private String deadlineYear;

    @Column(value = "deadline_month")
    private String deadlineMonth;

    @Column(value = "deadline_year_month")
    private String deadlineYearMonth;

    @Column(value = "pay_days")
    private String payDays;

    @Column(value = "pay_months")
    private String payMonths;

    @Column(value = "pay_years")
    private String payYears;

    @Column(value = "is_ret_on_time")
    private String isRetOnTime;

}
