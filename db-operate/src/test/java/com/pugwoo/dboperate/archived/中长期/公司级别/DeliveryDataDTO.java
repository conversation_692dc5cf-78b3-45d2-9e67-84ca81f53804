package com.pugwoo.dboperate.archived.中长期.公司级别;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.wooutils.string.StringTools;
import lombok.Data;

import java.math.BigDecimal;
import java.util.function.Function;

/**
 * 说明：这里经过思考，到底是用type+数值，还是直接变量名逐个type来分，而且用中文，不想再为这个分类起英文别名
 * 理由：这个是一个内部使用的DTO，便捷性更高于扩展性
 *
 * 但是如果涉及到粗粒度到细粒度的转换时，这里的扩展性又变得更加重要了，所以还是得用属性才行
 *
 * 扩展性还是高于便捷性阿，除非真的是不需要再迭代，不然便捷性是非常临时的，扩展性实际上也没有多不便捷，只是看熟练不熟练
 *
 */
@Data
public class DeliveryDataDTO {

    @Column("biz_type")
    private String bizType;

    @Column("unit")
    private BigDecimal unit;

    @Column("core")
    private BigDecimal core;

    @Column("project_name")
    private String projectName;

    @Column("plan_product_name")
    private String planProductName;

    @Column("quota_week")
    private Integer quotaWeek;

    // 后关联
    private String dept;

    // 后关联
    private String customBg;

    @Column("machine_type")
    private String machineType;

    // 后关联
    private String machineTypeFamily;

    public static Function<DeliveryDataDTO, String> mergeKey = o ->
            StringTools.join("@", o.getBizType(), o.getDept(), o.getProjectName(),
                    o.getCustomBg(), o.getMachineType(), o.getMachineTypeFamily(), o.getQuotaWeek());

}
