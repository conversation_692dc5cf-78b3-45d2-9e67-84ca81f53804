package com.pugwoo.dboperate.archived.用户分析;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.lang.DateUtils;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.LocalDate;

@SpringBootTest
public class NewUserStatsTest {

    @Resource
    private DBHelper ckStdCrpNewIdcDBHelper;

    @Test
    public void run() {
        ckStdCrpNewIdcDBHelper.setSlowSqlWarningValve(100000);
        LocalDate start = DateUtils.parseLocalDate("2022-01-31");
        LocalDate end = DateUtils.parseLocalDate("2025-02-18");
        while(!start.isAfter(end)) {
            Long count = count(start);
            System.out.println(start + ":" + count);
            start = start.plusDays(1).plusMonths(1).minusDays(1);
        }
    }
    
    private long count(LocalDate monthEndDate) {

        Long count = ckStdCrpNewIdcDBHelper.getRawOne(Long.class,
                """
                        select count(distinct uin) from dwd_txy_scale_df
                        where stat_time=?
                          and uin global not in (
                            select distinct uin from dwd_txy_scale_df where stat_time<=? and cur_bill_service_core>0
                                                   and cpu_or_gpu = 'CPU' and biz_type = 'cvm'
                                                  and app_role != 'LH'
                                                  and instance_type not like 'RS%' and instance_type not like 'RM%'
                                                                         and stat_time in (
                                    SELECT toDate(formatDateTime(subtractDays(addMonths(toDate('2020-01-01'), number + 1), 1), '%Y-%m-%d')) AS last_day_of_month
                                    FROM numbers(dateDiff('month', toDate('2020-01-01'), toDate('2050-12-31')) + 1)
                                )
                        ) and change_bill_core_from_last_month>0
                          and cpu_or_gpu = 'CPU' and biz_type = 'cvm'
                          and app_role != 'LH'
                          and instance_type not like 'RS%' and instance_type not like 'RM%'
                        """, monthEndDate, monthEndDate.withDayOfMonth(1).minusDays(1));
        return count;
    }

    /**

     CVM：
     2022-01-31:16868
     2022-02-28:15656
     2022-03-31:22264
     2022-04-30:9333
     2022-05-31:8347
     2022-06-30:7798
     2022-07-31:7124
     2022-08-31:7352
     2022-09-30:6918
     2022-10-31:7911
     2022-11-30:12353
     2022-12-31:5584
     2023-01-31:4977
     2023-02-28:12064
     2023-03-31:16950
     2023-04-30:16260
     2023-05-31:20434
     2023-06-30:6744
     2023-07-31:6120
     2023-08-31:5835
     2023-09-30:5712
     2023-10-31:5806
     2023-11-30:8059
     2023-12-31:6345
     2024-01-31:5784
     2024-02-29:2191
     2024-03-31:6325
     2024-04-30:6231
     2024-05-31:6668
     2024-06-30:6273
     2024-07-31:6322
     2024-08-31:6266
     2024-09-30:6144
     2024-10-31:6659
     2024-11-30:8284
     2024-12-31:6730
     2025-01-31:5917

     LH：
     2022-01-31:78282
     2022-02-28:79805
     2022-03-31:184217
     2022-04-30:77395
     2022-05-31:67218
     2022-06-30:74597
     2022-07-31:51373
     2022-08-31:61629
     2022-09-30:68646
     2022-10-31:79947
     2022-11-30:95002
     2022-12-31:54766
     2023-01-31:43959
     2023-02-28:65487
     2023-03-31:82809
     2023-04-30:76398
     2023-05-31:75001
     2023-06-30:72403
     2023-07-31:61117
     2023-08-31:56318
     2023-09-30:59681
     2023-10-31:64623
     2023-11-30:72836
     2023-12-31:55534
     2024-01-31:76548
     2024-02-29:33993
     2024-03-31:76951
     2024-04-30:60055
     2024-05-31:54137
     2024-06-30:48684


     *
     *
     */

}
