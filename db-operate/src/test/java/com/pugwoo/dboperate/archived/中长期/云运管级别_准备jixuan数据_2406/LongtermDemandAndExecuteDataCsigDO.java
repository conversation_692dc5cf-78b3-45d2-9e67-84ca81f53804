package com.pugwoo.dboperate.archived.中长期.云运管级别_准备jixuan数据_2406;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import com.pugwoo.wooutils.string.StringTools;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.function.Function;

/**
 * CISG需求和执行数据
 */
@Data
@ToString
@Table("longterm_demand_and_execute_data_csig")
public class LongtermDemandAndExecuteDataCsigDO {

    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    /** 周数，从1开始到53<br/>Column: [week_num] */
    @Column(value = "week_num")
    private Integer weekNum;

    /** 年份<br/>Column: [year] */
    @Column(value = "year")
    private Integer year;

    /** 行业<br/>Column: [industry_dept] */
    @Column(value = "industry_dept", insertValueScript = "''")
    private String industryDept;

    /** 年度总需求，核<br/>Column: [anual_total_demand_core] */
    @Column(value = "annual_total_demand_core", insertValueScript = "0")
    private BigDecimal anualTotalDemandCore;

    /** 年度总需求，台<br/>Column: [anual_total_demand_unit] */
    @Column(value = "annual_total_demand_unit", insertValueScript = "0")
    private BigDecimal anualTotalDemandUnit;

    /** 年度总完成，核<br/>Column: [anual_total_delivery_core] */
    @Column(value = "annual_total_delivery_core", insertValueScript = "0")
    private BigDecimal anualTotalDeliveryCore;

    /** 年度总完成，台<br/>Column: [anual_total_delivery_unit] */
    @Column(value = "annual_total_delivery_unit", insertValueScript = "0")
    private BigDecimal anualTotalDeliveryUnit;

    /** 年度累计到当周已经交付的核心数<br/>Column: [anual_cumulative_delivery_core] */
    @Column(value = "annual_cumulative_delivery_core", insertValueScript = "0")
    private BigDecimal anualCumulativeDeliveryCore;

    /** 年度累计到当周已经交付的台数<br/>Column: [anual_cumulative_delivery_unit] */
    @Column(value = "annual_cumulative_delivery_unit", insertValueScript = "0")
    private BigDecimal anualCumulativeDeliveryUnit;

    /**
     * 维度列表，用于和DTO进行映射匹配
     */
    public static Function<LongtermDemandAndExecuteDataCsigDO, String> dimMapping = o ->
            StringTools.join("@", o.getIndustryDept());

}