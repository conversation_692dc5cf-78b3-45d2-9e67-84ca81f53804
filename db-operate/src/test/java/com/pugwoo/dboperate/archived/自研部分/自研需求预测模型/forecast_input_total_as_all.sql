select
    toYear(toDate(use_time)) as year,
    toMonth(toDate(use_time)) as month,
      subtractDays(addMonths(toDate(concat(toString(toYear(toDate(use_time))), '-',
          case when toMonth(toDate(use_time))<10 then '0' else '' end, toString(toMonth(toDate(use_time))), '-01')), 1), 1) as stime,
    '国家' as region_name,
    '机型' as gins_family,
     sum(applied_core_amount) as cores
from dwd_yunti_cvm_demand_forecast_item_df
where stat_date='${snapshotDate}'
  and dept_id NOT IN (32,1129) -- 去掉运营资源中心、算力平台两个部门
  and resource_pool_type = 0 -- 自研池

  ${excludePlanProductIds}

  -- and plan_product_id not in (858,54,50/*,1194,712,473,1158,28,567,1195,747,68,1159,91,87,563,39,2886,109,559*/)

    ${timeRange} -- 时间范围

group by toYear(toDate(use_time)),toMonth(toDate(use_time))
having cores>0