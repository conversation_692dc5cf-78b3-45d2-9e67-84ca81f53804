package com.pugwoo.dboperate.archived.中长期.云运管级别_准备jixuan数据_2406;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.wooutils.string.StringTools;
import lombok.Data;

import java.math.BigDecimal;
import java.util.function.Function;

@Data
public class DemandAndDeliveryDTO {

    @Column(value = "dim_industry")
    private String dimIndustry;

    @Column(value = "anual_demand_cores")
    private BigDecimal anualDemandCores;

    @Column(value = "anual_demand_num")
    private BigDecimal anualDemandNum;

    @Column(value = "delivered_cores")
    private BigDecimal deliveredCores;

    @Column(value = "delivered_num")
    private BigDecimal deliveredNum;

    public LongtermDemandAndExecuteDataCsigDO trans(int weekNum, int year) {
        LongtermDemandAndExecuteDataCsigDO longtermDemandAndExecuteDataCsigDO = new LongtermDemandAndExecuteDataCsigDO();
        longtermDemandAndExecuteDataCsigDO.setWeekNum(weekNum);
        longtermDemandAndExecuteDataCsigDO.setYear(year);
        longtermDemandAndExecuteDataCsigDO.setIndustryDept(this.getDimIndustry());
        longtermDemandAndExecuteDataCsigDO.setAnualTotalDemandCore(this.getAnualDemandCores());
        longtermDemandAndExecuteDataCsigDO.setAnualTotalDemandUnit(this.getAnualDemandNum());
        longtermDemandAndExecuteDataCsigDO.setAnualCumulativeDeliveryCore(this.getDeliveredCores());
        longtermDemandAndExecuteDataCsigDO.setAnualCumulativeDeliveryUnit(this.getDeliveredNum());
        return longtermDemandAndExecuteDataCsigDO;
    }

    /**
     * 维度列表，用于和DTO进行映射匹配
     */
    public static Function<DemandAndDeliveryDTO, String> dimMapping = o ->
            StringTools.join("@", o.getDimIndustry());

}
