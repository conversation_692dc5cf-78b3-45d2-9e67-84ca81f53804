package com.pugwoo.dboperate.archived.中长期.云运管级别_准备jixuan数据_2406;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dboperate.archived.中长期.公司级别.ResPlanHolidayWeekDO;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.Rollback;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

@SpringBootTest
public class ProvideData {

    @Resource
    private DBHelper cloudDemandLabIdcDBHelper;
    @Resource
    private DBHelper ckCloudDemandNewIdcDBHelper;
    @Resource
    private DBHelper erpResplanIdcDBHelper;

    @Test
    @Rollback(false)
    @Transactional("cloudDemandLabIdcTransactionManager")
    public void run() {
        // 1. 查询节假周列表，从2023年1月9号(含)开始至今
        List<ResPlanHolidayWeekDO> holidays = erpResplanIdcDBHelper.getAll(ResPlanHolidayWeekDO.class, "order by year,week");
        holidays = ListUtils.filter(holidays, o -> o.getEnd().isAfter(DateUtils.parseLocalDate("2023-01-09"))
                && o.getEnd().isBefore(LocalDate.now()));

        // 2. 对于每一个节假周，查询周切片的全年需求、截止当周已完成，如果查询到的数据全为0，则跳过
        List<LongtermDemandAndExecuteDataCsigDO> result = new ArrayList<>();
        for (ResPlanHolidayWeekDO holiday : holidays) {
            int year = holiday.getYear();
            LocalDate weekEnd = holiday.getEnd();
            List<DemandAndDeliveryDTO> source = getDemandAndDelivery(year, DateUtils.format(weekEnd, "yyyyMMdd"));
            result.addAll(ListUtils.transform(source, o -> o.trans(holiday.getWeek(), holiday.getYear())));
        }

        // 3. 补充全年已完成的数据，这个不随周切片变化
        Map<Integer, List<LongtermDemandAndExecuteDataCsigDO>> years = ListUtils.toMapList(result, o -> o.getYear(), o -> o);
        for (Map.Entry<Integer, List<LongtermDemandAndExecuteDataCsigDO>> year : years.entrySet()) {
            String version = year.getKey() + "1231";
            List<DemandAndDeliveryDTO> wholeYearDelivery = getDemandAndDelivery(year.getKey(), version);
            for (LongtermDemandAndExecuteDataCsigDO d : year.getValue()) {
                for (DemandAndDeliveryDTO whole : wholeYearDelivery) {
                    // 目前只有一个维度进行了匹配
                    if (Objects.equals(LongtermDemandAndExecuteDataCsigDO.dimMapping.apply(d),
                            DemandAndDeliveryDTO.dimMapping.apply(whole))) {
                        d.setAnualTotalDeliveryCore(whole.getDeliveredCores());
                        d.setAnualTotalDeliveryUnit(whole.getDeliveredNum());
                    }
                }
            }
            // 检查每一周是否都有wholeYearDelivery的key，如果没有的则补充进去
            Map<Integer, List<LongtermDemandAndExecuteDataCsigDO>> weeks = ListUtils.toMapList(year.getValue(), o -> o.getWeekNum(), o -> o);
            for (List<LongtermDemandAndExecuteDataCsigDO> week : weeks.values()) {
                Set<String> set = ListUtils.toSet(week, o -> LongtermDemandAndExecuteDataCsigDO.dimMapping.apply(o));
                for (DemandAndDeliveryDTO whole : wholeYearDelivery) {
                    if (!set.contains(DemandAndDeliveryDTO.dimMapping.apply(whole))) {
                        LongtermDemandAndExecuteDataCsigDO d = new LongtermDemandAndExecuteDataCsigDO();
                        result.add(d);
                        d.setWeekNum(week.getFirst().getWeekNum());
                        d.setYear(week.getFirst().getYear());
                        d.setIndustryDept(whole.getDimIndustry());
                        d.setAnualTotalDeliveryCore(whole.getDeliveredCores());
                        d.setAnualTotalDeliveryUnit(whole.getDeliveredNum());
                    }
                }
            }
        }

        // 4. 写入数据库
        cloudDemandLabIdcDBHelper.executeRaw("delete from longterm_demand_and_execute_data_csig");
        cloudDemandLabIdcDBHelper.insertBatchWithoutReturnId(result);
    }

    /**
     * 从version看指定年份的完成数据，version的格式必须是yyyyMMdd
     */
    private List<DemandAndDeliveryDTO> getDemandAndDelivery(int year, String version) {

        // 一些版本没有数据，特别处理
        if (version.equals("20231029")) {
            version = "20231028";
        }
        if (version.equals("20230723")) {
            version = "20230722";
        }

        String yearStart = year + "-01";
        String yearEnd = year + "-12";

        return ckCloudDemandNewIdcDBHelper.getRaw(DemandAndDeliveryDTO.class,
                """
                        with raw as (SELECT (any(version))            AS `any_version`,
                               (any(p_ym))               AS `any_p_ym`,
                               (any(p_date))             AS `any_p_date`,
                               (any(dim_big_class))      AS `any_dim_big_class`,
                               (any(dim_product_class))  AS `any_dim_product_class`,
                               (any(dim_device_type))    AS `any_dim_device_type`,
                               (any(dim_instance_type))  AS `any_dim_instance_type`,
                               (any(dim_cpu_type))       AS `any_dim_cpu_type`,
                               (any(dim_net_type))       AS `any_dim_net_type`,
                               (any(dim_region))         AS `any_dim_region`,
                               (any(dim_region_class))   AS `any_dim_region_class`,
                               (any(dim_industry))       AS `any_dim_industry`,
                               (any(dim_customer))       AS `any_dim_customer`,
                               (any(dim_plan_product))   AS `any_dim_plan_product`,
                               (any(dim_reason1))        AS `any_dim_reason1`,
                               (any(dim_reason2))        AS `any_dim_reason2`,
                               (any(dim_supply_way))     AS `any_dim_supply_way`,
                               (any(p_index))            AS `any_p_index`,
                               (any(num))                AS `any_num`,
                               (any(cores))              AS `any_cores`,
                               (any(ori_id))             AS `any_ori_id`,
                               (COALESCE(SUM(num), 0))   AS `sum_num`,
                               (COALESCE(SUM(cores), 0)) AS `sum_cores`
                        FROM cloud_demand.`ads_tcres_demand_annual_excueted_report` t
                        WHERE ((version = ?) or (version = ?))
                          and (p_ym between ? and ?)
                        group by version, p_index, dim_industry
                        order by version, p_index, dim_industry)
           
                        select any_dim_industry as dim_industry,
                               sum(case when any_p_index='全年需求预测量' then sum_cores else 0 end) as "anual_demand_cores",
                               sum(case when any_p_index='全年需求预测量' then sum_num else 0 end) as "anual_demand_num",
                               sum(case when any_p_index='已交付量（交付月份）' then sum_cores else 0 end) as "delivered_cores",
                               sum(case when any_p_index='已交付量（交付月份）' then sum_num else 0 end) as "delivered_num"
                        from raw
                        group by any_dim_industry
                        """, version, version, yearStart, yearEnd);
    }

}
