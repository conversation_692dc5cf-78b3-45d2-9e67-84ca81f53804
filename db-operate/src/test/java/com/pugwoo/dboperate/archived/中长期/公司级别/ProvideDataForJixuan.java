package com.pugwoo.dboperate.archived.中长期.公司级别;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.lang.NumberUtils;
import com.pugwoo.wooutils.string.StringTools;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.Rollback;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 给jixuan提供中长期的数据
 */
@SpringBootTest
public class ProvideDataForJixuan {

    @Resource
    private DBHelper cloudDemandLabIdcDBHelper;
    @Resource
    private DBHelper erpResplanIdcDBHelper;
    @Resource
    private DeliveryDataService deliveryDataService;
    @Resource
    private DemandDataService demandDataService;
    @Resource
    private DBHelper ckStdCrpNewIdcDBHelper;
    @Resource
    private DBHelper ckCloudDemandNewIdcDBHelper;

    /**
     * 生成周数据
     */
    @Test
    @Rollback(false)
    @Transactional("cloudDemandLabIdcTransactionManager")
    public void generateWeekDict() {
        List<LongtermDemandAndExecuteWeekDictDO> result = new ArrayList<>();

        // 1. 查询最终生成的底表的所有年周
        List<Map> weeks = cloudDemandLabIdcDBHelper.getRaw(Map.class,
                """
                        select distinct year,week_num from longterm_demand_and_execute_data
                        order by year,week_num
                        """);
        result = ListUtils.transform(weeks, o -> {
            LongtermDemandAndExecuteWeekDictDO d = new LongtermDemandAndExecuteWeekDictDO();
            d.setYear(NumberUtils.parseInt(o.get("year")));
            d.setWeek(NumberUtils.parseInt(o.get("week_num")));
            return d;
        });
        // 2. 找节假周的数据吗，对于周为负数的不处理
        List<ResPlanHolidayWeekDO> holidays = erpResplanIdcDBHelper.getAll(ResPlanHolidayWeekDO.class, "order by year,week");
        Map<String, ResPlanHolidayWeekDO> yearToWeeks = ListUtils.toMap(holidays,
                o -> StringTools.join("-", o.getYear(), o.getWeek()), o -> o);
        result.forEach(o -> {
            ResPlanHolidayWeekDO weekDO = yearToWeeks.get(StringTools.join("-", o.getYear(), o.getWeek()));
            if (weekDO != null) {
                o.setWeekStart(weekDO.getStart().toString());
                o.setWeekEnd(weekDO.getEnd().toString());
                o.setDeliveryVersionDate(weekDO.getEnd().toString());
            }
        });

        // 3. 找子淋提供的数据里的版本号
        List<Map> demandVersion = ckStdCrpNewIdcDBHelper.getRaw(Map.class,
                """
                        select distinct year,week,version_date from dws_sop_all_year_demand_report
                        """);
        Map<String, String> demandVersionMap = ListUtils.toMap(demandVersion,
                o -> StringTools.join("-", o.get("year").toString(), o.get("week").toString()),
                o -> o.get("version_date").toString());
        result.forEach(o -> {
            String key = StringTools.join("-", o.getYear(), o.getWeek());
            o.setDemandVersionDate(demandVersionMap.getOrDefault(key, ""));
        });

        // 4. 整合并插入数据库
        cloudDemandLabIdcDBHelper.executeRaw("delete from longterm_demand_and_execute_week_dict");
        cloudDemandLabIdcDBHelper.insertBatchWithoutReturnId(result);
    }

    @Test
    @Rollback(false)
    @Transactional("cloudDemandLabIdcTransactionManager")
    public void run() {
        List<LongtermDemandAndExecuteDataDO> result = new ArrayList<>();

        // 1. 查询全量的节假周的数据
        List<ResPlanHolidayWeekDO> holidays = erpResplanIdcDBHelper.getAll(ResPlanHolidayWeekDO.class, "order by year,week");
        Map<Integer, List<ResPlanHolidayWeekDO>> yearToWeeks = ListUtils.toMapList(holidays, o -> o.getYear(), o -> o);

        // 2. 处理物理机和cvm完成数 从2019年起
        for (ResPlanHolidayWeekDO weekDO : holidays) {
            if (weekDO.getYear() < 2019 || weekDO.getEnd().isAfter(LocalDate.now())) {
                continue;
            }
            {
                List<DeliveryDataDTO> utilWeek = deliveryDataService.getPhysicalData(weekDO.getYear(), weekDO.getEnd().toString(), yearToWeeks.get(weekDO.getYear()));
                List<DeliveryDataDTO> totalYear = deliveryDataService.getPhysicalData(weekDO.getYear(), weekDO.getYear() + "-12-31", yearToWeeks.get(weekDO.getYear()));
                result.addAll(trans("物理机", weekDO, utilWeek, totalYear));
            }
            {
                List<DeliveryDataDTO> utilWeek = deliveryDataService.getCvmData(weekDO.getYear(), weekDO.getEnd().toString(), yearToWeeks.get(weekDO.getYear()));
                List<DeliveryDataDTO> totalYear = deliveryDataService.getCvmData(weekDO.getYear(), weekDO.getYear() + "-12-31", yearToWeeks.get(weekDO.getYear()));
                result.addAll(trans("CVM", weekDO, utilWeek, totalYear));
            }
        }

        /* 3. 需求数据，子淋提供*/
        List<LongtermDemandAndExecuteDataDO> demands = demandDataService.getAll();

        // 4. 整合数据
        result = merge(result, demands);

        // 5. 完成
        cloudDemandLabIdcDBHelper.executeRaw("delete from longterm_demand_and_execute_data");
        ckCloudDemandNewIdcDBHelper.executeRaw("truncate table cloud_demand.dws_longterm_demand_and_execute_data_local ON CLUSTER default_cluster");
        try {
            Thread.sleep(10000);
        } catch (InterruptedException ignored) {
        }
        List<List<LongtermDemandAndExecuteDataDO>> partition = ListUtils.partition(result, 100000);
        for (List<LongtermDemandAndExecuteDataDO> p : partition) {
            cloudDemandLabIdcDBHelper.insertBatchWithoutReturnId(p);
            List<LongtermDemandAndExecuteDataCkDO> p2 = ListUtils.transform(p,
                    o -> JSON.parse(JSON.toJson(o), LongtermDemandAndExecuteDataCkDO.class));
            ckCloudDemandNewIdcDBHelper.insertBatchWithoutReturnId(p2);
        }
    }

    private List<LongtermDemandAndExecuteDataDO> merge(List<LongtermDemandAndExecuteDataDO> delivery,
                                                       List<LongtermDemandAndExecuteDataDO> demand) {
        return ListUtils.merge(delivery, demand, LongtermDemandAndExecuteDataDO.mergeKey, LongtermDemandAndExecuteDataDO.mergeKey,
                (a, b) -> {
            LongtermDemandAndExecuteDataDO one = ListUtils.isNotEmpty(a) ? a.getFirst() : b.getFirst();
            LongtermDemandAndExecuteDataDO result = JSON.clone(one);
            result.setAnualTotalDemandCore(NumberUtils.sum(b, o -> o.getAnualTotalDemandCore()));
            result.setAnualTotalDemandUnit(NumberUtils.sum(b, o -> o.getAnualTotalDemandUnit()));
            result.setAnualCumulativeDeliveryCore(NumberUtils.sum(a, o -> o.getAnualCumulativeDeliveryCore()));
            result.setAnualCumulativeDeliveryUnit(NumberUtils.sum(a, o -> o.getAnualCumulativeDeliveryUnit()));
            result.setAnualTotalDeliveryCore(NumberUtils.sum(a, o -> o.getAnualTotalDeliveryCore()));
            result.setAnualTotalDeliveryUnit(NumberUtils.sum(a, o -> o.getAnualTotalDeliveryUnit()));
            return result;
        });
    }


    private List<LongtermDemandAndExecuteDataDO> trans(String resourceType,
                                                       ResPlanHolidayWeekDO weekDO, List<DeliveryDataDTO> utilWeek, List<DeliveryDataDTO> totalYear) {

        LongtermDemandAndExecuteDataDO data = new LongtermDemandAndExecuteDataDO();
        data.setYear(weekDO.getYear());
        data.setWeekNum(weekDO.getWeek());
        data.setResourceType(resourceType);

        return ListUtils.merge(utilWeek, totalYear,
                DeliveryDataDTO.mergeKey, DeliveryDataDTO.mergeKey, (a, b) -> {
                    DeliveryDataDTO one = ListUtils.isNotEmpty(a) ? a.getFirst() : b.getFirst();
                    LongtermDemandAndExecuteDataDO tmp = JSON.clone(data);
                    tmp.setQuotaWeek(one.getQuotaWeek());
                    tmp.setBizType(one.getBizType());
                    tmp.setProjectName(one.getProjectName());
                    tmp.setPlanProductName(one.getPlanProductName());
                    tmp.setDept(one.getDept());
                    tmp.setCustomBg(one.getCustomBg());
                    tmp.setMachineType(one.getMachineType());
                    tmp.setMachineTypeFamily(one.getMachineTypeFamily());
                    tmp.setAnualTotalDeliveryUnit(NumberUtils.sum(b, o -> o.getUnit()));
                    tmp.setAnualTotalDeliveryCore(NumberUtils.sum(b, o -> o.getCore()));
                    tmp.setAnualCumulativeDeliveryUnit(NumberUtils.sum(a, o -> o.getUnit()));
                    tmp.setAnualCumulativeDeliveryCore(NumberUtils.sum(a, o -> o.getCore()));
                    return tmp;
                });
    }
}
