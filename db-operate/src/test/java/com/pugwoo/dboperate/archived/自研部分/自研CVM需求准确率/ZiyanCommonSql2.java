package com.pugwoo.dboperate.archived.自研部分.自研CVM需求准确率;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dboperate.common.ForecastCommonService;
import com.pugwoo.dboperate.common.ReadFileUtils;
import lombok.Data;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.List;

@Service
public class ZiyanCommonSql2 {

    @Resource
    private DBHelper cloudDemandCommonDevDBHelper;
    @Resource
    private DBHelper erpIdcDBHelper;
    @Resource
    private ForecastCommonService forecastCommonService;
    @Resource
    private DBHelper obsIdcDBHelper;

    @Data
    public static class InstanceType2DeviceGroupDTO {
        @Column("cvm_instance_type")
        private String cvmInstanceType;
        @Column("device_group")
        private String deviceGroup;
    }

    @Data
    public static class City2CountryDTO {
        @Column("CityName")
        private String cityName;
        @Column("CountryChinese")
        private String countryChinese;
    }

    public String getSql() {
        String sql = ReadFileUtils.read("cvm_demand.sql");
        sql = sql.replace("${region}", "city_name");

        // 机型 -> 实例族
        List<InstanceType2DeviceGroupDTO> instanceType2DeviceGroup =
                obsIdcDBHelper.getRaw(InstanceType2DeviceGroupDTO.class,
                        """
                                select distinct CvmInstanceTypeCode as cvm_instance_type,CvmInstanceGroup as device_group from bas_obs_cloud_cvm_type
                             """);
        StringBuilder instanceTypeCaseWhen = new StringBuilder("(case ");
        for (InstanceType2DeviceGroupDTO dto : instanceType2DeviceGroup) {
            instanceTypeCaseWhen.append("WHEN splitByChar('.', instance_model)[1]='").append(dto.getCvmInstanceType()).append("' THEN '").append(dto.getDeviceGroup()).append("' ");
        }
        instanceTypeCaseWhen.append(" else if(splitByChar('.', instance_model)[1] IS NULL OR splitByChar('.', instance_model)[1] = '', instance_model, splitByChar('.', instance_model)[1]) end) ");
        sql = sql.replace("${instance_type}", instanceTypeCaseWhen.toString());

        return sql;

    }


}
