package com.pugwoo.dboperate.archived.中长期.公司级别;

import lombok.Data;
import lombok.ToString;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;

@Data
@ToString
@Table("longterm_demand_and_execute_week_dict")
public class LongtermDemandAndExecuteWeekDictDO {

    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Integer id;

    /** 年<br/>Column: [year] */
    @Column(value = "year")
    private Integer year;

    /** 周<br/>Column: [week] */
    @Column(value = "week")
    private Integer week;

    /** 节假周开始的日期<br/>Column: [week_start] */
    @Column(value = "week_start")
    private String weekStart;

    /** 节假周结束的日期<br/>Column: [week_end] */
    @Column(value = "week_end")
    private String weekEnd;

    /** 需求切片的日期<br/>Column: [demand_version_date] */
    @Column(value = "demand_version_date")
    private String demandVersionDate;

    /** 完成数据取数日期，仅对截止当周已完成有效<br/>Column: [delivery_version_date] */
    @Column(value = "delivery_version_date")
    private String deliveryVersionDate;

}