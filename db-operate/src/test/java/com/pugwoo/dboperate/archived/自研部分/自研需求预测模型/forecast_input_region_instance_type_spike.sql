-- 用于获得毛刺的明细，以用于剔除

select
    toYear(toDate(use_time)) as year,
    toMonth(toDate(use_time)) as month,
      subtractDays(addMonths(toDate(concat(toString(toYear(toDate(use_time))), '-',
          case when toMonth(toDate(use_time))<10 then '0' else '' end, toString(toMonth(toDate(use_time))), '-01')), 1), 1) as stime,
    plan_product_id,
    ${region} as region_name,
    ${instance_type} as gins_family,
     sum(applied_core_amount) as cores
from dwd_yunti_cvm_demand_forecast_item_df
where stat_date='${snapshotDate}'
  and dept_id NOT IN (32,1129) -- 去掉运营资源中心、算力平台两个部门
  and resource_pool_type = 0 -- 自研池

  -- 本次只预测主力机型主力地域
  and region_name in ('上海','南京','深圳','广州','天津')
  and splitByChar('.', instance_model)[1] in ('SA2','S5','IT5','S6t','S5t','I6t','SA3','S6','ITA3','IT5c','D2','D3','DA2','DA3','PNV4ne','PNV4','GI3X')

    ${timeRange} -- 时间范围

group by toYear(toDate(use_time)),toMonth(toDate(use_time)),plan_product_id,${region},${instance_type}
having cores>:spikeValve