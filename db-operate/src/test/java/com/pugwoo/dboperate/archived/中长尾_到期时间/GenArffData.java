package com.pugwoo.dboperate.archived.中长尾_到期时间;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import jakarta.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.util.List;
import java.util.Set;
import java.util.function.Function;

@SpringBootTest
public class GenArffData {

    @Resource
    private DBHelper ckCloudDemandNewIdcDBHelper;

    @Test
    public void genArffData() throws Exception {
        List<CloudCvmInstanceRetDataVO> list = ckCloudDemandNewIdcDBHelper.getRaw(CloudCvmInstanceRetDataVO.class,
                """
with raw as
(select
    uin,
    u_instance_id as instance_id,
    customhouse_title,
    region_name,
    zone_name,
    ginstype as instance_model,
    ginsfamily as instance_type,
    cpu_core_num,
    toDate(create_time) as buy_date, -- 购买日期
    toYear(buy_date) as buy_year,
    toMonth(buy_date) as buy_month, -- 购买月份
    toYYYYMM(buy_date) as buy_year_month, -- 购买年月
    toDate(deadline_date) as deadline_date, -- 到期日期
    toYear(deadline_date) as deadline_year, -- 到期年份
    toMonth(deadline_date) as deadline_month, -- 到期月份
    toYYYYMM(deadline_date) as deadline_year_month, -- 到期年月
    dateDiff('day', toDate(create_time), toDate(deadline_date))  as pay_days,  -- 服务器预付费的天数
    round(pay_days / 30, 0) as pay_months, -- 服务器预付费的月数
    round(pay_days / 365, 0) as pay_years, -- 服务器预付费的年数
        (case
             when u_instance_id global not in
                  (select u_instance_id from cloud_cvm_instance_ret_data where stat_time = '2023-11-01')
                 then 'yes'
             else 'no' end)                        as is_ret_on_time
 from cloud_cvm_instance_ret_data
 where paymode = '1'
   and app_role = '正常售卖'
   and stat_time = '2023-06-01'
   and deadline_date between '2023-10-01' and '2023-10-21'
   and uin global in (select uin from std_crp.dwd_txy_scale_df where dwd_txy_scale_df.stat_time='2023-10-01' and customer_tab_type='中长尾客户')
 )
                                                
                        select * from raw
                        """);
        // System.out.println(list.size());

        // 尝试放大36个月到期的量，现在是4000条，占了1/3这样，把它double一下，占1/2看看能不能发现规律
        // list.addAll(ListUtils.filter(list, o -> o.getPayMonths().equals("36.0")));

        File file = new File("d:/cvm_ret.arff");
        FileOutputStream out = new FileOutputStream(file);

        out.write("@relation cvmret\n\n".getBytes());

        out.write(toAttribute("uin", list, CloudCvmInstanceRetDataVO::getUin).getBytes());
        out.write(toAttribute("instance_id", list, CloudCvmInstanceRetDataVO::getInstanceId).getBytes());
        out.write(toAttribute("customhouse_title", list, CloudCvmInstanceRetDataVO::getCustomhouseTitle).getBytes());
        out.write(toAttribute("region_name", list, CloudCvmInstanceRetDataVO::getRegionName).getBytes());
        out.write(toAttribute("zone_name", list, CloudCvmInstanceRetDataVO::getZoneName).getBytes());
        out.write(toAttribute("instance_model", list, CloudCvmInstanceRetDataVO::getInstanceModel).getBytes());
        out.write(toAttribute("instance_type", list, CloudCvmInstanceRetDataVO::getInstanceType).getBytes());
        out.write(toAttribute("cpu_core_num", list, CloudCvmInstanceRetDataVO::getCpuCoreNum).getBytes());
        out.write(toAttribute("buy_date", list, CloudCvmInstanceRetDataVO::getBuyDate).getBytes());
        out.write(toAttribute("buy_year", list, CloudCvmInstanceRetDataVO::getBuyYear).getBytes());
        out.write(toAttribute("buy_month", list, CloudCvmInstanceRetDataVO::getBuyMonth).getBytes());
        out.write(toAttribute("buy_year_month", list, CloudCvmInstanceRetDataVO::getBuyYearMonth).getBytes());
        out.write(toAttribute("deadline_date", list, CloudCvmInstanceRetDataVO::getDeadlineDate).getBytes());
        out.write(toAttribute("deadline_year", list, CloudCvmInstanceRetDataVO::getDeadlineYear).getBytes());
        out.write(toAttribute("deadline_month", list, CloudCvmInstanceRetDataVO::getDeadlineMonth).getBytes());
        out.write(toAttribute("deadline_year_month", list, CloudCvmInstanceRetDataVO::getDeadlineYearMonth).getBytes());
        out.write(toAttribute("pay_days", list, CloudCvmInstanceRetDataVO::getPayDays).getBytes());
        out.write(toAttribute("pay_months", list, CloudCvmInstanceRetDataVO::getPayMonths).getBytes());
        out.write(toAttribute("pay_years", list, CloudCvmInstanceRetDataVO::getPayYears).getBytes());
        out.write(toAttribute("is_ret_on_time", list, CloudCvmInstanceRetDataVO::getIsRetOnTime).getBytes());

        out.write("\n@data\n".getBytes());

        for (CloudCvmInstanceRetDataVO vo : list) {
            out.write((
                    vo.getUin() + "," +
                    vo.getInstanceId() + "," +
                    vo.getCustomhouseTitle() + "," +
                    vo.getRegionName() + "," +
                    vo.getZoneName() + "," +
                    vo.getInstanceModel() + "," +
                    vo.getInstanceType() + "," +
                    vo.getCpuCoreNum() + "," +
                    vo.getBuyDate() + "," +
                    vo.getBuyYear() + "," +
                    vo.getBuyMonth() + "," +
                    vo.getBuyYearMonth() + "," +
                    vo.getDeadlineDate() + "," +
                    vo.getDeadlineYear() + "," +
                    vo.getDeadlineMonth() + "," +
                    vo.getDeadlineYearMonth() + "," +
                    vo.getPayDays() + "," +
                    vo.getPayMonths() + "," +
                    vo.getPayYears() + "," +
                    vo.getIsRetOnTime() + "\n").getBytes());
        }

        out.close();
    }


    public String toAttribute(String attributeName,
                              List<CloudCvmInstanceRetDataVO> list,
                              Function<CloudCvmInstanceRetDataVO, String> mapper) {
        Set<String> attributes = ListUtils.toSet(list, mapper);
        return "@attribute " + attributeName + " {" + String.join(",", attributes) + "}\n";
    }

}
