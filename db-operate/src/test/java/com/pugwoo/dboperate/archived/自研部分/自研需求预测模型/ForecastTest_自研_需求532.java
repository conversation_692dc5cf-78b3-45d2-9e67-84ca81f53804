package com.pugwoo.dboperate.archived.自研部分.自研需求预测模型;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dboperate.forecast_compute.entity.ForecastComputeTaskDO;
import com.pugwoo.dboperate.forecast_compute.entity.ForecastComputeTaskInputTestDatasetDO;
import com.pugwoo.dboperate.forecast_compute.enums.SerialIntervalEnum;
import com.pugwoo.dboperate.forecast_compute.enums.TaskTypeEnum;
import com.pugwoo.dboperate.forecast_compute.model.CreateTaskDTO;
import com.pugwoo.dboperate.forecast_compute.model.CreateTaskInputDataDTO;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.collect.MapUtils;
import com.pugwoo.wooutils.io.IOUtils;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import com.pugwoo.wooutils.net.Browser;
import com.pugwoo.wooutils.net.HttpResponse;
import com.pugwoo.wooutils.string.StringTools;
import lombok.Data;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;

@SpringBootTest
public class ForecastTest_自研_需求532 {

    @Resource
    private DBHelper ckYuntiDemandNewIdcDBHelper;
    @Resource
    private DBHelper cloudDemandCommonDevDBHelper;
    @Resource
    private ZiyanCommonSql ziyanCommonSql;

    private boolean enablePrintAccuracyRate = true;
    private boolean isCalNewCore = true; // 是否计算新增的准确率，目前只有新增，没有退回
    private int mode = 4; // 0=不分，1=分地域+机型大类，2=分国家+机型族，3=地域+含机型收敛的机型大类,4=地域+实例族，5=地域+实例族+毛刺剔除
    private List<Long> excludePlanProductIds = new ArrayList<>();

    private int spikeValve = 1000000000; // 毛刺剔除阀值

    @Test
    public void runMode123() throws Exception {
        System.out.println("===================== 不分 =======================");
        mode = 0;
        calAccuracyRate();

        System.out.println("================== 分国家+机型族 =================");
        mode = 2;
        calAccuracyRate();

        System.out.println("====================分地域+机型大类================");
        mode = 1;
        calAccuracyRate();

        System.out.println("=================地域+含机型收敛的机型大类===========");
        mode = 3;
        calAccuracyRate();
    }

    @Test
    public void testExcludePlanProductIds() throws Exception {
        List<Integer> topPlanProductId = ListUtils.newList(
                858,54,50,1194,712,473,1158,28,567,1195,747,68,1159,91,87,563,39,2886,109,559);

        for (Integer excludePlanProductId : topPlanProductId) {
            excludePlanProductIds = ListUtils.newList(excludePlanProductId.longValue());
            BigDecimal avgRate = calAccuracyRate();
            System.out.println("排除规划产品ID:" + excludePlanProductId + "，平均准确率:" + avgRate);
        }
    }

    @Test
    public void testOnce() throws Exception {
        mode = 5;
        calAccuracyRate();
    }

    private BigDecimal calAccuracyRate() throws Exception {
        cloudDemandCommonDevDBHelper.setSlowSqlWarningValve(100000);
        ckYuntiDemandNewIdcDBHelper.setSlowSqlWarningValve(100000);

        Map<LocalDate, Map<String, PredictAndRealDTO>> detail1 = calAccuracyRateAvg(1);
        Map<LocalDate, Map<String, PredictAndRealDTO>> detail2 = calAccuracyRateAvg(2);
        Map<LocalDate, Map<String, PredictAndRealDTO>> detail3 = calAccuracyRateAvg(3);

        // 按detail1 0.2  detail2 0.3  detail3 0.5的比例合成一个Map
        Map<LocalDate, Map<String, PredictAndRealDTO>> merged = new HashMap<>();
        for (Map.Entry<LocalDate, Map<String, PredictAndRealDTO>> e : detail1.entrySet()) { // 月份是肯定都有，所以这里只用detail1的key
            LocalDate date = e.getKey();
            Map<String, PredictAndRealDTO> m = new HashMap<>();
            merged.put(date, m);

            Map<String, PredictAndRealDTO> m1 = e.getValue();
            Map<String, PredictAndRealDTO> m2 = detail2.get(date);
            Map<String, PredictAndRealDTO> m3 = detail3.get(date);

            Set<String> keys = new HashSet<>();
            keys.addAll(m1.keySet());
            keys.addAll(m2.keySet());
            keys.addAll(m3.keySet());

            for (String key : keys) {
                PredictAndRealDTO d1 = m1.get(key);
                PredictAndRealDTO d2 = m2.get(key);
                PredictAndRealDTO d3 = m3.get(key);

                PredictAndRealDTO atLeastNotNull = d1 != null ? d1 : (d2 != null ? d2 : d3);

                PredictAndRealDTO result = new PredictAndRealDTO();
                result.setDim1(atLeastNotNull.getDim1());
                result.setDim2(atLeastNotNull.getDim2());
                result.setReal(atLeastNotNull.getReal());

                BigDecimal d1Predict = d1 == null ? BigDecimal.ZERO : d1.getPredict().multiply(BigDecimal.valueOf(0.2d));
                BigDecimal d2Predict = d2 == null ? BigDecimal.ZERO : d2.getPredict().multiply(BigDecimal.valueOf(0.3d));
                BigDecimal d3Predict = d3 == null ? BigDecimal.ZERO : d3.getPredict().multiply(BigDecimal.valueOf(0.5d));

                result.setPredict(d1Predict.add(d2Predict).add(d3Predict));

                List<Integer> taskIds = new ArrayList<>();
                if (d1 != null) {
                    taskIds.addAll(d1.getTaskIds());
                }
                if (d2 != null) {
                    taskIds.addAll(d2.getTaskIds());
                }
                if (d3 != null) {
                    taskIds.addAll(d3.getTaskIds());
                }
                result.setTaskIds(taskIds);

                m.put(key, result);
            }
        }

        Map<LocalDate, Map<String, PredictAndRealDTO>> merged2 = MapUtils.sortByKey(merged, false);

        // 计算准确率
        List<BigDecimal> rateList = new ArrayList<>();
        for (Map.Entry<LocalDate, Map<String, PredictAndRealDTO>> e : merged2.entrySet()) {
            BigDecimal rate = calAccuracyRate(e.getValue());
            Set<Integer> taskId = new HashSet<>();
            for (PredictAndRealDTO dto : e.getValue().values()) {
                taskId.addAll(dto.getTaskIds());
            }
            List<Integer> taskIdList = ListUtils.toList(taskId);
            ListUtils.sortAscNullLast(taskIdList, o -> o);
            if (enablePrintAccuracyRate) {
                System.out.println(e.getKey() + "准确率:" + rate + ", taskId:" + JSON.toJson(taskIdList));
            }
            rateList.add(rate);
        }

        // 计算平均准确率
        BigDecimal avgRate = NumberUtils.avg(rateList, 6);
        if (enablePrintAccuracyRate) {
            System.out.println("平均准确率:" + avgRate);
        }
        return avgRate;
    }

    @Data
    public static class PredictAndRealDTO {
        private String dim1;
        private String dim2;
        private BigDecimal predict;
        private BigDecimal real;
        private List<Integer> taskIds;
    }

    private Map<LocalDate, Map<String, PredictAndRealDTO>> calAccuracyRateAvg(int beforeNMonth) throws Exception {
        LocalDate start = DateUtils.parseLocalDate("2023-08");

        Map<Integer, LocalDate> taskIds = new LinkedHashMap<>();
        while (start.isBefore(DateUtils.parseLocalDate("2024-02-01"))) { // 最长填到当月的1号
            Integer taskId = createTask("自研CVM需求预测", start, beforeNMonth); // 提前N个月预测
            taskIds.put(taskId, start);
            start = start.plusMonths(1);
        }

        Map<LocalDate, Map<String, PredictAndRealDTO>> result = new HashMap<>();
        for (Integer taskId : taskIds.keySet()) {
            // 等待任务完成
            while(true) {
                ForecastComputeTaskDO task = cloudDemandCommonDevDBHelper.getOne(ForecastComputeTaskDO.class,
                        "where id=?", taskId);
                if (!"DONE".equals(task.getStatus())) {
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException ignored) {
                    }
                } else {
                    break;
                }
            }

            Map<String, PredictAndRealDTO> detail = getDetail(taskId, null);
            result.put(taskIds.get(taskId), detail);
        }

        return result;
    }

    private Integer createTask(String taskName, LocalDate predictMonth, int beforeNMonth) throws Exception {
        CreateTaskDTO task = new CreateTaskDTO();

        task.setCreateUser("nickxie");
        task.setTaskName(taskName);
        task.setTaskType(TaskTypeEnum.PREDICT.getCode());
        task.setSerialInterval(SerialIntervalEnum.MONTH.getCode());
        task.setInputDims(2);
        task.setInputDimsName("机型族,地域");
        task.setIsAutoFillData(true);
        task.setPredictIndexStart(1);
        task.setPredictIndexEnd(6); // 预测未来1到6个月

        CreateTaskDTO.Algorithm algorithm = new CreateTaskDTO.Algorithm(
                //       CreateTaskDTO.AlgorithmEnum.MA.getName(), ListUtils.newList(3));
                //       CreateTaskDTO.AlgorithmEnum.MAX.getName(), ListUtils.newList(3));
            //           CreateTaskDTO.AlgorithmEnum.ARIMA.getName(), ListUtils.newList(3,1,3));
                CreateTaskDTO.AlgorithmEnum.ARIMAX.getName(), ListUtils.newList(0,1,3,0,1,6,0.2));
        task.setAlgorithms(ListUtils.newList(algorithm));

        // 0,1,3,0,1,6,0.2 68.12%
        // 2,1,3,0,1,6,0.9 73.04%

        LocalDate inputBefore = predictMonth.minusMonths(beforeNMonth - 1);
        inputBefore = inputBefore.withDayOfMonth(1);
        String snapshotDate = DateUtils.formatDate(inputBefore);

        // 对于模式5，需要传入毛刺剔除阀值
        List<ZiyanCommonSql.SpikeDTO> spikes = new ArrayList<>();
        if (mode == 5) {
            String spikeSql = ziyanCommonSql.getSpikeSql();
            // spikeSql = spikeSql.replace("${timeRange}",
            //        "and toDate(use_time) < '" + DateUtils.formatDate(inputBefore) +"'"); // 用提前的数据来预测

            // 毛刺不需要时间范围
            spikeSql = spikeSql.replace("${timeRange}", "");

            spikeSql = spikeSql.replace("${snapshotDate}", DateUtils.formatDate(LocalDate.now().minusDays(1)));


            spikeSql = spikeSql.replace(":spikeValve", String.valueOf(spikeValve));
            List<InputWithPlanProductIdDTO> tmp = ckYuntiDemandNewIdcDBHelper.getRaw(InputWithPlanProductIdDTO.class, spikeSql);
            spikes = ListUtils.transform(tmp, o -> {
                ZiyanCommonSql.SpikeDTO dto = new ZiyanCommonSql.SpikeDTO();
                dto.setPlanProductId(o.getPlanProductId());
                dto.setYear(o.getYear());
                dto.setMonth(o.getMonth());
                dto.setRegionName(o.getRegionName());
                dto.setGinsFamily(o.getGinsFamily());
                return dto;
            });

            System.out.println("2023年至今毛刺条数:" + ListUtils.filter(spikes, o -> o.getYear() >= 2023).size());
        }

        String sql = ziyanCommonSql.getSql(isCalNewCore, mode, excludePlanProductIds, spikes);

        task.setInputDataDatasource("ck.yunti_demand");

        // 是否剔除弹性异常点，这个放在这里是因为它的输入和测试集sql不同
        String sqlForInput = "";

        sqlForInput = sql.replace("${timeRange}",
                "and toDate(use_time) < '" + DateUtils.formatDate(inputBefore) +"'"); // 用提前的数据来预测

        sqlForInput = sqlForInput.replace("${snapshotDate}", snapshotDate);
        task.setInputDataSql(sqlForInput);

        List<InputDTO> all = ckYuntiDemandNewIdcDBHelper.getRaw(InputDTO.class, sqlForInput);

        task.setInputData(ListUtils.transform(all, o -> {
            CreateTaskInputDataDTO d = new CreateTaskInputDataDTO();
            d.setDate(o.getStatTime());
            d.setValue(o.getCores());
            d.setDim1(o.getGinsFamily());
            d.setDim2(o.getRegionName());
            d.setDim3("");
            d.setDim4("");
            d.setDim5("");
            return d;
        }));

        // 测试集，测试集只留下要测试的数据接口，其它的不要，【所以】这里要测试的是5月，也就是提前1个月做预测

        // 是否剔除弹性异常点，这个放在这里是因为它的输入和测试集sql不同
        String testSql = sql.replace("${timeRange}",
                "and toYYYYMM(toDate(use_time))='" + DateUtils.format(predictMonth, "yyyyMM") +"'");
        String snapshotDate2 = DateUtils.format(predictMonth.withDayOfMonth(1).plusMonths(1));

        testSql = testSql.replace("${snapshotDate}", snapshotDate2);
        task.setTestDatasetSql(testSql);

        // 由于查询量非常大，这里分时间段进行查询，再合并起来
        List<InputDTO> test = ckYuntiDemandNewIdcDBHelper.getRaw(InputDTO.class, testSql);

        task.setTestDataset(ListUtils.transform(test, o -> {
            CreateTaskInputDataDTO d = new CreateTaskInputDataDTO();
            d.setDate(o.getStatTime());
            d.setValue(o.getCores());
            d.setDim1(o.getGinsFamily());
            d.setDim2(o.getRegionName());
            d.setDim3("");
            d.setDim4("");
            d.setDim5("");
            return d;
        }));

        Browser browser = new Browser();
        browser.addRequestHeader("Cookie", IOUtils.readClasspathResourceAsString("/废弃或已完成/db-operate/resources/exp-crp-cookie.txt").trim());

        HttpResponse resp = browser.postJson(
                "https://exp-crp.woa.com/cloud-demand-common/ops/submitForecastTask", task);
        SubmitResult parsed = JSON.parse(resp.getContentString(), SubmitResult.class);

        return parsed.getTaskId();
    }

    @Data
    public static class InputDTO {
        @Column("stime")
        private Date statTime;
        @Column("gins_family")
        private String ginsFamily;
        @Column("region_name")
        private String regionName;
        @Column("cores")
        private BigDecimal cores;
    }

    @Data
    public static class InputWithPlanProductIdDTO {
        @Column("year")
        private Integer year;
        @Column("month")
        private Integer month;
        @Column("stime")
        private Date statTime;
        @Column("gins_family")
        private String ginsFamily;
        @Column("region_name")
        private String regionName;
        @Column("plan_product_id")
        private Long planProductId;
        @Column("cores")
        private BigDecimal cores;
    }

    @Data
    public static class SubmitResult {
        private String uuid;
        private Integer taskId;
        private Boolean success;
    }


    @Data
    public static class CalAccuracyRateResult {
        List<ForecastComputeTaskInputTestDatasetVO> testDataset;
        BigDecimal accuracyRate;
    }

    public CalAccuracyRateResult calAccuracyRate(Integer taskId, String testDatasetExtraWhere) {
        // 1. 先读取测试集
        // 【特别说明】
        // 1) 现在是以真实值作为权重，也就是说，如果有预测，实际结果没有出现，还是认为准的，所以这个计算是按这种算法来简化了
        // 2) 现在是每次只测试一个月，所以直接对结果进行加权，不用分月份
        List<ForecastComputeTaskInputTestDatasetVO> testDataset = cloudDemandCommonDevDBHelper.getAll(
                ForecastComputeTaskInputTestDatasetVO.class,
                "where task_id=? " + (testDatasetExtraWhere == null ? "" : testDatasetExtraWhere), taskId);

        // 2. 再读取预测结果
        List<PredictResultDTO> predictResults = cloudDemandCommonDevDBHelper.getRaw(PredictResultDTO.class,
                "SELECT a.date,a.value,b.dim1,b.dim2,b.dim3,b.dim4,b.dim5\n" +
                        "FROM `forecast_compute_task_run_output` a\n" +
                        "LEFT JOIN `forecast_compute_task_run` b\n" +
                        "ON a.`task_run_id`=b.`id`\n" +
                        "WHERE a.task_id=?", taskId);

        // 3.1 合并数据
        testDataset = ListUtils.merge(testDataset, predictResults,
                o -> o.groupKey(), o -> o.groupKey(),
                (test, predict) -> {
                    if (ListUtils.isNotEmpty(test) && ListUtils.isNotEmpty(predict)) {
                        test.get(0).setPredictValue(predict.get(0).getValue());
                        return test.get(0);
                    } else {
                        return null;
                    }
                });
        testDataset = ListUtils.filter(testDataset, o -> o != null);

        // 4. 计算准确率
        ListUtils.forEach(testDataset, o -> {
            BigDecimal min = NumberUtils.min(o.getPredictValue(), o.getValue());
            BigDecimal max = NumberUtils.max(o.getPredictValue(), o.getValue());
            if (max.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal rate = NumberUtils.divide(min.multiply(BigDecimal.valueOf(100)), max, 6);
                o.setAccuracyRate(rate.doubleValue());
            } else {
                BigDecimal rate = BigDecimal.valueOf(100);
                o.setAccuracyRate(rate.doubleValue());
            }
        });

        // 5. 输出准确率，加权【现在是每次只测试一个月，所以直接对结果进行加权，不用分月份】
        BigDecimal total = NumberUtils.sum(testDataset, ForecastComputeTaskInputTestDatasetDO::getValue);
        ListUtils.forEach(testDataset, o -> {
            //   if (total.compareTo(BigDecimal.ZERO) <= 0) {
            //       o.setWeightedAccuracyRate(0d);
            //   } else {
            double rate = NumberUtils.divide(o.getValue(), total, 6)
                    .multiply(BigDecimal.valueOf(o.getAccuracyRate())).doubleValue();
            o.setWeightedAccuracyRate(rate);
            //  }
        });

        CalAccuracyRateResult result = new CalAccuracyRateResult();
        result.setTestDataset(testDataset);
        result.setAccuracyRate(NumberUtils.sum(testDataset, o -> o.getWeightedAccuracyRate()));

        return result;
    }

    private BigDecimal calAccuracyRate(Map<String, PredictAndRealDTO> map) {
        Collection<PredictAndRealDTO> list = map.values();
        BigDecimal total = NumberUtils.sum(list, o -> o.getReal());
        BigDecimal rate = BigDecimal.ZERO;

        for (PredictAndRealDTO o : list) {
            BigDecimal min = NumberUtils.min(o.getPredict(), o.getReal());
            BigDecimal max = NumberUtils.max(o.getPredict(), o.getReal());
            BigDecimal r = null;
            if (max.compareTo(BigDecimal.ZERO) > 0) {
                r = NumberUtils.divide(min.multiply(BigDecimal.valueOf(100)), max, 6);
            } else {
                r = BigDecimal.valueOf(100);
            }
            rate = rate.add(r.multiply(NumberUtils.divide(o.getReal(), total, 6)));
        }

        return rate;
    }

    private Map<String, PredictAndRealDTO> getDetail(Integer taskId, String testDatasetExtraWhere) {
        // 1. 先读取测试集
        // 【特别说明】
        // 1) 现在是以真实值作为权重，也就是说，如果有预测，实际结果没有出现，还是认为准的，所以这个计算是按这种算法来简化了
        // 2) 现在是每次只测试一个月，所以直接对结果进行加权，不用分月份
        List<ForecastComputeTaskInputTestDatasetVO> testDataset = cloudDemandCommonDevDBHelper.getAll(
                ForecastComputeTaskInputTestDatasetVO.class,
                "where task_id=? " + (testDatasetExtraWhere == null ? "" : testDatasetExtraWhere), taskId);

        // 2. 再读取预测结果
        List<PredictResultDTO> predictResults = cloudDemandCommonDevDBHelper.getRaw(PredictResultDTO.class,
                "SELECT a.date,a.value,b.dim1,b.dim2,b.dim3,b.dim4,b.dim5\n" +
                        "FROM `forecast_compute_task_run_output` a\n" +
                        "LEFT JOIN `forecast_compute_task_run` b\n" +
                        "ON a.`task_run_id`=b.`id`\n" +
                        "WHERE a.task_id=?", taskId);

        // 3.1 合并数据
        testDataset = ListUtils.merge(testDataset, predictResults,
                o -> o.groupKey(), o -> o.groupKey(),
                (test, predict) -> {
                    if (ListUtils.isNotEmpty(test) && ListUtils.isNotEmpty(predict)) {
                        test.get(0).setPredictValue(predict.get(0).getValue());
                        return test.get(0);
                    } else {
                        return null;
                    }
                });
        testDataset = ListUtils.filter(testDataset, o -> o != null);

        Map<String, PredictAndRealDTO> result = new HashMap<>();
        ListUtils.forEach(testDataset, o -> {
            String key = o.getDim1() + o.getDim2();
            PredictAndRealDTO dto = result.get(key);
            if (dto == null) {
                dto = new PredictAndRealDTO();
                dto.setDim1(o.getDim1());
                dto.setDim2(o.getDim2());
                dto.setPredict(o.getPredictValue());
                dto.setReal(o.getValue());
                dto.setTaskIds(ListUtils.newList(taskId));
                result.put(key, dto);
            } else {
                dto.setPredict(dto.getPredict().add(o.getPredictValue()));
                dto.setReal(dto.getReal().add(o.getValue()));
            }
        });

        return result;
    }

    @Data
    public static class ForecastComputeTaskInputTestDatasetVO extends  ForecastComputeTaskInputTestDatasetDO {

        /**预测值*/
        private BigDecimal predictValue;

        /**准确率百分比，已经乘以100*/
        private double accuracyRate;

        /**加权的百分比*/
        private double weightedAccuracyRate;


        /**当前测算的整体准确率*/
        private double globalAccuracyRate;
        /**移除了当前组合之后的准确率*/
        private double globalAccuracyRateIfRemove;
        /**差异，这个值为正表示这项预测做得好，为负表示预测做得不好*/
        private double diffAccuracyRate;


        public String groupKey() {
            return StringTools.join("&", getDate(), getDim1(), getDim2(), getDim3(), getDim4(), getDim5());
        }
    }

    @Data
    public static class PredictResultDTO {
        /** 时间序列日期<br/>Column: [date] */
        @Column(value = "date")
        private LocalDate date;
        /** 值<br/>Column: [value] */
        @Column(value = "value")
        private BigDecimal value;
        @Column(value = "dim1")
        private String dim1;
        @Column(value = "dim2")
        private String dim2;
        @Column(value = "dim3")
        private String dim3;
        @Column(value = "dim4")
        private String dim4;
        @Column(value = "dim5")
        private String dim5;

        public String groupKey() {
            return StringTools.join("&", getDate(), getDim1(), getDim2(), getDim3(), getDim4(), getDim5());
        }
    }
}
