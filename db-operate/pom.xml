<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>3.3.5</version> <!-- 先回退到3.3.5，不然http client 5包某个class没找到，后续再处理这个问题 -->
		<relativePath/> <!-- lookup parent from repository -->
	</parent>
	<groupId>com.pugwoo</groupId>
	<artifactId>db-operate</artifactId>
	<version>0.0.1-SNAPSHOT</version>
	<name>db-operate</name>
	<description>Demo project for Spring Boot</description>

	<properties>
		<java.version>21</java.version>
	</properties>

	<dependencies>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-jdbc</artifactId>
		</dependency>
		<dependency>
			<groupId>com.mysql</groupId>
			<artifactId>mysql-connector-j</artifactId>
		</dependency>

		<!-- 如果升级，需要试试 SyncMarkingScaleAndSpikeData-->
		<dependency>
			<groupId>com.clickhouse</groupId>
			<artifactId>clickhouse-jdbc</artifactId>
			<version>0.6.0-patch4</version>
		</dependency>
		<!-- clickhosue-jdbc 0.4.6+ 默认使用lz4压缩，所以需要引入lz4-java包 -->
		<dependency>
			<groupId>org.lz4</groupId>
			<artifactId>lz4-java</artifactId>
			<version>1.8.0</version>
		</dependency>
		<!-- clickhouse优先用这个 -->
		<!-- 如果升级，需要试试 SyncMarkingScaleAndSpikeData-->
		<dependency>
			<groupId>org.apache.httpcomponents.client5</groupId>
			<artifactId>httpclient5</artifactId>
			<version>5.3.1</version>
		</dependency>

		<dependency>
			<groupId>com.pugwoo</groupId>
			<artifactId>nimble-orm</artifactId>
			<version>1.7.1</version>
		</dependency>
		<dependency>
			<groupId>com.pugwoo</groupId>
			<artifactId>woo-utils</artifactId>
			<version>1.3.6</version>
		</dependency>
		<dependency>
			<groupId>com.pugwoo</groupId>
			<artifactId>redis-helper</artifactId>
			<version>1.5.3</version>
		</dependency>

		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>easyexcel</artifactId>
			<version>3.3.4</version>
		</dependency>
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-aop</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
			</plugin>
		</plugins>
	</build>

</project>
